🧪 小规模测试 - 5个subjects
==================================
📊 预处理数据集(5个subjects)...
2025-08-07 22:12:30,445 - INFO - 开始预处理完整Sleep-EDF数据集
2025-08-07 22:12:30,446 - INFO - 找到 39 个PSG文件
2025-08-07 22:12:30,446 - INFO - 
处理 Subject 1/39
2025-08-07 22:12:30,446 - INFO - PSG: SC4001E0-PSG.edf
2025-08-07 22:12:30,446 - INFO - Hypnogram: SC4001E0-Hypnogram.edf
2025-08-07 22:12:30,446 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001E0-PSG.edf
2025-08-07 22:12:35,551 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001E0-Hypnogram.edf
2025-08-07 22:12:35,564 - ERROR - 处理出错: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001E0-Hypnogram.edf"
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/preprocess_full_dataset.py", line 55, in process_subject
    annotations = mne.read_annotations(hypnogram_file)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/annotations.py", line 1198, in read_annotations
    _check_fname(
  File "<decorator-gen-0>", line 12, in _check_fname
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/utils/check.py", line 274, in _check_fname
    raise FileNotFoundError(f'{name} does not exist: "{fname}"')
FileNotFoundError: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001E0-Hypnogram.edf"
2025-08-07 22:12:35,606 - ERROR - ✗ Subject 1 处理失败
2025-08-07 22:12:35,607 - INFO - 
处理 Subject 2/39
2025-08-07 22:12:35,607 - INFO - PSG: SC4002E0-PSG.edf
2025-08-07 22:12:35,607 - INFO - Hypnogram: SC4002E0-Hypnogram.edf
2025-08-07 22:12:35,607 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4002E0-PSG.edf
2025-08-07 22:12:41,810 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4002E0-Hypnogram.edf
2025-08-07 22:12:41,811 - ERROR - 处理出错: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4002E0-Hypnogram.edf"
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/preprocess_full_dataset.py", line 55, in process_subject
    annotations = mne.read_annotations(hypnogram_file)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/annotations.py", line 1198, in read_annotations
    _check_fname(
  File "<decorator-gen-0>", line 12, in _check_fname
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/utils/check.py", line 274, in _check_fname
    raise FileNotFoundError(f'{name} does not exist: "{fname}"')
FileNotFoundError: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4002E0-Hypnogram.edf"
2025-08-07 22:12:41,846 - ERROR - ✗ Subject 2 处理失败
2025-08-07 22:12:41,846 - INFO - 
处理 Subject 3/39
2025-08-07 22:12:41,846 - INFO - PSG: SC4011E0-PSG.edf
2025-08-07 22:12:41,846 - INFO - Hypnogram: SC4011E0-Hypnogram.edf
2025-08-07 22:12:41,846 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4011E0-PSG.edf
2025-08-07 22:12:49,495 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4011E0-Hypnogram.edf
2025-08-07 22:12:49,495 - ERROR - 处理出错: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4011E0-Hypnogram.edf"
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/preprocess_full_dataset.py", line 55, in process_subject
    annotations = mne.read_annotations(hypnogram_file)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/annotations.py", line 1198, in read_annotations
    _check_fname(
  File "<decorator-gen-0>", line 12, in _check_fname
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/utils/check.py", line 274, in _check_fname
    raise FileNotFoundError(f'{name} does not exist: "{fname}"')
FileNotFoundError: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4011E0-Hypnogram.edf"
2025-08-07 22:12:49,533 - ERROR - ✗ Subject 3 处理失败
2025-08-07 22:12:49,533 - INFO - 
处理 Subject 4/39
2025-08-07 22:12:49,533 - INFO - PSG: SC4012E0-PSG.edf
2025-08-07 22:12:49,533 - INFO - Hypnogram: SC4012E0-Hypnogram.edf
2025-08-07 22:12:49,533 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4012E0-PSG.edf
2025-08-07 22:12:53,261 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4012E0-Hypnogram.edf
2025-08-07 22:12:53,261 - ERROR - 处理出错: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4012E0-Hypnogram.edf"
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/preprocess_full_dataset.py", line 55, in process_subject
    annotations = mne.read_annotations(hypnogram_file)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/annotations.py", line 1198, in read_annotations
    _check_fname(
  File "<decorator-gen-0>", line 12, in _check_fname
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/utils/check.py", line 274, in _check_fname
    raise FileNotFoundError(f'{name} does not exist: "{fname}"')
FileNotFoundError: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4012E0-Hypnogram.edf"
2025-08-07 22:12:53,301 - ERROR - ✗ Subject 4 处理失败
2025-08-07 22:12:53,301 - INFO - 
处理 Subject 5/39
2025-08-07 22:12:53,301 - INFO - PSG: SC4021E0-PSG.edf
2025-08-07 22:12:53,301 - INFO - Hypnogram: SC4021E0-Hypnogram.edf
2025-08-07 22:12:53,301 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4021E0-PSG.edf
2025-08-07 22:13:03,128 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4021E0-Hypnogram.edf
2025-08-07 22:13:03,128 - ERROR - 处理出错: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4021E0-Hypnogram.edf"
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/preprocess_full_dataset.py", line 55, in process_subject
    annotations = mne.read_annotations(hypnogram_file)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/annotations.py", line 1198, in read_annotations
    _check_fname(
  File "<decorator-gen-0>", line 12, in _check_fname
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/mne/utils/check.py", line 274, in _check_fname
    raise FileNotFoundError(f'{name} does not exist: "{fname}"')
FileNotFoundError: fname does not exist: "/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4021E0-Hypnogram.edf"
2025-08-07 22:13:03,170 - ERROR - ✗ Subject 5 处理失败
2025-08-07 22:13:03,171 - INFO - 
============================================================
2025-08-07 22:13:03,171 - INFO - 预处理完成！
2025-08-07 22:13:03,171 - INFO - 成功: 0/5 subjects
2025-08-07 22:13:03,171 - INFO - 失败: [1, 2, 3, 4, 5]
2025-08-07 22:13:03,171 - INFO - 数据保存在: ./processed_data_small
2025-08-07 22:13:03,171 - INFO - 数据集划分信息保存在: ./processed_data_small/dataset_split.json

🏗️ 训练MAMBAFORMER模型(10 epochs)...
2025-08-07 22:13:07,663 - INFO - 日志文件: ./log/mambaformer_small_test_20250807_221307.log
2025-08-07 22:13:07,664 - INFO - 🧪 MAMBAFORMER Sleep Stage Classification - 完整数据集实验
2025-08-07 22:13:07,664 - INFO - ============================================================
2025-08-07 22:13:07,664 - INFO - 实验配置:
{
  "data_path": "./processed_data_small",
  "train_subjects": "1,2,3",
  "val_subjects": "4",
  "test_subjects": "5",
  "batch_size": 64,
  "num_workers": 4,
  "model_name": "mambaformer",
  "model_type": "Epoch",
  "d_model": 64,
  "num_layers": 4,
  "nhead": 8,
  "window_size": 50,
  "epochs": 10,
  "lr": 0.0001,
  "weight_decay": 0.0001,
  "patience": 5,
  "lr_patience": 10,
  "use_progressive": true,
  "save_dir": "./checkpoints",
  "log_dir": "./log",
  "experiment_name": "mambaformer_small_test",
  "resume": null,
  "device": "auto",
  "use_amp": false
}
2025-08-07 22:13:07,749 - INFO - 📊 加载完整数据集...
2025-08-07 22:13:07,749 - INFO - 训练集subjects: ['1', '2', '3']
2025-08-07 22:13:07,749 - INFO - 验证集subjects: ['4']
2025-08-07 22:13:07,749 - INFO - 测试集subjects: ['5']
✓ 使用GPU: NVIDIA GeForce RTX 4090
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_full_dataset.py", line 543, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_full_dataset.py", line 517, in main
    train_loader, val_loader, test_loader = create_data_loaders(args, device, logger)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_full_dataset.py", line 145, in create_data_loaders
    train_loader, val_loader, test_loader = get_dataset(device, args, only_val=False)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/datasets/sleep_edf.py", line 287, in get_dataset
    [train_eeg_list, val_eeg_list] = split_data(eeg_list,args.train_data_list,args.val_data_list)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/datasets/sleep_edf.py", line 17, in split_data
    train_data_list = data_list[train_list]
IndexError: index 1 is out of bounds for axis 0 with size 0

✅ 测试完成！
📊 结果保存在 ./log 目录
