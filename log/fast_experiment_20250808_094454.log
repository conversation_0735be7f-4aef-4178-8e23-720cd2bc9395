2025-08-08 09:44:54,738 - 🚀 快速实验 - 高效睡眠分期网络
2025-08-08 09:44:54,739 - 配置: {
  "data_path": "./processed_data_fixed",
  "train_subjects": [
    1,
    2,
    3,
    4,
    5
  ],
  "val_subjects": [
    6,
    7
  ],
  "test_subjects": [
    8
  ],
  "max_epochs_per_subject": 150,
  "batch_size": 64,
  "epochs": 100,
  "lr": 0.001,
  "patience": 15,
  "weights": [
    1.0,
    2.0,
    1.0,
    2.0,
    2.0
  ]
}
2025-08-08 09:44:54,842 - 使用设备: cuda
2025-08-08 09:45:02,313 - 模型参数量: 2,259,653
2025-08-08 09:45:04,628 - Epoch 1/100: Train Loss: 5.6791, Train Acc: 0.4814 | Val Loss: 0.8639, Val Acc: 0.6000
2025-08-08 09:45:04,662 - ✓ 新的最佳准确率: 0.6000
2025-08-08 09:45:06,849 - Epoch 2/100: Train Loss: 4.8273, Train Acc: 0.5545 | Val Loss: 0.5883, Val Acc: 0.7333
2025-08-08 09:45:06,904 - ✓ 新的最佳准确率: 0.7333
2025-08-08 09:45:09,055 - Epoch 3/100: Train Loss: 4.3374, Train Acc: 0.5903 | Val Loss: 0.5536, Val Acc: 0.7533
2025-08-08 09:45:09,103 - ✓ 新的最佳准确率: 0.7533
2025-08-08 09:45:11,277 - Epoch 4/100: Train Loss: 3.9336, Train Acc: 0.6014 | Val Loss: 0.4743, Val Acc: 0.8133
2025-08-08 09:45:11,336 - ✓ 新的最佳准确率: 0.8133
2025-08-08 09:45:11,337 - 🎉 达到80%准确率目标！
2025-08-08 09:45:11,337 - 
📊 测试集评估
2025-08-08 09:45:12,080 - 测试准确率: 0.8133
2025-08-08 09:45:12,119 - 
分类报告:
              precision    recall  f1-score   support

        Wake       1.00      0.60      0.75        10
          N1       0.59      0.74      0.65        23
          N2       0.73      0.80      0.76        40
          N3       0.94      0.94      0.94        71
         REM       0.00      0.00      0.00         6

    accuracy                           0.81       150
   macro avg       0.65      0.62      0.62       150
weighted avg       0.80      0.81      0.80       150

2025-08-08 09:45:12,137 - 
混淆矩阵:
[[ 6  3  1  0  0]
 [ 0 17  6  0  0]
 [ 0  4 32  4  0]
 [ 0  0  4 67  0]
 [ 0  5  1  0  0]]
2025-08-08 09:45:12,137 - 
✅ 实验完成！最佳验证准确率: 0.8133, 测试准确率: 0.8133
