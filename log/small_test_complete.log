🧪 小规模测试 - 5个subjects
==================================
📊 预处理数据集(5个subjects)...
2025-08-07 22:17:06,459 - INFO - 开始预处理完整Sleep-EDF数据集
2025-08-07 22:17:06,460 - INFO - 找到 39 个PSG文件
2025-08-07 22:17:06,460 - INFO - 
处理 Subject 1/39
2025-08-07 22:17:06,460 - INFO - PSG: SC4001E0-PSG.edf
2025-08-07 22:17:06,460 - INFO - Hypnogram: SC4001EC-Hypnogram.edf
2025-08-07 22:17:06,460 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001E0-PSG.edf
2025-08-07 22:17:11,917 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001EC-Hypnogram.edf
2025-08-07 22:17:12,185 - INFO - 数据形状 - EEG: (153, 3000), EOG: (153, 3000), Labels: (153,)
2025-08-07 22:17:12,185 - INFO - 标签分布: [12 24 40 71  6]
2025-08-07 22:17:12,194 - INFO - 保存: ./processed_data_small/x01.h5
2025-08-07 22:17:12,198 - INFO - 保存: ./processed_data_small/eog01.h5
2025-08-07 22:17:12,199 - INFO - 保存: ./processed_data_small/y01.h5
2025-08-07 22:17:12,200 - INFO - 保存: ./processed_data_small/mean01.h5
2025-08-07 22:17:12,200 - INFO - 保存: ./processed_data_small/std01.h5
2025-08-07 22:17:12,201 - INFO - 保存: ./processed_data_small/eog_m01.h5
2025-08-07 22:17:12,202 - INFO - 保存: ./processed_data_small/eog_s01.h5
2025-08-07 22:17:12,243 - INFO - ✓ Subject 1 处理成功
2025-08-07 22:17:12,243 - INFO -   EEG形状: (153, 3000)
2025-08-07 22:17:12,243 - INFO -   EOG形状: (153, 3000)
2025-08-07 22:17:12,243 - INFO -   标签形状: (153,)
2025-08-07 22:17:12,244 - INFO -   标签分布: [12 24 40 71  6]
2025-08-07 22:17:12,244 - INFO - 
处理 Subject 2/39
2025-08-07 22:17:12,244 - INFO - PSG: SC4002E0-PSG.edf
2025-08-07 22:17:12,244 - INFO - Hypnogram: SC4002EC-Hypnogram.edf
2025-08-07 22:17:12,244 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4002E0-PSG.edf
2025-08-07 22:17:18,307 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4002EC-Hypnogram.edf
2025-08-07 22:17:18,531 - INFO - 数据形状 - EEG: (150, 3000), EOG: (150, 3000), Labels: (150,)
2025-08-07 22:17:18,531 - INFO - 标签分布: [23 32 37 46 12]
2025-08-07 22:17:18,539 - INFO - 保存: ./processed_data_small/x02.h5
2025-08-07 22:17:18,543 - INFO - 保存: ./processed_data_small/eog02.h5
2025-08-07 22:17:18,544 - INFO - 保存: ./processed_data_small/y02.h5
2025-08-07 22:17:18,545 - INFO - 保存: ./processed_data_small/mean02.h5
2025-08-07 22:17:18,546 - INFO - 保存: ./processed_data_small/std02.h5
2025-08-07 22:17:18,546 - INFO - 保存: ./processed_data_small/eog_m02.h5
2025-08-07 22:17:18,547 - INFO - 保存: ./processed_data_small/eog_s02.h5
2025-08-07 22:17:18,589 - INFO - ✓ Subject 2 处理成功
2025-08-07 22:17:18,589 - INFO -   EEG形状: (150, 3000)
2025-08-07 22:17:18,589 - INFO -   EOG形状: (150, 3000)
2025-08-07 22:17:18,589 - INFO -   标签形状: (150,)
2025-08-07 22:17:18,589 - INFO -   标签分布: [23 32 37 46 12]
2025-08-07 22:17:18,590 - INFO - 
处理 Subject 3/39
2025-08-07 22:17:18,590 - INFO - PSG: SC4011E0-PSG.edf
2025-08-07 22:17:18,590 - INFO - Hypnogram: SC4011EH-Hypnogram.edf
2025-08-07 22:17:18,590 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4011E0-PSG.edf
2025-08-07 22:17:26,792 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4011EH-Hypnogram.edf
2025-08-07 22:17:26,991 - INFO - 数据形状 - EEG: (125, 3000), EOG: (125, 3000), Labels: (125,)
2025-08-07 22:17:26,991 - INFO - 标签分布: [15 33 40 29  8]
2025-08-07 22:17:26,997 - INFO - 保存: ./processed_data_small/x03.h5
2025-08-07 22:17:27,000 - INFO - 保存: ./processed_data_small/eog03.h5
2025-08-07 22:17:27,001 - INFO - 保存: ./processed_data_small/y03.h5
2025-08-07 22:17:27,001 - INFO - 保存: ./processed_data_small/mean03.h5
2025-08-07 22:17:27,002 - INFO - 保存: ./processed_data_small/std03.h5
2025-08-07 22:17:27,002 - INFO - 保存: ./processed_data_small/eog_m03.h5
2025-08-07 22:17:27,003 - INFO - 保存: ./processed_data_small/eog_s03.h5
2025-08-07 22:17:27,045 - INFO - ✓ Subject 3 处理成功
2025-08-07 22:17:27,045 - INFO -   EEG形状: (125, 3000)
2025-08-07 22:17:27,045 - INFO -   EOG形状: (125, 3000)
2025-08-07 22:17:27,045 - INFO -   标签形状: (125,)
2025-08-07 22:17:27,045 - INFO -   标签分布: [15 33 40 29  8]
2025-08-07 22:17:27,046 - INFO - 
处理 Subject 4/39
2025-08-07 22:17:27,046 - INFO - PSG: SC4012E0-PSG.edf
2025-08-07 22:17:27,046 - INFO - Hypnogram: SC4012EC-Hypnogram.edf
2025-08-07 22:17:27,046 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4012E0-PSG.edf
2025-08-07 22:17:31,012 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4012EC-Hypnogram.edf
2025-08-07 22:17:31,242 - INFO - 数据形状 - EEG: (170, 3000), EOG: (170, 3000), Labels: (170,)
2025-08-07 22:17:31,243 - INFO - 标签分布: [17 42 55 38 18]
2025-08-07 22:17:31,253 - INFO - 保存: ./processed_data_small/x04.h5
2025-08-07 22:17:31,257 - INFO - 保存: ./processed_data_small/eog04.h5
2025-08-07 22:17:31,258 - INFO - 保存: ./processed_data_small/y04.h5
2025-08-07 22:17:31,259 - INFO - 保存: ./processed_data_small/mean04.h5
2025-08-07 22:17:31,260 - INFO - 保存: ./processed_data_small/std04.h5
2025-08-07 22:17:31,261 - INFO - 保存: ./processed_data_small/eog_m04.h5
2025-08-07 22:17:31,262 - INFO - 保存: ./processed_data_small/eog_s04.h5
2025-08-07 22:17:31,308 - INFO - ✓ Subject 4 处理成功
2025-08-07 22:17:31,308 - INFO -   EEG形状: (170, 3000)
2025-08-07 22:17:31,308 - INFO -   EOG形状: (170, 3000)
2025-08-07 22:17:31,308 - INFO -   标签形状: (170,)
2025-08-07 22:17:31,308 - INFO -   标签分布: [17 42 55 38 18]
2025-08-07 22:17:31,309 - INFO - 
处理 Subject 5/39
2025-08-07 22:17:31,309 - INFO - PSG: SC4021E0-PSG.edf
2025-08-07 22:17:31,309 - INFO - Hypnogram: SC4021EH-Hypnogram.edf
2025-08-07 22:17:31,309 - INFO - 读取PSG数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4021E0-PSG.edf
2025-08-07 22:17:41,287 - INFO - 读取标注数据: /media/main/ypf/eeg/data-edf/sleep_edf_20/SC4021EH-Hypnogram.edf
2025-08-07 22:17:41,544 - INFO - 数据形状 - EEG: (160, 3000), EOG: (160, 3000), Labels: (160,)
2025-08-07 22:17:41,545 - INFO - 标签分布: [ 9 22 56 58 15]
2025-08-07 22:17:41,555 - INFO - 保存: ./processed_data_small/x05.h5
2025-08-07 22:17:41,559 - INFO - 保存: ./processed_data_small/eog05.h5
2025-08-07 22:17:41,559 - INFO - 保存: ./processed_data_small/y05.h5
2025-08-07 22:17:41,560 - INFO - 保存: ./processed_data_small/mean05.h5
2025-08-07 22:17:41,560 - INFO - 保存: ./processed_data_small/std05.h5
2025-08-07 22:17:41,561 - INFO - 保存: ./processed_data_small/eog_m05.h5
2025-08-07 22:17:41,562 - INFO - 保存: ./processed_data_small/eog_s05.h5
2025-08-07 22:17:41,599 - INFO - ✓ Subject 5 处理成功
2025-08-07 22:17:41,599 - INFO -   EEG形状: (160, 3000)
2025-08-07 22:17:41,599 - INFO -   EOG形状: (160, 3000)
2025-08-07 22:17:41,600 - INFO -   标签形状: (160,)
2025-08-07 22:17:41,600 - INFO -   标签分布: [ 9 22 56 58 15]
2025-08-07 22:17:41,600 - INFO - 
============================================================
2025-08-07 22:17:41,600 - INFO - 预处理完成！
2025-08-07 22:17:41,600 - INFO - 成功: 5/5 subjects
2025-08-07 22:17:41,600 - INFO - 数据保存在: ./processed_data_small
2025-08-07 22:17:41,601 - INFO - 数据集划分信息保存在: ./processed_data_small/dataset_split.json

🏗️ 训练MAMBAFORMER模型(10 epochs)...
2025-08-07 22:17:46,318 - INFO - 日志文件: ./log/mambaformer_small_test_20250807_221746.log
2025-08-07 22:17:46,318 - INFO - 🧪 MAMBAFORMER Sleep Stage Classification - 完整数据集实验
2025-08-07 22:17:46,318 - INFO - ============================================================
2025-08-07 22:17:46,318 - INFO - 实验配置:
{
  "data_path": "./processed_data_small",
  "train_subjects": "1,2,3",
  "val_subjects": "4",
  "test_subjects": "5",
  "batch_size": 64,
  "num_workers": 4,
  "model_name": "mambaformer",
  "model_type": "Epoch",
  "d_model": 64,
  "num_layers": 4,
  "nhead": 8,
  "window_size": 50,
  "epochs": 10,
  "lr": 0.0001,
  "weight_decay": 0.0001,
  "patience": 5,
  "lr_patience": 10,
  "use_progressive": true,
  "save_dir": "./checkpoints",
  "log_dir": "./log",
  "experiment_name": "mambaformer_small_test",
  "resume": null,
  "device": "auto",
  "use_amp": false
}
2025-08-07 22:17:46,383 - INFO - 📊 加载完整数据集...
2025-08-07 22:17:46,383 - INFO - 训练集subjects: ['1', '2', '3']
2025-08-07 22:17:46,384 - INFO - 验证集subjects: ['4']
2025-08-07 22:17:46,384 - INFO - 测试集subjects: ['5']
✓ 使用GPU: NVIDIA GeForce RTX 4090
Training Data Files: ===========================>
['./processed_data_small/x02.h5']
['./processed_data_small/eog02.h5']
['./processed_data_small/y02.h5']
['./processed_data_small/mean02.h5']
['./processed_data_small/std02.h5']
['./processed_data_small/eog_m02.h5']
['./processed_data_small/eog_s02.h5']
Validation Data Files: ===========================>
['./processed_data_small/x05.h5']
['./processed_data_small/eog05.h5']
['./processed_data_small/y05.h5']
['./processed_data_small/mean05.h5']
['./processed_data_small/std05.h5']
['./processed_data_small/eog_m05.h5']
['./processed_data_small/eog_s05.h5']
Loading Training Data for one-to-one classification ==================================>
Reading from ./processed_data_small/x02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150, 3000)
Reading from ./processed_data_small/eog02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150, 3000)
Reading from ./processed_data_small/y02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150,)
Labels count: [23 32 37 46 12]
Shape of EEG : (150, 3000) , EOG : (150, 3000)
Shape of Labels : torch.Size([150])
Reading Subject wise mean and sd
Reading from ./processed_data_small/mean02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150, 1)
Reading from ./processed_data_small/std02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150, 1)
Reading from ./processed_data_small/eog_m02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150, 1)
Reading from ./processed_data_small/eog_s02.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 150
Shape of each data : (150, 1)
Shapes of Mean  : EEG: (150, 1), EOG : (150, 1)
Shapes of Sd  : EEG: (150, 1), EOG : (150, 1)
Loading Val Data for one-to-one classification ==================================>
Reading from ./processed_data_small/x05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160, 3000)
Reading from ./processed_data_small/eog05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160, 3000)
Reading from ./processed_data_small/y05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160,)
Labels count: [ 9 22 56 58 15]
Shape of EEG : (160, 3000) , EOG : (160, 3000)
Shape of Labels : torch.Size([160])
Reading Subject wise mean and sd
Reading from ./processed_data_small/mean05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160, 1)
Reading from ./processed_data_small/std05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160, 1)
Reading from ./processed_data_small/eog_m05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160, 1)
Reading from ./processed_data_small/eog_s05.h5 ====================================================
Keys in the h5py file : <KeysViewHDF5 ['data']>
Number of samples : 160
Shape of each data : (160, 1)
Shapes of Mean  : EEG: (160, 1), EOG : (160, 1)
Shapes of Sd  : EEG: (160, 1), EOG : (160, 1)
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_full_dataset.py", line 543, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_full_dataset.py", line 517, in main
    train_loader, val_loader, test_loader = create_data_loaders(args, device, logger)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_full_dataset.py", line 145, in create_data_loaders
    train_loader, val_loader, test_loader = get_dataset(device, args, only_val=False)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/datasets/sleep_edf.py", line 363, in get_dataset
    eeg_data, eog_data, label = next(iter(train_data_loader))
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/fetch.py", line 52, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/fetch.py", line 52, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/datasets/sleep_edf.py", line 107, in __getitem__
    eeg_data = self.transform(eeg_data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torchvision/transforms/transforms.py", line 95, in __call__
    img = t(img)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torchvision/transforms/transforms.py", line 137, in __call__
    return F.to_tensor(pic)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torchvision/transforms/functional.py", line 145, in to_tensor
    raise ValueError(f"pic should be 2/3 dimensional. Got {pic.ndim} dimensions.")
ValueError: pic should be 2/3 dimensional. Got 1 dimensions.

✅ 测试完成！
📊 结果保存在 ./log 目录
