2025-08-07 22:19:54,330 - INFO - 日志文件: ./log/mambaformer_sleepEDF_20250807_221954.log
2025-08-07 22:19:54,331 - INFO - 🧪 MAMBAFORMER Sleep Stage Classification
2025-08-07 22:19:54,331 - INFO - 配置: {
  "data_path": "./processed_data_small",
  "train_subjects": [
    "1",
    "2",
    "3"
  ],
  "val_subjects": [
    "4"
  ],
  "test_subjects": [
    "5"
  ],
  "batch_size": 64,
  "epochs": 20,
  "lr": 0.0001,
  "patience": 5,
  "experiment_name": "mambaformer_sleepEDF",
  "log_dir": "./log",
  "save_dir": "./checkpoints"
}
2025-08-07 22:19:54,367 - INFO - 使用设备: cuda
2025-08-07 22:19:54,495 - INFO - 训练集: 428 样本, 7 批次
2025-08-07 22:19:54,495 - INFO - 验证集: 170 样本, 3 批次
2025-08-07 22:19:54,495 - INFO - 测试集: 160 样本, 3 批次
2025-08-07 22:19:54,723 - INFO - 模型参数量: 1,303,308
加载数据集: 3 subjects, 428 epochs
EEG形状: torch.Size([428, 3000]), EOG形状: torch.Size([428, 3000])
标签分布: tensor([ 50,  89, 117, 146,  26])
加载数据集: 1 subjects, 170 epochs
EEG形状: torch.Size([170, 3000]), EOG形状: torch.Size([170, 3000])
标签分布: tensor([17, 42, 55, 38, 18])
加载数据集: 1 subjects, 160 epochs
EEG形状: torch.Size([160, 3000]), EOG形状: torch.Size([160, 3000])
标签分布: tensor([ 9, 22, 56, 58, 15])

Epoch 1 [Train]:   0%|          | 0/7 [00:00<?, ?it/s]
Epoch 1 [Train]:   0%|          | 0/7 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_mambaformer_compatible.py", line 331, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_mambaformer_compatible.py", line 264, in main
    train_loss, train_acc = train_epoch(model, train_loader, optimizer, device, epoch, logger)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/train_mambaformer_compatible.py", line 129, in train_epoch
    losses = model.compute_loss(outputs, labels, stage="both")
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/models/mambaformer_net.py", line 320, in compute_loss
    consistency_loss = self.classifier.compute_consistency_loss(outputs, labels)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/models/progressive_classifier.py", line 295, in compute_consistency_loss
    coarse_labels = self.fine_to_coarse_mapping[labels]
RuntimeError: indices should be either on cpu or on the same device as the indexed tensor (cpu)
