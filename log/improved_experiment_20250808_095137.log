2025-08-08 09:51:37,425 - 🚀 改进版实验 - 目标85%+准确率
2025-08-08 09:51:37,425 - 配置: {
  "data_path": "./processed_data_fixed",
  "train_subjects": [
    1,
    2,
    3,
    4,
    5,
    6
  ],
  "val_subjects": [
    7,
    8
  ],
  "test_subjects": [
    9
  ],
  "max_epochs_per_subject": 200,
  "batch_size": 48,
  "epochs": 50,
  "lr": 0.0005,
  "patience": 10,
  "focal_gamma": 2.0,
  "weights": [
    1.0,
    2.0,
    1.0,
    2.0,
    3.0
  ]
}
2025-08-08 09:51:37,522 - 使用设备: cuda
2025-08-08 09:51:45,157 - 模型参数量: 4,043,052
2025-08-08 09:51:48,490 - Epoch 1/50: Train Loss: 5.5105, Train Acc: 0.4040 | Val Acc: 0.5490
2025-08-08 09:51:48,490 - 类别准确率: W=0.67, N1=0.00, N2=0.00, N3=0.99, REM=1.00
2025-08-08 09:51:48,545 - ✓ 新的最佳准确率: 0.5490
2025-08-08 09:51:51,567 - Epoch 2/50: Train Loss: 4.7976, Train Acc: 0.4962 | Val Acc: 0.6144
2025-08-08 09:51:51,567 - 类别准确率: W=0.67, N1=0.08, N2=0.28, N3=0.94, REM=1.00
2025-08-08 09:51:51,671 - ✓ 新的最佳准确率: 0.6144
2025-08-08 09:51:54,897 - Epoch 3/50: Train Loss: 4.5055, Train Acc: 0.5181 | Val Acc: 0.6471
2025-08-08 09:51:54,898 - 类别准确率: W=0.75, N1=0.08, N2=0.33, N3=0.97, REM=1.00
2025-08-08 09:51:54,998 - ✓ 新的最佳准确率: 0.6471
2025-08-08 09:51:58,100 - Epoch 4/50: Train Loss: 4.2279, Train Acc: 0.5598 | Val Acc: 0.6667
2025-08-08 09:51:58,100 - 类别准确率: W=0.58, N1=0.21, N2=0.35, N3=0.99, REM=1.00
2025-08-08 09:51:58,184 - ✓ 新的最佳准确率: 0.6667
2025-08-08 09:52:01,378 - Epoch 5/50: Train Loss: 4.0642, Train Acc: 0.5971 | Val Acc: 0.6928
2025-08-08 09:52:01,378 - 类别准确率: W=0.75, N1=0.50, N2=0.35, N3=0.92, REM=1.00
2025-08-08 09:52:01,491 - ✓ 新的最佳准确率: 0.6928
2025-08-08 09:52:04,533 - Epoch 6/50: Train Loss: 3.9862, Train Acc: 0.5950 | Val Acc: 0.7124
2025-08-08 09:52:04,534 - 类别准确率: W=0.75, N1=0.54, N2=0.38, N3=0.94, REM=0.83
2025-08-08 09:52:04,641 - ✓ 新的最佳准确率: 0.7124
2025-08-08 09:52:07,675 - Epoch 7/50: Train Loss: 3.8051, Train Acc: 0.6411 | Val Acc: 0.7451
2025-08-08 09:52:07,675 - 类别准确率: W=0.75, N1=0.62, N2=0.35, N3=0.99, REM=1.00
2025-08-08 09:52:07,773 - ✓ 新的最佳准确率: 0.7451
2025-08-08 09:52:10,732 - Epoch 8/50: Train Loss: 3.7158, Train Acc: 0.6641 | Val Acc: 0.7974
2025-08-08 09:52:10,733 - 类别准确率: W=1.00, N1=0.83, N2=0.38, N3=0.97, REM=1.00
2025-08-08 09:52:10,832 - ✓ 新的最佳准确率: 0.7974
2025-08-08 09:52:13,851 - Epoch 9/50: Train Loss: 3.8072, Train Acc: 0.6136 | Val Acc: 0.8170
2025-08-08 09:52:13,851 - 类别准确率: W=0.83, N1=0.92, N2=0.50, N3=0.94, REM=1.00
2025-08-08 09:52:13,948 - ✓ 新的最佳准确率: 0.8170
2025-08-08 09:52:16,961 - Epoch 10/50: Train Loss: 3.5277, Train Acc: 0.6872 | Val Acc: 0.8235
2025-08-08 09:52:16,961 - 类别准确率: W=1.00, N1=0.88, N2=0.42, N3=0.99, REM=1.00
2025-08-08 09:52:17,060 - ✓ 新的最佳准确率: 0.8235
2025-08-08 09:52:19,989 - Epoch 11/50: Train Loss: 3.4771, Train Acc: 0.6729 | Val Acc: 0.8562
2025-08-08 09:52:19,990 - 类别准确率: W=0.75, N1=0.96, N2=0.60, N3=0.97, REM=1.00
2025-08-08 09:52:20,098 - ✓ 新的最佳准确率: 0.8562
2025-08-08 09:52:20,098 - 🎉 达到85%准确率目标！
2025-08-08 09:52:20,098 - 
📊 测试集评估
2025-08-08 09:52:20,822 - 测试准确率: 0.8562
2025-08-08 09:52:20,834 - 
分类报告:
              precision    recall  f1-score   support

        Wake      1.000     0.750     0.857        12
          N1      0.821     0.958     0.885        24
          N2      0.889     0.600     0.716        40
          N3      0.831     0.972     0.896        71
         REM      1.000     1.000     1.000         6

    accuracy                          0.856       153
   macro avg      0.908     0.856     0.871       153
weighted avg      0.865     0.856     0.848       153

2025-08-08 09:52:20,836 - 
混淆矩阵:
[[ 9  3  0  0  0]
 [ 0 23  1  0  0]
 [ 0  2 24 14  0]
 [ 0  0  2 69  0]
 [ 0  0  0  0  6]]
2025-08-08 09:52:20,836 - 
✅ 实验完成！最佳验证准确率: 0.8562, 测试准确率: 0.8562
