# 项目结构说明

## 🏗️ 整体架构

本项目包含两个独立的睡眠分期分类实现：

### 1. cmt_original/ - 原始CMT实现
- 基于Cross-Modal Transformer的原始代码
- 包含序列级和epoch级的CMT模型
- 完整的预处理和数据准备pipeline

### 2. mambaformer/ - MAMBAFORMER实现（当前重点）
- 简化但有效的MAMBAFORMER架构
- 无数据泄露的K折交叉验证
- 面向ICASSP 2026论文投稿

## 📁 详细目录说明

```
Cross-Modal-Transformer/
│
├── cmt_original/                 # 原始CMT代码
│   ├── models/                   # CMT模型
│   ├── preprocessing/            # 数据预处理
│   ├── utils/                    # 工具函数
│   └── data_preparations/        # 数据准备
│
├── mambaformer/                  # MAMBAFORMER实现 ⭐
│   ├── models/                   # 模型定义
│   ├── training/                 # 训练脚本
│   ├── evaluation/               # 评估工具
│   ├── preprocessing/            # 预处理
│   ├── utils/                    # 工具函数
│   ├── run_training.py           # 快速运行脚本
│   └── README.md                 # MAMBAFORMER说明
│
├── configs/                      # 共享配置文件
│   ├── subject_aware_folds.json  # K折分割配置
│   └── *.json                    # 其他配置
│
├── logs/                         # 训练日志
├── checkpoints/                  # 模型权重
├── datasets/                     # 原始数据集
├── experiments/                  # 实验管理
├── docs/                         # 项目文档
└── README.md                     # 主README
```

## 🚀 如何使用

### 运行MAMBAFORMER实验
```bash
cd mambaformer
python run_training.py
```

### 运行原始CMT（如需要）
```bash
cd cmt_original
# 参考原始文档运行
```

## ✅ 这种组织的优势

1. **代码分离**: CMT和MAMBAFORMER代码完全独立，不会相互干扰
2. **清晰结构**: 一眼就能看出哪些是原始代码，哪些是新实现
3. **保持兼容**: 所有导入路径保持相对独立，代码可以正常运行
4. **便于管理**: 可以独立开发、测试和部署两个系统
5. **共享资源**: configs、logs等目录在根目录共享使用

## 📝 注意事项

- 主要工作集中在 `mambaformer/` 目录
- 配置文件和结果保存在根目录的 `configs/`
- 日志文件保存在根目录的 `logs/`
- 数据路径使用绝对路径，不受目录结构影响