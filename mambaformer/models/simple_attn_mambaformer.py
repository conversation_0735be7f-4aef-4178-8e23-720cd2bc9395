"""
简化版MAMBAFORMER - 用于AttnSleep集成
去除复杂的跨模态注意力，专注于核心功能验证
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

class GELU(nn.Module):
    """GELU激活函数"""
    def __init__(self):
        super(GELU, self).__init__()
        
    def forward(self, x):
        return torch.nn.functional.gelu(x)

class SELayer(nn.Module):
    """Squeeze-and-Excitation注意力层"""
    def __init__(self, channel, reduction=16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1)
        return x * y.expand_as(x)

class SEBasicBlock(nn.Module):
    """SE残差块"""
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None, reduction=16):
        super(SEBasicBlock, self).__init__()
        self.conv1 = nn.Conv1d(inplanes, planes, stride)
        self.bn1 = nn.BatchNorm1d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv1d(planes, planes, 1)
        self.bn2 = nn.BatchNorm1d(planes)
        self.se = SELayer(planes, reduction)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.se(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)
        return out

class MultiScaleCNN(nn.Module):
    """
    多尺度CNN特征提取器 - 简化版
    """
    def __init__(self, input_channels=3, afr_reduced_cnn_size=80):
        super(MultiScaleCNN, self).__init__()
        self.input_channels = input_channels
        drate = 0.3
        self.GELU = GELU()
        
        # 短时特征路径
        self.features1 = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6, bias=False, padding=24),
            nn.BatchNorm1d(64),
            self.GELU,
            nn.MaxPool1d(kernel_size=8, stride=2, padding=4),
            nn.Dropout(drate),

            nn.Conv1d(64, 128, kernel_size=8, stride=1, bias=False, padding=4),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.Conv1d(128, 128, kernel_size=8, stride=1, bias=False, padding=4),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.MaxPool1d(kernel_size=4, stride=4, padding=2)
        )

        # 长时特征路径
        self.features2 = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=400, stride=50, bias=False, padding=200),
            nn.BatchNorm1d(64),
            self.GELU,
            nn.MaxPool1d(kernel_size=4, stride=2, padding=2),
            nn.Dropout(drate),

            nn.Conv1d(64, 128, kernel_size=7, stride=1, bias=False, padding=3),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.Conv1d(128, 128, kernel_size=7, stride=1, bias=False, padding=3),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.MaxPool1d(kernel_size=2, stride=2, padding=1)
        )
        
        self.dropout = nn.Dropout(drate)
        self.inplanes = 128
        
        # AFR层
        self.AFR = self._make_layer(SEBasicBlock, afr_reduced_cnn_size, 1)

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv1d(self.inplanes, planes * block.expansion,
                          kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm1d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x):
        # 确保输入形状正确: (batch, channels, seq_len)
        if len(x.shape) == 2:
            x = x.unsqueeze(1)
        elif len(x.shape) == 3 and x.shape[1] == 3000:
            x = x.transpose(1, 2)
            
        x1 = self.features1(x)
        x2 = self.features2(x)
        x_concat = torch.cat((x1, x2), dim=2)
        x_concat = self.dropout(x_concat)
        x_concat = self.AFR(x_concat)
        return x_concat

class MultiHeadAttention(nn.Module):
    """简化的多头自注意力"""
    def __init__(self, d_model, n_heads=8, dropout=0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.W_q = nn.Linear(d_model, d_model, bias=False)
        self.W_k = nn.Linear(d_model, d_model, bias=False)
        self.W_v = nn.Linear(d_model, d_model, bias=False)
        self.W_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.size()
        
        # 生成Q, K, V
        Q = self.W_q(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.W_k(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.W_v(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力
        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        
        return self.W_o(context)

class TransformerBlock(nn.Module):
    """简化的Transformer块"""
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super().__init__()
        
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # 自注意力 + 残差连接
        attn_output = self.attention(x)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络 + 残差连接
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x

class SimpleMAMBAFORMER(nn.Module):
    """
    简化版MAMBAFORMER - 专注于集成验证
    使用CNN + Transformer结构，去除复杂的跨模态和MAMBA组件
    """
    def __init__(self, input_channels=3, num_classes=5, d_model=128, 
                 n_transformer_layers=4, n_heads=8, dropout=0.15, 
                 afr_reduced_cnn_size=80, use_multitask=False):
        super(SimpleMAMBAFORMER, self).__init__()
        
        self.input_channels = input_channels
        self.num_classes = num_classes
        self.d_model = d_model
        self.use_multitask = use_multitask
        
        # 1. CNN特征提取
        self.cnn = MultiScaleCNN(
            input_channels=input_channels, 
            afr_reduced_cnn_size=afr_reduced_cnn_size
        )
        
        # 2. 特征投影
        self.feature_projection = None  # 延迟初始化
        
        # 3. Transformer编码器
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_model * 4, dropout)
            for _ in range(n_transformer_layers)
        ])
        
        # 4. 分类头
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, num_classes)
        )
        
        # 5. 多任务辅助头（可选）
        if use_multitask:
            self.rem_head = nn.Sequential(
                nn.Linear(d_model, d_model // 4),
                nn.GELU(),
                nn.Dropout(dropout * 0.5),
                nn.Linear(d_model // 4, 1),
                nn.Sigmoid()
            )
            
            self.sws_head = nn.Sequential(
                nn.Linear(d_model, d_model // 4),
                nn.GELU(),
                nn.Dropout(dropout * 0.5),
                nn.Linear(d_model // 4, 1),
                nn.Sigmoid()
            )
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """权重初始化"""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight.data, 0.0, 0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias.data)
        elif isinstance(module, nn.Conv1d):
            torch.nn.init.normal_(module.weight.data, 0.0, 0.02)
        elif isinstance(module, (nn.BatchNorm1d, nn.LayerNorm)):
            torch.nn.init.ones_(module.weight.data)
            torch.nn.init.zeros_(module.bias.data)
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入tensor，形状可以是：
               - (batch, channels, seq_len)
        
        Returns:
            stage_logits 或 dict with keys: 'stage_logits', 'rem_scores', 'sws_scores'
        """
        batch_size = x.shape[0]
        
        # 1. CNN特征提取
        cnn_features = self.cnn(x)  # (batch, afr_reduced_cnn_size, seq_len)
        
        # 2. 特征投影 - 自适应初始化
        seq_len = cnn_features.shape[2]
        cnn_channels = cnn_features.shape[1]
        
        # 延迟初始化投影层
        if self.feature_projection is None:
            self.feature_projection = nn.Sequential(
                nn.Linear(cnn_channels, self.d_model),
                nn.LayerNorm(self.d_model),
                nn.GELU(),
                nn.Dropout(0.1)
            ).to(cnn_features.device)
        
        # 3. 为Transformer准备序列
        seq_features = cnn_features.transpose(1, 2)  # (batch, seq_len, cnn_channels)
        seq_input = self.feature_projection(seq_features)  # (batch, seq_len, d_model)
        
        # 4. Transformer编码
        encoded = seq_input
        for transformer_layer in self.transformer_layers:
            encoded = transformer_layer(encoded)
        
        # 5. 全局池化
        encoded_transposed = encoded.transpose(1, 2)  # (batch, d_model, seq_len)
        pooled = self.global_pool(encoded_transposed).squeeze(-1)  # (batch, d_model)
        
        # 6. 分类
        stage_logits = self.classifier(pooled)  # (batch, num_classes)
        
        # 7. 多任务输出
        if self.use_multitask:
            rem_scores = self.rem_head(pooled).squeeze(-1)  # (batch,)
            sws_scores = self.sws_head(pooled).squeeze(-1)  # (batch,)
            
            return {
                'stage_logits': stage_logits,
                'rem_scores': rem_scores,
                'sws_scores': sws_scores
            }
        else:
            return stage_logits

# 为AttnSleep兼容性创建接口
class SimplifiedMAMBAFORMER(nn.Module):
    """简化版MAMBAFORMER的AttnSleep兼容接口"""
    def __init__(self):
        super(SimplifiedMAMBAFORMER, self).__init__()
        
        self.model = SimpleMAMBAFORMER(
            input_channels=1,  # AttnSleep单通道
            num_classes=5,
            d_model=80,  # 与AttnSleep d_model保持一致
            n_transformer_layers=4,
            n_heads=8,
            dropout=0.15,
            afr_reduced_cnn_size=80,
            use_multitask=False
        )
    
    def forward(self, x):
        return self.model(x)

# 多通道版本
class MultiChannelSimplifiedMAMBAFORMER(nn.Module):
    """多通道简化版MAMBAFORMER"""
    def __init__(self):
        super(MultiChannelSimplifiedMAMBAFORMER, self).__init__()
        
        self.model = SimpleMAMBAFORMER(
            input_channels=3,  # EEG + 2xEOG
            num_classes=5,
            d_model=128,
            n_transformer_layers=4,
            n_heads=8,
            dropout=0.15,
            afr_reduced_cnn_size=80,
            use_multitask=True
        )
    
    def forward(self, x):
        return self.model(x)