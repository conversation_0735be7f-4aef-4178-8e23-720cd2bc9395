"""
改进版MAMBAFORMER - 解决过拟合问题
1. 减少模型复杂度
2. 增强正则化
3. 改进数据划分
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class RegularizedCrossModalAttention(nn.Module):
    """带强正则化的CrossModal注意力"""
    def __init__(self, d_model, n_heads=4, dropout=0.3):  # 减少头数，增加dropout
        super().__init__()
        self.d_model = d_model
        self.n_heads = n_heads
        self.head_dim = d_model // n_heads
        
        # 查询、键、值投影 - 添加权重衰减
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        
        # 输出投影
        self.out_linear = nn.Linear(d_model, d_model)
        
        # 强正则化
        self.dropout = nn.Dropout(dropout)
        self.attn_dropout = nn.Dropout(dropout * 0.5)  # 注意力dropout
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重，减小方差
        self._init_weights()
        
    def _init_weights(self):
        """保守的权重初始化"""
        for module in [self.q_linear, self.k_linear, self.v_linear, self.out_linear]:
            nn.init.normal_(module.weight, std=0.02)  # 更小的初始化
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, query_modality, key_value_modalities, mask=None):
        batch_size, seq_len, _ = query_modality.shape
        
        # 计算查询 - 添加dropout
        Q = self.dropout(self.q_linear(query_modality))
        Q = Q.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        
        # 合并键值模态
        all_kv = torch.cat([query_modality] + key_value_modalities, dim=1)
        
        K = self.dropout(self.k_linear(all_kv))
        V = self.dropout(self.v_linear(all_kv))
        
        K = K.view(batch_size, -1, self.n_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, -1, self.n_heads, self.head_dim).transpose(1, 2)
        
        # 注意力计算 - 温度缩放
        temperature = math.sqrt(self.head_dim) * 1.5  # 增大温度，平滑注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / temperature
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.attn_dropout(attn_weights)  # 注意力dropout
        
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        output = self.out_linear(context)
        output = self.layer_norm(output + query_modality)  # 残差连接
        
        return output, attn_weights


class SimpleModalityEncoder(nn.Module):
    """简化的模态编码器 - 减少参数"""
    def __init__(self, input_channels, d_model, modality_name):
        super().__init__()
        self.modality_name = modality_name
        
        # 简化的CNN - 减少层数和参数
        self.conv_layers = nn.Sequential(
            nn.Conv1d(input_channels, 32, kernel_size=25, stride=6),  # 减少通道数
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64), 
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU()
        )
        
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        self.modality_embedding = nn.Parameter(torch.randn(1, 1, d_model) * 0.02)
        
    def forward(self, x):
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)
        x = x.unsqueeze(1) + self.modality_embedding
        x = x.squeeze(1)
        return x


class ImprovedCrossModalMAMBAFORMER(nn.Module):
    """
    改进版CrossModal MAMBAFORMER
    - 减少模型复杂度
    - 增强正则化
    - 解决过拟合
    """
    def __init__(self, n_classes=5, d_model=96, n_heads=4, n_layers=3,  # 减少参数
                 dropout=0.3, seq_len=5):  # 增加dropout
        super().__init__()
        
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 简化的模态编码器
        self.eeg_encoder = SimpleModalityEncoder(3, d_model, "EEG")
        self.eog_encoder = SimpleModalityEncoder(1, d_model, "EOG")
        self.emg_encoder = SimpleModalityEncoder(1, d_model, "EMG")
        
        # 简化的CrossModal注意力
        self.eeg_cross_attn = RegularizedCrossModalAttention(d_model, n_heads, dropout)
        self.eog_cross_attn = RegularizedCrossModalAttention(d_model, n_heads, dropout)
        
        # 简化融合层
        self.fusion = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # 简化的Transformer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,  # 减少FFN大小
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True  # Pre-LN，更稳定
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=n_layers
        )
        
        # 输出层 - 添加更多正则化
        self.pre_classifier_dropout = nn.Dropout(dropout * 1.5)
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 简化的辅助任务
        self.auxiliary_head = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 2)
        )
        
        # 初始化
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建ImprovedCrossModalMAMBAFORMER: "
                    f"参数量={total_params:,}, d_model={d_model}, n_heads={n_heads}")
    
    def _init_weights(self):
        """保守的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 分离模态
        eeg_data = x[:, :, :, :3]
        eog_data = x[:, :, :, 3:4]
        emg_data = x[:, :, :, 4:5] if channels > 4 else eog_data.clone()
        
        # Reshape和编码
        eeg_flat = eeg_data.view(-1, time_steps, 3).transpose(1, 2)
        eog_flat = eog_data.view(-1, time_steps, 1).transpose(1, 2)
        emg_flat = emg_data.view(-1, time_steps, 1).transpose(1, 2)
        
        eeg_features = self.eeg_encoder(eeg_flat).view(batch_size, seq_len, self.d_model)
        eog_features = self.eog_encoder(eog_flat).view(batch_size, seq_len, self.d_model)
        emg_features = self.emg_encoder(emg_flat).view(batch_size, seq_len, self.d_model)
        
        # 简化的CrossModal融合 - 只用两个主要模态
        eeg_enhanced, _ = self.eeg_cross_attn(eeg_features, [eog_features, emg_features])
        eog_enhanced, _ = self.eog_cross_attn(eog_features, [eeg_features, emg_features])
        
        # 简单的加权融合
        fused_features = self.fusion(torch.cat([eeg_enhanced, eog_enhanced], dim=-1))
        
        # 时序建模
        fused_features = fused_features.transpose(0, 1)
        fused_features = self.pos_encoder(fused_features)
        fused_features = fused_features.transpose(0, 1)
        
        encoded = self.transformer_encoder(fused_features)
        
        # 输出预测
        encoded = self.pre_classifier_dropout(encoded)
        main_output = self.classifier(encoded)
        aux_output = self.auxiliary_head(encoded)
        
        return main_output, aux_output


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)