"""
完整的wICA-ICLabel-CrossModal MAMBAFORMER架构
整合所有组件的端到端睡眠分期模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional

# 导入各个组件
from crossmodal_attention import CrossModalTransformerLayer, create_crossmodal_transformer
from mamba_ssm import BiDirectionalMamba, create_sleep_mamba_stack

class MultiScaleCNNFeatureExtractor(nn.Module):
    """
    多尺度CNN特征提取器
    针对不同睡眠特征的多尺度时频特征提取
    """
    
    def __init__(self, input_channels=3, output_dim=128, dropout=0.1):
        super().__init__()
        
        # 多尺度卷积核，对应不同的睡眠特征
        # 小尺度：捕获高频特征（肌电、K复合波）
        self.conv_small = nn.Sequential(
            nn.Conv1d(input_channels, 32, kernel_size=10, stride=5, padding=5),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2)
        )
        
        # 中尺度：捕获中频特征（睡眠纺锤波、α波）
        self.conv_medium = nn.Sequential(
            nn.Conv1d(input_channels, 32, kernel_size=50, stride=25, padding=25),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2)
        )
        
        # 大尺度：捕获低频特征（慢波、δ波）
        self.conv_large = nn.Sequential(
            nn.Conv1d(input_channels, 32, kernel_size=200, stride=100, padding=100),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2)
        )
        
        # 时序下采样层
        self.temporal_reduction = nn.Sequential(
            nn.Conv1d(96, 128, kernel_size=10, stride=5, padding=5),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4),
            
            nn.Conv1d(128, output_dim, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(output_dim),
            nn.ReLU(),
        )
        
        # 自适应池化到固定长度
        self.adaptive_pool = nn.AdaptiveAvgPool1d(11)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入信号 (batch_size, channels, seq_len)
            
        Returns:
            features: 多尺度特征 (batch_size, output_dim, 11)
        """
        # 提取多尺度特征
        feat_small = self.conv_small(x)
        feat_medium = self.conv_medium(x)
        feat_large = self.conv_large(x)
        
        # 对齐时间维度
        min_len = min(feat_small.size(2), feat_medium.size(2), feat_large.size(2))
        feat_small = feat_small[:, :, :min_len]
        feat_medium = feat_medium[:, :, :min_len]
        feat_large = feat_large[:, :, :min_len]
        
        # 特征融合
        multi_scale_features = torch.cat([feat_small, feat_medium, feat_large], dim=1)
        
        # 时序降维
        features = self.temporal_reduction(multi_scale_features)
        features = self.adaptive_pool(features)
        features = self.dropout(features)
        
        return features

class MAMBAFORMER(nn.Module):
    """
    完整的MAMBAFORMER架构
    整合wICA-ICLabel预处理（外部）+ CrossModal注意力 + MAMBA序列建模
    """
    
    def __init__(self,
                 input_channels: int = 3,
                 num_classes: int = 5,
                 d_model: int = 128,
                 n_crossmodal_layers: int = 2,
                 n_mamba_layers: int = 4,
                 n_heads: int = 8,
                 dropout: float = 0.1,
                 use_bidirectional_mamba: bool = True,
                 sleep_stage_conditioning: bool = True):
        super().__init__()
        
        self.input_channels = input_channels
        self.num_classes = num_classes
        self.d_model = d_model
        
        # 1. 多尺度CNN特征提取
        self.feature_extractor = MultiScaleCNNFeatureExtractor(
            input_channels=input_channels,
            output_dim=d_model,
            dropout=dropout
        )
        
        # 2. 模态分离投影（EEG和EOG）
        self.eeg_projection = nn.Linear(d_model, d_model)
        self.eog_projection = nn.Linear(d_model, d_model)
        
        # 3. 位置编码
        self.positional_encoding = nn.Parameter(torch.randn(1, 11, d_model))
        
        # 4. CrossModal跨模态注意力层
        self.crossmodal_layers = nn.ModuleList([
            CrossModalTransformerLayer(
                d_model=d_model,
                n_heads=n_heads,
                dropout=dropout,
                n_sleep_stages=num_classes
            ) for _ in range(n_crossmodal_layers)
        ])
        
        # 5. MAMBA序列建模层
        self.mamba_encoder = create_sleep_mamba_stack(
            d_model=d_model,
            n_layers=n_mamba_layers,
            bidirectional=use_bidirectional_mamba,
            d_state=16,
            expand_factor=2,
            dropout=dropout,
            sleep_stage_conditioning=sleep_stage_conditioning
        )
        
        # 6. 睡眠阶段分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, num_classes)
        )
        
        # 7. 辅助任务：睡眠特征检测器
        self.rem_detector = nn.Linear(d_model, 1)  # REM检测
        self.spindle_detector = nn.Linear(d_model, 1)  # 纺锤波检测
        self.sws_detector = nn.Linear(d_model, 1)  # 慢波睡眠检测
        
    def extract_modal_features(self, features):
        """
        从CNN特征中提取模态特定特征
        
        Args:
            features: CNN特征 (batch_size, d_model, seq_len)
            
        Returns:
            eeg_features: EEG特征
            eog_features: EOG特征
        """
        # 转换维度
        features = features.transpose(1, 2)  # (B, seq_len, d_model)
        
        # 假设前2/3通道主要是EEG，后1/3主要包含EOG信息
        # 实际应用中可以根据通道配置调整
        eeg_weight = torch.tensor([1.0, 1.0, 0.3]).view(1, 1, 3, 1).to(features.device)
        eog_weight = torch.tensor([0.3, 0.3, 1.0]).view(1, 1, 3, 1).to(features.device)
        
        # 通过加权投影分离模态
        batch_size, seq_len, _ = features.shape
        
        # 应用模态特定投影
        eeg_features = self.eeg_projection(features)
        eog_features = self.eog_projection(features)
        
        return eeg_features, eog_features
        
    def forward(self, x, return_attention=False):
        """
        前向传播
        
        Args:
            x: 输入信号 (batch_size, channels, seq_len)
               假设已经过wICA-ICLabel预处理
            return_attention: 是否返回注意力权重
            
        Returns:
            outputs: 字典，包含分类结果和辅助输出
        """
        batch_size = x.size(0)
        
        # 1. CNN特征提取
        cnn_features = self.feature_extractor(x)  # (B, d_model, 11)
        
        # 2. 提取模态特定特征
        eeg_features, eog_features = self.extract_modal_features(cnn_features)
        
        # 3. 添加位置编码
        eeg_features = eeg_features + self.positional_encoding
        eog_features = eog_features + self.positional_encoding
        
        # 4. CrossModal跨模态融合
        crossmodal_features = None
        attention_weights = []
        stage_predictions_list = []
        
        for i, layer in enumerate(self.crossmodal_layers):
            if i == 0:
                # 第一层：使用原始EEG和EOG特征
                crossmodal_out, layer_info = layer(eeg_features, eog_features)
            else:
                # 后续层：使用融合后的特征
                crossmodal_out, layer_info = layer(crossmodal_out, crossmodal_out)
                
            crossmodal_features = crossmodal_out
            
            if return_attention:
                attention_weights.append(layer_info['attention_info'])
            stage_predictions_list.append(layer_info['stage_predictions'])
            
        # 5. MAMBA序列建模
        # 使用最后一层的阶段预测作为条件
        predicted_stages = torch.argmax(stage_predictions_list[-1], dim=-1)
        mamba_features = self.mamba_encoder(crossmodal_features, predicted_stages)
        
        # 6. 全局池化
        # 对序列维度进行池化，得到固定大小的表示
        global_features = mamba_features.mean(dim=1)  # (B, d_model)
        
        # 7. 主任务：睡眠阶段分类
        stage_logits = self.classifier(global_features)  # (B, num_classes)
        
        # 8. 辅助任务
        rem_scores = self.rem_detector(global_features).squeeze(-1)  # (B,)
        spindle_scores = self.spindle_detector(global_features).squeeze(-1)  # (B,)
        sws_scores = self.sws_detector(global_features).squeeze(-1)  # (B,)
        
        # 9. 组织输出
        outputs = {
            'stage_logits': stage_logits,
            'stage_probs': F.softmax(stage_logits, dim=-1),
            'rem_scores': torch.sigmoid(rem_scores),
            'spindle_scores': torch.sigmoid(spindle_scores),
            'sws_scores': torch.sigmoid(sws_scores),
            'global_features': global_features,
            'sequential_features': mamba_features
        }
        
        if return_attention:
            outputs['attention_weights'] = attention_weights
            outputs['stage_predictions_intermediate'] = stage_predictions_list
            
        return outputs

class MAMBAFORMERWithPreprocessing(nn.Module):
    """
    带完整预处理管道的MAMBAFORMER
    集成wICA和ICLabel预处理
    """
    
    def __init__(self, 
                 mambaformer_config: dict,
                 use_wica: bool = True,
                 use_iclabel: bool = True):
        super().__init__()
        
        self.use_wica = use_wica
        self.use_iclabel = use_iclabel
        
        # 主模型
        self.mambaformer = MAMBAFORMER(**mambaformer_config)
        
        # 注：wICA和ICLabel将在预处理阶段使用，不作为nn.Module
        
    def forward(self, x, preprocessed=False, return_attention=False):
        """
        前向传播
        
        Args:
            x: 输入信号 (batch_size, channels, seq_len)
            preprocessed: 是否已经预处理过
            return_attention: 是否返回注意力权重
            
        Returns:
            outputs: 模型输出
        """
        if not preprocessed:
            # 在实际应用中，这里应该调用wICA和ICLabel预处理
            # 由于预处理通常在CPU上进行且较慢，建议离线预处理
            print("警告：建议使用预处理后的数据以获得最佳性能")
            
        # 通过主模型
        outputs = self.mambaformer(x, return_attention=return_attention)
        
        return outputs

# 模型创建便利函数
def create_mambaformer(input_channels=3,
                      num_classes=5,
                      d_model=128,
                      n_crossmodal_layers=2,
                      n_mamba_layers=4,
                      n_heads=8,
                      dropout=0.1,
                      use_bidirectional_mamba=True,
                      sleep_stage_conditioning=True,
                      use_preprocessing=True):
    """
    创建MAMBAFORMER模型
    """
    config = {
        'input_channels': input_channels,
        'num_classes': num_classes,
        'd_model': d_model,
        'n_crossmodal_layers': n_crossmodal_layers,
        'n_mamba_layers': n_mamba_layers,
        'n_heads': n_heads,
        'dropout': dropout,
        'use_bidirectional_mamba': use_bidirectional_mamba,
        'sleep_stage_conditioning': sleep_stage_conditioning
    }
    
    if use_preprocessing:
        model = MAMBAFORMERWithPreprocessing(
            mambaformer_config=config,
            use_wica=True,
            use_iclabel=True
        )
    else:
        model = MAMBAFORMER(**config)
        
    return model

# 使用示例和测试
if __name__ == "__main__":
    # 测试参数
    batch_size = 4
    channels = 3
    seq_len = 3000
    num_classes = 5
    
    print("测试完整MAMBAFORMER架构...")
    
    # 创建测试数据
    x = torch.randn(batch_size, channels, seq_len)
    print(f"输入形状: {x.shape}")
    
    # 创建模型
    model = create_mambaformer(
        input_channels=channels,
        num_classes=num_classes,
        d_model=128,
        n_crossmodal_layers=2,
        n_mamba_layers=4
    )
    
    # 统计参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    # 前向传播测试
    print("\n前向传播测试...")
    outputs = model(x, preprocessed=True, return_attention=True)
    
    print(f"阶段分类输出形状: {outputs['stage_logits'].shape}")
    print(f"阶段概率形状: {outputs['stage_probs'].shape}")
    print(f"REM检测分数形状: {outputs['rem_scores'].shape}")
    print(f"纺锤波检测分数形状: {outputs['spindle_scores'].shape}")
    print(f"慢波检测分数形状: {outputs['sws_scores'].shape}")
    print(f"全局特征形状: {outputs['global_features'].shape}")
    print(f"序列特征形状: {outputs['sequential_features'].shape}")
    
    # 测试推理速度
    print("\n推理速度测试...")
    import time
    
    model.eval()
    with torch.no_grad():
        start_time = time.time()
        for _ in range(10):
            _ = model(x, preprocessed=True)
        avg_time = (time.time() - start_time) / 10
        
    print(f"平均推理时间: {avg_time:.4f}秒/批次")
    print(f"吞吐量: {batch_size/avg_time:.2f}样本/秒")
    
    print("\nMAMBAFORMER架构测试完成！")