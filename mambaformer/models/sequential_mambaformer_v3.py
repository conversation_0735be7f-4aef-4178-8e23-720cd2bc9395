"""
序列MAMBAFORMER模型 V3 - 渐进式多模态版本
目标：在V2成功基础上，逐步添加4通道多模态支持
策略：保持V2的anti-overfitting优势，温和添加多模态特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class ModalityAwareFeatureExtractor(nn.Module):
    """模态感知特征提取器 - V3温和多模态方法"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # 独立的模态特征提取器（轻量级）
        # EEG特征提取 (3通道)
        self.eeg_extractor = nn.Sequential(
            nn.Conv1d(3, 48, kernel_size=50, stride=6),
            nn.BatchNorm1d(48),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(48, 96, kernel_size=8, stride=1),
            nn.BatchNorm1d(96),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(96, d_model//2, kernel_size=4, stride=1),  # 64维
            nn.BatchNorm1d(d_model//2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # EOG+EMG特征提取 (1通道，轻量级)
        self.auxiliary_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, d_model//4, kernel_size=4, stride=1),  # 32维
            nn.BatchNorm1d(d_model//4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 特征融合层（轻量级）
        fusion_dim = d_model//2 + 2*(d_model//4)  # 64 + 32 + 32 = 128
        self.fusion = nn.Sequential(
            nn.Linear(fusion_dim, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5)
        )
    
    def forward(self, x):
        """
        Args:
            x: (batch, channels, time_steps) where channels=4 [EEG1,EEG2,EEG3,EOG,EMG]
        Returns:
            features: (batch, d_model)
        """
        # 分离模态
        eeg_data = x[:, :3, :]  # 前3个通道：EEG
        eog_data = x[:, 3:4, :]  # 第4个通道：EOG
        emg_data = x[:, 4:5, :]  # 第5个通道：EMG (如果存在)
        
        # 特征提取
        eeg_features = self.eeg_extractor(eeg_data)  # (batch, 64, seq)
        eog_features = self.auxiliary_extractor(eog_data)  # (batch, 32, seq)
        
        # 处理EMG（如果存在第5通道）
        if x.size(1) > 4:
            emg_features = self.auxiliary_extractor(emg_data)  # (batch, 32, seq)
        else:
            # 如果没有EMG，用零填充
            batch_size = x.size(0)
            seq_len = eog_features.size(2)
            emg_features = torch.zeros(batch_size, 32, seq_len, device=x.device)
        
        # 全局池化
        eeg_features = self.global_pool(eeg_features).squeeze(-1)  # (batch, 64)
        eog_features = self.global_pool(eog_features).squeeze(-1)  # (batch, 32)
        emg_features = self.global_pool(emg_features).squeeze(-1)  # (batch, 32)
        
        # 特征融合
        combined_features = torch.cat([eeg_features, eog_features, emg_features], dim=1)
        fused_features = self.fusion(combined_features)
        
        return fused_features


class SequentialMAMBAFORMER_V3(nn.Module):
    """
    序列MAMBAFORMER模型 V3 - 渐进式多模态版本
    输入: (batch, seq_len, time_steps, channels) where channels=4 [EEG1,EEG2,EEG3,EOG,EMG]
    输出: (batch, seq_len, n_classes)
    
    主要特点：
    1. 基于V2的成功架构和anti-overfitting策略
    2. 温和添加4通道多模态支持
    3. 轻量级模态融合，避免过拟合
    4. 保持V2的regularization优势
    """
    def __init__(self, input_channels=4, n_classes=5, d_model=128, 
                 n_heads=8, n_layers=4, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.input_channels = input_channels
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # V3模态感知特征提取器
        self.feature_extractor = ModalityAwareFeatureExtractor(d_model, dropout)
        
        # 时序位置编码（保持V2设置）
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器（保持V2成功配置）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 分类器（保持V2正则化策略）
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器（保持V2设置）
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)  # REM/SWS vs 其他
        )
        
        # V2的权重初始化策略
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建SequentialMAMBAFORMER_V3: 参数量={total_params:,}, "
                    f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}, "
                    f"channels={input_channels} (多模态)")
    
    def _init_weights(self):
        """V2的成功权重初始化策略"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)  # V2的保守策略
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 确保输入通道数正确
        if channels == 3:
            # 如果输入是3通道(EEG)，补零到4通道
            padding = torch.zeros(batch_size, seq_len, time_steps, 1, device=x.device)
            x = torch.cat([x, padding], dim=-1)
        elif channels > 4:
            # 如果超过4通道，截断到4通道
            x = x[:, :, :, :4]
        
        # 重塑为 (batch*seq_len, channels, time_steps) 进行特征提取
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        
        # 提取每个epoch的多模态特征
        features = self.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)
        
        # 重塑回序列形式
        features = features.view(batch_size, seq_len, self.d_model)
        
        # 添加位置编码并转换为 (seq_len, batch, d_model) 格式
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # 转回 (batch, seq_len, d_model)
        
        # Transformer编码
        encoded_features = self.transformer_encoder(features)
        
        # 分类预测
        main_output = self.classifier(encoded_features)  # (batch, seq_len, n_classes)
        aux_output = self.auxiliary_head(encoded_features)  # (batch, seq_len, 2)
        
        return main_output, aux_output


# 损失函数保持与V2一致
class SequentialFocalLoss(nn.Module):
    """序列Focal损失 - 与V2保持一致"""
    def __init__(self, alpha=1, gamma=2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        # 重塑输入
        inputs = inputs.view(-1, inputs.size(-1))  # (batch*seq_len, n_classes)
        targets = targets.view(-1)  # (batch*seq_len,)
        
        # 计算focal loss
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失 - 与V2保持一致"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        """
        Args:
            predictions: (batch, seq_len, n_classes)
        """
        if predictions.size(1) <= 1:
            return torch.tensor(0.0, device=predictions.device)
        
        # 计算相邻预测的差异
        diff = predictions[:, 1:] - predictions[:, :-1]
        consistency_loss = torch.mean(torch.sum(diff ** 2, dim=-1))
        
        return self.weight * consistency_loss


# V2的温和数据增强策略
class MildDataAugmentation:
    """温和的数据增强 - 保持V2策略"""
    def __init__(self, p=0.5):
        self.p = p
    
    def __call__(self, data, labels):
        if torch.rand(1).item() > self.p:
            return data, labels
        
        # 只使用安全的增强方法
        # 1. 轻微的高斯噪声
        if torch.rand(1).item() < 0.3:
            noise_std = data.std() * 0.02  # 很小的噪声
            noise = torch.randn_like(data) * noise_std
            data = data + noise
        
        # 2. 轻微的幅度缩放
        if torch.rand(1).item() < 0.3:
            scale = torch.FloatTensor(1).uniform_(0.95, 1.05).to(data.device)
            data = data * scale
        
        return data, labels