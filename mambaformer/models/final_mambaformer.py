"""
终极版MAMBAFORMER - 基于实验结果的最终优化版本
解决关键问题：
1. 严重过拟合问题 (验证F1 80% vs 测试F1 57%)
2. REM和N1类别检测失败
3. 类别不平衡问题
4. 模型复杂度过高
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging
from collections import Counter


class BalancedCrossModalAttention(nn.Module):
    """平衡的CrossModal注意力 - 极简版本"""
    def __init__(self, d_model, n_heads=4, dropout=0.4):
        super().__init__()
        self.d_model = d_model
        self.n_heads = n_heads
        self.head_dim = d_model // n_heads
        
        # 简化的线性层
        self.qkv = nn.Linear(d_model, d_model * 3)
        self.out = nn.Linear(d_model, d_model)
        
        # 强正则化
        self.dropout = nn.Dropout(dropout)
        self.attn_dropout = nn.Dropout(dropout * 0.7)
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 权重初始化
        nn.init.xavier_uniform_(self.qkv.weight, gain=0.5)
        nn.init.xavier_uniform_(self.out.weight, gain=0.5)
        
    def forward(self, x):
        B, L, D = x.shape
        
        # 自注意力而不是复杂的跨模态注意力
        qkv = self.qkv(x).reshape(B, L, 3, self.n_heads, self.head_dim)
        q, k, v = qkv.permute(2, 0, 3, 1, 4)
        
        # 温度缩放 - 更平滑的注意力
        scale = (self.head_dim ** -0.5) * 0.7
        attn = (q @ k.transpose(-2, -1)) * scale
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_dropout(attn)
        
        out = (attn @ v).transpose(1, 2).reshape(B, L, D)
        out = self.out(out)
        return self.layer_norm(x + self.dropout(out))


class CompactModalityEncoder(nn.Module):
    """紧凑的模态编码器"""
    def __init__(self, input_channels, d_model):
        super().__init__()
        
        # 极简CNN - 只保留核心特征提取
        self.conv = nn.Sequential(
            nn.Conv1d(input_channels, 24, kernel_size=50, stride=12),
            nn.BatchNorm1d(24),
            nn.GELU(),
            nn.Dropout(0.3),
            nn.MaxPool1d(8),
            
            nn.Conv1d(24, 48, kernel_size=8, stride=1),
            nn.BatchNorm1d(48), 
            nn.GELU(),
            nn.Dropout(0.3),
            nn.MaxPool1d(4),
            
            nn.Conv1d(48, d_model, kernel_size=4, stride=1),
            nn.AdaptiveAvgPool1d(1)
        )
        
    def forward(self, x):
        x = self.conv(x)
        return x.squeeze(-1)


class FinalMAMBAFORMER(nn.Module):
    """
    终极版MAMBAFORMER
    - 极简架构，参数量控制在300K以内
    - 强正则化防止过拟合
    - 类别平衡损失函数
    - 增强的数据增强
    """
    def __init__(self, n_classes=5, d_model=64, n_heads=4, n_layers=2,
                 dropout=0.4, seq_len=5):
        super().__init__()
        
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 紧凑的模态编码器
        self.eeg_encoder = CompactModalityEncoder(3, d_model)
        self.eog_encoder = CompactModalityEncoder(1, d_model)
        self.emg_encoder = CompactModalityEncoder(1, d_model)
        
        # 简单的模态融合
        self.modality_weights = nn.Parameter(torch.tensor([0.5, 0.3, 0.2]))
        self.fusion_norm = nn.LayerNorm(d_model)
        
        # 极简的CrossModal注意力
        self.cross_attn = BalancedCrossModalAttention(d_model, n_heads, dropout)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(1, seq_len, d_model) * 0.02)
        
        # 简化的Transformer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,  # 小的FFN
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 分类器 - 特别设计用于类别平衡
        self.pre_classifier = nn.Sequential(
            nn.Dropout(dropout * 1.2),
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 分离的分类头 - 每个类别独立学习
        self.class_heads = nn.ModuleList([
            nn.Linear(d_model // 2, 1) for _ in range(n_classes)
        ])
        
        # 辅助分类器 - 简化
        self.aux_head = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 2)
        )
        
        self._init_weights()
        
        # 打印参数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建FinalMAMBAFORMER: 参数量={total_params:,}, d_model={d_model}")
        
    def _init_weights(self):
        """保守的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.5)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
    
    def forward(self, x):
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 分离模态
        eeg_data = x[:, :, :, :3].reshape(-1, time_steps, 3).transpose(1, 2)
        eog_data = x[:, :, :, 3:4].reshape(-1, time_steps, 1).transpose(1, 2)
        emg_data = x[:, :, :, 4:5] if channels > 4 else eog_data.clone()
        if channels <= 4:
            emg_data = eog_data.clone()
        else:
            emg_data = emg_data.reshape(-1, time_steps, 1).transpose(1, 2)
        
        # 编码
        eeg_feat = self.eeg_encoder(eeg_data).view(batch_size, seq_len, self.d_model)
        eog_feat = self.eog_encoder(eog_data).view(batch_size, seq_len, self.d_model)
        emg_feat = self.emg_encoder(emg_data).view(batch_size, seq_len, self.d_model)
        
        # 加权融合
        weights = F.softmax(self.modality_weights, dim=0)
        fused = weights[0] * eeg_feat + weights[1] * eog_feat + weights[2] * emg_feat
        fused = self.fusion_norm(fused)
        
        # 位置编码
        fused = fused + self.pos_embedding
        
        # CrossModal注意力
        enhanced = self.cross_attn(fused)
        
        # Transformer编码
        encoded = self.transformer(enhanced)
        
        # 分类预测
        pre_cls = self.pre_classifier(encoded)
        
        # 分离的类别预测
        class_logits = []
        for head in self.class_heads:
            logits = head(pre_cls).squeeze(-1)  # [batch, seq_len]
            class_logits.append(logits)
        
        # 组合预测 [batch, seq_len, n_classes]
        main_output = torch.stack(class_logits, dim=-1)
        
        # 辅助预测
        aux_output = self.aux_head(encoded)
        
        return main_output, aux_output


class FocalLossWithClassBalance(nn.Module):
    """类别平衡的Focal损失"""
    def __init__(self, alpha=None, gamma=2.0, class_weights=None):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha)
        else:
            self.alpha = None
        
        if class_weights is not None:
            self.class_weights = torch.tensor(class_weights)
        else:
            self.class_weights = None
            
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs.view(-1, inputs.size(-1)), 
                                targets.view(-1), 
                                reduction='none',
                                weight=self.class_weights.to(inputs.device) if self.class_weights is not None else None)
        
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            alpha = self.alpha.to(inputs.device)
            alpha_t = alpha[targets.view(-1)]
            focal_loss = alpha_t * focal_loss
            
        return focal_loss.mean()


def calculate_class_weights(train_loader):
    """计算类别权重"""
    class_counts = Counter()
    total_samples = 0
    
    for _, labels in train_loader:
        labels_flat = labels.view(-1).cpu().numpy()
        for label in labels_flat:
            class_counts[label] += 1
            total_samples += 1
    
    # 计算逆频率权重
    class_weights = []
    for i in range(5):  # 5个类别
        if i in class_counts:
            weight = total_samples / (5 * class_counts[i])
        else:
            weight = 1.0
        class_weights.append(weight)
    
    logging.info(f"类别分布: {dict(class_counts)}")
    logging.info(f"类别权重: {[f'{w:.3f}' for w in class_weights]}")
    
    return class_weights


class EnhancedDataAugmentation:
    """增强的数据增强"""
    def __init__(self, p=0.8):
        self.p = p
    
    def __call__(self, data, labels):
        if torch.rand(1).item() > self.p:
            return data, labels
            
        # 时间混合 - 针对序列数据
        if torch.rand(1).item() < 0.3:
            seq_len = data.shape[1]
            perm_indices = torch.randperm(seq_len)
            data = data[:, perm_indices]
            labels = labels[:, perm_indices]
        
        # 通道dropout - 随机丢弃某些通道
        if torch.rand(1).item() < 0.2:
            channel_mask = torch.bernoulli(torch.full((data.shape[-1],), 0.8)).to(data.device)
            data = data * channel_mask.view(1, 1, 1, -1)
        
        # 幅度缩放
        if torch.rand(1).item() < 0.5:
            scales = torch.FloatTensor(data.shape[-1]).uniform_(0.8, 1.2).to(data.device)
            data = data * scales.view(1, 1, 1, -1)
        
        # 高斯噪声
        if torch.rand(1).item() < 0.4:
            noise_std = data.std() * 0.03
            noise = torch.randn_like(data) * noise_std
            data = data + noise
        
        return data, labels