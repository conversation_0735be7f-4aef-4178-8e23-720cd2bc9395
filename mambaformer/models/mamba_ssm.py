"""
MAMBA状态空间模型
专门用于长序列睡眠EEG信号的高效建模
基于选择性状态空间模型(Selective State Space Models)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional

class SelectiveSSM(nn.Module):
    """
    选择性状态空间模型核心
    实现Mamba论文中的选择性机制
    """
    
    def __init__(self, 
                 d_model: int,
                 d_state: int = 16,
                 d_conv: int = 4,
                 expand_factor: int = 2,
                 dt_rank: str = "auto",
                 dt_min: float = 0.001,
                 dt_max: float = 0.1,
                 dt_init: str = "random",
                 dt_scale: float = 1.0,
                 bias: bool = False,
                 conv_bias: bool = True):
        super().__init__()
        
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand_factor = expand_factor
        self.d_inner = d_model * expand_factor
        
        if dt_rank == "auto":
            self.dt_rank = math.ceil(d_model / 16)
        else:
            self.dt_rank = dt_rank
            
        # 输入投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2, bias=bias)
        
        # 卷积层（用于局部依赖）
        self.conv1d = nn.Conv1d(
            in_channels=self.d_inner,
            out_channels=self.d_inner,
            kernel_size=d_conv,
            padding=d_conv // 2,
            groups=self.d_inner,
            bias=conv_bias
        )
        
        # SSM参数投影
        self.x_proj = nn.Linear(self.d_inner, self.dt_rank + 2 * d_state, bias=False)
        
        # Δt参数
        self.dt_proj = nn.Linear(self.dt_rank, self.d_inner, bias=True)
        
        # A参数（状态转移矩阵）
        A = torch.arange(1, d_state + 1, dtype=torch.float32).repeat(self.d_inner, 1)
        self.A_log = nn.Parameter(torch.log(A))
        
        # D参数（跳跃连接）
        self.D = nn.Parameter(torch.ones(self.d_inner))
        
        # 输出投影
        self.out_proj = nn.Linear(self.d_inner, d_model, bias=bias)
        
        # 初始化
        self._init_parameters(dt_min, dt_max, dt_init, dt_scale)
        
    def _init_parameters(self, dt_min, dt_max, dt_init, dt_scale):
        """初始化参数"""
        # 初始化Δt投影
        if dt_init == "constant":
            nn.init.constant_(self.dt_proj.weight, dt_scale)
        elif dt_init == "random":
            nn.init.uniform_(self.dt_proj.weight, -dt_scale, dt_scale)
            
        # 限制Δt的范围
        dt = torch.exp(
            torch.rand(self.d_inner) * (math.log(dt_max) - math.log(dt_min)) + math.log(dt_min)
        )
        inv_dt = dt + torch.log(-torch.expm1(-dt))
        with torch.no_grad():
            self.dt_proj.bias.copy_(inv_dt)
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 (batch_size, seq_len, d_model)
            
        Returns:
            output: 输出张量 (batch_size, seq_len, d_model)
        """
        batch_size, seq_len, _ = x.shape
        
        # 1. 输入投影
        xz = self.in_proj(x)  # (B, L, 2*d_inner)
        x_inner, z = xz.chunk(2, dim=-1)  # (B, L, d_inner) each
        
        # 2. 卷积处理（局部依赖）
        x_conv = self.conv1d(x_inner.transpose(1, 2)).transpose(1, 2)  # (B, L, d_inner)
        x_conv = F.silu(x_conv)
        
        # 3. SSM参数生成
        x_dbl = self.x_proj(x_conv)  # (B, L, dt_rank + 2*d_state)
        dt, B, C = torch.split(x_dbl, [self.dt_rank, self.d_state, self.d_state], dim=-1)
        
        # 4. Δt计算
        dt = self.dt_proj(dt)  # (B, L, d_inner)
        dt = F.softplus(dt + self.dt_proj.bias)
        
        # 5. A矩阵
        A = -torch.exp(self.A_log.float())  # (d_inner, d_state)
        
        # 6. 运行SSM
        y = self.selective_scan(x_conv, dt, A, B, C)
        
        # 7. 跳跃连接
        y = y + x_inner * self.D.unsqueeze(0).unsqueeze(0)
        
        # 8. 门控机制
        y = y * F.silu(z)
        
        # 9. 输出投影
        output = self.out_proj(y)
        
        return output
    
    def selective_scan(self, x, dt, A, B, C):
        """
        选择性扫描算法
        高效计算状态空间模型
        """
        batch_size, seq_len, d_inner = x.shape
        d_state = A.shape[1]
        
        # 离散化
        dt = dt.unsqueeze(-1)  # (B, L, d_inner, 1)
        A_discrete = torch.exp(A.unsqueeze(0).unsqueeze(0) * dt)  # (B, L, d_inner, d_state)
        B_discrete = B.unsqueeze(2) * dt  # (B, L, 1, d_state) * (B, L, d_inner, 1) -> (B, L, d_inner, d_state)
        
        # 初始化状态
        h = torch.zeros(batch_size, d_inner, d_state, device=x.device, dtype=x.dtype)
        
        outputs = []
        
        # 序列扫描
        for t in range(seq_len):
            # 状态更新
            h = h * A_discrete[:, t] + x[:, t].unsqueeze(-1) * B_discrete[:, t]
            
            # 输出计算
            y_t = torch.sum(h * C[:, t].unsqueeze(1), dim=-1)  # (B, d_inner)
            outputs.append(y_t)
            
        # 组合输出
        y = torch.stack(outputs, dim=1)  # (B, L, d_inner)
        
        return y

class MambaBlock(nn.Module):
    """
    Mamba基础块
    包含残差连接和层归一化
    """
    
    def __init__(self, d_model, **ssm_kwargs):
        super().__init__()
        
        self.norm = nn.LayerNorm(d_model)
        self.ssm = SelectiveSSM(d_model, **ssm_kwargs)
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入 (batch_size, seq_len, d_model)
            
        Returns:
            output: 输出 (batch_size, seq_len, d_model)
        """
        # 预归一化
        residual = x
        x = self.norm(x)
        
        # SSM处理
        x = self.ssm(x)
        
        # 残差连接
        output = x + residual
        
        return output

class SleepMambaEncoder(nn.Module):
    """
    睡眠专用Mamba编码器
    针对睡眠EEG信号特点进行优化
    """
    
    def __init__(self,
                 d_model: int = 128,
                 n_layers: int = 8,
                 d_state: int = 16,
                 expand_factor: int = 2,
                 dropout: float = 0.1,
                 sleep_stage_conditioning: bool = True):
        super().__init__()
        
        self.d_model = d_model
        self.n_layers = n_layers
        self.sleep_stage_conditioning = sleep_stage_conditioning
        
        # Mamba层
        self.layers = nn.ModuleList([
            MambaBlock(
                d_model=d_model,
                d_state=d_state,
                expand_factor=expand_factor
            ) for _ in range(n_layers)
        ])
        
        # 睡眠阶段条件编码
        if sleep_stage_conditioning:
            self.stage_embeddings = nn.Embedding(5, d_model)  # 5个睡眠阶段
            self.stage_projection = nn.Linear(d_model * 2, d_model)
            
        # 输出层归一化
        self.final_norm = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, sleep_stage=None):
        """
        前向传播
        
        Args:
            x: 输入序列 (batch_size, seq_len, d_model)
            sleep_stage: 睡眠阶段标签 (batch_size,) - 可选
            
        Returns:
            output: 编码后的表示 (batch_size, seq_len, d_model)
        """
        # 睡眠阶段条件编码
        if self.sleep_stage_conditioning and sleep_stage is not None:
            stage_emb = self.stage_embeddings(sleep_stage).unsqueeze(1)  # (B, 1, d_model)
            stage_emb = stage_emb.expand(-1, x.size(1), -1)  # (B, L, d_model)
            
            # 融合阶段信息
            x_with_stage = torch.cat([x, stage_emb], dim=-1)  # (B, L, 2*d_model)
            x = self.stage_projection(x_with_stage)  # (B, L, d_model)
            
        # 通过Mamba层
        for layer in self.layers:
            x = layer(x)
            x = self.dropout(x)
            
        # 最终归一化
        output = self.final_norm(x)
        
        return output

class BiDirectionalMamba(nn.Module):
    """
    双向Mamba
    同时处理前向和后向序列依赖
    """
    
    def __init__(self, d_model, n_layers=4, **mamba_kwargs):
        super().__init__()
        
        self.forward_mamba = SleepMambaEncoder(
            d_model=d_model,
            n_layers=n_layers,
            **mamba_kwargs
        )
        
        self.backward_mamba = SleepMambaEncoder(
            d_model=d_model,
            n_layers=n_layers,
            **mamba_kwargs
        )
        
        # 双向融合
        self.bidirectional_fusion = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
    def forward(self, x, sleep_stage=None):
        """
        双向Mamba前向传播
        """
        # 前向处理
        forward_out = self.forward_mamba(x, sleep_stage)
        
        # 后向处理（翻转序列）
        x_reversed = torch.flip(x, dims=[1])
        backward_out = self.backward_mamba(x_reversed, sleep_stage)
        backward_out = torch.flip(backward_out, dims=[1])  # 翻转回来
        
        # 双向融合
        bidirectional = torch.cat([forward_out, backward_out], dim=-1)
        output = self.bidirectional_fusion(bidirectional)
        
        return output

# 便利函数
def create_sleep_mamba_stack(d_model=128,
                           n_layers=6,
                           bidirectional=True,
                           **kwargs):
    """
    创建睡眠专用Mamba模型栈
    """
    if bidirectional:
        return BiDirectionalMamba(
            d_model=d_model,
            n_layers=n_layers,
            **kwargs
        )
    else:
        return SleepMambaEncoder(
            d_model=d_model,
            n_layers=n_layers,
            **kwargs
        )

# 使用示例和测试
if __name__ == "__main__":
    # 测试参数
    batch_size = 4
    seq_len = 11  # 压缩后的序列长度
    d_model = 128
    
    print("测试Mamba状态空间模型...")
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, d_model)
    sleep_stage = torch.randint(0, 5, (batch_size,))
    
    print(f"输入形状: {x.shape}")
    print(f"睡眠阶段: {sleep_stage.shape}")
    
    # 测试基础SelectiveSSM
    print("\n1. 测试基础SelectiveSSM...")
    ssm = SelectiveSSM(d_model)
    ssm_output = ssm(x)
    print(f"SSM输出形状: {ssm_output.shape}")
    
    # 测试MambaBlock
    print("\n2. 测试MambaBlock...")
    mamba_block = MambaBlock(d_model)
    block_output = mamba_block(x)
    print(f"MambaBlock输出形状: {block_output.shape}")
    
    # 测试SleepMambaEncoder
    print("\n3. 测试SleepMambaEncoder...")
    sleep_mamba = SleepMambaEncoder(d_model=d_model, n_layers=4)
    encoder_output = sleep_mamba(x, sleep_stage)
    print(f"SleepMamba输出形状: {encoder_output.shape}")
    
    # 测试BiDirectionalMamba
    print("\n4. 测试BiDirectionalMamba...")
    bi_mamba = BiDirectionalMamba(d_model=d_model, n_layers=2)
    bi_output = bi_mamba(x, sleep_stage)
    print(f"双向Mamba输出形状: {bi_output.shape}")
    
    # 性能测试
    print("\n5. 性能测试...")
    import time
    
    # 测试长序列性能
    long_seq = torch.randn(2, 100, d_model)  # 更长的序列
    
    start_time = time.time()
    long_output = sleep_mamba(long_seq)
    mamba_time = time.time() - start_time
    
    print(f"长序列({long_seq.shape})处理时间: {mamba_time:.4f}秒")
    print(f"长序列输出形状: {long_output.shape}")
    
    print("Mamba状态空间模型测试完成！")