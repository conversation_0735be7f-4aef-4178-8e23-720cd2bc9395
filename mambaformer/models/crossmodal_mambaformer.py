"""
CrossModal MAMBAFORMER模型
Phase 3: 实现真正的CrossModal Attention
支持EEG, EOG, EMG跨模态注意力机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class CrossModalAttention(nn.Module):
    """跨模态注意力机制"""
    def __init__(self, d_model, n_heads=8, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.n_heads = n_heads
        self.head_dim = d_model // n_heads
        
        assert self.head_dim * n_heads == d_model
        
        # 查询、键、值投影
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        
        # 输出投影
        self.out_linear = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, query_modality, key_value_modalities, mask=None):
        """
        Args:
            query_modality: (batch, seq_len, d_model) 查询模态
            key_value_modalities: list of (batch, seq_len, d_model) 键值模态列表
            mask: attention mask
        Returns:
            output: (batch, seq_len, d_model)
        """
        batch_size, seq_len, _ = query_modality.shape
        
        # 计算查询
        Q = self.q_linear(query_modality)
        Q = Q.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        
        # 合并所有键值模态
        all_kv = torch.cat([query_modality] + key_value_modalities, dim=1)  # (batch, n_modalities*seq_len, d_model)
        
        # 计算键和值
        K = self.k_linear(all_kv)
        V = self.v_linear(all_kv)
        
        K = K.view(batch_size, -1, self.n_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, -1, self.n_heads, self.head_dim).transpose(1, 2)
        
        # 注意力计算
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 加权求和
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        # 输出投影和残差连接
        output = self.out_linear(context)
        output = self.layer_norm(output + query_modality)
        
        return output, attn_weights


class ModalityEncoder(nn.Module):
    """单模态编码器"""
    def __init__(self, input_channels, d_model, modality_name):
        super().__init__()
        self.modality_name = modality_name
        
        # CNN特征提取
        self.conv_layers = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU()
        )
        
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 模态特定的位置embedding
        self.modality_embedding = nn.Parameter(torch.randn(1, 1, d_model))
        
    def forward(self, x):
        """
        Args:
            x: (batch*seq_len, channels, time_steps)
        Returns:
            features: (batch*seq_len, d_model)
        """
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)  # (batch*seq_len, d_model)
        
        # 添加模态embedding
        x = x.unsqueeze(1) + self.modality_embedding  # (batch*seq_len, 1, d_model)
        x = x.squeeze(1)  # (batch*seq_len, d_model)
        
        return x


class CrossModalFusion(nn.Module):
    """CrossModal融合模块"""
    def __init__(self, d_model, n_heads=8, dropout=0.1):
        super().__init__()
        
        # EEG作为主模态的CrossModal Attention
        self.eeg_cross_attn = CrossModalAttention(d_model, n_heads, dropout)
        
        # EOG的CrossModal Attention  
        self.eog_cross_attn = CrossModalAttention(d_model, n_heads, dropout)
        
        # EMG的CrossModal Attention
        self.emg_cross_attn = CrossModalAttention(d_model, n_heads, dropout)
        
        # 最终融合层
        self.fusion_attn = nn.MultiheadAttention(d_model, n_heads, dropout=dropout, batch_first=True)
        self.fusion_norm = nn.LayerNorm(d_model)
        
        # 门控机制
        self.gate_eeg = nn.Linear(d_model, 1)
        self.gate_eog = nn.Linear(d_model, 1)
        self.gate_emg = nn.Linear(d_model, 1)
        
    def forward(self, eeg_features, eog_features, emg_features):
        """
        Args:
            eeg_features: (batch, seq_len, d_model)
            eog_features: (batch, seq_len, d_model)
            emg_features: (batch, seq_len, d_model)
        Returns:
            fused_features: (batch, seq_len, d_model)
        """
        # CrossModal Attention for each modality
        eeg_enhanced, eeg_attn = self.eeg_cross_attn(eeg_features, [eog_features, emg_features])
        eog_enhanced, eog_attn = self.eog_cross_attn(eog_features, [eeg_features, emg_features])
        emg_enhanced, emg_attn = self.emg_cross_attn(emg_features, [eeg_features, eog_features])
        
        # 门控权重
        gate_eeg = torch.sigmoid(self.gate_eeg(eeg_enhanced))
        gate_eog = torch.sigmoid(self.gate_eog(eog_enhanced))
        gate_emg = torch.sigmoid(self.gate_emg(emg_enhanced))
        
        # 加权融合
        weighted_eeg = gate_eeg * eeg_enhanced
        weighted_eog = gate_eog * eog_enhanced
        weighted_emg = gate_emg * emg_enhanced
        
        # 堆叠所有模态特征
        all_modalities = torch.stack([weighted_eeg, weighted_eog, weighted_emg], dim=2)  # (batch, seq_len, 3, d_model)
        all_modalities = all_modalities.view(-1, 3, self.fusion_attn.embed_dim)  # (batch*seq_len, 3, d_model)
        
        # 最终的跨模态注意力融合
        fused, _ = self.fusion_attn(all_modalities, all_modalities, all_modalities)
        fused = fused.mean(dim=1)  # (batch*seq_len, d_model)
        
        # Reshape back
        batch_size = eeg_features.shape[0]
        seq_len = eeg_features.shape[1]
        fused = fused.view(batch_size, seq_len, -1)
        
        # 残差连接
        residual = (weighted_eeg + weighted_eog + weighted_emg) / 3.0
        fused = self.fusion_norm(fused + residual)
        
        return fused


class CrossModalSequentialMAMBAFORMER(nn.Module):
    """
    CrossModal序列MAMBAFORMER
    Phase 3: 真正的跨模态注意力机制
    """
    def __init__(self, n_classes=5, d_model=128, n_heads=8, n_layers=4, 
                 dropout=0.1, seq_len=5):
        super().__init__()
        
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 三个模态的编码器
        self.eeg_encoder = ModalityEncoder(3, d_model, "EEG")  # EEG: 3通道
        self.eog_encoder = ModalityEncoder(1, d_model, "EOG")  # EOG: 1通道  
        self.emg_encoder = ModalityEncoder(1, d_model, "EMG")  # EMG: 1通道 (暂用EOG通道)
        
        # CrossModal融合
        self.crossmodal_fusion = CrossModalFusion(d_model, n_heads, dropout)
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=n_layers
        )
        
        # 输出层
        self.classifier = nn.Linear(d_model, n_classes)
        self.auxiliary_head = nn.Linear(d_model, 2)
        
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
        
        logging.info(f"创建CrossModalSequentialMAMBAFORMER: "
                    f"seq_len={seq_len}, EEG(3)+EOG(1)+EMG(1), "
                    f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
    
    def _init_weights(self):
        """初始化权重"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, 5)  # 5通道: EEG(3) + EOG(1) + EMG(1)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 分离模态
        eeg_data = x[:, :, :, :3]    # EEG: 前3通道
        eog_data = x[:, :, :, 3:4]   # EOG: 第4通道
        emg_data = x[:, :, :, 4:5]   # EMG: 第5通道 (暂用第4通道复制)
        
        # 处理EMG数据缺失的情况
        if channels == 4:
            emg_data = eog_data.clone()  # 暂时用EOG数据
        
        # Reshape for modality encoding
        eeg_flat = eeg_data.view(-1, time_steps, 3).transpose(1, 2)  # (batch*seq_len, 3, time_steps)
        eog_flat = eog_data.view(-1, time_steps, 1).transpose(1, 2)  # (batch*seq_len, 1, time_steps) 
        emg_flat = emg_data.view(-1, time_steps, 1).transpose(1, 2)  # (batch*seq_len, 1, time_steps)
        
        # 模态编码
        eeg_features = self.eeg_encoder(eeg_flat)  # (batch*seq_len, d_model)
        eog_features = self.eog_encoder(eog_flat)  # (batch*seq_len, d_model)
        emg_features = self.emg_encoder(emg_flat)  # (batch*seq_len, d_model)
        
        # Reshape back to sequence format
        eeg_features = eeg_features.view(batch_size, seq_len, self.d_model)
        eog_features = eog_features.view(batch_size, seq_len, self.d_model)
        emg_features = emg_features.view(batch_size, seq_len, self.d_model)
        
        # CrossModal融合
        fused_features = self.crossmodal_fusion(eeg_features, eog_features, emg_features)
        
        # 时序建模
        fused_features = fused_features.transpose(0, 1)  # (seq_len, batch, d_model)
        fused_features = self.pos_encoder(fused_features)
        fused_features = fused_features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Transformer编码
        encoded = self.transformer_encoder(fused_features)
        
        # 分类输出
        encoded = self.dropout(encoded)
        main_output = self.classifier(encoded)
        aux_output = self.auxiliary_head(encoded)
        
        return main_output, aux_output


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)