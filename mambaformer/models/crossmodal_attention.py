"""
CrossModal跨模态注意力机制
专门用于EEG-EOG信号的深度融合和交互建模
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

class MultiHeadCrossModalAttention(nn.Module):
    """
    多头跨模态注意力机制
    实现EEG和EOG模态之间的深度交互
    """
    
    def __init__(self, d_model, n_heads=8, dropout=0.1, temperature=1.0):
        super().__init__()
        
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        self.temperature = temperature
        
        # 查询、键、值投影层
        self.W_q = nn.Linear(d_model, d_model, bias=False)
        self.W_k = nn.Linear(d_model, d_model, bias=False)  
        self.W_v = nn.Linear(d_model, d_model, bias=False)
        
        # 跨模态交互权重
        self.cross_modal_gate = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.Sigmoid()
        )
        
        # 输出投影
        self.W_o = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)
        
        # LayerNorm for residual connection
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, query_modal, key_value_modal, mask=None):
        """
        跨模态注意力前向传播
        
        Args:
            query_modal: 查询模态 (batch_size, seq_len, d_model)
            key_value_modal: 键值模态 (batch_size, seq_len, d_model) 
            mask: 注意力掩码 (optional)
            
        Returns:
            output: 跨模态融合后的特征
            attention_weights: 注意力权重
        """
        batch_size, seq_len, _ = query_modal.size()
        
        # 1. 计算跨模态门控权重
        modal_concat = torch.cat([query_modal, key_value_modal], dim=-1)
        gate_weights = self.cross_modal_gate(modal_concat)
        
        # 2. 线性变换得到Q, K, V
        Q = self.W_q(query_modal).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key_value_modal).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.W_v(key_value_modal).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # 3. 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / (math.sqrt(self.d_k) * self.temperature)
        
        # 4. 应用掩码（如果有）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
            
        # 5. 注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 6. 应用注意力权重
        context = torch.matmul(attention_weights, V)
        
        # 7. 重塑并投影
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        output = self.W_o(context)
        
        # 8. 应用跨模态门控
        output = output * gate_weights
        
        # 9. 残差连接和LayerNorm
        output = self.layer_norm(query_modal + self.dropout(output))
        
        return output, attention_weights.mean(dim=1)  # 平均多头注意力权重

class CrossModalFusionBlock(nn.Module):
    """
    跨模态融合块
    包含双向跨模态注意力和前馈网络
    """
    
    def __init__(self, d_model, n_heads=8, d_ff=None, dropout=0.1):
        super().__init__()
        
        if d_ff is None:
            d_ff = 4 * d_model
            
        # EEG -> EOG 跨模态注意力
        self.eeg_to_eog_attention = MultiHeadCrossModalAttention(
            d_model, n_heads, dropout
        )
        
        # EOG -> EEG 跨模态注意力  
        self.eog_to_eeg_attention = MultiHeadCrossModalAttention(
            d_model, n_heads, dropout
        )
        
        # 模态融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
        
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, eeg_features, eog_features):
        """
        跨模态融合前向传播
        
        Args:
            eeg_features: EEG特征 (batch_size, seq_len, d_model)
            eog_features: EOG特征 (batch_size, seq_len, d_model)
            
        Returns:
            fused_features: 融合特征
            attention_info: 注意力信息
        """
        # 1. 双向跨模态注意力
        eeg_enhanced, eeg_to_eog_attn = self.eeg_to_eog_attention(
            eeg_features, eog_features
        )
        
        eog_enhanced, eog_to_eeg_attn = self.eog_to_eeg_attention(
            eog_features, eeg_features
        )
        
        # 2. 模态融合
        concatenated = torch.cat([eeg_enhanced, eog_enhanced], dim=-1)
        fused = self.fusion_layer(concatenated)
        
        # 3. 前馈网络和残差连接
        ff_output = self.feed_forward(fused)
        output = self.layer_norm(fused + ff_output)
        
        # 4. 收集注意力信息
        attention_info = {
            'eeg_to_eog_attention': eeg_to_eog_attn,
            'eog_to_eeg_attention': eog_to_eeg_attn
        }
        
        return output, attention_info

class SleepStageSpecificAttention(nn.Module):
    """
    睡眠阶段特异性注意力
    根据不同睡眠阶段调整跨模态交互模式
    """
    
    def __init__(self, d_model, n_sleep_stages=5):
        super().__init__()
        
        self.d_model = d_model
        self.n_sleep_stages = n_sleep_stages
        
        # 睡眠阶段嵌入
        self.stage_embeddings = nn.Embedding(n_sleep_stages, d_model)
        
        # 阶段特异性注意力权重生成器
        self.stage_attention_generator = nn.ModuleDict({
            'wake': nn.Linear(d_model, d_model),      # 清醒状态：高频注意力
            'n1': nn.Linear(d_model, d_model),        # N1：过渡期注意力
            'n2': nn.Linear(d_model, d_model),        # N2：睡眠纺锤波注意力
            'n3': nn.Linear(d_model, d_model),        # N3：慢波注意力
            'rem': nn.Linear(d_model, d_model),       # REM：眼动注意力
        })
        
        # 阶段预测器（辅助训练）
        self.stage_predictor = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(d_model, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, n_sleep_stages)
        )
        
    def forward(self, fused_features, predicted_stage=None):
        """
        应用睡眠阶段特异性注意力
        
        Args:
            fused_features: 融合特征 (batch_size, seq_len, d_model)
            predicted_stage: 预测的睡眠阶段 (optional)
            
        Returns:
            stage_enhanced_features: 阶段增强特征
            stage_predictions: 阶段预测（辅助）
        """
        batch_size, seq_len, _ = fused_features.size()
        
        # 1. 预测睡眠阶段（如果没有提供）
        stage_logits = self.stage_predictor(fused_features.transpose(1, 2))
        stage_probs = F.softmax(stage_logits, dim=-1)
        
        if predicted_stage is None:
            predicted_stage = torch.argmax(stage_probs, dim=-1)
            
        # 2. 生成阶段特异性注意力权重
        stage_names = ['wake', 'n1', 'n2', 'n3', 'rem']
        enhanced_features = []
        
        for i, stage_name in enumerate(stage_names):
            # 当前阶段的样本掩码
            stage_mask = (predicted_stage == i).float().unsqueeze(-1).unsqueeze(-1)
            
            # 应用阶段特异性注意力
            stage_attention = self.stage_attention_generator[stage_name](fused_features)
            stage_enhanced = fused_features + stage_attention * stage_mask
            enhanced_features.append(stage_enhanced)
            
        # 3. 融合所有阶段的增强特征
        stage_enhanced_features = torch.stack(enhanced_features, dim=0).sum(dim=0)
        
        return stage_enhanced_features, stage_logits

class CrossModalTransformerLayer(nn.Module):
    """
    完整的跨模态Transformer层
    整合跨模态注意力和睡眠阶段特异性处理
    """
    
    def __init__(self, d_model, n_heads=8, d_ff=None, dropout=0.1, n_sleep_stages=5):
        super().__init__()
        
        # 跨模态融合块
        self.cross_modal_fusion = CrossModalFusionBlock(
            d_model, n_heads, d_ff, dropout
        )
        
        # 睡眠阶段特异性注意力
        self.stage_specific_attention = SleepStageSpecificAttention(
            d_model, n_sleep_stages
        )
        
        # 输出层归一化
        self.output_norm = nn.LayerNorm(d_model)
        
    def forward(self, eeg_features, eog_features, predicted_stage=None):
        """
        完整跨模态Transformer层前向传播
        """
        # 1. 跨模态融合
        fused_features, attention_info = self.cross_modal_fusion(
            eeg_features, eog_features
        )
        
        # 2. 睡眠阶段特异性增强
        enhanced_features, stage_predictions = self.stage_specific_attention(
            fused_features, predicted_stage
        )
        
        # 3. 输出归一化
        output = self.output_norm(enhanced_features)
        
        return output, {
            'attention_info': attention_info,
            'stage_predictions': stage_predictions
        }

# 便利函数
def create_crossmodal_transformer(d_model=128, 
                                n_layers=4,
                                n_heads=8, 
                                dropout=0.1,
                                n_sleep_stages=5):
    """
    创建多层跨模态Transformer
    """
    layers = nn.ModuleList([
        CrossModalTransformerLayer(
            d_model, n_heads, d_model*4, dropout, n_sleep_stages
        ) for _ in range(n_layers)
    ])
    
    return layers

# 使用示例和测试
if __name__ == "__main__":
    # 测试参数
    batch_size = 4
    seq_len = 11
    d_model = 128
    
    # 创建测试数据
    eeg_features = torch.randn(batch_size, seq_len, d_model)
    eog_features = torch.randn(batch_size, seq_len, d_model)
    
    print("测试跨模态注意力机制...")
    print(f"输入形状 - EEG: {eeg_features.shape}, EOG: {eog_features.shape}")
    
    # 测试单层
    cross_modal_layer = CrossModalTransformerLayer(d_model)
    output, info = cross_modal_layer(eeg_features, eog_features)
    
    print(f"输出形状: {output.shape}")
    print(f"阶段预测形状: {info['stage_predictions'].shape}")
    print(f"EEG->EOG注意力形状: {info['attention_info']['eeg_to_eog_attention'].shape}")
    print(f"EOG->EEG注意力形状: {info['attention_info']['eog_to_eeg_attention'].shape}")
    
    # 测试多层
    print("\n测试多层跨模态Transformer...")
    layers = create_crossmodal_transformer(d_model=d_model, n_layers=2)
    
    current_output = output
    for i, layer in enumerate(layers):
        current_output, layer_info = layer(eeg_features, eog_features)
        print(f"Layer {i+1} 输出形状: {current_output.shape}")
    
    print("跨模态注意力机制测试完成！")