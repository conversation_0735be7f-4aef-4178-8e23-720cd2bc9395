"""
MAMBAFORMER集成到AttnSleep框架
利用成熟的数据加载和训练系统，插入我们的完整架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from copy import deepcopy

# 导入我们的核心模块
from crossmodal_attention import CrossModalTransformerLayer, SleepStageSpecificAttention
from mamba_ssm import BiDirectionalMamba, MambaBlock

class GELU(nn.Module):
    """GELU激活函数（兼容老版本PyTorch）"""
    def __init__(self):
        super(GELU, self).__init__()
        
    def forward(self, x):
        return torch.nn.functional.gelu(x)

class SELayer(nn.Module):
    """Squeeze-and-Excitation注意力层"""
    def __init__(self, channel, reduction=16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1)
        return x * y.expand_as(x)

class SEBasicBlock(nn.Module):
    """SE残差块"""
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None, reduction=16):
        super(SEBasicBlock, self).__init__()
        self.conv1 = nn.Conv1d(inplanes, planes, stride)
        self.bn1 = nn.BatchNorm1d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv1d(planes, planes, 1)
        self.bn2 = nn.BatchNorm1d(planes)
        self.se = SELayer(planes, reduction)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.se(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)
        return out

class EnhancedMRCNN(nn.Module):
    """
    增强版多分辨率CNN特征提取器
    支持多通道EEG+EOG输入
    """
    def __init__(self, input_channels=3, afr_reduced_cnn_size=128):
        super(EnhancedMRCNN, self).__init__()
        self.input_channels = input_channels
        drate = 0.3  # 降低dropout
        self.GELU = GELU()
        
        # 短时特征路径（高频细节）
        self.features1 = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6, bias=False, padding=24),
            nn.BatchNorm1d(64),
            self.GELU,
            nn.MaxPool1d(kernel_size=8, stride=2, padding=4),
            nn.Dropout(drate),

            nn.Conv1d(64, 128, kernel_size=8, stride=1, bias=False, padding=4),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.Conv1d(128, 128, kernel_size=8, stride=1, bias=False, padding=4),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.MaxPool1d(kernel_size=4, stride=4, padding=2)
        )

        # 长时特征路径（低频模式）
        self.features2 = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=400, stride=50, bias=False, padding=200),
            nn.BatchNorm1d(64),
            self.GELU,
            nn.MaxPool1d(kernel_size=4, stride=2, padding=2),
            nn.Dropout(drate),

            nn.Conv1d(64, 128, kernel_size=7, stride=1, bias=False, padding=3),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.Conv1d(128, 128, kernel_size=7, stride=1, bias=False, padding=3),
            nn.BatchNorm1d(128),
            self.GELU,

            nn.MaxPool1d(kernel_size=2, stride=2, padding=1)
        )
        
        # 跨模态特征路径（专门用于EEG-EOG融合）
        if input_channels >= 3:
            self.crossmodal_features = nn.Sequential(
                nn.Conv1d(input_channels, 32, kernel_size=200, stride=25, bias=False, padding=100),
                nn.BatchNorm1d(32),
                self.GELU,
                nn.MaxPool1d(kernel_size=8, stride=2, padding=4),
                nn.Dropout(drate * 0.5),
                
                nn.Conv1d(32, 64, kernel_size=16, stride=1, bias=False, padding=8),
                nn.BatchNorm1d(64),
                self.GELU,
                
                nn.Conv1d(64, 128, kernel_size=16, stride=1, bias=False, padding=8),
                nn.BatchNorm1d(128),
                self.GELU,
                
                nn.MaxPool1d(kernel_size=2, stride=2, padding=1)
            )
        
        self.dropout = nn.Dropout(drate)
        self.inplanes = 128
        
        # 自适应特征精炼（AFR）层
        self.AFR = self._make_layer(SEBasicBlock, afr_reduced_cnn_size, 1)

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv1d(self.inplanes, planes * block.expansion,
                          kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm1d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x):
        # 确保输入形状正确: (batch, channels, seq_len)
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # (batch, 1, seq_len)
        elif len(x.shape) == 3 and x.shape[1] == 3000:
            x = x.transpose(1, 2)  # (batch, channels, seq_len)
            
        # 多路径特征提取
        x1 = self.features1(x)  # 短时特征
        x2 = self.features2(x)  # 长时特征
        
        if self.input_channels >= 3 and hasattr(self, 'crossmodal_features'):
            x3 = self.crossmodal_features(x)  # 跨模态特征
            x_concat = torch.cat((x1, x2, x3), dim=2)
        else:
            x_concat = torch.cat((x1, x2), dim=2)
        
        x_concat = self.dropout(x_concat)
        x_concat = self.AFR(x_concat)
        return x_concat

class MambaTransformerEncoder(nn.Module):
    """
    MAMBA-Transformer混合编码器
    结合状态空间模型的长序列建模能力和Transformer的注意力机制
    """
    def __init__(self, d_model, n_crossmodal_layers=2, n_mamba_layers=4, 
                 n_heads=8, dropout=0.1, use_bidirectional_mamba=True,
                 sleep_stage_conditioning=True):
        super(MambaTransformerEncoder, self).__init__()
        self.d_model = d_model
        self.use_bidirectional_mamba = use_bidirectional_mamba
        self.sleep_stage_conditioning = sleep_stage_conditioning
        
        # 1. CrossModal注意力层
        self.crossmodal_layers = nn.ModuleList([
            CrossModalTransformerLayer(
                d_model=d_model,
                n_heads=n_heads,
                n_sleep_stages=5 if sleep_stage_conditioning else None,
                dropout=dropout
            ) for _ in range(n_crossmodal_layers)
        ])
        
        # 2. MAMBA状态空间层
        if use_bidirectional_mamba:
            self.mamba_layers = nn.ModuleList([
                BiDirectionalMamba(d_model=d_model) for _ in range(n_mamba_layers)
            ])
        else:
            self.mamba_layers = nn.ModuleList([
                MambaBlock(d_model=d_model) for _ in range(n_mamba_layers)
            ])
        
        # 3. 层标准化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(n_crossmodal_layers + n_mamba_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, eeg_features, eog_features=None):
        """
        Args:
            eeg_features: (batch, seq_len, d_model)
            eog_features: (batch, seq_len, d_model) 可选，如果为None则使用eeg_features
        Returns:
            encoded: (batch, seq_len, d_model)
        """
        # 如果没有提供EOG特征，使用EEG特征
        if eog_features is None:
            eog_features = eeg_features
        
        # 确保输入维度正确: (batch, seq_len, d_model)
        for features in [eeg_features, eog_features]:
            if len(features.shape) == 3:
                if features.shape[-1] != self.d_model:
                    # 如果最后一维不是d_model，需要调整
                    if features.shape[1] == self.d_model:
                        # 输入是 (batch, d_model, seq_len)，转换为 (batch, seq_len, d_model)
                        features = features.transpose(1, 2)
            
            # 验证输入形状
            assert len(features.shape) == 3, f"期望3D输入，得到{len(features.shape)}D: {features.shape}"
            assert features.shape[-1] == self.d_model, f"期望最后一维是{self.d_model}，得到{features.shape[-1]}: {features.shape}"
        
        layer_idx = 0
        x = eeg_features  # 主要特征流
        
        # 1. CrossModal注意力处理
        for crossmodal_layer in self.crossmodal_layers:
            residual = x
            x_normed = self.layer_norms[layer_idx](x)
            eog_normed = self.layer_norms[layer_idx](eog_features)
            
            # 跨模态注意力 - 返回两个值，我们只需要特征
            x_out, _ = crossmodal_layer(x_normed, eog_normed)
            x = self.dropout(x_out) + residual
            layer_idx += 1
        
        # 2. MAMBA状态空间处理
        for mamba_layer in self.mamba_layers:
            residual = x
            x = self.layer_norms[layer_idx](x)
            x = mamba_layer(x)
            x = self.dropout(x) + residual
            layer_idx += 1
            
        return x

class AttnMAMBAFORMER(nn.Module):
    """
    完整的MAMBAFORMER架构，集成到AttnSleep框架
    wICA-ICLabel-CrossModal-MAMBA的完整实现
    """
    def __init__(self, input_channels=3, num_classes=5, d_model=128, 
                 n_crossmodal_layers=2, n_mamba_layers=4, n_heads=8, 
                 dropout=0.15, use_bidirectional_mamba=True, 
                 sleep_stage_conditioning=True, 
                 afr_reduced_cnn_size=128, use_multitask=True):
        super(AttnMAMBAFORMER, self).__init__()
        
        self.input_channels = input_channels
        self.num_classes = num_classes
        self.d_model = d_model
        self.use_multitask = use_multitask
        self.afr_reduced_cnn_size = afr_reduced_cnn_size
        
        # 1. 增强版多分辨率CNN特征提取
        self.mrcnn = EnhancedMRCNN(
            input_channels=input_channels, 
            afr_reduced_cnn_size=afr_reduced_cnn_size
        )
        
        # 2. 特征维度投影 - 自适应计算CNN输出维度
        self.feature_projection = None  # 延迟初始化
        self.d_model = d_model
        
        # 3. MAMBA-Transformer编码器
        self.encoder = MambaTransformerEncoder(
            d_model=d_model,
            n_crossmodal_layers=n_crossmodal_layers,
            n_mamba_layers=n_mamba_layers,
            n_heads=n_heads,
            dropout=dropout,
            use_bidirectional_mamba=use_bidirectional_mamba,
            sleep_stage_conditioning=sleep_stage_conditioning
        )
        
        # 4. 分类头
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, num_classes)
        )
        
        # 5. 多任务辅助头（可选）
        if use_multitask:
            self.rem_head = nn.Sequential(
                nn.Linear(d_model, d_model // 4),
                nn.GELU(),
                nn.Dropout(dropout * 0.5),
                nn.Linear(d_model // 4, 1),
                nn.Sigmoid()
            )
            
            self.sws_head = nn.Sequential(
                nn.Linear(d_model, d_model // 4),
                nn.GELU(),
                nn.Dropout(dropout * 0.5),
                nn.Linear(d_model // 4, 1),
                nn.Sigmoid()
            )
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """权重初始化"""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight.data, 0.0, 0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias.data)
        elif isinstance(module, nn.Conv1d):
            torch.nn.init.normal_(module.weight.data, 0.0, 0.02)
        elif isinstance(module, (nn.BatchNorm1d, nn.LayerNorm)):
            torch.nn.init.ones_(module.weight.data)
            torch.nn.init.zeros_(module.bias.data)
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入tensor，形状可以是：
               - (batch, seq_len) 单通道
               - (batch, channels, seq_len) 多通道
               - (batch, seq_len, channels) 需要转置
        
        Returns:
            如果use_multitask=True:
                dict with keys: 'stage_logits', 'rem_scores', 'sws_scores'
            否则:
                stage_logits tensor
        """
        batch_size = x.shape[0]
        
        # 1. CNN特征提取
        cnn_features = self.mrcnn(x)  # (batch, afr_reduced_cnn_size, seq_len)
        
        # 2. 特征投影 - 自适应初始化
        seq_len = cnn_features.shape[2]  # 使用CNN的时间维度
        cnn_channels = cnn_features.shape[1]
        
        # 延迟初始化投影层
        if self.feature_projection is None:
            self.feature_projection = nn.Sequential(
                nn.Linear(cnn_channels, self.d_model),
                nn.LayerNorm(self.d_model),
                nn.GELU(),
                nn.Dropout(0.1)
            ).to(cnn_features.device)
        
        # 3. 为Transformer编码器准备序列
        # 使用CNN的时间维度作为序列长度
        seq_features = cnn_features.transpose(1, 2)  # (batch, seq_len, cnn_channels)
        seq_input = self.feature_projection(seq_features)  # (batch, seq_len, d_model)
        
        # 4. 分离EEG和EOG特征用于跨模态注意力
        # 简化处理：将输入特征分成两个部分作为EEG和EOG
        eeg_features = seq_input  # 使用完整特征作为EEG
        eog_features = seq_input  # 使用相同特征作为EOG（实际应用中可以更复杂的分离）
        
        # 5. MAMBA-Transformer编码
        encoded = self.encoder(eeg_features, eog_features)  # (batch, seq_len, d_model)
        
        # 5. 全局池化
        # 转换回 (batch, d_model, seq_len) 用于池化
        encoded_transposed = encoded.transpose(1, 2)  # (batch, d_model, seq_len)
        pooled = self.global_pool(encoded_transposed).squeeze(-1)  # (batch, d_model)
        
        # 6. 分类
        stage_logits = self.classifier(pooled)  # (batch, num_classes)
        
        # 7. 多任务输出
        if self.use_multitask:
            rem_scores = self.rem_head(pooled).squeeze(-1)  # (batch,)
            sws_scores = self.sws_head(pooled).squeeze(-1)  # (batch,)
            
            return {
                'stage_logits': stage_logits,
                'rem_scores': rem_scores,
                'sws_scores': sws_scores
            }
        else:
            return stage_logits

    def get_attention_weights(self):
        """获取注意力权重用于可视化"""
        attention_weights = {}
        
        # CrossModal注意力权重
        for i, layer in enumerate(self.encoder.crossmodal_layers):
            if hasattr(layer, 'attention_weights'):
                attention_weights[f'crossmodal_layer_{i}'] = layer.attention_weights
        
        return attention_weights

# 为了兼容AttnSleep框架，创建一个简化的接口
class MAMBAFORMER(nn.Module):
    """
    MAMBAFORMER的AttnSleep兼容接口
    """
    def __init__(self):
        super(MAMBAFORMER, self).__init__()
        
        # 使用优化的配置参数
        self.model = AttnMAMBAFORMER(
            input_channels=1,  # AttnSleep使用单通道
            num_classes=5,
            d_model=128,
            n_crossmodal_layers=2,
            n_mamba_layers=4,
            n_heads=8,
            dropout=0.15,
            use_bidirectional_mamba=True,
            sleep_stage_conditioning=True,
            afr_reduced_cnn_size=80,  # 与AttnSleep保持一致
            use_multitask=False  # 简化输出，只返回分类结果
        )
    
    def forward(self, x):
        """
        兼容AttnSleep的前向传播
        
        Args:
            x: (batch, 1, seq_len) 单通道EEG信号
            
        Returns:
            stage_logits: (batch, 5) 睡眠分期logits
        """
        return self.model(x)

# 用于多通道数据的版本
class MultiChannelMAMBAFORMER(nn.Module):
    """
    多通道MAMBAFORMER，用于EEG+EOG数据
    """
    def __init__(self):
        super(MultiChannelMAMBAFORMER, self).__init__()
        
        self.model = AttnMAMBAFORMER(
            input_channels=3,  # EEG + 2xEOG
            num_classes=5,
            d_model=128,
            n_crossmodal_layers=3,  # 增加跨模态层
            n_mamba_layers=4,
            n_heads=8,
            dropout=0.15,
            use_bidirectional_mamba=True,
            sleep_stage_conditioning=True,
            afr_reduced_cnn_size=128,
            use_multitask=True  # 启用多任务学习
        )
    
    def forward(self, x):
        return self.model(x)