"""
序列MAMBAFORMER模型
支持多epoch序列输入和输出
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class EpochFeatureExtractor(nn.Module):
    """单个epoch的特征提取器"""
    def __init__(self, input_channels=3, d_model=128):
        super().__init__()
        
        # CNN特征提取
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU()
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x):
        """
        Args:
            x: (batch, channels, time_steps)
        Returns:
            features: (batch, d_model)
        """
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)
        return x


class SequentialMAMBAFORMER(nn.Module):
    """
    序列MAMBAFORMER模型
    输入: (batch, seq_len, time_steps, channels)
    输出: (batch, seq_len, n_classes)
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=128, 
                 n_heads=8, n_layers=4, dropout=0.1, seq_len=5):
        super().__init__()
        
        self.input_channels = input_channels
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # Epoch特征提取器
        self.feature_extractor = EpochFeatureExtractor(input_channels, d_model)
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器（处理时序关系）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=n_layers
        )
        
        # 输出层（每个时间步独立分类）
        self.classifier = nn.Linear(d_model, n_classes)
        
        # 辅助任务头（REM/SWS检测）
        self.auxiliary_head = nn.Linear(d_model, 2)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
        
        logging.info(f"创建SequentialMAMBAFORMER: "
                    f"seq_len={seq_len}, channels={input_channels}, "
                    f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
    
    def _init_weights(self):
        """初始化权重"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 1. 提取每个epoch的特征
        # Reshape: (batch*seq_len, time_steps, channels)
        x = x.view(-1, time_steps, channels)
        # Transpose for Conv1d: (batch*seq_len, channels, time_steps)
        x = x.transpose(1, 2)
        
        # 特征提取
        features = self.feature_extractor(x)  # (batch*seq_len, d_model)
        
        # Reshape back: (batch, seq_len, d_model)
        features = features.view(batch_size, seq_len, self.d_model)
        
        # 2. 时序建模
        # 添加位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Transformer编码
        encoded = self.transformer_encoder(features)  # (batch, seq_len, d_model)
        
        # 3. 分类输出
        encoded = self.dropout(encoded)
        main_output = self.classifier(encoded)  # (batch, seq_len, n_classes)
        aux_output = self.auxiliary_head(encoded)  # (batch, seq_len, 2)
        
        return main_output, aux_output


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失，鼓励相邻epoch预测的平滑性"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        """
        Args:
            predictions: (batch, seq_len, n_classes)
        Returns:
            loss: scalar
        """
        if predictions.size(1) <= 1:
            return 0.0
        
        # 计算相邻预测的差异
        pred_probs = F.softmax(predictions, dim=-1)
        diff = pred_probs[:, 1:, :] - pred_probs[:, :-1, :]
        loss = torch.mean(torch.abs(diff))
        
        return self.weight * loss


class SequentialFocalLoss(nn.Module):
    """序列Focal Loss"""
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        """
        Args:
            inputs: (batch, seq_len, n_classes)
            targets: (batch, seq_len)
        Returns:
            loss: scalar
        """
        # Reshape for loss calculation
        batch_size, seq_len, n_classes = inputs.shape
        inputs = inputs.view(-1, n_classes)  # (batch*seq_len, n_classes)
        targets = targets.view(-1)  # (batch*seq_len,)
        
        # 计算CE loss
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # 计算pt
        p = F.softmax(inputs, dim=1)
        pt = p.gather(1, targets.view(-1, 1)).squeeze(1)
        
        # 计算focal loss
        loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss.view(batch_size, seq_len)


def create_sequential_model(config):
    """创建序列模型的工厂函数"""
    model = SequentialMAMBAFORMER(
        input_channels=config.get('input_channels', 3),
        n_classes=config.get('n_classes', 5),
        d_model=config.get('d_model', 128),
        n_heads=config.get('n_heads', 8),
        n_layers=config.get('n_layers', 4),
        dropout=config.get('dropout', 0.1),
        seq_len=config.get('seq_len', 5)
    )
    return model