"""
多模态MAMBAFORMER - 基于原版CMT技术的渐进式多模态融合

核心技术来自原版Cross-Modal-Transformer的研究:
1. EEG自查询机制：EEG作为Query，EOG作为Key/Value
2. 轻量化跨模态注意力设计
3. 多尺度窗口嵌入：多种kernel size的卷积特征提取
4. 层次化融合策略：先EEG-EOG，再添加EMG
5. 温和的数据增强和正则化

基于V8的成功基础，逐步添加多模态特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging
from einops import rearrange, repeat
from typing import Optional, Tuple


class MultiScaleWindowEmbedding(nn.Module):
    """
    多尺度窗口嵌入 - 借鉴CMT的Window_Embedding技术
    使用不同kernel size捕获多时间尺度特征
    """
    def __init__(self, in_channels=1, d_model=128, window_size=50):
        super().__init__()
        
        # 粗粒度特征 (大窗口)
        self.coarse_conv = nn.Sequential(
            nn.Conv1d(in_channels, d_model//4, kernel_size=window_size, stride=window_size//2),
            nn.BatchNorm1d(d_model//4),
            nn.LeakyReLU(),
        )
        
        # 中等粒度特征 (中等窗口)
        self.medium_conv = nn.Sequential(
            nn.Conv1d(in_channels, d_model//8, kernel_size=25, stride=5),
            nn.BatchNorm1d(d_model//8),
            nn.LeakyReLU(),
            nn.Conv1d(d_model//8, d_model//4, kernel_size=5, stride=5),
            nn.BatchNorm1d(d_model//4),
            nn.LeakyReLU(),
        )
        
        # 细粒度特征 (小窗口)
        self.fine_conv = nn.Sequential(
            nn.Conv1d(in_channels, d_model//4, kernel_size=10, stride=10),
            nn.BatchNorm1d(d_model//4),
            nn.LeakyReLU(),
            nn.Conv1d(d_model//4, d_model//2, kernel_size=3, stride=3),
            nn.BatchNorm1d(d_model//2),
            nn.LeakyReLU(),
        )
        
        # 融合层
        self.fusion_conv = nn.Sequential(
            nn.Conv1d(d_model, d_model, kernel_size=1, stride=1),
            nn.BatchNorm1d(d_model),
            nn.LeakyReLU(),
        )
        
        # 全局平均池化到固定长度
        self.global_pool = nn.AdaptiveAvgPool1d(100)  # 固定输出长度
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, d_model))
        
    def forward(self, x):
        """
        Args:
            x: (batch, time_steps, channels) -> (batch, channels, time_steps)
        Returns:
            features: (batch, seq_len+1, d_model) [CLS + features]
        """
        if x.dim() == 3:
            x = x.transpose(1, 2)  # (B, C, T)
        elif x.dim() == 2:
            x = x.unsqueeze(1)  # (B, 1, T)
        
        batch_size = x.shape[0]
        
        # 多尺度特征提取
        coarse_feat = self.coarse_conv(x)  # (B, d_model//4, T1)
        medium_feat = self.medium_conv(x)  # (B, d_model//4, T2)
        fine_feat = self.fine_conv(x)      # (B, d_model//2, T3)
        
        # 统一长度并拼接
        coarse_feat = self.global_pool(coarse_feat)  # (B, d_model//4, 100)
        medium_feat = self.global_pool(medium_feat)  # (B, d_model//4, 100)
        fine_feat = self.global_pool(fine_feat)      # (B, d_model//2, 100)
        
        # 拼接多尺度特征
        combined = torch.cat([coarse_feat, medium_feat, fine_feat], dim=1)  # (B, d_model, 100)
        
        # 融合
        fused = self.fusion_conv(combined)  # (B, d_model, 100)
        
        # 转换为transformer格式
        features = fused.transpose(1, 2)  # (B, 100, d_model)
        
        # 添加CLS token
        cls_tokens = repeat(self.cls_token, '() s e -> b s e', b=batch_size)
        features = torch.cat([cls_tokens, features], dim=1)  # (B, 101, d_model)
        
        return features


class CrossModalAttention(nn.Module):
    """
    跨模态注意力机制 - 基于CMT的Enhanced Cross Attention
    EEG作为Query，EOG/EMG作为Key/Value
    """
    def __init__(self, d_model=128, n_heads=8, dropout=0.1, lightweight=True):
        super().__init__()
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.lightweight = lightweight
        
        # EEG Query投影
        self.eeg_query_proj = nn.Linear(d_model, d_model)
        
        # 轻量化设计：减少key维度
        self.key_dim = d_model // 2 if lightweight else d_model
        
        # EOG Key/Value投影
        self.eog_key_proj = nn.Linear(d_model, self.key_dim)
        self.eog_value_proj = nn.Linear(d_model, d_model)
        
        # 跨模态注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            kdim=self.key_dim,
            vdim=d_model,
            num_heads=n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 动态权重网络
        self.weight_net = nn.Sequential(
            nn.Linear(d_model * 2, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 2),
            nn.Softmax(dim=-1)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, eeg_features, eog_features):
        """
        Args:
            eeg_features: (batch, seq_len, d_model)
            eog_features: (batch, seq_len, d_model)
        Returns:
            fused_features: (batch, seq_len, d_model)
            attention_weights: attention weights
        """
        # Stage 1: 投影
        eeg_query = self.eeg_query_proj(eeg_features)
        eog_key = self.eog_key_proj(eog_features)
        eog_value = self.eog_value_proj(eog_features)
        
        # Stage 2: 跨模态注意力
        cross_attended, attention_weights = self.cross_attention(
            query=eeg_query,
            key=eog_key,
            value=eog_value,
            need_weights=True
        )
        
        # Stage 3: 残差连接和归一化
        cross_attended = self.dropout(cross_attended)
        cross_output = self.norm1(cross_attended + eog_features)
        
        # Stage 4: 动态权重融合
        combined_features = torch.cat([eeg_features, cross_output], dim=-1)
        weights = self.weight_net(combined_features)  # (B, L, 2)
        
        w_eeg = weights[..., 0:1]     # (B, L, 1)
        w_cross = weights[..., 1:2]   # (B, L, 1)
        
        fused_features = w_eeg * eeg_features + w_cross * cross_output
        
        # Stage 5: 最终归一化
        output = self.norm2(fused_features)
        
        return output, attention_weights


class MultiModalEpochEncoder(nn.Module):
    """
    多模态单epoch编码器
    逐步融合EEG, EOG, EMG信号
    """
    def __init__(self, d_model=128, n_heads=8, dropout=0.1, use_eog=True, use_emg=False):
        super().__init__()
        
        self.use_eog = use_eog
        self.use_emg = use_emg
        self.d_model = d_model
        
        # 各模态的特征提取器
        self.eeg_encoder = MultiScaleWindowEmbedding(in_channels=1, d_model=d_model)
        
        if use_eog:
            self.eog_encoder = MultiScaleWindowEmbedding(in_channels=1, d_model=d_model)
            self.eeg_eog_fusion = CrossModalAttention(d_model, n_heads, dropout)
        
        if use_emg:
            self.emg_encoder = MultiScaleWindowEmbedding(in_channels=1, d_model=d_model)
            self.multimodal_fusion = CrossModalAttention(d_model, n_heads, dropout)
        
        # Self-attention for final feature refinement
        self.self_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, eeg_data, eog_data=None, emg_data=None):
        """
        Args:
            eeg_data: (batch, time_steps) - EEG信号
            eog_data: (batch, time_steps) - EOG信号 (optional)
            emg_data: (batch, time_steps) - EMG信号 (optional)
        Returns:
            features: (batch, d_model) - CLS token features
            attention_weights: dict of attention weights
        """
        attention_weights = {}
        
        # Stage 1: EEG特征提取
        eeg_features = self.eeg_encoder(eeg_data)  # (B, 101, d_model)
        fused_features = eeg_features
        
        # Stage 2: EEG-EOG融合 (如果启用EOG)
        if self.use_eog and eog_data is not None:
            eog_features = self.eog_encoder(eog_data)  # (B, 101, d_model)
            fused_features, eog_attn = self.eeg_eog_fusion(
                eeg_features, eog_features
            )
            attention_weights['eeg_eog'] = eog_attn
        
        # Stage 3: 添加EMG (如果启用EMG)
        if self.use_emg and emg_data is not None:
            emg_features = self.emg_encoder(emg_data)  # (B, 101, d_model)
            fused_features, emg_attn = self.multimodal_fusion(
                fused_features, emg_features
            )
            attention_weights['multimodal'] = emg_attn
        
        # Stage 4: Self-attention refinement
        refined_features, self_attn = self.self_attention(
            fused_features, fused_features, fused_features
        )
        refined_features = self.norm(refined_features + fused_features)
        attention_weights['self'] = self_attn
        
        # 返回CLS token特征
        cls_features = refined_features[:, 0, :]  # (B, d_model)
        
        return cls_features, attention_weights


class MultiModalMAMBAFORMER(nn.Module):
    """
    多模态序列MAMBAFORMER
    支持渐进式多模态融合：EEG -> EEG+EOG -> EEG+EOG+EMG
    """
    def __init__(self, n_classes=5, d_model=128, n_heads=8, n_layers=4, 
                 dropout=0.15, seq_len=5, use_eog=True, use_emg=False):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        self.use_eog = use_eog
        self.use_emg = use_emg
        
        # 多模态epoch编码器
        self.epoch_encoder = MultiModalEpochEncoder(
            d_model=d_model, n_heads=n_heads, dropout=dropout,
            use_eog=use_eog, use_emg=use_emg
        )
        
        # 位置编码
        self.pos_encoding = self._create_positional_encoding(seq_len, d_model)
        
        # Transformer序列编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, num_layers=n_layers
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器 - 深睡眠检测
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)
        )
        
        # 权重初始化
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        modality_desc = f"EEG{'_EOG' if use_eog else ''}{'_EMG' if use_emg else ''}"
        logging.info(f"创建MultiModalMAMBAFORMER ({modality_desc}): 参数量={total_params:,}")
        
    def _create_positional_encoding(self, seq_len, d_model):
        """创建位置编码"""
        pe = torch.zeros(seq_len, d_model)
        position = torch.arange(0, seq_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pe.unsqueeze(0), requires_grad=False)
        
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, eeg_data, eog_data=None, emg_data=None, return_features=False):
        """
        Args:
            eeg_data: (batch, seq_len, time_steps)
            eog_data: (batch, seq_len, time_steps) - optional
            emg_data: (batch, seq_len, time_steps) - optional
            return_features: 是否返回中间特征用于自适应损失
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2) or dict of aux_outputs for V11
            modality_features: (eeg_feat, eog_feat, emg_feat) if return_features=True
        """
        batch_size, seq_len = eeg_data.shape[:2]
        
        # Stage 1: 各epoch的多模态特征提取
        sequence_features = []
        all_attention_weights = []
        eeg_features = []
        eog_features = []
        emg_features = []
        
        for t in range(seq_len):
            eeg_t = eeg_data[:, t, :]  # (batch, time_steps)
            eog_t = eog_data[:, t, :] if eog_data is not None else None
            emg_t = emg_data[:, t, :] if emg_data is not None else None
            
            epoch_feat, attn_weights = self.epoch_encoder(eeg_t, eog_t, emg_t)
            
            if return_features and isinstance(epoch_feat, tuple):
                # 如果返回了模态特征
                combined_feat, eeg_feat, eog_feat, emg_feat = epoch_feat
                sequence_features.append(combined_feat)
                eeg_features.append(eeg_feat)
                if eog_feat is not None:
                    eog_features.append(eog_feat)
                if emg_feat is not None:
                    emg_features.append(emg_feat)
            else:
                sequence_features.append(epoch_feat)
            
            all_attention_weights.append(attn_weights)
        
        # Stage 2: 组成序列特征
        sequence_features = torch.stack(sequence_features, dim=1)  # (B, seq_len, d_model)
        
        if return_features and eeg_features:
            eeg_features = torch.stack(eeg_features, dim=1)
            eog_features = torch.stack(eog_features, dim=1) if eog_features else None
            emg_features = torch.stack(emg_features, dim=1) if emg_features else None
        
        # Stage 3: 添加位置编码
        sequence_features = sequence_features + self.pos_encoding[:, :seq_len, :]
        
        # Stage 4: Transformer序列编码
        encoded_sequence = self.transformer_encoder(sequence_features)
        
        # Stage 5: 分类预测
        main_output = self.classifier(encoded_sequence)
        
        # V11支持多个辅助任务
        if return_features:
            aux_outputs = {
                'deep_sleep': self.auxiliary_head(encoded_sequence),
                'rem_detection': self.auxiliary_head(encoded_sequence),  # 可以使用相同的head
                'wake_detection': self.auxiliary_head(encoded_sequence)
            }
            modality_features = (eeg_features, eog_features, emg_features) if eeg_features else None
            return main_output, aux_outputs, modality_features
        else:
            aux_output = self.auxiliary_head(encoded_sequence)
            return main_output, aux_output


class ProgressiveMultiModalTrainer:
    """
    渐进式多模态训练器
    阶段1: EEG only
    阶段2: EEG + EOG
    阶段3: EEG + EOG + EMG (未来)
    """
    
    @staticmethod
    def create_eeg_only_model(config):
        """创建只使用EEG的模型"""
        return MultiModalMAMBAFORMER(
            n_classes=config['n_classes'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len'],
            use_eog=False,
            use_emg=False
        )
    
    @staticmethod
    def create_eeg_eog_model(config):
        """创建EEG+EOG模型"""
        return MultiModalMAMBAFORMER(
            n_classes=config['n_classes'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len'],
            use_eog=True,
            use_emg=False
        )
    
    @staticmethod
    def create_full_multimodal_model(config):
        """创建完整多模态模型(EEG+EOG+EMG) - 兼容方法"""
        return MultiModalMAMBAFORMER(
            n_classes=config['n_classes'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len'],
            use_eog=True,
            use_emg=True
        )
    
    @staticmethod  
    def create_complete_model(config):
        """创建完整三模态模型(EEG+EOG+EMG) - V11专用"""
        return MultiModalMAMBAFORMER(
            n_classes=config['n_classes'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len'],
            use_eog=True,
            use_emg=True
        )


if __name__ == "__main__":
    # 测试模型
    config = {
        'n_classes': 5,
        'd_model': 128,
        'n_heads': 8,
        'n_layers': 4,
        'dropout': 0.1,
        'seq_len': 5
    }
    
    # 测试EEG-only模型
    print("Testing EEG-only model...")
    model_eeg = ProgressiveMultiModalTrainer.create_eeg_only_model(config)
    eeg_data = torch.randn(2, 5, 3000)  # batch=2, seq=5, time=3000
    main_out, aux_out = model_eeg(eeg_data)
    print(f"EEG-only: main_out={main_out.shape}, aux_out={aux_out.shape}")
    
    # 测试EEG+EOG模型
    print("\nTesting EEG+EOG model...")
    model_eeg_eog = ProgressiveMultiModalTrainer.create_eeg_eog_model(config)
    eog_data = torch.randn(2, 5, 3000)  # EOG数据
    main_out, aux_out = model_eeg_eog(eeg_data, eog_data)
    print(f"EEG+EOG: main_out={main_out.shape}, aux_out={aux_out.shape}")