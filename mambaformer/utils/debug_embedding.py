"""
Debug Window_Embedding tensor concatenation issue
"""

import torch
import torch.nn as nn

# 测试数据 - 原始Cross-Modal Transformer用的可能是3000采样点
batch_size = 4
seq_len = 3000  # 30秒@100Hz，匹配Sleep-EDF数据
window_size = 50  # 默认window size

print(f"Using seq_len={seq_len}, window_size={window_size}")
x = torch.randn(batch_size, 1, seq_len)

print(f"Input shape: {x.shape}")

# 复制原始projection层配置
emb_size = 64

projection_1 = nn.Sequential(
    nn.Conv1d(1, emb_size//4, kernel_size=50, stride=50),
    nn.LeakyReLU(),
    nn.BatchNorm1d(emb_size//4),
)

projection_2 = nn.Sequential(
    nn.Conv1d(1, emb_size//8, kernel_size=5, stride=5),
    nn.LeakyReLU(),
    nn.Conv1d(emb_size//8, emb_size//4, kernel_size=5, stride=5),
    nn.LeakyReLU(),
    nn.Conv1d(emb_size//4, (emb_size-emb_size//4)//2, kernel_size=2, stride=2),
    nn.LeakyReLU(),
    nn.BatchNorm1d((emb_size-emb_size//4)//2),
)

projection_3 = nn.Sequential(
    nn.Conv1d(1, emb_size//4, kernel_size=25, stride=25),
    nn.LeakyReLU(),
    nn.Conv1d(emb_size//4, (emb_size-emb_size//4)//2, kernel_size=2, stride=2),
    nn.LeakyReLU(),
    nn.BatchNorm1d((emb_size-emb_size//4)//2),
)

# 测试每个projection
x_1 = projection_1(x)
x_2 = projection_2(x)
x_3 = projection_3(x)

print(f"x_1 shape: {x_1.shape}")
print(f"x_2 shape: {x_2.shape}")
print(f"x_3 shape: {x_3.shape}")

# 计算输出长度
def calc_conv_output_size(input_size, kernel_size, stride, padding=0):
    return (input_size + 2 * padding - kernel_size) // stride + 1

# Projection 1: 3000 -> 3000//50 = 60
out1_len = calc_conv_output_size(3000, 50, 50)
print(f"Expected x_1 length: {out1_len}")

# Projection 2: 3000 -> 600 -> 120 -> 60
out2_len = calc_conv_output_size(3000, 5, 5)  # 600
out2_len = calc_conv_output_size(out2_len, 5, 5)  # 120
out2_len = calc_conv_output_size(out2_len, 2, 2)  # 60
print(f"Expected x_2 length: {out2_len}")

# Projection 3: 3000 -> 120 -> 60
out3_len = calc_conv_output_size(3000, 25, 25)  # 120
out3_len = calc_conv_output_size(out3_len, 2, 2)  # 60
print(f"Expected x_3 length: {out3_len}")