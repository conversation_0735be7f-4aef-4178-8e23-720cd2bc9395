"""
多模态序列数据集
支持EEG, EOG, EMG的渐进式加载
基于现有的4通道数据: EEG-Fpz-Cz, EEG-Pz-Oz, EOG-horizontal, Chin-EMG
"""

import os
import torch
import numpy as np
from torch.utils.data import Dataset
import logging
from typing import List, Tuple, Optional, Dict


class MultiModalSequenceDataset(Dataset):
    """
    多模态序列数据集
    支持渐进式多模态加载：EEG -> EEG+EOG -> EEG+EOG+EMG
    """
    
    def __init__(self, 
                 data_files: List[str], 
                 seq_len: int = 5,
                 use_channels: int = 3,
                 use_eog: bool = False,
                 use_emg: bool = False,
                 max_samples_per_file: Optional[int] = None,
                 transform=None,
                 is_training: bool = True):
        """
        Args:
            data_files: npz文件路径列表
            seq_len: 序列长度
            use_channels: 使用的EEG通道数 (1-2)
            use_eog: 是否使用EOG信号
            use_emg: 是否使用EMG信号
            max_samples_per_file: 每个文件最大样本数
            transform: 数据变换
            is_training: 是否为训练模式
        """
        
        self.seq_len = seq_len
        self.use_channels = min(use_channels, 2)  # 最多2个EEG通道
        self.use_eog = use_eog
        self.use_emg = use_emg
        self.transform = transform
        self.is_training = is_training
        self.max_samples_per_file = max_samples_per_file
        
        # 通道配置
        self.channel_config = self._get_channel_config()
        
        # 存储序列信息
        self.sequences = []
        self.sequence_info = []
        self.epoch_to_file_mapping = {}
        self.total_epochs = 0
        
        # 加载数据
        self._load_data(data_files)
        
        logging.info(f"创建多模态序列数据集: {len(self.sequences)}个序列, "
                    f"序列长度={seq_len}, {self.channel_config['description']}")
        
    def _get_channel_config(self):
        """获取通道配置"""
        config = {
            'eeg_channels': [],
            'eog_channels': [],
            'emg_channels': [],
            'description': ''
        }
        
        # EEG通道 (从前两个通道选择)
        if self.use_channels >= 1:
            config['eeg_channels'].append(0)  # EEG-Fpz-Cz
        if self.use_channels >= 2:
            config['eeg_channels'].append(1)  # EEG-Pz-Oz
            
        # EOG通道
        if self.use_eog:
            config['eog_channels'].append(2)  # EOG-horizontal
            
        # EMG通道
        if self.use_emg:
            config['emg_channels'].append(3)  # Chin-EMG
        
        # 生成描述
        desc_parts = [f"EEG×{len(config['eeg_channels'])}"]
        if self.use_eog:
            desc_parts.append("EOG×1")
        if self.use_emg:
            desc_parts.append("EMG×1")
        config['description'] = "+".join(desc_parts)
        
        return config
    
    def _load_data(self, data_files: List[str]):
        """加载数据文件"""
        file_epoch_start = 0
        
        for file_idx, data_file in enumerate(data_files):
            if not os.path.exists(data_file):
                logging.warning(f"加载文件 {data_file} 失败: 文件不存在")
                continue
                
            try:
                # 加载数据
                data = np.load(data_file)
                epochs = data['x']  # (n_epochs, 3000, 4)
                labels = data['y']  # (n_epochs,)
                
                n_epochs = len(epochs)
                
                # 应用采样限制
                if self.max_samples_per_file is not None:
                    n_epochs = min(n_epochs, self.max_samples_per_file)
                    epochs = epochs[:n_epochs]
                    labels = labels[:n_epochs]
                
                # 创建序列
                file_sequences = self._create_sequences_from_file(
                    epochs, labels, file_idx, file_epoch_start
                )
                
                self.sequences.extend(file_sequences)
                file_epoch_start += n_epochs
                self.total_epochs += n_epochs
                
                logging.debug(f"从文件 {os.path.basename(data_file)} 加载了 "
                            f"{n_epochs} 个epochs, 创建了 {len(file_sequences)} 个序列")
                            
            except Exception as e:
                logging.warning(f"加载文件 {data_file} 失败: {str(e)}")
                continue
    
    def _create_sequences_from_file(self, 
                                   epochs: np.ndarray, 
                                   labels: np.ndarray, 
                                   file_id: int,
                                   epoch_start_idx: int) -> List[Dict]:
        """从单个文件创建序列"""
        sequences = []
        n_epochs = len(epochs)
        
        # 创建滑动窗口序列 (stride=1)
        for start_idx in range(n_epochs - self.seq_len + 1):
            sequence_data = {}
            sequence_labels = labels[start_idx:start_idx + self.seq_len]
            
            # 提取EEG数据
            eeg_data = []
            for ch_idx in self.channel_config['eeg_channels']:
                seq_eeg = epochs[start_idx:start_idx + self.seq_len, :, ch_idx]  # (seq_len, 3000)
                eeg_data.append(seq_eeg)
            
            if len(eeg_data) == 1:
                sequence_data['eeg'] = eeg_data[0]  # (seq_len, 3000)
            else:
                # 多通道EEG平均或拼接
                sequence_data['eeg'] = np.mean(eeg_data, axis=0)  # (seq_len, 3000)
            
            # 提取EOG数据
            if self.use_eog and len(self.channel_config['eog_channels']) > 0:
                eog_ch = self.channel_config['eog_channels'][0]
                sequence_data['eog'] = epochs[start_idx:start_idx + self.seq_len, :, eog_ch]
            
            # 提取EMG数据
            if self.use_emg and len(self.channel_config['emg_channels']) > 0:
                emg_ch = self.channel_config['emg_channels'][0] 
                sequence_data['emg'] = epochs[start_idx:start_idx + self.seq_len, :, emg_ch]
            
            # 存储序列信息
            sequence_info = {
                'file_id': file_id,
                'start_epoch_idx': epoch_start_idx + start_idx,
                'end_epoch_idx': epoch_start_idx + start_idx + self.seq_len - 1,
                'seq_in_file_start': start_idx,
                'labels': sequence_labels
            }
            
            sequences.append({
                'data': sequence_data,
                'labels': torch.LongTensor(sequence_labels),
                'info': sequence_info
            })
            
            self.sequence_info.append(sequence_info)
        
        return sequences
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        data = sequence['data']
        labels = sequence['labels']
        
        # 转换为tensor
        eeg_data = torch.FloatTensor(data['eeg'])  # (seq_len, 3000)
        
        result = [eeg_data]
        
        # EOG数据
        if self.use_eog and 'eog' in data:
            eog_data = torch.FloatTensor(data['eog'])  # (seq_len, 3000)
            result.append(eog_data)
        else:
            # 创建零张量占位符
            result.append(torch.zeros_like(eeg_data))
        
        # EMG数据  
        if self.use_emg and 'emg' in data:
            emg_data = torch.FloatTensor(data['emg'])  # (seq_len, 3000)
            result.append(emg_data)
        else:
            # 创建零张量占位符
            result.append(torch.zeros_like(eeg_data))
        
        result.append(labels)
        
        # 应用数据变换
        if self.transform:
            result = self.transform(result)
        
        return tuple(result)
    
    def get_sequence_info(self, idx: int) -> Optional[Dict]:
        """获取序列信息"""
        if 0 <= idx < len(self.sequence_info):
            return self.sequence_info[idx]
        return None
    
    def get_total_epochs(self) -> int:
        """获取总epoch数"""
        return self.total_epochs
    
    def get_channel_info(self) -> Dict:
        """获取通道信息"""
        return {
            'use_channels': self.use_channels,
            'use_eog': self.use_eog,
            'use_emg': self.use_emg,
            'config': self.channel_config
        }
    
    def get_data_statistics(self) -> Dict:
        """获取数据统计信息"""
        if not self.sequences:
            return {}
        
        # 统计标签分布
        all_labels = []
        for seq in self.sequences:
            all_labels.extend(seq['labels'].tolist())
        
        label_counts = {}
        for label in all_labels:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        return {
            'total_sequences': len(self.sequences),
            'total_epochs': self.total_epochs,
            'seq_len': self.seq_len,
            'label_distribution': label_counts,
            'channel_info': self.get_channel_info()
        }


def create_multimodal_dataloaders(train_files: List[str],
                                val_files: List[str], 
                                test_files: List[str],
                                config: Dict,
                                use_eog: bool = False,
                                use_emg: bool = False):
    """
    创建多模态数据加载器
    
    Args:
        train_files, val_files, test_files: 文件列表
        config: 配置参数
        use_eog: 是否使用EOG
        use_emg: 是否使用EMG
    """
    
    # 创建数据集
    train_dataset = MultiModalSequenceDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=config.get('use_channels', 3),
        use_eog=use_eog,
        use_emg=use_emg,
        max_samples_per_file=config.get('max_samples_per_file'),
        is_training=True
    )
    
    val_dataset = MultiModalSequenceDataset(
        val_files,
        seq_len=config['seq_len'], 
        use_channels=config.get('use_channels', 3),
        use_eog=use_eog,
        use_emg=use_emg,
        max_samples_per_file=config.get('max_samples_per_file'),
        is_training=False
    )
    
    test_dataset = MultiModalSequenceDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=config.get('use_channels', 3),
        use_eog=use_eog,
        use_emg=use_emg,
        max_samples_per_file=config.get('max_samples_per_file'),
        is_training=False
    )
    
    # 打印数据统计
    logging.info("训练集统计:")
    train_stats = train_dataset.get_data_statistics()
    for key, value in train_stats.items():
        logging.info(f"  {key}: {value}")
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=config.get('num_workers', 4),
        pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config.get('num_workers', 4),
        pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config.get('num_workers', 4),
        pin_memory=True
    )
    
    return (train_loader, val_loader, test_loader, 
            train_dataset, val_dataset, test_dataset)


if __name__ == "__main__":
    # 测试多模态数据集
    import json
    
    # 加载数据划分
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    
    # 测试不同模态配置
    configs = [
        {'name': 'EEG-only', 'use_eog': False, 'use_emg': False},
        {'name': 'EEG+EOG', 'use_eog': True, 'use_emg': False},
        {'name': 'EEG+EOG+EMG', 'use_eog': True, 'use_emg': True},
    ]
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        dataset = MultiModalSequenceDataset(
            train_files[:2],  # 只用前两个文件测试
            seq_len=5,
            use_channels=2,
            use_eog=config['use_eog'],
            use_emg=config['use_emg']
        )
        
        # 测试数据获取
        sample = dataset[0]
        print(f"  EEG shape: {sample[0].shape}")
        print(f"  EOG shape: {sample[1].shape if sample[1] is not None else None}")  
        print(f"  EMG shape: {sample[2].shape if sample[2] is not None else None}")
        print(f"  Labels shape: {sample[3].shape}")
        print(f"  Statistics: {dataset.get_data_statistics()}")