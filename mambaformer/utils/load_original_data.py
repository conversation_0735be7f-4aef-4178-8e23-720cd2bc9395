#!/usr/bin/env python3
"""
使用原始Cross-Modal Transformer的数据加载方式
检查数据格式和预处理是否正确
"""

import os
import h5py
import numpy as np
import torch
from torch.utils.data import DataLoader
from pathlib import Path

# 导入原始数据集类
from datasets.sleep_edf import SleepEDF_MultiChan_Dataset, get_dataset

def check_original_data_format():
    """检查原始模型期望的数据格式"""
    
    # 原始数据路径（需要调整为实际路径）
    data_paths = [
        '/home/<USER>/Sleep_EDF_Dataset',  # 原始路径
        './Sleep_EDF_Dataset',            # 可能的本地路径
        '../data-edf/sleep_edf_20',       # 我们的数据路径
        './processed_data_fixed'          # 我们处理后的数据
    ]
    
    # 查找可用的数据路径
    available_path = None
    for path in data_paths:
        if os.path.exists(path):
            available_path = path
            print(f"找到数据路径: {path}")
            break
    
    if not available_path:
        print("未找到数据路径")
        return
    
    # 检查数据文件格式
    print("\n检查数据文件格式:")
    
    # 检查h5文件
    h5_files = list(Path(available_path).glob('*.h5'))
    if h5_files:
        print(f"找到 {len(h5_files)} 个h5文件")
        
        # 检查第一个文件的格式
        sample_file = h5_files[0]
        print(f"\n检查样本文件: {sample_file}")
        
        with h5py.File(sample_file, 'r') as f:
            print(f"Keys: {list(f.keys())}")
            for key in f.keys():
                data = f[key]
                print(f"  {key}: shape={data.shape}, dtype={data.dtype}")
                if len(data.shape) > 0 and data.shape[0] < 10:
                    print(f"    数据样本: {data[:5]}")
    
    # 检查原始模型的数据加载方式
    print("\n原始模型数据加载器期望:")
    print("- EEG数据: x01.h5, x02.h5, ... (形状: [n_epochs, seq_len])")
    print("- EOG数据: eog01.h5, eog02.h5, ... (形状: [n_epochs, seq_len])")
    print("- 标签: y01.h5, y02.h5, ... (形状: [n_epochs])")
    print("- 均值: mean01.h5, mean02.h5, ...")
    print("- 标准差: std01.h5, std02.h5, ...")

def load_data_original_way(data_path, fold_list):
    """使用原始方式加载数据"""
    
    # 原始数据加载逻辑
    eeg_data_list = []
    eog_data_list = []
    label_list = []
    
    for fold in fold_list:
        # 原始命名格式
        eeg_file = os.path.join(data_path, f'x{fold+1:02d}.h5')
        eog_file = os.path.join(data_path, f'eog{fold+1:02d}.h5')
        label_file = os.path.join(data_path, f'y{fold+1:02d}.h5')
        
        if os.path.exists(eeg_file):
            with h5py.File(eeg_file, 'r') as f:
                eeg_data = f['data'][:]
                eeg_data_list.append(eeg_data)
                print(f"加载 EEG fold {fold}: shape={eeg_data.shape}")
        
        if os.path.exists(eog_file):
            with h5py.File(eog_file, 'r') as f:
                eog_data = f['data'][:]
                eog_data_list.append(eog_data)
                print(f"加载 EOG fold {fold}: shape={eog_data.shape}")
        
        if os.path.exists(label_file):
            with h5py.File(label_file, 'r') as f:
                labels = f['data'][:]
                label_list.append(labels)
                print(f"加载标签 fold {fold}: shape={labels.shape}, unique={np.unique(labels)}")
    
    if eeg_data_list:
        # 合并数据
        all_eeg = np.concatenate(eeg_data_list, axis=0)
        all_eog = np.concatenate(eog_data_list, axis=0)
        all_labels = np.concatenate(label_list, axis=0)
        
        print(f"\n合并后数据:")
        print(f"EEG: {all_eeg.shape}")
        print(f"EOG: {all_eog.shape}")
        print(f"标签: {all_labels.shape}")
        print(f"标签分布: {np.bincount(all_labels.astype(int))}")
        
        # 检查数据范围
        print(f"\nEEG数据范围: [{np.min(all_eeg):.4f}, {np.max(all_eeg):.4f}]")
        print(f"EOG数据范围: [{np.min(all_eog):.4f}, {np.max(all_eog):.4f}]")
        
        return all_eeg, all_eog, all_labels
    
    return None, None, None

def test_original_dataset_class():
    """测试原始数据集类"""
    
    try:
        # 使用原始参数
        class Args:
            data_path = './processed_data_fixed'
            train_data_list = [0, 1, 2]
            val_data_list = [3]
            batch_size = 32
            window_size = 50
            num_seq = 15
            
        args = Args()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 尝试使用原始的get_dataset函数
        print("\n测试原始数据加载函数...")
        # train_loader, val_loader = get_dataset(device, args)
        
        # 如果失败，使用我们的数据
        print("\n使用我们的数据格式...")
        eeg_data, eog_data, labels = load_data_original_way(
            args.data_path, args.train_data_list
        )
        
        if eeg_data is not None:
            print("\n数据加载成功!")
            print(f"数据统计:")
            print(f"- EEG均值: {np.mean(eeg_data):.4f}, 标准差: {np.std(eeg_data):.4f}")
            print(f"- EOG均值: {np.mean(eog_data):.4f}, 标准差: {np.std(eog_data):.4f}")
            
            # 检查标签平衡
            label_counts = np.bincount(labels.astype(int))
            label_percentages = label_counts / len(labels) * 100
            print(f"\n标签分布:")
            class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
            for i, (count, pct) in enumerate(zip(label_counts, label_percentages)):
                print(f"  {class_names[i]}: {count} ({pct:.1f}%)")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

def compare_with_reference():
    """与参考模型结果比较"""
    
    print("\n参考模型(84.8%准确率)的数据分布:")
    print("Wake: 19.6%")
    print("N1: 6.6%")  
    print("N2: 42.1%")
    print("N3: 13.5%")
    print("REM: 18.2%")
    
    # 加载我们的数据检查分布
    data_path = './processed_data_fixed'
    all_labels = []
    
    for i in range(1, 6):  # 检查前5个受试者
        label_file = os.path.join(data_path, f'y{i:02d}.h5')
        if os.path.exists(label_file):
            with h5py.File(label_file, 'r') as f:
                labels = f['data'][:]
                all_labels.append(labels)
    
    if all_labels:
        all_labels = np.concatenate(all_labels)
        label_counts = np.bincount(all_labels.astype(int))
        label_percentages = label_counts / len(all_labels) * 100
        
        print(f"\n我们的数据分布 ({len(all_labels)} 样本):")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for i, (count, pct) in enumerate(zip(label_counts, label_percentages)):
            print(f"{class_names[i]}: {count} ({pct:.1f}%)")

if __name__ == "__main__":
    print("🔍 检查原始Cross-Modal Transformer数据格式")
    print("=" * 60)
    
    # 检查数据格式
    check_original_data_format()
    
    print("\n" + "=" * 60)
    
    # 测试数据加载
    test_original_dataset_class()
    
    print("\n" + "=" * 60)
    
    # 与参考结果比较
    compare_with_reference()