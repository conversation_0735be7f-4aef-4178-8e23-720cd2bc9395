"""
调试Sleep-EDF标注解析
"""

import mne
from pathlib import Path

data_dir = Path('../data-edf/sleep_edf_20')

# 测试一个文件
psg_file = data_dir / 'SC4052E0-PSG.edf'
hypno_file = data_dir / 'SC4052EC-Hypnogram.edf'

print(f"PSG file: {psg_file}")
print(f"Hypnogram file: {hypno_file}")

# 加载标注 - 使用正确的方法
annotations = mne.read_annotations(str(hypno_file))

print(f"\nAnnotations count: {len(annotations)}")
print(f"First 10 annotations:")

for i, annot in enumerate(annotations[:10]):
    print(f"  {i}: onset={annot['onset']:.2f}, duration={annot['duration']:.2f}, desc='{annot['description']}'")

print(f"\nUnique descriptions:")
unique_desc = set(annot['description'] for annot in annotations)
for desc in sorted(unique_desc):
    count = sum(1 for annot in annotations if annot['description'] == desc)
    print(f"  '{desc}': {count}")

print(f"\nTotal duration: {annotations[-1]['onset'] + annotations[-1]['duration']:.2f} seconds")

# 检查PSG数据长度
psg_raw = mne.io.read_raw_edf(str(psg_file), preload=True, verbose=False)
psg_duration = len(psg_raw) / psg_raw.info['sfreq']
print(f"PSG duration: {psg_duration:.2f} seconds")