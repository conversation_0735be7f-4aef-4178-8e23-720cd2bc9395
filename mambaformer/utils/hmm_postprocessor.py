"""
HMM后处理器 - 睡眠状态转换约束和时序一致性优化

基于睡眠医学领域知识，睡眠状态转换遵循特定的生理学规律：
1. 从清醒到深睡眠需要经过浅睡眠阶段
2. REM睡眠通常不会直接转换到深睡眠
3. 连续的相同状态更符合睡眠生理学

核心技术：
- 隐马尔可夫模型 (HMM) 状态转换概率建模
- 维特比算法最优路径解码
- 睡眠状态转换约束矩阵
- 时序平滑和噪声抑制
"""

import numpy as np
import torch
import torch.nn as nn
from scipy.stats import multivariate_normal
import logging
from typing import List, Dict, Tuple, Optional


class SleepStageHMM:
    """睡眠阶段隐马尔可夫模型"""
    
    def __init__(self, n_states: int = 5):
        """
        Args:
            n_states: 睡眠阶段数量 (Wake, N1, N2, N3, REM)
        """
        self.n_states = n_states
        self.state_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        
        # 初始化参数
        self.initial_probs = None      # 初始状态概率
        self.transition_probs = None   # 转换概率矩阵
        self.emission_probs = None     # 发射概率参数
        
        # 根据睡眠医学知识初始化
        self._init_sleep_stage_priors()
        
    def _init_sleep_stage_priors(self):
        """基于睡眠医学知识初始化HMM参数"""
        
        # 1. 初始状态概率 - 通常从清醒开始
        self.initial_probs = np.array([0.7, 0.2, 0.08, 0.01, 0.01])
        
        # 2. 状态转换概率矩阵 - 基于睡眠生理学
        self.transition_probs = np.array([
            # From: Wake  N1    N2    N3    REM   To:
            [  0.85, 0.12, 0.02, 0.00, 0.01], # Wake
            [  0.15, 0.65, 0.18, 0.00, 0.02], # N1  
            [  0.05, 0.10, 0.70, 0.12, 0.03], # N2
            [  0.00, 0.02, 0.15, 0.80, 0.03], # N3
            [  0.10, 0.15, 0.05, 0.02, 0.68]  # REM
        ])
        
        # 确保每行和为1
        self.transition_probs = self.transition_probs / self.transition_probs.sum(axis=1, keepdims=True)
        
        logging.info("初始化睡眠状态转换概率矩阵")
        logging.info("转换约束: Wake→N1→N2→N3, REM可与各阶段转换但限制直接到N3")
        
    def fit(self, observations: np.ndarray, true_labels: Optional[np.ndarray] = None):
        """
        训练HMM参数
        
        Args:
            observations: 观察序列 (n_samples, n_features) - 神经网络的softmax概率
            true_labels: 真实标签序列 (可选，用于监督学习)
        """
        n_samples, n_features = observations.shape
        
        # 使用监督学习优化转换概率（如果有标签）
        if true_labels is not None:
            self._update_transition_probs_supervised(true_labels)
        
        # 发射概率建模 - 假设观察为多元高斯分布
        self.emission_means = np.zeros((self.n_states, n_features))
        self.emission_covs = np.zeros((self.n_states, n_features, n_features))
        
        for state in range(self.n_states):
            # 使用softmax输出的期望作为均值
            self.emission_means[state] = np.eye(n_features)[state]  # one-hot期望
            
            # 协方差矩阵 - 对角占优，体现预测置信度
            self.emission_covs[state] = np.eye(n_features) * 0.1
            self.emission_covs[state][state, state] = 0.05  # 主对角更小的方差
            
        logging.info("HMM参数训练完成")
        
    def _update_transition_probs_supervised(self, labels: np.ndarray):
        """基于真实标签更新转换概率"""
        transition_counts = np.zeros((self.n_states, self.n_states))
        
        for i in range(len(labels) - 1):
            current_state = labels[i]
            next_state = labels[i + 1]
            if 0 <= current_state < self.n_states and 0 <= next_state < self.n_states:
                transition_counts[current_state, next_state] += 1
        
        # 平滑处理避免零概率 + 保持生理学约束
        alpha = 0.1  # 平滑参数
        supervised_probs = transition_counts / (transition_counts.sum(axis=1, keepdims=True) + 1e-8)
        
        # 结合先验知识和观察数据
        self.transition_probs = (1 - alpha) * self.transition_probs + alpha * supervised_probs
        
        # 确保行和为1
        self.transition_probs = self.transition_probs / self.transition_probs.sum(axis=1, keepdims=True)
        
    def decode(self, observations: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        维特比解码 - 找到最可能的状态序列
        
        Args:
            observations: 观察序列 (seq_len, n_states) - softmax概率
            
        Returns:
            path: 最优状态路径
            prob: 路径概率
        """
        seq_len = len(observations)
        
        # 初始化维特比表格
        viterbi = np.zeros((seq_len, self.n_states))
        path = np.zeros((seq_len, self.n_states), dtype=int)
        
        # 初始化第一步
        for state in range(self.n_states):
            emission_prob = self._calculate_emission_prob(observations[0], state)
            viterbi[0, state] = np.log(self.initial_probs[state] + 1e-8) + np.log(emission_prob + 1e-8)
        
        # 前向传播
        for t in range(1, seq_len):
            for curr_state in range(self.n_states):
                emission_prob = self._calculate_emission_prob(observations[t], curr_state)
                
                # 计算从所有前一状态到当前状态的概率
                transition_scores = []
                for prev_state in range(self.n_states):
                    score = (viterbi[t-1, prev_state] + 
                            np.log(self.transition_probs[prev_state, curr_state] + 1e-8))
                    transition_scores.append(score)
                
                # 选择最大概率路径
                best_prev_state = np.argmax(transition_scores)
                viterbi[t, curr_state] = transition_scores[best_prev_state] + np.log(emission_prob + 1e-8)
                path[t, curr_state] = best_prev_state
        
        # 反向追踪找到最优路径
        states = np.zeros(seq_len, dtype=int)
        states[-1] = np.argmax(viterbi[-1])
        
        for t in range(seq_len - 2, -1, -1):
            states[t] = path[t + 1, states[t + 1]]
        
        path_prob = np.max(viterbi[-1])
        
        return states, path_prob
        
    def _calculate_emission_prob(self, observation: np.ndarray, state: int) -> float:
        """计算发射概率"""
        try:
            # 使用多元高斯分布计算发射概率
            mean = self.emission_means[state]
            cov = self.emission_covs[state]
            
            # 处理奇异协方差矩阵
            try:
                prob = multivariate_normal.pdf(observation, mean=mean, cov=cov)
            except np.linalg.LinAlgError:
                # 如果协方差矩阵奇异，使用对角协方差
                cov_diag = np.diag(np.diag(cov))
                prob = multivariate_normal.pdf(observation, mean=mean, cov=cov_diag)
            
            return max(prob, 1e-10)  # 避免零概率
            
        except Exception as e:
            # 备用方案：使用cosine相似度
            similarity = np.dot(observation, self.emission_means[state])
            return max(similarity, 1e-10)
    
    def smooth_predictions(self, raw_predictions: np.ndarray, 
                          window_size: int = 5) -> np.ndarray:
        """
        时序平滑预测结果
        
        Args:
            raw_predictions: 原始预测 (seq_len, n_states)
            window_size: 平滑窗口大小
            
        Returns:
            smoothed_predictions: 平滑后的预测
        """
        seq_len = len(raw_predictions)
        smoothed = raw_predictions.copy()
        
        # 滑动窗口平滑
        for i in range(seq_len):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(seq_len, i + window_size // 2 + 1)
            
            # 计算窗口内的加权平均
            window_weights = self._calculate_temporal_weights(end_idx - start_idx)
            window_preds = raw_predictions[start_idx:end_idx]
            
            smoothed[i] = np.average(window_preds, axis=0, weights=window_weights)
        
        return smoothed
    
    def _calculate_temporal_weights(self, window_len: int) -> np.ndarray:
        """计算时序权重"""
        # 高斯权重，中心权重最大
        center = window_len // 2
        weights = np.exp(-0.5 * ((np.arange(window_len) - center) / (window_len / 4)) ** 2)
        return weights / weights.sum()


class HMMPostProcessor:
    """HMM后处理器主类"""
    
    def __init__(self, n_states: int = 5, 
                 enable_smoothing: bool = True,
                 smoothing_window: int = 5):
        """
        Args:
            n_states: 状态数量
            enable_smoothing: 是否启用平滑
            smoothing_window: 平滑窗口大小
        """
        self.hmm = SleepStageHMM(n_states)
        self.enable_smoothing = enable_smoothing
        self.smoothing_window = smoothing_window
        self.is_fitted = False
        
    def fit(self, train_predictions: np.ndarray, 
            train_labels: Optional[np.ndarray] = None):
        """
        训练HMM后处理器
        
        Args:
            train_predictions: 训练集预测概率 (n_samples, seq_len, n_states)
            train_labels: 训练集真实标签 (n_samples, seq_len)
        """
        # 将所有序列拼接成单一长序列进行训练
        all_predictions = []
        all_labels = []
        
        for i in range(len(train_predictions)):
            all_predictions.append(train_predictions[i])
            if train_labels is not None:
                all_labels.append(train_labels[i])
        
        combined_predictions = np.vstack(all_predictions)
        combined_labels = np.concatenate(all_labels) if train_labels is not None else None
        
        # 训练HMM
        self.hmm.fit(combined_predictions, combined_labels)
        self.is_fitted = True
        
        logging.info(f"HMM后处理器训练完成，处理了{len(combined_predictions)}个样本")
        
    def process(self, predictions: np.ndarray) -> np.ndarray:
        """
        处理预测结果
        
        Args:
            predictions: 原始预测概率 (seq_len, n_states) 或 (batch, seq_len, n_states)
            
        Returns:
            processed_predictions: 后处理预测结果
        """
        if not self.is_fitted:
            logging.warning("HMM未训练，返回原始预测")
            return predictions
        
        # 处理批量或单序列
        if predictions.ndim == 3:
            # 批量处理
            batch_size = predictions.shape[0]
            processed = np.zeros_like(predictions)
            
            for b in range(batch_size):
                processed[b] = self._process_single_sequence(predictions[b])
            
            return processed
        else:
            # 单序列处理
            return self._process_single_sequence(predictions)
    
    def _process_single_sequence(self, predictions: np.ndarray) -> np.ndarray:
        """处理单个序列"""
        # Step 1: 时序平滑
        if self.enable_smoothing:
            smoothed_preds = self.hmm.smooth_predictions(predictions, self.smoothing_window)
        else:
            smoothed_preds = predictions
        
        # Step 2: HMM解码
        optimal_states, path_prob = self.hmm.decode(smoothed_preds)
        
        # Step 3: 转换为概率分布
        processed_probs = np.zeros_like(predictions)
        for t, state in enumerate(optimal_states):
            processed_probs[t, state] = 1.0
            
        # Step 4: 软化处理（避免过度确定性）
        alpha = 0.8  # 保持一定的不确定性
        final_probs = alpha * processed_probs + (1 - alpha) * smoothed_preds
        
        return final_probs
    
    def evaluate_improvement(self, raw_predictions: np.ndarray, 
                           processed_predictions: np.ndarray,
                           true_labels: np.ndarray) -> Dict[str, float]:
        """评估后处理改进效果"""
        from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
        
        # 转换为标签
        raw_labels = np.argmax(raw_predictions, axis=-1).flatten()
        processed_labels = np.argmax(processed_predictions, axis=-1).flatten()
        true_labels_flat = true_labels.flatten()
        
        # 计算指标
        improvements = {
            'raw_accuracy': accuracy_score(true_labels_flat, raw_labels),
            'processed_accuracy': accuracy_score(true_labels_flat, processed_labels),
            'raw_f1': f1_score(true_labels_flat, raw_labels, average='macro'),
            'processed_f1': f1_score(true_labels_flat, processed_labels, average='macro'),
            'raw_kappa': cohen_kappa_score(true_labels_flat, raw_labels),
            'processed_kappa': cohen_kappa_score(true_labels_flat, processed_labels)
        }
        
        # 计算改进
        improvements['accuracy_gain'] = improvements['processed_accuracy'] - improvements['raw_accuracy']
        improvements['f1_gain'] = improvements['processed_f1'] - improvements['raw_f1']
        improvements['kappa_gain'] = improvements['processed_kappa'] - improvements['raw_kappa']
        
        return improvements


def test_hmm_postprocessor():
    """测试HMM后处理器"""
    import torch
    np.random.seed(42)
    
    # 模拟数据
    seq_len = 100
    n_states = 5
    batch_size = 10
    
    # 生成模拟softmax概率输出
    raw_predictions = np.random.dirichlet(np.ones(n_states), size=(batch_size, seq_len))
    
    # 生成模拟真实标签
    true_labels = np.random.randint(0, n_states, size=(batch_size, seq_len))
    
    # 创建并测试后处理器
    processor = HMMPostProcessor(n_states=n_states, enable_smoothing=True)
    
    # 训练
    processor.fit(raw_predictions[:8], true_labels[:8])  # 用前8个样本训练
    
    # 测试
    test_preds = raw_predictions[8:]
    test_labels = true_labels[8:]
    
    processed_preds = processor.process(test_preds)
    
    # 评估改进
    improvements = processor.evaluate_improvement(test_preds, processed_preds, test_labels)
    
    print("HMM后处理改进效果:")
    for metric, value in improvements.items():
        print(f"  {metric}: {value:.4f}")
    
    return processor


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_hmm_postprocessor()