"""
集成学习框架 - MAMBAFORMER多版本融合
整合V7, V8, V10, V11, V12的最佳模型
目标：突破85% Macro F1，冲击ICASSP 2026

核心思想：
1. 多架构融合：单模态(V7,V8) + 双模态(V10) + 三模态(V11) + 深度(V12)
2. 智能权重分配：基于验证性能和模态专长
3. HMM后处理：时序平滑和生理约束
4. 渐进式集成：从简单到复杂的融合策略
"""

import os
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats
from collections import defaultdict
import json

# HMM后处理
try:
    from .hmm_postprocessor import SleepStageHMMProcessor
except ImportError:
    SleepStageHMMProcessor = None


class EnsembleWeightLearner(nn.Module):
    """动态集成权重学习器"""
    
    def __init__(self, num_models=5, num_classes=5, hidden_dim=64):
        super().__init__()
        self.num_models = num_models
        self.num_classes = num_classes
        
        # 模型置信度评估网络
        self.confidence_net = nn.Sequential(
            nn.Linear(num_models * num_classes, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, num_models),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, model_outputs: torch.Tensor) -> torch.Tensor:
        """
        Args:
            model_outputs: (batch, seq_len, num_models, num_classes)
        Returns:
            weights: (batch, seq_len, num_models)
        """
        batch_size, seq_len, num_models, num_classes = model_outputs.shape
        
        # 展平输入
        flattened = model_outputs.view(batch_size * seq_len, -1)
        weights = self.confidence_net(flattened)
        weights = weights.view(batch_size, seq_len, num_models)
        
        return weights


class MAMBAFORMEREnsemble:
    """MAMBAFORMER集成学习系统"""
    
    def __init__(self, model_configs: Dict, device='cuda'):
        self.device = device
        self.model_configs = model_configs
        self.models = {}
        self.model_weights = {}
        self.performance_history = defaultdict(list)
        
        # 预定义的基础权重（基于历史性能）
        self.base_weights = {
            'V7_EEG': 0.25,      # 单模态基线，稳定可靠
            'V8_Enhanced': 0.20,  # 增强版单模态
            'V10_EEG_EOG': 0.25,  # 双模态融合
            'V11_Complete': 0.15, # 三模态完整版
            'V12_Deep': 0.15      # 深度架构
        }
        
        # 动态权重学习器
        self.weight_learner = EnsembleWeightLearner(
            num_models=len(self.base_weights),
            num_classes=5
        ).to(device)
        
        # HMM后处理器
        self.hmm_processor = None
        if SleepStageHMMProcessor:
            self.hmm_processor = SleepStageHMMProcessor()
    
    def load_model(self, version: str, model_path: str, model_class, config: Dict):
        """加载单个模型"""
        try:
            model = model_class(**config)
            if os.path.exists(model_path):
                state_dict = torch.load(model_path, map_location=self.device)
                model.load_state_dict(state_dict)
                logging.info(f"✅ 成功加载 {version}: {model_path}")
            else:
                logging.warning(f"⚠️  模型文件不存在: {model_path}")
                return False
                
            model.to(self.device)
            model.eval()
            self.models[version] = model
            return True
            
        except Exception as e:
            logging.error(f"❌ 加载 {version} 失败: {e}")
            return False
    
    def get_model_predictions(self, data_batch: Dict) -> Dict[str, torch.Tensor]:
        """获取所有模型的预测"""
        predictions = {}
        
        with torch.no_grad():
            for version, model in self.models.items():
                try:
                    if version in ['V7_EEG', 'V8_Enhanced']:
                        # 单模态模型：只使用EEG
                        eeg_data = data_batch['eeg']
                        result = model(eeg_data)
                        if isinstance(result, tuple):
                            outputs = result[0]  # 主输出
                        else:
                            outputs = result
                        
                    elif version == 'V10_EEG_EOG':
                        # 双模态模型：EEG + EOG  
                        eeg_data = data_batch['eeg']
                        eog_data = data_batch['eog']
                        result = model(eeg_data, eog_data, return_features=False)
                        if isinstance(result, tuple):
                            outputs = result[0]  # 主输出
                        else:
                            outputs = result
                        
                    elif version in ['V11_Complete', 'V12_Deep']:
                        # 完整模型：EEG + EOG + EMG
                        eeg_data = data_batch['eeg']
                        eog_data = data_batch['eog'] 
                        emg_data = data_batch['emg']
                        result = model(eeg_data, eog_data, emg_data, return_features=False)
                        if isinstance(result, tuple):
                            outputs = result[0]  # 主输出
                        else:
                            outputs = result
                    
                    predictions[version] = torch.softmax(outputs, dim=-1)
                    
                except Exception as e:
                    logging.warning(f"⚠️  {version} 预测失败: {e}")
                    # 使用随机预测作为fallback
                    batch_size, seq_len = data_batch['eeg'].shape[:2]
                    predictions[version] = torch.rand(
                        batch_size, seq_len, 5, device=self.device
                    )
                    predictions[version] = torch.softmax(predictions[version], dim=-1)
        
        return predictions
    
    def voting_ensemble(self, predictions: Dict[str, torch.Tensor]) -> torch.Tensor:
        """简单投票集成"""
        weighted_preds = []
        total_weight = 0
        
        for version, pred in predictions.items():
            weight = self.base_weights.get(version, 0.1)
            weighted_preds.append(pred * weight)
            total_weight += weight
        
        # 归一化
        ensemble_pred = torch.stack(weighted_preds, dim=0).sum(dim=0) / total_weight
        return ensemble_pred
    
    def learned_ensemble(self, predictions: Dict[str, torch.Tensor]) -> torch.Tensor:
        """学习权重的集成"""
        if len(predictions) == 0:
            return None
            
        # 堆叠所有预测
        pred_list = []
        version_order = sorted(predictions.keys())  # 保证顺序一致
        
        for version in version_order:
            pred_list.append(predictions[version])
        
        model_outputs = torch.stack(pred_list, dim=2)  # (B, L, M, C)
        
        # 学习权重
        weights = self.weight_learner(model_outputs)  # (B, L, M)
        
        # 加权融合
        weights = weights.unsqueeze(-1)  # (B, L, M, 1)
        ensemble_pred = (model_outputs * weights).sum(dim=2)  # (B, L, C)
        
        return ensemble_pred
    
    def temporal_ensemble(self, predictions: Dict[str, torch.Tensor]) -> torch.Tensor:
        """考虑时序的集成"""
        # 首先进行基础集成
        base_ensemble = self.voting_ensemble(predictions)
        
        # 如果有HMM后处理器，应用时序约束
        if self.hmm_processor and base_ensemble is not None:
            # 转换为CPU numpy进行HMM处理
            base_pred_np = base_ensemble.cpu().numpy()
            batch_size, seq_len, num_classes = base_pred_np.shape
            
            processed_preds = []
            for b in range(batch_size):
                sequence_probs = base_pred_np[b]  # (seq_len, num_classes)
                processed_seq = self.hmm_processor.smooth_predictions(sequence_probs)
                processed_preds.append(processed_seq)
            
            processed_ensemble = torch.tensor(
                np.stack(processed_preds), device=self.device, dtype=torch.float32
            )
            return processed_ensemble
        
        return base_ensemble
    
    def adaptive_ensemble(self, predictions: Dict[str, torch.Tensor], 
                         confidence_threshold: float = 0.8) -> torch.Tensor:
        """自适应集成：根据预测置信度动态调整权重"""
        if len(predictions) == 0:
            return None
            
        ensemble_pred = torch.zeros_like(list(predictions.values())[0])
        total_confidence = torch.zeros(
            ensemble_pred.shape[:-1], device=self.device
        )  # (B, L)
        
        for version, pred in predictions.items():
            # 计算置信度 (最大概率)
            confidence = torch.max(pred, dim=-1)[0]  # (B, L)
            
            # 根据置信度调整权重
            base_weight = self.base_weights.get(version, 0.1)
            adaptive_weight = confidence * base_weight
            
            # 加权累积
            ensemble_pred += pred * adaptive_weight.unsqueeze(-1)
            total_confidence += adaptive_weight
        
        # 归一化
        ensemble_pred = ensemble_pred / (total_confidence.unsqueeze(-1) + 1e-8)
        
        return ensemble_pred
    
    def predict(self, data_batch: Dict, ensemble_method: str = 'adaptive') -> Dict:
        """集成预测主函数"""
        predictions = self.get_model_predictions(data_batch)
        
        if len(predictions) == 0:
            raise ValueError("没有可用的模型进行预测")
        
        # 选择集成方法
        if ensemble_method == 'voting':
            ensemble_pred = self.voting_ensemble(predictions)
        elif ensemble_method == 'learned':
            ensemble_pred = self.learned_ensemble(predictions)
        elif ensemble_method == 'temporal':
            ensemble_pred = self.temporal_ensemble(predictions)
        elif ensemble_method == 'adaptive':
            ensemble_pred = self.adaptive_ensemble(predictions)
        else:
            raise ValueError(f"未知的集成方法: {ensemble_method}")
        
        # 计算最终预测类别
        final_preds = torch.argmax(ensemble_pred, dim=-1)
        
        return {
            'predictions': final_preds,
            'probabilities': ensemble_pred,
            'individual_predictions': predictions,
            'ensemble_method': ensemble_method
        }
    
    def evaluate_ensemble(self, test_loader, ensemble_methods: List[str] = None):
        """评估不同集成方法的性能"""
        if ensemble_methods is None:
            ensemble_methods = ['voting', 'adaptive', 'temporal']
        
        results = {}
        
        for method in ensemble_methods:
            logging.info(f"🔍 评估集成方法: {method}")
            
            all_predictions = []
            all_labels = []
            
            for batch_data in test_loader:
                # 数据格式化
                data_batch = {
                    'eeg': batch_data[0].to(self.device),
                    'eog': batch_data[1].to(self.device) if batch_data[1] is not None else None,
                    'emg': batch_data[2].to(self.device) if batch_data[2] is not None else None
                }
                labels = batch_data[3]
                
                # 集成预测
                try:
                    result = self.predict(data_batch, ensemble_method=method)
                    predictions = result['predictions'].cpu().numpy()
                    
                    all_predictions.extend(predictions.flatten())
                    all_labels.extend(labels.numpy().flatten())
                    
                except Exception as e:
                    logging.error(f"❌ {method} 预测失败: {e}")
                    continue
            
            # 计算指标
            if all_predictions and all_labels:
                from .enhanced_metrics import get_comprehensive_metrics
                metrics = get_comprehensive_metrics(
                    np.array(all_labels), np.array(all_predictions)
                )
                results[method] = metrics
                
                logging.info(f"📊 {method} 结果:")
                logging.info(f"  Accuracy: {metrics['accuracy']:.4f}")
                logging.info(f"  Macro F1: {metrics['macro_f1']:.4f}")
                logging.info(f"  Kappa: {metrics['kappa']:.4f}")
        
        return results
    
    def save_ensemble_config(self, save_path: str):
        """保存集成配置"""
        config = {
            'model_configs': self.model_configs,
            'base_weights': self.base_weights,
            'performance_history': dict(self.performance_history),
            'loaded_models': list(self.models.keys())
        }
        
        with open(save_path, 'w') as f:
            json.dump(config, f, indent=2, default=str)
        
        logging.info(f"💾 集成配置已保存: {save_path}")
    
    def train_weight_learner(self, train_loader, num_epochs: int = 10):
        """训练动态权重学习器"""
        if len(self.models) == 0:
            logging.error("❌ 没有加载的模型，无法训练权重学习器")
            return
        
        logging.info("🚀 开始训练动态权重学习器")
        optimizer = torch.optim.Adam(self.weight_learner.parameters(), lr=1e-4)
        criterion = nn.CrossEntropyLoss()
        
        for epoch in range(num_epochs):
            total_loss = 0
            num_batches = 0
            
            for batch_data in train_loader:
                data_batch = {
                    'eeg': batch_data[0].to(self.device),
                    'eog': batch_data[1].to(self.device) if batch_data[1] is not None else None,
                    'emg': batch_data[2].to(self.device) if batch_data[2] is not None else None
                }
                labels = batch_data[3].to(self.device)
                
                # 获取个体模型预测
                predictions = self.get_model_predictions(data_batch)
                if len(predictions) == 0:
                    continue
                
                # 学习权重集成
                ensemble_pred = self.learned_ensemble(predictions)
                if ensemble_pred is None:
                    continue
                
                # 计算损失
                loss = criterion(
                    ensemble_pred.view(-1, 5), labels.view(-1)
                )
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            avg_loss = total_loss / max(num_batches, 1)
            logging.info(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.4f}")
        
        logging.info("✅ 动态权重学习器训练完成")


def create_ensemble_system(checkpoint_dir: str = '../../checkpoints') -> MAMBAFORMEREnsemble:
    """创建完整的集成系统"""
    
    # 模型配置
    model_configs = {
        'V7_EEG': {
            'checkpoint': os.path.join(checkpoint_dir, 'sequential_v7_balanced.pth'),
            'config': {
                'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_layers': 4,
                'dropout': 0.15, 'seq_len': 5
            }
        },
        'V8_Enhanced': {
            'checkpoint': os.path.join(checkpoint_dir, 'sequential_v8_enhanced.pth'), 
            'config': {
                'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_layers': 4,
                'dropout': 0.15, 'seq_len': 5
            }
        },
        'V10_EEG_EOG': {
            'checkpoint': os.path.join(checkpoint_dir, 'multimodal_v10_eeg_eog.pth'),
            'config': {
                'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_layers': 4,
                'dropout': 0.12, 'seq_len': 5, 'use_eog': True, 'use_emg': False
            }
        },
        'V11_Complete': {
            'checkpoint': os.path.join(checkpoint_dir, 'multimodal_v11_complete.pth'),
            'config': {
                'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_layers': 4,
                'dropout': 0.12, 'seq_len': 5, 'use_eog': True, 'use_emg': True  
            }
        },
        'V12_Deep': {
            'checkpoint': os.path.join(checkpoint_dir, 'deep_v12.pth'),
            'config': {
                'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_layers': 7,
                'dropout': 0.18, 'seq_len': 5, 'use_eog': True, 'use_emg': False
            }
        }
    }
    
    # 创建集成系统
    ensemble = MAMBAFORMEREnsemble(model_configs)
    
    logging.info("🌟 MAMBAFORMER集成学习系统创建完成!")
    logging.info("🎯 目标: 突破85% Macro F1，冲击ICASSP 2026!")
    
    return ensemble


if __name__ == "__main__":
    # 测试集成系统
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 创建MAMBAFORMER集成学习系统")
    ensemble = create_ensemble_system()
    print("✅ 集成系统创建完成!")
    
    print("\n🎯 系统特性:")
    print("  • 5个模型版本融合 (V7→V12)")
    print("  • 4种集成方法 (投票/自适应/时序/学习)")
    print("  • HMM后处理时序平滑") 
    print("  • 动态权重学习")
    print("  • 目标: 85% Macro F1")
    
    print("\n🌟 为ICASSP 2026做好准备!")