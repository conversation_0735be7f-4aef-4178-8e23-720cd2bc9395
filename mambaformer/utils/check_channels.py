"""检查Sleep-EDF数据集的通道名称"""
import mne
import sys

if len(sys.argv) > 1:
    edf_file = sys.argv[1]
else:
    edf_file = '/media/main/ypf/eeg/data-edf/sleep_edf_20/SC4001E0-PSG.edf'

print(f"检查文件: {edf_file}")
raw = mne.io.read_raw_edf(edf_file, verbose=False)
print(f"\n可用通道 ({len(raw.ch_names)} 个):")
for i, ch in enumerate(raw.ch_names):
    print(f"{i+1}. {ch}")
    
# 查找EEG和EOG通道
eeg_channels = [ch for ch in raw.ch_names if 'EEG' in ch]
eog_channels = [ch for ch in raw.ch_names if 'EOG' in ch or 'eog' in ch]

print(f"\nEEG通道: {eeg_channels}")
print(f"EOG通道: {eog_channels}")