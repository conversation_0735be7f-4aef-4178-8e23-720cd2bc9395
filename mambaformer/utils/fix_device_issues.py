#!/usr/bin/env python3
"""
修复模型中的设备不匹配问题
"""

import sys
import re

def fix_mambaformer_net():
    """修复mambaformer_net.py中的设备问题"""
    file_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/models/mambaformer_net.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 修复setup_loss_functions方法
    old_setup = """    def setup_loss_functions(self):
        \"\"\"Setup loss functions for different stages\"\"\"
        # Class weights for focal loss (addressing class imbalance)
        # Typical sleep distribution: W(20%), N1(5%), N2(50%), N3(15%), REM(10%)
        class_weights = torch.tensor([0.8, 2.0, 0.6, 1.2, 1.5])  # Inverse frequency weighting
        
        self.focal_loss = FocalLoss(alpha=class_weights, gamma=2.0)
        self.ce_loss = nn.CrossEntropyLoss()
        
        # Register class_weights as buffer so it moves with the model
        self.register_buffer('class_weights_buffer', class_weights)"""
    
    new_setup = """    def setup_loss_functions(self):
        \"\"\"Setup loss functions for different stages\"\"\"
        # Class weights for focal loss (addressing class imbalance)
        # Typical sleep distribution: W(20%), N1(5%), N2(50%), N3(15%), REM(10%)
        class_weights = torch.tensor([0.8, 2.0, 0.6, 1.2, 1.5])  # Inverse frequency weighting
        
        # Register class_weights as buffer so it moves with the model
        self.register_buffer('class_weights_buffer', class_weights)
        
        # Use the registered buffer for FocalLoss (will have correct device)
        self.focal_loss = FocalLoss(alpha=self.class_weights_buffer, gamma=2.0)
        self.ce_loss = nn.CrossEntropyLoss()"""
    
    if old_setup in content:
        content = content.replace(old_setup, new_setup)
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"✅ 修复了 {file_path} 中的设备问题")
    else:
        print(f"⚠️  在 {file_path} 中未找到需要修复的内容")

def fix_progressive_classifier():
    """确保progressive_classifier.py中的设备处理正确"""
    file_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/models/progressive_classifier.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查是否已经有设备检查
    if "if self.alpha.device != targets.device:" in content:
        print(f"✅ {file_path} 中的FocalLoss设备检查已存在")
    else:
        print(f"⚠️  需要在 {file_path} 中添加FocalLoss设备检查")
    
    # 检查fine_to_coarse_mapping的设备处理
    if "if self.fine_to_coarse_mapping.device != labels.device:" in content:
        print(f"✅ {file_path} 中的fine_to_coarse_mapping设备检查已存在")
    else:
        print(f"⚠️  需要在 {file_path} 中添加fine_to_coarse_mapping设备检查")
    
    return True

def main():
    print("🔧 修复模型中的设备不匹配问题...")
    
    # 修复mambaformer_net.py
    fix_mambaformer_net()
    
    # 检查progressive_classifier.py
    fix_progressive_classifier()
    
    print("\n✅ 设备问题修复完成！")
    print("现在可以重新运行训练脚本了。")

if __name__ == "__main__":
    main()