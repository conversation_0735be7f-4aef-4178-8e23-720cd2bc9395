import h5py
import numpy as np

# 检查几个文件的内容
files_to_check = ['x01.h5', 'x02.h5', 'x03.h5', 'x04.h5', 'x05.h5', 'x10.h5', 'x20.h5']

print("检查数据文件内容：")
print("-" * 80)

for file in files_to_check:
    filepath = f'processed_data_fixed/{file}'
    try:
        with h5py.File(filepath, 'r') as f:
            # 获取数据集的键
            keys = list(f.keys())
            print(f"\n文件: {file}")
            print(f"数据集键: {keys}")
            
            # 获取数据形状和一些统计信息
            for key in keys:
                data = f[key][()]
                print(f"  {key} 形状: {data.shape}")
                print(f"  {key} 前5个样本的均值: {[np.mean(data[i]) for i in range(min(5, len(data)))]}")
                print(f"  {key} 数据范围: [{np.min(data):.4f}, {np.max(data):.4f}]")
                
    except Exception as e:
        print(f"读取 {file} 时出错: {e}")

# 检查标签文件
print("\n" + "="*80)
print("检查标签文件：")
y_files = ['y01.h5', 'y02.h5', 'y10.h5', 'y20.h5']

for file in y_files:
    filepath = f'processed_data_fixed/{file}'
    try:
        with h5py.File(filepath, 'r') as f:
            keys = list(f.keys())
            print(f"\n文件: {file}")
            print(f"数据集键: {keys}")
            
            for key in keys:
                data = f[key][()]
                print(f"  {key} 形状: {data.shape}")
                print(f"  {key} 唯一值: {np.unique(data)}")
                print(f"  {key} 前20个值: {data[:20]}")
                
    except Exception as e:
        print(f"读取 {file} 时出错: {e}")