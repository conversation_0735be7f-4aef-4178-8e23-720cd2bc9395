#!/usr/bin/env python3
"""
监控训练进度
"""

import os
import time
import re
from pathlib import Path

def monitor_latest_log():
    """监控最新的日志文件"""
    log_dir = './log'
    
    # 找到最新的日志文件
    log_files = list(Path(log_dir).glob('mambaformer_sleepEDF_fixed*.log'))
    if not log_files:
        print("未找到日志文件")
        return
    
    latest_log = max(log_files, key=os.path.getmtime)
    print(f"监控日志: {latest_log}")
    
    # 实时读取日志
    best_val_acc = 0
    pattern_epoch = re.compile(r'Epoch (\d+)/(\d+): Train Loss: ([\d.]+), Train Acc: ([\d.]+) \| Val Loss: ([\d.]+), Val Acc: ([\d.]+)')
    pattern_test = re.compile(r'测试准确率: ([\d.]+)')
    
    with open(latest_log, 'r') as f:
        # 读取已有内容
        lines = f.readlines()
        
        for line in lines:
            # 查找epoch信息
            match = pattern_epoch.search(line)
            if match:
                epoch, total_epochs, train_loss, train_acc, val_loss, val_acc = match.groups()
                val_acc = float(val_acc)
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                print(f"Epoch {epoch}/{total_epochs}: Train Acc={float(train_acc)*100:.1f}%, Val Acc={val_acc*100:.1f}% {'*' if val_acc == best_val_acc else ''}")
            
            # 查找测试结果
            match_test = pattern_test.search(line)
            if match_test:
                test_acc = float(match_test.group(1))
                print(f"\n✅ 最终测试准确率: {test_acc*100:.1f}%")
                
        # 查找分类报告
        if "分类报告:" in lines[-20:]:  # 检查最后20行
            print("\n分类报告:")
            in_report = False
            for line in lines[-20:]:
                if "分类报告:" in line:
                    in_report = True
                elif in_report and "accuracy" in line:
                    print(line.strip())
                    break
                elif in_report:
                    print(line.strip())

if __name__ == "__main__":
    monitor_latest_log()