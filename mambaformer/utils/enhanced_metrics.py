"""
增强的评估指标 - 参考原版CMT经验
包括Kappa系数、Macro-F1和详细的类别指标
"""

import numpy as np
from sklearn.metrics import cohen_kappa_score, f1_score, classification_report, confusion_matrix
import torch


def calculate_kappa(y_true, y_pred):
    """计算<PERSON>'s Kappa系数"""
    return cohen_kappa_score(y_true, y_pred)


def calculate_macro_f1(y_true, y_pred):
    """计算Macro-F1分数"""
    return f1_score(y_true, y_pred, average='macro')


def calculate_per_class_metrics(y_true, y_pred, n_classes=5):
    """计算每个类别的详细指标"""
    cm = confusion_matrix(y_true, y_pred, labels=list(range(n_classes)))
    
    per_class_metrics = {}
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    for i in range(n_classes):
        TP = cm[i, i]
        FP = cm[:, i].sum() - TP
        FN = cm[i, :].sum() - TP
        TN = cm.sum() - TP - FP - FN
        
        # Sensitivity (Recall)
        sensitivity = TP / (TP + FN) if (TP + FN) > 0 else 0
        
        # Specificity
        specificity = TN / (TN + FP) if (TN + FP) > 0 else 0
        
        # Precision
        precision = TP / (TP + FP) if (TP + FP) > 0 else 0
        
        # F1 Score
        f1 = 2 * TP / (2 * TP + FP + FN) if (2 * TP + FP + FN) > 0 else 0
        
        per_class_metrics[class_names[i]] = {
            'sensitivity': sensitivity,
            'specificity': specificity,
            'precision': precision,
            'f1': f1,
            'support': int(cm[i, :].sum())
        }
    
    return per_class_metrics, cm


def get_comprehensive_metrics(y_true, y_pred, n_classes=5):
    """获取全面的评估指标"""
    # 基础指标
    accuracy = (y_true == y_pred).mean()
    kappa = calculate_kappa(y_true, y_pred)
    macro_f1 = calculate_macro_f1(y_true, y_pred)
    
    # 每类指标
    per_class_metrics, cm = calculate_per_class_metrics(y_true, y_pred, n_classes)
    
    # 平均sensitivity和specificity (用于G-mean)
    avg_sensitivity = np.mean([m['sensitivity'] for m in per_class_metrics.values()])
    avg_specificity = np.mean([m['specificity'] for m in per_class_metrics.values()])
    g_mean = np.sqrt(avg_sensitivity * avg_specificity)
    
    return {
        'accuracy': accuracy,
        'kappa': kappa,
        'macro_f1': macro_f1,
        'g_mean': g_mean,
        'avg_sensitivity': avg_sensitivity,
        'avg_specificity': avg_specificity,
        'per_class_metrics': per_class_metrics,
        'confusion_matrix': cm
    }


def log_detailed_metrics(metrics, phase='Val', logger=None):
    """记录详细的评估指标"""
    if logger is None:
        return
    
    logger.info(f"\n{'='*60}")
    logger.info(f"{phase} Phase - Comprehensive Metrics:")
    logger.info(f"{'='*60}")
    logger.info(f"Overall Accuracy: {metrics['accuracy']:.4f}")
    logger.info(f"Cohen's Kappa (κ): {metrics['kappa']:.4f}")
    logger.info(f"Macro-F1 Score: {metrics['macro_f1']:.4f}")
    logger.info(f"G-Mean: {metrics['g_mean']:.4f}")
    logger.info(f"Avg Sensitivity: {metrics['avg_sensitivity']:.4f}")
    logger.info(f"Avg Specificity: {metrics['avg_specificity']:.4f}")
    
    # 每类详细指标
    logger.info(f"\n📊 Per-Class Metrics:")
    logger.info(f"{'Class':>5} | {'Sens':>6} | {'Spec':>6} | {'Prec':>6} | {'F1':>6} | {'Support':>7}")
    logger.info(f"{'-'*50}")
    
    for class_name, metrics_dict in metrics['per_class_metrics'].items():
        logger.info(f"{class_name:>5} | "
                   f"{metrics_dict['sensitivity']:>6.3f} | "
                   f"{metrics_dict['specificity']:>6.3f} | "
                   f"{metrics_dict['precision']:>6.3f} | "
                   f"{metrics_dict['f1']:>6.3f} | "
                   f"{metrics_dict['support']:>7}")
    
    # 混淆矩阵
    logger.info(f"\n🔄 Confusion Matrix:")
    cm = metrics['confusion_matrix']
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logger.info("     " + " ".join(f"{name:>6}" for name in class_names))
    for i, row in enumerate(cm):
        logger.info(f"{class_names[i]:>4} " + " ".join(f"{val:>6}" for val in row))


class AverageMeter:
    """计算和存储平均值和当前值 - 参考原版CMT"""
    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count if self.count != 0 else 0