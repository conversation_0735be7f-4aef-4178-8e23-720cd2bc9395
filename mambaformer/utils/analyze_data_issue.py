#!/usr/bin/env python3
"""
分析数据处理问题
"""

import numpy as np
import h5py
import matplotlib.pyplot as plt
import os

def load_h5_data(filepath):
    """加载h5文件"""
    with h5py.File(filepath, 'r') as f:
        return f['data'][:]

def analyze_data_distribution():
    """分析数据分布"""
    data_dir = './processed_data_small'
    
    print("🔍 分析数据分布...")
    
    # 加载第一个subject的数据
    eeg_data = load_h5_data(os.path.join(data_dir, 'x01.h5'))
    eog_data = load_h5_data(os.path.join(data_dir, 'eog01.h5'))
    labels = load_h5_data(os.path.join(data_dir, 'y01.h5'))
    
    # 加载均值和标准差
    mean_eeg = load_h5_data(os.path.join(data_dir, 'mean01.h5'))
    std_eeg = load_h5_data(os.path.join(data_dir, 'std01.h5'))
    mean_eog = load_h5_data(os.path.join(data_dir, 'eog_m01.h5'))
    std_eog = load_h5_data(os.path.join(data_dir, 'eog_s01.h5'))
    
    print(f"\n数据形状:")
    print(f"  EEG: {eeg_data.shape}")
    print(f"  EOG: {eog_data.shape}")
    print(f"  Labels: {labels.shape}")
    print(f"  Mean EEG: {mean_eeg.shape}")
    print(f"  Std EEG: {std_eeg.shape}")
    
    print(f"\n标签分布:")
    label_counts = np.bincount(labels.astype(int))
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    for i, count in enumerate(label_counts):
        if i < len(class_names):
            print(f"  {class_names[i]}: {count} ({count/len(labels)*100:.1f}%)")
    
    print(f"\n数据统计:")
    print(f"  EEG - 全局均值: {np.mean(eeg_data):.6f}, 全局标准差: {np.std(eeg_data):.6f}")
    print(f"  EEG - 最小值: {np.min(eeg_data):.6f}, 最大值: {np.max(eeg_data):.6f}")
    print(f"  EOG - 全局均值: {np.mean(eog_data):.6f}, 全局标准差: {np.std(eog_data):.6f}")
    print(f"  EOG - 最小值: {np.min(eog_data):.6f}, 最大值: {np.max(eog_data):.6f}")
    
    # 检查标准化方式
    print(f"\n检查标准化:")
    print(f"  Mean EEG shape: {mean_eeg.shape} (应该是 {eeg_data.shape[0]} x 1)")
    print(f"  Std EEG shape: {std_eeg.shape} (应该是 {eeg_data.shape[0]} x 1)")
    
    # 测试不同的标准化方式
    print(f"\n测试标准化方式:")
    
    # 方式1：每个epoch独立标准化（我们当前的方式）
    eeg_norm1 = (eeg_data - mean_eeg) / (std_eeg + 1e-8)
    print(f"  方式1 (per-epoch) - 均值: {np.mean(eeg_norm1):.6f}, 标准差: {np.std(eeg_norm1):.6f}")
    
    # 方式2：全局标准化
    global_mean = np.mean(eeg_data)
    global_std = np.std(eeg_data)
    eeg_norm2 = (eeg_data - global_mean) / (global_std + 1e-8)
    print(f"  方式2 (global) - 均值: {np.mean(eeg_norm2):.6f}, 标准差: {np.std(eeg_norm2):.6f}")
    
    # 方式3：通道级标准化（可能是原始代码的方式）
    # 假设原始数据可能有多个通道，我们需要检查
    print(f"\n原始数据分析:")
    sample_eeg = eeg_data[0]
    print(f"  单个epoch形状: {sample_eeg.shape}")
    print(f"  单个epoch均值: {np.mean(sample_eeg):.6f}")
    print(f"  单个epoch标准差: {np.std(sample_eeg):.6f}")
    
    # 可视化几个样本
    plt.figure(figsize=(15, 8))
    
    # 绘制不同类别的样本
    for i, class_name in enumerate(class_names):
        class_indices = np.where(labels == i)[0]
        if len(class_indices) > 0:
            plt.subplot(2, 3, i+1)
            sample_idx = class_indices[0]
            plt.plot(eeg_data[sample_idx][:500], alpha=0.7, label='EEG')
            plt.plot(eog_data[sample_idx][:500], alpha=0.7, label='EOG')
            plt.title(f'{class_name} (Label={i})')
            plt.xlabel('Time points')
            plt.ylabel('Amplitude')
            plt.legend()
            plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('data_samples.png')
    print(f"\n📊 样本可视化保存到: data_samples.png")
    
    # 检查是否有异常值
    print(f"\n异常值检查:")
    eeg_outliers = np.sum(np.abs(eeg_data) > 5 * global_std)
    eog_outliers = np.sum(np.abs(eog_data) > 5 * np.std(eog_data))
    print(f"  EEG异常值 (>5σ): {eeg_outliers} ({eeg_outliers/eeg_data.size*100:.2f}%)")
    print(f"  EOG异常值 (>5σ): {eog_outliers} ({eog_outliers/eog_data.size*100:.2f}%)")

def check_original_data_format():
    """检查原始数据格式要求"""
    print("\n📋 原始代码数据格式要求:")
    print("  1. 数据应该已经过滤波和重采样到100Hz")
    print("  2. 每个epoch是30秒 (3000个数据点)")
    print("  3. 标签: 0=Wake, 1=N1, 2=N2, 3=N3/N4, 4=REM")
    print("  4. 需要subject-wise的均值和标准差进行标准化")
    print("  5. 原始代码使用h5py格式存储")

if __name__ == "__main__":
    analyze_data_distribution()
    check_original_data_format()