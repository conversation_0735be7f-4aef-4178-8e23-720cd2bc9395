#!/usr/bin/env python3
"""
详细分析Sleep-EDF-20数据处理过程，检查是否存在数据泄露
"""

import os
from pathlib import Path
import json

def analyze_subject_mapping():
    """分析受试者映射关系"""
    
    # 原始数据目录
    raw_data_dir = Path('../data-edf/sleep_edf_20')
    processed_data_dir = Path('./real_sleepedf_processed')
    
    # 获取所有PSG文件
    psg_files = sorted(raw_data_dir.glob('*PSG.edf'))
    
    print("Sleep-EDF-20 数据集结构分析")
    print("=" * 80)
    
    # 解析文件名，理解命名规则
    print("\n1. 原始文件命名规则：")
    print("   SC4XXYY-PSG.edf")
    print("   其中：XX = 受试者编号 (00-19)")
    print("         YY = 记录编号 (E0=第一晚, EC=第二晚)")
    
    # 收集所有受试者信息
    subject_recordings = {}
    
    for psg_file in psg_files:
        filename = psg_file.name
        # SC4001E0-PSG.edf
        subject_code = filename[2:6]  # 4001
        subject_num = int(subject_code[:2])  # 40 -> 0 (受试者00)
        night_code = filename[6:8]  # E0 或 EC
        
        if subject_num not in subject_recordings:
            subject_recordings[subject_num] = []
        
        subject_recordings[subject_num].append({
            'file': filename,
            'code': subject_code,
            'night': 'Night1' if night_code == 'E0' else 'Night2'
        })
    
    print(f"\n2. 发现的受试者和记录：")
    for subj_num in sorted(subject_recordings.keys()):
        recordings = subject_recordings[subj_num]
        print(f"\n   受试者 {subj_num:02d}:")
        for rec in recordings:
            print(f"      {rec['file']} ({rec['night']})")
    
    # 分析预处理时的映射
    print("\n" + "=" * 80)
    print("3. 预处理时的文件映射：")
    
    # 检查处理后的文件
    processed_files = sorted(processed_data_dir.glob('subj*_eeg.h5'))
    
    print(f"\n   处理后的文件数量: {len(processed_files)}")
    print(f"   原始PSG文件数量: {len(psg_files)}")
    
    # 问题分析
    print("\n4. 潜在的数据泄露问题：")
    print("\n   ⚠️  问题1: 同一受试者的两晚数据可能被当作不同受试者")
    print("      - SC4001E0 和 SC4002E0 是同一个人(受试者00)的两晚")
    print("      - 预处理脚本将它们作为 subj01 和 subj02")
    
    print("\n   ⚠️  问题2: 数据划分没有考虑受试者级别")
    print("      - 同一受试者的不同晚可能分布在训练集和测试集")
    print("      - 这会导致严重的数据泄露！")
    
    # 加载数据划分
    split_file = processed_data_dir / 'data_split.json'
    if split_file.exists():
        with open(split_file, 'r') as f:
            split_info = json.load(f)
        
        print("\n5. 当前数据划分：")
        print(f"   训练集: subj{split_info['train'][0]:02d} - subj{split_info['train'][-1]:02d}")
        print(f"   验证集: subj{split_info['val'][0]:02d} - subj{split_info['val'][-1]:02d}")
        print(f"   测试集: subj{split_info['test'][0]:02d} - subj{split_info['test'][-1]:02d}")
        
        # 检查具体的泄露情况
        print("\n6. 数据泄露分析：")
        
        # 建立处理后的索引到原始受试者的映射
        processed_to_original = {}
        for idx, psg_file in enumerate(psg_files, 1):
            filename = psg_file.name
            subject_code = filename[2:6]
            subject_num = int(subject_code[:2])  # 真实受试者编号
            processed_to_original[idx] = subject_num
        
        # 检查训练集和测试集是否包含同一受试者
        train_subjects = set()
        val_subjects = set()
        test_subjects = set()
        
        for idx in split_info['train']:
            if idx in processed_to_original:
                train_subjects.add(processed_to_original[idx])
        
        for idx in split_info['val']:
            if idx in processed_to_original:
                val_subjects.add(processed_to_original[idx])
                
        for idx in split_info['test']:
            if idx in processed_to_original:
                test_subjects.add(processed_to_original[idx])
        
        # 检查重叠
        train_val_overlap = train_subjects & val_subjects
        train_test_overlap = train_subjects & test_subjects
        val_test_overlap = val_subjects & test_subjects
        
        if train_test_overlap:
            print(f"\n   ❌ 严重问题：以下受试者同时出现在训练集和测试集：")
            for subj in train_test_overlap:
                print(f"      受试者 {subj:02d}")
        
        if train_val_overlap:
            print(f"\n   ⚠️  问题：以下受试者同时出现在训练集和验证集：")
            for subj in train_val_overlap:
                print(f"      受试者 {subj:02d}")
        
        if val_test_overlap:
            print(f"\n   ⚠️  问题：以下受试者同时出现在验证集和测试集：")
            for subj in val_test_overlap:
                print(f"      受试者 {subj:02d}")
        
        if not (train_test_overlap or train_val_overlap or val_test_overlap):
            print("\n   ✅ 好消息：不同数据集之间没有受试者重叠")
            print("   ⚠️  但是：这可能只是因为巧合，而不是设计使然")
    
    # 建议
    print("\n" + "=" * 80)
    print("7. 正确的处理方法：")
    print("\n   1) 识别所有受试者（00-19共20个人）")
    print("   2) 将每个受试者的所有记录（1-2晚）合并")
    print("   3) 在受试者级别进行数据划分")
    print("   4) 确保同一受试者的所有数据都在同一个集合中")
    
    print("\n8. 为什么93.87%的准确率可疑：")
    print("   - 如果同一受试者的不同晚分布在训练和测试集")
    print("   - 模型可能学到了受试者特定的特征")
    print("   - 这不是真正的跨受试者泛化")
    print("   - 真实的跨受试者准确率应该会低一些")

if __name__ == "__main__":
    analyze_subject_mapping()