#!/usr/bin/env python3
"""
验证真实Sleep-EDF数据的独特性
确保没有数据重复
"""

import h5py
import numpy as np
from pathlib import Path
import hashlib

def compute_data_hash(data):
    """计算数据的哈希值"""
    return hashlib.md5(data.tobytes()).hexdigest()

def verify_uniqueness(data_dir):
    """验证所有受试者数据的独特性"""
    data_dir = Path(data_dir)
    
    # 收集所有EEG数据文件
    eeg_files = sorted(data_dir.glob('subj*_eeg.h5'))
    
    print(f"找到 {len(eeg_files)} 个受试者的数据文件")
    
    # 存储每个文件的特征
    file_info = {}
    data_hashes = {}
    
    for eeg_file in eeg_files:
        subj_id = eeg_file.stem.split('_')[0]
        
        with h5py.File(eeg_file, 'r') as f:
            data = f['data'][:]
            
            # 计算基本统计信息
            info = {
                'shape': data.shape,
                'mean': float(np.mean(data)),
                'std': float(np.std(data)),
                'min': float(np.min(data)),
                'max': float(np.max(data)),
                'first_epoch_hash': compute_data_hash(data[0]),
                'file_size': eeg_file.stat().st_size
            }
            
            file_info[subj_id] = info
            
            # 计算前10个epoch的哈希值（避免处理整个大文件）
            n_epochs_to_check = min(10, len(data))
            for i in range(n_epochs_to_check):
                epoch_hash = compute_data_hash(data[i])
                if epoch_hash not in data_hashes:
                    data_hashes[epoch_hash] = []
                data_hashes[epoch_hash].append((subj_id, i))
    
    # 检查重复
    print("\n检查数据重复...")
    duplicates_found = False
    
    # 检查完全相同的文件（通过统计信息）
    for subj1 in file_info:
        for subj2 in file_info:
            if subj1 < subj2:  # 避免重复比较
                info1 = file_info[subj1]
                info2 = file_info[subj2]
                
                # 如果形状、均值、标准差都相同，可能是重复
                if (info1['shape'] == info2['shape'] and 
                    abs(info1['mean'] - info2['mean']) < 1e-6 and
                    abs(info1['std'] - info2['std']) < 1e-6 and
                    info1['file_size'] == info2['file_size']):
                    
                    print(f"⚠️  可能的重复: {subj1} 和 {subj2}")
                    print(f"   形状: {info1['shape']}")
                    print(f"   文件大小: {info1['file_size']}")
                    duplicates_found = True
    
    # 检查相同的epoch
    print("\n检查epoch级别的重复...")
    for epoch_hash, locations in data_hashes.items():
        if len(locations) > 1:
            print(f"⚠️  发现相同的epoch数据:")
            for subj, epoch_idx in locations:
                print(f"   {subj} 的第 {epoch_idx} 个epoch")
            duplicates_found = True
    
    if not duplicates_found:
        print("✅ 未发现数据重复，所有受试者的数据都是独特的")
    
    # 显示数据统计
    print("\n数据统计汇总:")
    print(f"{'受试者':<10} {'形状':<20} {'均值':<10} {'标准差':<10} {'文件大小(MB)':<15}")
    print("-" * 75)
    
    for subj_id in sorted(file_info.keys()):
        info = file_info[subj_id]
        print(f"{subj_id:<10} {str(info['shape']):<20} "
              f"{info['mean']:<10.4f} {info['std']:<10.4f} "
              f"{info['file_size']/1024/1024:<15.2f}")
    
    return not duplicates_found

if __name__ == "__main__":
    data_dir = './real_sleepedf_processed'
    print(f"验证数据目录: {data_dir}")
    print("=" * 75)
    
    is_unique = verify_uniqueness(data_dir)
    
    if is_unique:
        print("\n✅ 数据验证通过！所有受试者的数据都是真实且独特的。")
    else:
        print("\n❌ 发现数据问题！请检查数据预处理过程。")