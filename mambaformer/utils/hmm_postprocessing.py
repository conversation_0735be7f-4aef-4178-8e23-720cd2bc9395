"""
HMM后处理模块
用于睡眠阶段预测的时序平滑
"""

import numpy as np
import logging

try:
    from hmmlearn import hmm
    HMM_AVAILABLE = True
except ImportError:
    HMM_AVAILABLE = False
    logging.warning("hmmlearn not installed. HMM post-processing will be disabled.")


class HMMPostProcessor:
    """
    使用隐马尔可夫模型进行睡眠阶段预测的后处理
    考虑睡眠阶段转换的时序约束
    """
    
    def __init__(self, n_states=5):
        self.n_states = n_states
        self.model = None
        if HMM_AVAILABLE:
            self._init_model()
        else:
            logging.warning("HMM not available. Using fallback smoothing.")
        
    def _init_model(self):
        """初始化HMM模型"""
        self.model = hmm.MultinomialHMM(n_components=self.n_states)
        
        # 初始概率 - 基于睡眠开始通常是清醒状态
        self.model.startprob_ = np.array([0.8, 0.15, 0.04, 0.005, 0.005])  # Wake, N1, N2, N3, REM
        
        # 转移概率矩阵 - 基于睡眠阶段转换的生理规律
        # 行：当前状态，列：下一个状态
        self.model.transmat_ = np.array([
            # Wake -> [Wake, N1, N2, N3, REM]
            [0.92, 0.07, 0.008, 0.001, 0.001],
            # N1 -> [Wake, N1, N2, N3, REM]
            [0.15, 0.45, 0.35, 0.03, 0.02],
            # N2 -> [Wake, N1, N2, N3, REM]
            [0.02, 0.08, 0.75, 0.10, 0.05],
            # N3 -> [Wake, N1, N2, N3, REM]
            [0.001, 0.009, 0.15, 0.80, 0.04],
            # REM -> [Wake, N1, N2, N3, REM]
            [0.10, 0.05, 0.15, 0.01, 0.69]
        ])
        
        # 确保转移概率和为1
        self.model.transmat_ = self.model.transmat_ / self.model.transmat_.sum(axis=1, keepdims=True)
        
        # 发射概率将从数据中学习
        
    def fit_emission_probabilities(self, predictions, true_labels):
        """
        根据模型预测和真实标签学习发射概率
        
        Args:
            predictions: 模型预测的概率 [N, n_states]
            true_labels: 真实标签 [N]
        """
        # 计算混淆矩阵作为发射概率的估计
        confusion_matrix = np.zeros((self.n_states, self.n_states))
        
        pred_labels = np.argmax(predictions, axis=1)
        for true, pred in zip(true_labels, pred_labels):
            confusion_matrix[true, pred] += 1
        
        # 归一化得到发射概率
        emission_prob = confusion_matrix / confusion_matrix.sum(axis=1, keepdims=True)
        emission_prob = np.clip(emission_prob, 1e-6, 1.0)  # 避免零概率
        
        self.model.emissionprob_ = emission_prob
        
        logging.info("HMM发射概率矩阵已更新")
        
    def smooth_predictions(self, predictions):
        """
        使用HMM对预测序列进行平滑
        
        Args:
            predictions: 模型预测的概率 [N, n_states]
            
        Returns:
            smoothed_labels: 平滑后的标签 [N]
        """
        pred_labels = np.argmax(predictions, axis=1)
        
        if not HMM_AVAILABLE or self.model is None:
            # 使用简单的中值滤波作为后备
            from scipy.signal import medfilt
            smoothed_labels = medfilt(pred_labels, kernel_size=5)
            return smoothed_labels.astype(int)
        
        if self.model.emissionprob_ is None:
            # 如果没有学习发射概率，使用默认的
            self.model.emissionprob_ = np.eye(self.n_states) * 0.8 + 0.2 / self.n_states
        
        try:
            # 使用Viterbi算法找到最可能的状态序列
            smoothed_labels = self.model.predict(pred_labels.reshape(-1, 1))
        except:
            # 如果HMM失败，返回原始预测
            logging.warning("HMM平滑失败，返回原始预测")
            smoothed_labels = pred_labels
        
        return smoothed_labels
    
    def smooth_with_confidence(self, predictions, confidence_threshold=0.7):
        """
        基于置信度的选择性平滑
        只对低置信度的预测进行平滑
        
        Args:
            predictions: 模型预测的概率 [N, n_states]
            confidence_threshold: 置信度阈值
            
        Returns:
            smoothed_labels: 平滑后的标签 [N]
        """
        pred_labels = np.argmax(predictions, axis=1)
        max_probs = np.max(predictions, axis=1)
        
        # HMM平滑
        smoothed_labels = self.smooth_predictions(predictions)
        
        # 对高置信度的预测保持原样
        high_confidence_mask = max_probs > confidence_threshold
        smoothed_labels[high_confidence_mask] = pred_labels[high_confidence_mask]
        
        return smoothed_labels
    
    def adaptive_smooth(self, predictions, window_size=11):
        """
        自适应窗口平滑
        根据局部一致性调整平滑强度
        
        Args:
            predictions: 模型预测的概率 [N, n_states]
            window_size: 滑动窗口大小
            
        Returns:
            smoothed_labels: 平滑后的标签 [N]
        """
        pred_labels = np.argmax(predictions, axis=1)
        smoothed_labels = pred_labels.copy()
        
        half_window = window_size // 2
        
        for i in range(half_window, len(pred_labels) - half_window):
            window = pred_labels[i-half_window:i+half_window+1]
            
            # 计算窗口内的一致性
            unique, counts = np.unique(window, return_counts=True)
            consistency = np.max(counts) / len(window)
            
            # 如果一致性低，应用HMM平滑
            if consistency < 0.6:
                # 获取窗口内的预测
                window_preds = predictions[i-half_window:i+half_window+1]
                
                # HMM平滑
                smoothed_window = self.smooth_predictions(window_preds)
                
                # 更新中心点
                smoothed_labels[i] = smoothed_window[half_window]
        
        return smoothed_labels
    
    def ensemble_smooth(self, predictions_list, weights=None):
        """
        集成多个模型的预测并进行HMM平滑
        
        Args:
            predictions_list: 多个模型的预测列表 [M, N, n_states]
            weights: 各模型权重 [M]
            
        Returns:
            smoothed_labels: 平滑后的标签 [N]
        """
        if weights is None:
            weights = np.ones(len(predictions_list)) / len(predictions_list)
        
        # 加权平均
        ensemble_preds = np.zeros_like(predictions_list[0])
        for pred, weight in zip(predictions_list, weights):
            ensemble_preds += pred * weight
        
        # HMM平滑
        smoothed_labels = self.smooth_predictions(ensemble_preds)
        
        return smoothed_labels


class RuleBasedPostProcessor:
    """
    基于规则的后处理器
    实施睡眠生理学约束
    """
    
    def __init__(self):
        # 最小持续时间（以30秒epoch为单位）
        self.min_durations = {
            0: 1,   # Wake - 至少30秒
            1: 2,   # N1 - 至少1分钟
            2: 4,   # N2 - 至少2分钟
            3: 4,   # N3 - 至少2分钟
            4: 3    # REM - 至少1.5分钟
        }
        
        # 不可能的转换
        self.forbidden_transitions = [
            (0, 3),  # Wake -> N3 (必须经过N1/N2)
            (0, 4),  # Wake -> REM (通常经过NREM)
            (3, 4),  # N3 -> REM (通常经过N2)
            (4, 3),  # REM -> N3 (通常经过N2)
        ]
        
    def apply_duration_constraint(self, labels):
        """应用最小持续时间约束"""
        smoothed = labels.copy()
        
        i = 0
        while i < len(labels):
            current_stage = labels[i]
            
            # 找到当前阶段的结束位置
            j = i
            while j < len(labels) and labels[j] == current_stage:
                j += 1
            
            duration = j - i
            min_duration = self.min_durations.get(current_stage, 1)
            
            # 如果持续时间太短，尝试合并到相邻阶段
            if duration < min_duration:
                # 查看前后的阶段
                prev_stage = labels[i-1] if i > 0 else -1
                next_stage = labels[j] if j < len(labels) else -1
                
                # 优先合并到持续时间更长的相邻阶段
                if prev_stage != -1 and next_stage != -1:
                    # 检查前后阶段的持续时间
                    prev_duration = 0
                    k = i - 1
                    while k >= 0 and labels[k] == prev_stage:
                        prev_duration += 1
                        k -= 1
                    
                    next_duration = 0
                    k = j
                    while k < len(labels) and labels[k] == next_stage:
                        next_duration += 1
                        k += 1
                    
                    # 合并到更长的阶段
                    if prev_duration >= next_duration:
                        smoothed[i:j] = prev_stage
                    else:
                        smoothed[i:j] = next_stage
                elif prev_stage != -1:
                    smoothed[i:j] = prev_stage
                elif next_stage != -1:
                    smoothed[i:j] = next_stage
            
            i = j
        
        return smoothed
    
    def apply_transition_constraint(self, labels):
        """应用转换约束"""
        smoothed = labels.copy()
        
        for i in range(1, len(labels)):
            prev_stage = smoothed[i-1]
            curr_stage = labels[i]
            
            # 检查是否是禁止的转换
            if (prev_stage, curr_stage) in self.forbidden_transitions:
                # 插入中间阶段
                if (prev_stage, curr_stage) == (0, 3):  # Wake -> N3
                    smoothed[i] = 2  # 插入N2
                elif (prev_stage, curr_stage) == (0, 4):  # Wake -> REM
                    smoothed[i] = 1  # 插入N1
                elif (prev_stage, curr_stage) == (3, 4):  # N3 -> REM
                    smoothed[i] = 2  # 插入N2
                elif (prev_stage, curr_stage) == (4, 3):  # REM -> N3
                    smoothed[i] = 2  # 插入N2
        
        return smoothed
    
    def smooth(self, labels):
        """应用所有规则"""
        # 先应用转换约束
        smoothed = self.apply_transition_constraint(labels)
        
        # 再应用持续时间约束
        smoothed = self.apply_duration_constraint(smoothed)
        
        return smoothed


def test_hmm_postprocessor():
    """测试HMM后处理器"""
    # 创建测试数据
    np.random.seed(42)
    n_samples = 1000
    
    # 模拟预测概率
    predictions = np.random.dirichlet(np.ones(5), size=n_samples)
    
    # 添加一些噪声
    pred_labels = np.argmax(predictions, axis=1)
    
    # 创建HMM处理器
    processor = HMMPostProcessor()
    
    # 平滑预测
    smoothed = processor.smooth_predictions(predictions)
    
    # 计算改变的数量
    changes = np.sum(pred_labels != smoothed)
    print(f"HMM平滑改变了 {changes}/{n_samples} 个预测 ({changes/n_samples*100:.1f}%)")
    
    # 测试规则处理器
    rule_processor = RuleBasedPostProcessor()
    rule_smoothed = rule_processor.smooth(pred_labels)
    
    rule_changes = np.sum(pred_labels != rule_smoothed)
    print(f"规则平滑改变了 {rule_changes}/{n_samples} 个预测 ({rule_changes/n_samples*100:.1f}%)")


if __name__ == "__main__":
    test_hmm_postprocessor()