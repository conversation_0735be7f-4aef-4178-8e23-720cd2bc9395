#!/usr/bin/env python3
"""
修复数据预处理问题
"""

import os
import numpy as np
import mne
import h5py
from glob import glob
import logging
from pathlib import Path

# 设置MNE日志级别
mne.set_log_level('WARNING')

def setup_logger():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    return logging.getLogger(__name__)

def process_subject_fixed(psg_file, hypnogram_file, output_dir, subject_id=1):
    """
    修复后的数据处理函数
    """
    logger = setup_logger()
    
    try:
        logger.info(f"处理Subject {subject_id}")
        
        # 读取PSG数据
        raw = mne.io.read_raw_edf(psg_file, preload=True)
        logger.info(f"采样率: {raw.info['sfreq']} Hz")
        
        # 获取通道信息
        logger.info(f"所有通道: {raw.ch_names}")
        
        # 选择EEG和EOG通道
        eeg_channels = [ch for ch in raw.ch_names if 'EEG' in ch]
        eog_channels = [ch for ch in raw.ch_names if 'EOG' in ch]
        
        if not eeg_channels:
            # 尝试其他常见的EEG通道名
            eeg_channels = [ch for ch in raw.ch_names if any(x in ch for x in ['Fpz-Cz', 'Pz-Oz', 'C3', 'C4'])]
        
        if not eog_channels:
            # 尝试其他常见的EOG通道名
            eog_channels = [ch for ch in raw.ch_names if any(x in ch for x in ['E1', 'E2', 'EOG horizontal'])]
        
        logger.info(f"EEG通道: {eeg_channels}")
        logger.info(f"EOG通道: {eog_channels}")
        
        # 检查数据单位和范围
        if eeg_channels:
            eeg_data_sample = raw.get_data(picks=eeg_channels[0])
            logger.info(f"EEG数据范围: min={np.min(eeg_data_sample):.6f}, max={np.max(eeg_data_sample):.6f}")
            logger.info(f"EEG单位: {raw.info['chs'][raw.ch_names.index(eeg_channels[0])]['unit']}")
            
            # 如果数据在伏特范围，转换为微伏
            if np.max(np.abs(eeg_data_sample)) < 1:
                logger.info("检测到数据可能是伏特单位，转换为微伏...")
                raw.apply_function(lambda x: x * 1e6, picks=eeg_channels + eog_channels)
        
        # 重采样到100Hz（如果需要）
        if raw.info['sfreq'] != 100:
            logger.info(f"重采样从 {raw.info['sfreq']}Hz 到 100Hz...")
            raw.resample(100)
        
        # 滤波（根据睡眠分期的标准）
        logger.info("应用滤波: 0.3-35Hz...")
        raw.filter(0.3, 35, fir_design='firwin')
        
        # 读取睡眠分期标注
        annotations = mne.read_annotations(hypnogram_file)
        raw.set_annotations(annotations)
        
        # 标签映射 - 确保与原始代码一致
        event_mapping = {
            'Sleep stage W': 0,
            'Sleep stage 1': 1,
            'Sleep stage 2': 2,
            'Sleep stage 3': 3,
            'Sleep stage 4': 3,  # S4合并到S3
            'Sleep stage R': 4,
            'Sleep stage ?': -1  # 未知阶段，稍后过滤
        }
        
        # 创建epochs (30秒窗口)
        events, event_id = mne.events_from_annotations(raw, event_id=event_mapping)
        
        # 过滤掉未知阶段
        valid_events = events[events[:, 2] >= 0]
        
        # 创建epochs
        epochs = mne.Epochs(
            raw, 
            valid_events, 
            event_id={k: v for k, v in event_mapping.items() if v >= 0},
            tmin=0, 
            tmax=30 - 1/raw.info['sfreq'],
            baseline=None,
            preload=True
        )
        
        logger.info(f"创建了 {len(epochs)} 个epochs")
        
        # 提取数据
        eeg_data = []
        eog_data = []
        labels = []
        
        # 获取每个epoch的数据
        for idx in range(len(epochs)):
            # 获取标签
            label = valid_events[idx, 2]
            
            # 获取EEG数据（如果有多个通道，取平均）
            if len(eeg_channels) > 0:
                eeg_picks = mne.pick_channels(epochs.ch_names, eeg_channels)
                eeg_epoch = epochs.get_data()[idx][eeg_picks, :]
                if len(eeg_picks) > 1:
                    eeg_epoch = np.mean(eeg_epoch, axis=0)
                else:
                    eeg_epoch = eeg_epoch[0]
                    
                # 获取EOG数据
                if len(eog_channels) > 0:
                    eog_picks = mne.pick_channels(epochs.ch_names, eog_channels)
                    eog_epoch = epochs.get_data()[idx][eog_picks, :]
                    if len(eog_picks) > 1:
                        eog_epoch = np.mean(eog_epoch, axis=0)
                    else:
                        eog_epoch = eog_epoch[0]
                else:
                    # 如果没有EOG，使用零填充
                    eog_epoch = np.zeros_like(eeg_epoch)
                
                eeg_data.append(eeg_epoch)
                eog_data.append(eog_epoch)
                labels.append(label)
        
        if len(eeg_data) == 0:
            logger.error("没有提取到有效数据")
            return None
        
        # 转换为numpy数组
        eeg_data = np.array(eeg_data)
        eog_data = np.array(eog_data)
        labels = np.array(labels)
        
        logger.info(f"数据形状 - EEG: {eeg_data.shape}, EOG: {eog_data.shape}")
        logger.info(f"数据范围 - EEG: [{np.min(eeg_data):.2f}, {np.max(eeg_data):.2f}] μV")
        logger.info(f"标签分布: {np.bincount(labels)}")
        
        # 计算全局统计（用于标准化）
        # 注意：原始代码可能使用的是全局统计而不是per-epoch统计
        mean_eeg = np.mean(eeg_data)
        std_eeg = np.std(eeg_data)
        mean_eog = np.mean(eog_data)
        std_eog = np.std(eog_data)
        
        # 为兼容性，也保存per-epoch的统计
        mean_eeg_per_epoch = np.mean(eeg_data, axis=1, keepdims=True)
        std_eeg_per_epoch = np.std(eeg_data, axis=1, keepdims=True)
        mean_eog_per_epoch = np.mean(eog_data, axis=1, keepdims=True)
        std_eog_per_epoch = np.std(eog_data, axis=1, keepdims=True)
        
        # 标准化数据（使用全局统计）
        eeg_data_norm = (eeg_data - mean_eeg) / (std_eeg + 1e-8)
        eog_data_norm = (eog_data - mean_eog) / (std_eog + 1e-8)
        
        logger.info(f"标准化后范围 - EEG: [{np.min(eeg_data_norm):.2f}, {np.max(eeg_data_norm):.2f}]")
        
        # 保存数据
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存原始数据（未标准化）
        h5_files = {
            f'x{subject_id:02d}.h5': eeg_data,
            f'eog{subject_id:02d}.h5': eog_data,
            f'y{subject_id:02d}.h5': labels,
            # 保存全局统计
            f'mean{subject_id:02d}.h5': np.ones((len(eeg_data), 1)) * mean_eeg,
            f'std{subject_id:02d}.h5': np.ones((len(eeg_data), 1)) * std_eeg,
            f'eog_m{subject_id:02d}.h5': np.ones((len(eog_data), 1)) * mean_eog,
            f'eog_s{subject_id:02d}.h5': np.ones((len(eog_data), 1)) * std_eog
        }
        
        for filename, data in h5_files.items():
            filepath = os.path.join(output_dir, filename)
            with h5py.File(filepath, 'w') as f:
                f.create_dataset('data', data=data)
            logger.info(f"保存: {filepath}")
        
        return True
        
    except Exception as e:
        logger.error(f"处理出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """重新处理数据"""
    import argparse
    parser = argparse.ArgumentParser('修复数据预处理')
    parser.add_argument('--data_dir', type=str, 
                        default='/media/main/ypf/eeg/data-edf/sleep_edf_20',
                        help='原始数据目录')
    parser.add_argument('--output_dir', type=str, 
                        default='./processed_data_fixed',
                        help='输出目录')
    parser.add_argument('--test_subjects', type=int, 
                        default=5,
                        help='测试用的subject数量')
    
    args = parser.parse_args()
    
    logger = setup_logger()
    logger.info("🔧 修复数据预处理...")
    
    # 查找数据文件
    psg_files = sorted(glob(os.path.join(args.data_dir, '*PSG.edf')))
    hyp_files = sorted(glob(os.path.join(args.data_dir, '*Hypnogram.edf')))
    
    logger.info(f"找到 {len(psg_files)} 个PSG文件")
    
    # 处理前N个subjects
    success_count = 0
    for i, (psg, hyp) in enumerate(zip(psg_files[:args.test_subjects], 
                                        hyp_files[:args.test_subjects])):
        logger.info(f"\n处理第 {i+1}/{args.test_subjects} 个subject...")
        if process_subject_fixed(psg, hyp, args.output_dir, subject_id=i+1):
            success_count += 1
    
    logger.info(f"\n✅ 处理完成! 成功: {success_count}/{args.test_subjects}")
    
if __name__ == "__main__":
    main()