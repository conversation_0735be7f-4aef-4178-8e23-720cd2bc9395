# 序列MAMBAFORMER实验总结

## 关键发现：数据泄露问题

### 问题描述
V1-V4版本存在严重的数据泄露：
- 使用`random_split`从训练集中随机划分验证集
- 同一被试的数据可能同时出现在训练集和验证集中
- 导致验证指标虚高，无法反映真实泛化性能

### 解决方案
V5版本实施了正确的被试级别划分：
- 训练集：14个被试（70%）
- 验证集：2个被试（10%）
- 测试集：4个被试（20%）
- 同一被试的所有数据必须在同一个集合中

## 实验结果对比

### 数据泄露版本（V1-V4）

| 版本 | 架构特点 | 测试准确率 | 测试F1 | Kappa | Macro-F1 | REM F1 | 验证-测试差距 |
|-----|---------|-----------|--------|-------|----------|--------|--------------|
| V1 | 基线 | 91.8% | 57.7% | - | - | 0% | 23% |
| V2 | 温和正则化 | 73.4% | 56.8% | 0.630 | 0.568 | 0% | 10.8% |
| V3 | 4通道多模态 | 72.2% | 55.9% | - | - | 0% | 10.5% |
| V4 | 加权损失 | 11.6% | 7.8% | -0.048 | 0.078 | 20.7% | 0.15% |

### 正确划分版本（V5）

| 版本 | 架构特点 | 验证准确率 | 验证F1 | Kappa | Macro-F1 | 状态 |
|-----|---------|-----------|--------|-------|----------|------|
| V5 | 基于V2+正确划分 | 58.4% | 53.8% | 0.420 | 0.538 | 训练中 |

## 关键指标说明

### Cohen's Kappa系数
- 衡量分类器性能优于随机猜测的程度
- 考虑了偶然一致性的影响
- 解释：
  - κ < 0: 比随机还差
  - 0 < κ < 0.2: 轻微一致
  - 0.2 < κ < 0.4: 一般一致
  - 0.4 < κ < 0.6: 中等一致
  - 0.6 < κ < 0.8: 较强一致
  - κ > 0.8: 几乎完全一致

### Macro-F1
- 每个类别F1分数的算术平均
- 对所有类别同等重视，不受类别不平衡影响
- 适合评估睡眠分期这种类别不平衡的任务

## 经验教训

1. **数据划分的重要性**
   - 必须按被试级别划分，避免数据泄露
   - 使用固定的划分确保实验可比性
   - 验证集用于调参，测试集仅用于最终评估

2. **指标展示**
   - Kappa和Macro-F1是重要的补充指标
   - 特别是对于类别不平衡的任务
   - 成功实现了用户要求的指标展示

3. **架构选择**
   - V2的温和正则化策略表现最平衡
   - 过度的加权损失（V4）会破坏整体性能
   - REM检测失败需要更精细的解决方案

4. **真实性能**
   - V5的初步结果（Val F1≈54%, Kappa≈0.42）更接近真实性能
   - 之前的高指标（如V1的91.8%准确率）是数据泄露的结果
   - 正确的评估方法是改进模型的基础

## 下一步建议

1. **完成V5训练**并获得真实的测试集性能
2. **基于V5结果**调整超参数和架构
3. **探索REM检测**的其他解决方案：
   - 温和的类别权重（如[1.0, 1.3, 1.0, 1.3, 1.5]）
   - Focal loss参数调整
   - 专门的REM特征提取
4. **参考原版CMT**的更多技巧：
   - 时序特征增强
   - 多尺度特征融合
   - 后处理平滑

## 结论

修复数据泄露后，模型的真实性能显著下降，但这是必要的修正。V5提供了可靠的基准，后续的改进都应基于这个正确的评估框架。