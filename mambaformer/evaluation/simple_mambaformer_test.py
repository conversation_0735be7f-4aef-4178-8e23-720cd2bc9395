"""
简化的MAMBAFORMER测试脚本
直接加载NPZ数据，测试模型功能
"""

import os
import numpy as np
import torch
import torch.nn as nn
from sklearn.metrics import accuracy_score, f1_score
import glob

# 导入我们的模型
from attn_mambaformer import MAMBAFORMER

def test_single_file():
    """测试单个NPZ文件的数据加载"""
    print("测试单个NPZ文件...")
    
    # 找到第一个NPZ文件
    npz_pattern = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/*.npz"
    npz_files = glob.glob(npz_pattern)
    
    if not npz_files:
        print("❌ 找不到NPZ文件")
        return False, None, None
    
    first_file = npz_files[0]
    print(f"加载文件: {os.path.basename(first_file)}")
    
    try:
        data = np.load(first_file)
        x = data['x']
        y = data['y']
        
        print(f"数据形状: x={x.shape}, y={y.shape}")
        print(f"数据类型: x={x.dtype}, y={y.dtype}")
        print(f"标签分布: {np.bincount(y)}")
        print(f"x范围: [{x.min():.3f}, {x.max():.3f}]")
        
        # 取一小部分数据用于测试
        if len(x) > 100:
            x_sample = x[:100]
            y_sample = y[:100]
        else:
            x_sample = x
            y_sample = y
            
        print(f"测试样本: x={x_sample.shape}, y={y_sample.shape}")
        return True, x_sample, y_sample
        
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")
        return False, None, None

def test_model_basic():
    """测试模型基本功能"""
    print("\n测试模型基本功能...")
    
    try:
        # 创建模型 - 使用简化的多通道版本
        from simple_attn_mambaformer import MultiChannelSimplifiedMAMBAFORMER
        model = MultiChannelSimplifiedMAMBAFORMER()
        print(f"✅ 模型创建成功 (简化多通道版本)")
        
        # 参数统计
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"总参数量: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        
        return True, model
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_forward_pass(model, x_sample):
    """测试前向传播"""
    print("\n测试前向传播...")
    
    try:
        # 准备数据 - 处理4通道数据
        if len(x_sample.shape) == 3 and x_sample.shape[2] == 4:  # (N, seq_len, 4)
            # 转换为 (N, 3, seq_len) 但只取前3个通道（EEG + 2xEOG）
            x_tensor = torch.FloatTensor(x_sample[:, :, :3]).transpose(1, 2)
        elif len(x_sample.shape) == 2:  # (N, seq_len)
            x_tensor = torch.FloatTensor(x_sample).unsqueeze(1)  # (N, 1, seq_len)
        else:  # (N, channels, seq_len)
            x_tensor = torch.FloatTensor(x_sample)
        
        print(f"输入tensor形状: {x_tensor.shape}")
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            output = model(x_tensor)
            
        # 处理多任务输出
        expected_shape = (x_tensor.shape[0], 5)  # (batch_size, num_classes)
        
        if isinstance(output, dict):
            stage_logits = output['stage_logits']
            print(f"输出类型: 多任务输出字典")
            print(f"主分类器形状: {stage_logits.shape}")
            print(f"主分类器范围: [{stage_logits.min().item():.3f}, {stage_logits.max().item():.3f}]")
            
            if 'rem_scores' in output:
                print(f"REM检测形状: {output['rem_scores'].shape}")
            if 'sws_scores' in output:
                print(f"SWS检测形状: {output['sws_scores'].shape}")
            
            # 检查主分类器输出
            if stage_logits.shape == expected_shape:
                print("✅ 前向传播正常")
                return True, output
            else:
                print(f"❌ 输出形状异常，期望 {expected_shape}，实际 {stage_logits.shape}")
                return False, None
        else:
            print(f"输出形状: {output.shape}")
            print(f"输出范围: [{output.min().item():.3f}, {output.max().item():.3f}]")
            
            # 检查输出是否合理
            if output.shape == expected_shape:
                print("✅ 前向传播正常")
                return True, output
            else:
                print(f"❌ 输出形状错误，期望 {expected_shape}，实际 {output.shape}")
                return False, None
            
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_training_step(model, x_sample, y_sample):
    """测试单步训练"""
    print("\n测试单步训练...")
    
    try:
        # 准备数据 - 处理4通道数据
        if len(x_sample.shape) == 3 and x_sample.shape[2] == 4:  # (N, seq_len, 4)
            # 转换为 (N, 4, seq_len) 但只取前3个通道（EEG + 2xEOG）
            x_tensor = torch.FloatTensor(x_sample[:, :, :3]).transpose(1, 2)
        elif len(x_sample.shape) == 2:
            x_tensor = torch.FloatTensor(x_sample).unsqueeze(1)
        else:
            x_tensor = torch.FloatTensor(x_sample)
        y_tensor = torch.LongTensor(y_sample)
        
        # 设置训练模式
        model.train()
        
        # 损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
        
        # 单步训练
        optimizer.zero_grad()
        
        output = model(x_tensor)
        
        # 处理多任务输出
        if isinstance(output, dict):
            stage_logits = output['stage_logits']
        else:
            stage_logits = output
            
        loss = criterion(stage_logits, y_tensor)
        
        print(f"损失值: {loss.item():.4f}")
        
        loss.backward()
        
        # 检查梯度
        grad_norms = []
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.data.norm(2).item()
                grad_norms.append(grad_norm)
        
        if grad_norms:
            avg_grad_norm = np.mean(grad_norms)
            max_grad_norm = np.max(grad_norms)
            print(f"平均梯度范数: {avg_grad_norm:.6f}")
            print(f"最大梯度范数: {max_grad_norm:.6f}")
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 计算准确率
        with torch.no_grad():
            model.eval()
            output = model(x_tensor)
            
            # 处理多任务输出
            if isinstance(output, dict):
                stage_logits = output['stage_logits']
            else:
                stage_logits = output
                
            preds = torch.argmax(stage_logits, dim=1)
            accuracy = accuracy_score(y_sample, preds.numpy())
            
        print(f"训练前准确率: {accuracy:.4f}")
        print("✅ 单步训练成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 单步训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gpu_compatibility():
    """测试GPU兼容性"""
    print("\n测试GPU兼容性...")
    
    if not torch.cuda.is_available():
        print("⚠️  CUDA不可用，使用CPU")
        return True, "cpu"
    
    try:
        device = torch.device("cuda")
        
        # 创建模型并移动到GPU - 使用简化多通道版本
        from simple_attn_mambaformer import MultiChannelSimplifiedMAMBAFORMER
        model = MultiChannelSimplifiedMAMBAFORMER().to(device)
        
        # 创建测试数据 - 3通道
        x_test = torch.randn(4, 3, 3000).to(device)
        
        # GPU前向传播
        with torch.no_grad():
            output = model(x_test)
            
        print(f"✅ GPU兼容性正常")
        print(f"GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
        
        return True, "cuda"
        
    except Exception as e:
        print(f"⚠️  GPU测试失败，将使用CPU: {e}")
        return True, "cpu"

def main():
    """主测试函数"""
    print("MAMBAFORMER 功能测试")
    print("=" * 50)
    
    # 1. 测试数据加载
    success, x_sample, y_sample = test_single_file()
    if not success:
        print("数据加载测试失败，无法继续")
        return
    
    # 2. 测试模型创建
    success, model = test_model_basic()
    if not success:
        print("模型创建失败，无法继续")
        return
    
    # 3. 测试前向传播
    success, output = test_forward_pass(model, x_sample)
    if not success:
        print("前向传播测试失败")
        return
    
    # 4. 测试单步训练
    success = test_training_step(model, x_sample, y_sample)
    if not success:
        print("训练测试失败")
        return
    
    # 5. 测试GPU兼容性
    success, device = test_gpu_compatibility()
    
    print("\n" + "=" * 50)
    print("🎉 MAMBAFORMER 功能测试完成!")
    print(f"推荐设备: {device.upper()}")
    print("\n可以开始完整训练:")
    print("1. 单fold测试: python train_attn_mambaformer.py --fold_id 0")
    print("2. 所有fold: python train_attn_mambaformer.py --all_folds")
    print("3. 确认数据路径正确")

if __name__ == '__main__':
    main()