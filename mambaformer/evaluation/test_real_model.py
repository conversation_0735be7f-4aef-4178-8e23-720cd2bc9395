#!/usr/bin/env python3
"""
测试真实Sleep-EDF-20数据训练的模型
"""

import os
import torch
import numpy as np
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import json
from pathlib import Path

# 导入模型和数据集
from train_real_sleepedf import EfficientSleepNet, RealSleepEDFDataset
from torch.utils.data import DataLoader

def test_model():
    # 配置
    config = {
        'data_dir': './real_sleepedf_processed',
        'batch_size': 64,
        'model_path': './checkpoints/real_sleepedf_best.pth'
    }
    
    # 检查模型文件
    if not os.path.exists(config['model_path']):
        print(f"错误：找不到模型文件 {config['model_path']}")
        return
    
    # 加载数据分割
    split_file = Path(config['data_dir']) / 'data_split.json'
    with open(split_file, 'r') as f:
        split_info = json.load(f)
    
    test_subjects = split_info['test']
    
    print(f"测试集: {len(test_subjects)} 个受试者")
    print(f"受试者ID: {test_subjects}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n使用设备: {device}")
    
    # 加载测试数据
    print("\n加载测试数据...")
    test_dataset = RealSleepEDFDataset(
        config['data_dir'], 
        test_subjects,
        augment=False
    )
    
    test_loader = DataLoader(
        test_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    
    # 加载模型
    print("\n加载模型...")
    model = EfficientSleepNet().to(device)
    model.load_state_dict(torch.load(config['model_path']))
    model.eval()
    
    # 测试
    print("\n开始测试...")
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_idx, (eeg, eog, labels) in enumerate(test_loader):
            eeg = eeg.to(device)
            eog = eog.to(device)
            labels = labels.to(device)
            
            outputs = model(eeg, eog)
            _, predicted = outputs.max(1)
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            if batch_idx % 10 == 0:
                print(f"  处理批次 {batch_idx}/{len(test_loader)}")
    
    # 计算指标
    accuracy = accuracy_score(all_labels, all_preds)
    print(f"\n📊 测试结果:")
    print(f"总准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 详细分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(all_labels, all_preds, 
                                 target_names=class_names, digits=3)
    print(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    print(f"\n混淆矩阵:")
    print("       ", "  ".join(f"{name:>5}" for name in class_names))
    for i, name in enumerate(class_names):
        print(f"{name:>5}: ", "  ".join(f"{cm[i,j]:5d}" for j in range(5)))
    
    # 计算每类的准确率
    print(f"\n各类别准确率:")
    class_acc = cm.diagonal() / cm.sum(axis=1)
    for i, name in enumerate(class_names):
        print(f"  {name}: {class_acc[i]:.3f} ({cm[i,i]}/{cm[i,:].sum()})")
    
    # 保存结果
    results = {
        'accuracy': float(accuracy),
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'class_accuracies': {name: float(acc) for name, acc in zip(class_names, class_acc)}
    }
    
    with open('./log/real_sleepedf_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ 测试完成！结果保存在 ./log/real_sleepedf_test_results.json")
    
    # 检查是否达到目标
    if accuracy >= 0.80:
        print(f"\n🎉 达到80%准确率目标！实际准确率: {accuracy*100:.2f}%")
    else:
        print(f"\n⚠️  未达到80%目标，当前准确率: {accuracy*100:.2f}%")

if __name__ == "__main__":
    test_model()