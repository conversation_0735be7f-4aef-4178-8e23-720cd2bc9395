"""
基于受试者的K折交叉验证 - 避免数据泄露
确保同一受试者的所有数据都在同一个fold中
"""

import os
import numpy as np
import glob
from collections import defaultdict

def parse_subject_id(filename):
    """从文件名提取受试者ID
    
    Args:
        filename: SC4001E0.npz -> 返回 "00"
    
    Returns:
        subject_id: 受试者编号字符串
    """
    import re
    # 正确解析：SC4001E0 -> 受试者00，SC4002E0 -> 受试者00（同一人的不同夜晚）
    # SC4011E0 -> 受试者01，SC4012E0 -> 受试者01（同一人的不同夜晚）
    match = re.search(r'SC4(\d)(\d)\dE\d+', filename)
    if match:
        # 前两位数字组成受试者ID
        return match.group(1) + match.group(2)
    else:
        raise ValueError(f"无法从文件名中提取受试者ID: {filename}")

def group_files_by_subject(data_dir):
    """按受试者分组数据文件
    
    Args:
        data_dir: NPZ文件目录
        
    Returns:
        subject_files: {subject_id: [file1, file2, ...]}
        subject_list: 按subject_id排序的列表
    """
    npz_pattern = os.path.join(data_dir, "*.npz")
    files = sorted(glob.glob(npz_pattern))
    
    subject_files = defaultdict(list)
    
    for file_path in files:
        filename = os.path.basename(file_path)
        subject_id = parse_subject_id(filename)
        subject_files[subject_id].append(file_path)
    
    # 排序以确保一致性
    subject_list = sorted(subject_files.keys())
    
    print(f"发现 {len(subject_list)} 个受试者:")
    for subject_id in subject_list:
        print(f"  受试者 {subject_id}: {len(subject_files[subject_id])} 个文件")
    
    return dict(subject_files), subject_list

def create_subject_aware_folds(data_dir, n_folds=5, random_seed=42):
    """创建基于受试者的K折划分
    
    Args:
        data_dir: 数据目录
        n_folds: 折数
        random_seed: 随机种子
        
    Returns:
        folds: {fold_id: {'train': [files...], 'test': [files...]}}
        subject_fold_map: {subject_id: fold_id}
    """
    # 按受试者分组文件
    subject_files, subject_list = group_files_by_subject(data_dir)
    
    n_subjects = len(subject_list)
    if n_subjects < n_folds:
        raise ValueError(f"受试者数量 ({n_subjects}) 少于折数 ({n_folds})")
    
    # 随机打乱受试者顺序
    np.random.seed(random_seed)
    shuffled_subjects = np.random.permutation(subject_list)
    
    # 分配受试者到各个fold
    subjects_per_fold = n_subjects // n_folds
    remaining_subjects = n_subjects % n_folds
    
    fold_subjects = {}
    subject_fold_map = {}
    
    start_idx = 0
    for fold_id in range(n_folds):
        # 计算当前fold的受试者数量
        current_fold_size = subjects_per_fold
        if fold_id < remaining_subjects:
            current_fold_size += 1
        
        end_idx = start_idx + current_fold_size
        fold_subjects[fold_id] = shuffled_subjects[start_idx:end_idx].tolist()
        
        # 记录受试者-fold映射
        for subject_id in fold_subjects[fold_id]:
            subject_fold_map[subject_id] = fold_id
        
        start_idx = end_idx
    
    # 创建文件级别的fold划分
    folds = {}
    for fold_id in range(n_folds):
        test_subjects = fold_subjects[fold_id]
        train_subjects = [s for s in subject_list if s not in test_subjects]
        
        # 收集训练和测试文件
        train_files = []
        test_files = []
        
        for subject_id in train_subjects:
            train_files.extend(subject_files[subject_id])
        
        for subject_id in test_subjects:
            test_files.extend(subject_files[subject_id])
        
        folds[fold_id] = {
            'train': train_files,
            'test': test_files,
            'train_subjects': train_subjects,
            'test_subjects': test_subjects
        }
        
        print(f"\nFold {fold_id + 1}:")
        print(f"  训练受试者: {train_subjects} ({len(train_files)} 文件)")
        print(f"  测试受试者: {test_subjects} ({len(test_files)} 文件)")
    
    return folds, subject_fold_map

def validate_no_subject_leakage(folds):
    """验证没有受试者泄露
    
    Args:
        folds: fold数据结构
        
    Returns:
        bool: True表示没有泄露
    """
    print("\n验证数据泄露...")
    
    used_test_subjects = set()
    
    for fold_id, fold_data in folds.items():
        train_subjects = set(fold_data['train_subjects'])
        test_subjects = set(fold_data['test_subjects'])
        
        # 检查当前fold内部是否有重叠
        overlap = train_subjects.intersection(test_subjects)
        if overlap:
            print(f"❌ Fold {fold_id + 1} 内部有受试者重叠: {overlap}")
            return False
        
        # 检查测试受试者是否与之前的fold重复
        if used_test_subjects.intersection(test_subjects):
            print(f"❌ Fold {fold_id + 1} 测试受试者与其他fold重叠: {used_test_subjects.intersection(test_subjects)}")
            return False
        
        used_test_subjects.update(test_subjects)
        
        print(f"✅ Fold {fold_id + 1}: {len(train_subjects)}个训练受试者, {len(test_subjects)}个测试受试者")
    
    print("✅ 验证通过：没有受试者泄露")
    print(f"所有测试受试者: {sorted(used_test_subjects)}")
    print(f"总测试受试者数: {len(used_test_subjects)}")
    
    return True

def main():
    """测试函数"""
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    
    print("Sleep-EDF-20 基于受试者的K折划分")
    print("=" * 50)
    
    # 创建受试者感知的K折划分
    folds, subject_fold_map = create_subject_aware_folds(data_dir, n_folds=5)
    
    # 验证没有泄露
    is_valid = validate_no_subject_leakage(folds)
    
    if is_valid:
        print("\n🎉 受试者感知的K折划分创建成功！")
        print("可以安全地用于跨受试者泛化性能评估")
        
        # 保存fold信息
        import json
        fold_info = {
            'folds': {},
            'subject_fold_map': subject_fold_map,
            'total_subjects': len(subject_fold_map),
            'n_folds': len(folds)
        }
        
        for fold_id, fold_data in folds.items():
            fold_info['folds'][str(fold_id)] = {
                'train_files': fold_data['train'],
                'test_files': fold_data['test'],
                'train_subjects': fold_data['train_subjects'],
                'test_subjects': fold_data['test_subjects']
            }
        
        with open('subject_aware_folds.json', 'w') as f:
            json.dump(fold_info, f, indent=2)
        
        print("Fold信息已保存至: subject_aware_folds.json")
        
        return folds
    else:
        print("\n❌ K折划分验证失败")
        return None

if __name__ == "__main__":
    main()