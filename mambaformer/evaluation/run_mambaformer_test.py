"""
MAMBAFORMER测试脚本 - 使用AttnSleep数据格式
先在单个fold上测试，确保集成正常工作
"""

import sys
import os
import numpy as np
import torch
import torch.nn as nn
import json
from sklearn.metrics import accuracy_score, f1_score, classification_report

# 添加AttnSleep路径
sys.path.append('../AttnSleep')

# 导入AttnSleep组件
from utils.util import load_folds_data

# 导入我们的模型
from attn_mambaformer import MAMBAFORMER

def load_single_npz_file(file_path):
    """加载单个NPZ文件"""
    data = np.load(file_path)
    return data['x'], data['y']

def test_data_loading():
    """测试数据加载"""
    print("测试数据加载...")
    
    # 数据路径
    np_data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    
    if not os.path.exists(np_data_dir):
        print(f"数据目录不存在: {np_data_dir}")
        return False
    
    # 加载fold数据
    try:
        folds_data = load_folds_data(np_data_dir, 5)  # 使用5折进行快速测试
        print(f"成功加载 {len(folds_data)} 折数据")
        
        # 检查第一个fold的数据
        fold_0_train = folds_data[0][0]
        fold_0_test = folds_data[0][1]
        
        print(f"Fold 0: 训练文件数 {len(fold_0_train)}, 测试文件数 {len(fold_0_test)}")
        
        # 加载第一个训练文件检查数据格式
        first_train_file = fold_0_train[0]
        x, y = load_single_npz_file(first_train_file)
        
        print(f"数据形状: x={x.shape}, y={y.shape}")
        print(f"标签分布: {np.bincount(y)}")
        print(f"数据类型: x={x.dtype}, y={y.dtype}")
        print(f"数据范围: x_min={x.min():.3f}, x_max={x.max():.3f}")
        
        return True, folds_data
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return False, None

def test_model():
    """测试模型初始化和前向传播"""
    print("\n测试模型...")
    
    # 创建模型
    model = MAMBAFORMER()
    
    # 打印模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数量: {total_params:,}")
    
    # 创建测试数据
    batch_size = 4
    seq_len = 3000
    test_input = torch.randn(batch_size, 1, seq_len)
    
    print(f"测试输入形状: {test_input.shape}")
    
    # 前向传播测试
    model.eval()
    with torch.no_grad():
        try:
            output = model(test_input)
            print(f"输出形状: {output.shape}")
            print(f"输出范围: [{output.min().item():.3f}, {output.max().item():.3f}]")
            
            # 检查输出是否合理
            if output.shape == (batch_size, 5):  # 5个睡眠分期类别
                print("✅ 模型前向传播正常")
                return True
            else:
                print(f"❌ 输出形状异常，期望 ({batch_size}, 5)，实际 {output.shape}")
                return False
                
        except Exception as e:
            print(f"❌ 前向传播失败: {e}")
            return False

def quick_training_test(folds_data):
    """快速训练测试"""
    print("\n快速训练测试...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = MAMBAFORMER().to(device)
    
    # 简单的训练设置
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # 加载一小部分数据进行测试
    fold_0_train = folds_data[0][0][:2]  # 只用前2个文件
    
    # 加载训练数据
    all_x = []
    all_y = []
    
    for file_path in fold_0_train:
        x, y = load_single_npz_file(file_path)
        # 限制样本数量以加快测试
        if len(x) > 50:
            x = x[:50]
            y = y[:50]
        all_x.append(x)
        all_y.append(y)
    
    # 合并数据
    train_x = np.vstack(all_x)
    train_y = np.concatenate(all_y)
    
    print(f"训练数据: {train_x.shape}, 标签: {train_y.shape}")
    print(f"标签分布: {np.bincount(train_y)}")
    
    # 转换为tensor
    train_x = torch.FloatTensor(train_x).unsqueeze(1)  # (N, 1, seq_len)
    train_y = torch.LongTensor(train_y)
    
    # 创建简单的数据加载器
    dataset = torch.utils.data.TensorDataset(train_x, train_y)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=8, shuffle=True)
    
    # 快速训练几个batch
    model.train()
    print("开始快速训练测试...")
    
    for epoch in range(2):  # 只训练2个epoch
        epoch_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, (data, target) in enumerate(dataloader):
            if batch_idx >= 5:  # 只训练5个batch
                break
                
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            epoch_loss += loss.item()
            
            # 预测
            preds = torch.argmax(output, dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(target.cpu().numpy())
        
        # 计算指标
        accuracy = accuracy_score(all_labels, all_preds) if all_labels else 0
        
        print(f"Epoch {epoch + 1}: Loss={epoch_loss:.4f}, Acc={accuracy:.4f}")
    
    print("✅ 快速训练测试完成")
    return True

def main():
    """主测试函数"""
    print("MAMBAFORMER + AttnSleep 集成测试")
    print("=" * 50)
    
    # 1. 测试数据加载
    success, folds_data = test_data_loading()
    if not success:
        print("❌ 数据加载测试失败")
        return
    
    # 2. 测试模型
    if not test_model():
        print("❌ 模型测试失败")
        return
    
    # 3. 快速训练测试
    try:
        if not quick_training_test(folds_data):
            print("❌ 训练测试失败")
            return
    except Exception as e:
        print(f"❌ 训练测试异常: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！MAMBAFORMER集成成功")
    print("现在可以运行完整训练:")
    print("python train_attn_mambaformer.py --fold_id 0")
    print("或运行所有fold:")
    print("python train_attn_mambaformer.py --all_folds")

if __name__ == '__main__':
    main()