"""
Test script for MambaFormer implementation
Small batch testing before full dataset training
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.mambaformer_net import MambaFormerSleepNet, Epoch_Cross_Transformer_Network
from models.progressive_classifier import <PERSON><PERSON>alLoss
from preprocessing import wICAProcessor

def test_wica_preprocessing():
    """测试wICA预处理"""
    print("=" * 60)
    print("Testing wICA Preprocessing...")
    
    # 模拟EEG数据 (1个通道, 3000个采样点 = 30秒@100Hz)
    np.random.seed(42)
    eeg_data = np.random.randn(1, 3000) * 100
    
    # 添加一些伪影
    # 眼动伪影 (低频)
    t = np.linspace(0, 30, 3000)
    eye_artifact = 500 * np.sin(0.5 * t) 
    eeg_data[0, :1000] += eye_artifact[:1000]
    
    # 肌电伪影 (高频)
    muscle_artifact = 200 * np.random.randn(1000)
    eeg_data[0, 1500:2500] += muscle_artifact
    
    try:
        # 初始化wICA处理器
        wica_processor = wICAProcessor(n_components=5, random_state=42)
        
        # 处理数据
        clean_data, ic_components, ic_labels = wica_processor.fit_transform(eeg_data)
        
        print(f"✓ Original data shape: {eeg_data.shape}")
        print(f"✓ Clean data shape: {clean_data.shape}")
        print(f"✓ IC components: {len(ic_components)}")
        print(f"✓ IC labels: {ic_labels}")
        
        # 统计伪影移除情况
        artifact_count = len([l for l in ic_labels if l in ['eye', 'muscle', 'heart', 'line_noise']])
        print(f"✓ Artifacts removed: {artifact_count}/{len(ic_labels)} components")
        
        print("✓ wICA preprocessing test passed!")
        return True
        
    except Exception as e:
        print(f"✗ wICA preprocessing test failed: {e}")
        return False

def test_mamba_block():
    """测试Mamba块"""
    print("=" * 60) 
    print("Testing Mamba Block...")
    
    try:
        from models.mamba import MambaBlock
        
        # 测试参数
        batch_size, seq_len, d_model = 4, 61, 64
        
        # 创建Mamba块
        mamba = MambaBlock(d_model=d_model, d_state=16, d_conv=4)
        
        # 测试输入
        x = torch.randn(batch_size, seq_len, d_model)
        
        # 前向传播
        output = mamba(x)
        
        print(f"✓ Input shape: {x.shape}")
        print(f"✓ Output shape: {output.shape}")
        print(f"✓ Parameters: {sum(p.numel() for p in mamba.parameters()):,}")
        
        # 验证输出维度
        assert output.shape == x.shape, f"Shape mismatch: {output.shape} != {x.shape}"
        
        print("✓ Mamba block test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Mamba block test failed: {e}")
        return False

def test_mambaformer_encoder():
    """测试MambaFormer编码器"""
    print("=" * 60)
    print("Testing MambaFormer Encoder...")
    
    try:
        from models.mamba import MambaFormerEncoder
        
        # 测试参数
        batch_size, seq_len, d_model = 4, 61, 64
        
        # 创建编码器
        encoder = MambaFormerEncoder(
            d_model=d_model,
            num_layers=2,
            nhead=8,
            use_mamba=True
        )
        
        # 测试输入
        x = torch.randn(batch_size, seq_len, d_model)
        
        # 前向传播
        output = encoder(x)
        
        print(f"✓ Input shape: {x.shape}")
        print(f"✓ Output shape: {output.shape}")
        print(f"✓ Parameters: {sum(p.numel() for p in encoder.parameters()):,}")
        
        # 验证输出维度
        assert output.shape == x.shape
        
        print("✓ MambaFormer encoder test passed!")
        return True
        
    except Exception as e:
        print(f"✗ MambaFormer encoder test failed: {e}")
        return False

def test_cross_modal_attention():
    """测试跨模态注意力"""
    print("=" * 60)
    print("Testing Cross-Modal Attention...")
    
    try:
        from models.enhanced_cross_attention import EnhancedCrossModalAttention
        
        # 测试参数
        batch_size, seq_len, d_model = 4, 61, 64
        
        # 创建跨模态注意力
        cross_attention = EnhancedCrossModalAttention(
            d_model=d_model,
            nhead=8,
            lightweight=True
        )
        
        # 测试输入
        eeg_features = torch.randn(batch_size, seq_len, d_model)
        eog_features = torch.randn(batch_size, seq_len, d_model)
        
        # 前向传播
        fused_features, attention_weights = cross_attention(eeg_features, eog_features)
        
        print(f"✓ EEG input shape: {eeg_features.shape}")
        print(f"✓ EOG input shape: {eog_features.shape}")
        print(f"✓ Fused output shape: {fused_features.shape}")
        print(f"✓ Attention weights shape: {attention_weights.shape}")
        print(f"✓ Parameters: {sum(p.numel() for p in cross_attention.parameters()):,}")
        
        # 验证输出维度
        assert fused_features.shape == eeg_features.shape
        
        print("✓ Cross-modal attention test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Cross-modal attention test failed: {e}")
        return False

def test_progressive_classifier():
    """测试渐进式分类器"""
    print("=" * 60)
    print("Testing Progressive Classifier...")
    
    try:
        from models.progressive_classifier import ProgressiveClassifier
        
        # 测试参数
        batch_size, d_model = 8, 128
        
        # 创建分类器
        classifier = ProgressiveClassifier(
            d_model=d_model,
            hidden_dim=256,
            use_uncertainty=True
        )
        
        # 测试输入
        features = torch.randn(batch_size, d_model)
        labels = torch.randint(0, 5, (batch_size,))
        
        # 前向传播
        outputs = classifier(features, stage="both")
        
        print(f"✓ Input shape: {features.shape}")
        print(f"✓ Output keys: {list(outputs.keys())}")
        print(f"✓ Coarse logits shape: {outputs['coarse_logits'].shape}")
        print(f"✓ Fine logits shape: {outputs['fine_logits'].shape}")
        print(f"✓ Parameters: {sum(p.numel() for p in classifier.parameters()):,}")
        
        # 测试不确定性预测
        uncertainty_outputs = classifier.predict_with_uncertainty(features, mc_samples=5)
        print(f"✓ Uncertainty prediction keys: {list(uncertainty_outputs.keys())}")
        
        # 测试一致性损失
        consistency_loss = classifier.compute_consistency_loss(outputs, labels)
        print(f"✓ Consistency loss: {consistency_loss.item():.4f}")
        
        print("✓ Progressive classifier test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Progressive classifier test failed: {e}")
        return False

def test_complete_network():
    """测试完整网络"""
    print("=" * 60)
    print("Testing Complete MambaFormer Network...")
    
    try:
        # 测试参数
        batch_size = 4
        seq_len = 3000  # 30秒 @ 100Hz
        
        # 创建网络
        model = MambaFormerSleepNet(
            d_model=64,
            num_mambaformer_layers=2,  # 减少层数加速测试
            window_size=50,  # 使用默认window_size=50以匹配3000长度输入
            use_wica_online=False,  # 关闭在线wICA加速测试
            use_progressive=True
        )
        
        # 测试输入
        eeg = torch.randn(batch_size, 1, seq_len)
        eog = torch.randn(batch_size, 1, seq_len) 
        labels = torch.randint(0, 5, (batch_size,))
        
        print(f"✓ Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 前向传播
        outputs = model(eeg, eog, stage="both")
        
        print(f"✓ EEG input shape: {eeg.shape}")
        print(f"✓ EOG input shape: {eog.shape}")
        print(f"✓ Output keys: {list(outputs.keys())}")
        
        if 'fine_logits' in outputs:
            print(f"✓ Fine logits shape: {outputs['fine_logits'].shape}")
        if 'coarse_logits' in outputs:
            print(f"✓ Coarse logits shape: {outputs['coarse_logits'].shape}")
            
        # 测试损失计算
        losses = model.compute_loss(outputs, labels, stage="both")
        print(f"✓ Loss keys: {list(losses.keys())}")
        print(f"✓ Total loss: {losses['total_loss'].item():.4f}")
        
        # 测试预测
        predictions = model.predict(eeg, eog, with_uncertainty=True)
        print(f"✓ Prediction keys: {list(predictions.keys())}")
        
        # 测试兼容性包装器
        compat_model = Epoch_Cross_Transformer_Network(d_model=64, window_size=50)
        compat_output = compat_model(eeg, eog, finetune=True)
        print(f"✓ Compatible output shapes: {[x.shape if torch.is_tensor(x) else len(x) for x in compat_output]}")
        
        print("✓ Complete network test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Complete network test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_focal_loss():
    """测试Focal Loss"""
    print("=" * 60)
    print("Testing Focal Loss...")
    
    try:
        # 模拟类别不平衡数据
        batch_size = 100
        num_classes = 5
        
        # 创建不平衡的标签分布 (模拟睡眠分期分布)
        labels = torch.cat([
            torch.zeros(40),      # W: 40%
            torch.ones(5),        # S1: 5% (少数类)
            torch.full((35,), 2), # S2: 35%
            torch.full((10,), 3), # S3: 10%
            torch.full((10,), 4)  # REM: 10%
        ]).long()
        
        # 随机logits
        logits = torch.randn(batch_size, num_classes)
        
        # 测试Focal Loss
        focal_loss = FocalLoss(gamma=2.0)
        loss_focal = focal_loss(logits, labels)
        
        # 对比标准交叉熵
        ce_loss = nn.CrossEntropyLoss()
        loss_ce = ce_loss(logits, labels)
        
        print(f"✓ Focal Loss: {loss_focal.item():.4f}")
        print(f"✓ Cross Entropy Loss: {loss_ce.item():.4f}")
        print(f"✓ Label distribution: {torch.bincount(labels).tolist()}")
        
        print("✓ Focal loss test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Focal loss test failed: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 Starting MambaFormer Implementation Tests...")
    print("=" * 80)
    
    tests = [
        ("wICA Preprocessing", test_wica_preprocessing),
        ("Mamba Block", test_mamba_block),
        ("MambaFormer Encoder", test_mambaformer_encoder), 
        ("Cross-Modal Attention", test_cross_modal_attention),
        ("Progressive Classifier", test_progressive_classifier),
        ("Focal Loss", test_focal_loss),
        ("Complete Network", test_complete_network),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 Test Results Summary:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status:8} | {test_name}")
        if success:
            passed += 1
    
    print("=" * 80)
    print(f"🎯 Tests Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Ready for Sleep-EDF data testing.")
    else:
        print("⚠️  Some tests failed. Please fix issues before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n🚀 Next Steps:")
        print("1. Check data-edf/ directory for Sleep-EDF files")
        print("2. Run preprocessing on small batch of data")  
        print("3. Train MambaFormer on sample data")
        print("4. Compare with baseline Cross-Modal Transformer")
    
    sys.exit(0 if success else 1)