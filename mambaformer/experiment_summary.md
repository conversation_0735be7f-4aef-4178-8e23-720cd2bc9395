# 序列MAMBAFORMER实验总结 - 完整进展

## 🔍 关键发现与解决方案

### 1. 数据泄露问题（已解决）
- **问题**：V1-V4使用`random_split`从训练集随机划分验证集，导致同一被试数据同时出现在训练和验证集
- **影响**：验证指标虚高，无法反映真实泛化性能
- **解决**：V5开始实施正确的被试级别划分（训练14个被试，验证2个，测试4个）

### 2. 数据截断问题（已解决）
- **问题**：`max_samples_per_file=150`限制导致只使用每个文件前150个epochs
- **影响**：REM睡眠（通常在后期）被大量截断，测试集只有30个REM样本（应有8065个）
- **解决**：V6/V7移除限制，使用完整数据

### 3. 指标展示（已完成）
- **要求**："结果里Kappa (κ) 系数 和 Macro-F1也很重要，log里要展示"
- **实现**：从V2开始成功集成Kappa和Macro-F1指标
- **参考**：学习了原版CMT的指标计算方法

## 📊 实验版本对比

| 版本 | 数据策略 | 模型特点 | 主要问题 | 关键结果 |
|------|---------|---------|----------|----------|
| V1-V4 | 数据泄露 | 各种架构尝试 | 验证指标虚高 | 不可信 |
| V5 | 正确划分+截断 | 基础架构 | REM样本过少 | 测试F1=64.7%, Kappa=0.799, REM F1=0% |
| V6 | 正确划分+完整数据 | 类别权重[1,1.3,1,1.3,1.5] | 训练不稳定 | 验证性能崩溃（进行中） |
| V7 | 正确划分+完整数据 | Focal Loss (γ=1.5) | - | 训练中 |

## 🎯 当前状态

### V5结果（正确划分但数据截断）
- 测试准确率：86.1%
- 测试F1：64.7%
- 测试Kappa：0.799（较强一致）
- REM F1：0%（因为测试集只有30个REM样本）

### V6进展（完整数据+类别权重）
- 使用温和的类别权重[1.0, 1.3, 1.0, 1.3, 1.5]
- 训练不稳定，验证准确率只有14%
- 说明类别权重可能过度影响了训练

### V7策略（完整数据+Focal Loss）
- 使用Focal Loss替代类别权重
- gamma=1.5，平衡难易样本
- 综合评估整体性能和REM检测
- 正在训练中

## 📈 数据集统计（使用完整数据）

### 训练集（14个被试）
- 总序列数：27,761
- 包含REM epochs：5,252

### 验证集（2个被试）
- 总序列数：4,677  
- 包含REM epochs：852

### 测试集（4个被试）
- 总序列数：9,714
- 包含REM epochs：1,613

## 🚀 后续计划

1. **等待V7结果**
   - 预期能够平衡整体性能和REM检测
   - Focal Loss应该比类别权重更稳定

2. **如果V7仍无法检测REM**
   - 尝试专门的REM增强策略
   - 考虑多阶段训练（先整体，后REM）
   - 探索时序特征增强

3. **参考原版CMT更多技巧**
   - 后处理平滑
   - 多尺度特征
   - 跨模态注意力（在基础性能稳定后）

## 💡 经验教训

1. **数据完整性至关重要**
   - 正确的被试级别划分是基础
   - 不能随意截断数据，特别是时序数据
   - REM等少数类容易被截断影响

2. **平衡策略需要谨慎**
   - 过强的类别权重会破坏整体性能
   - Focal Loss可能是更好的选择
   - 需要同时关注整体和少数类性能

3. **评估指标的重要性**
   - Kappa系数能更好地反映分类性能
   - Macro-F1对所有类别一视同仁
   - 需要综合多个指标评估模型

## 🔄 实时更新
- V6：训练不稳定，考虑停止
- V7：正在训练，使用Focal Loss策略
- 下一步：根据V7结果决定后续优化方向