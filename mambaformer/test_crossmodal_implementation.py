"""
测试CrossModal MAMBAFORMER实现
"""

import os
import sys
import torch
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.crossmodal_mambaformer import CrossModalSequentialMAMBAFORMER, CrossModalAttention
from utils.sequence_dataset import SequenceSleepDataset


def test_crossmodal_attention():
    """测试CrossModal注意力机制"""
    print("="*60)
    print("测试CrossModal注意力机制...")
    
    batch_size = 2
    seq_len = 5
    d_model = 128
    
    # 创建CrossModal注意力层
    cross_attn = CrossModalAttention(d_model, n_heads=8)
    
    # 模拟模态数据
    eeg_features = torch.randn(batch_size, seq_len, d_model)
    eog_features = torch.randn(batch_size, seq_len, d_model)
    emg_features = torch.randn(batch_size, seq_len, d_model)
    
    # 测试CrossModal注意力
    output, attn_weights = cross_attn(eeg_features, [eog_features, emg_features])
    
    print(f"EEG输入形状: {eeg_features.shape}")
    print(f"EOG输入形状: {eog_features.shape}")
    print(f"EMG输入形状: {emg_features.shape}")
    print(f"CrossModal输出形状: {output.shape}")
    print(f"注意力权重形状: {attn_weights.shape}")
    
    # 验证形状
    assert output.shape == eeg_features.shape, f"输出形状错误: {output.shape}"
    assert attn_weights.shape[0] == batch_size, f"注意力权重批次维度错误"
    assert attn_weights.shape[1] == 8, f"注意力权重头数错误"  # n_heads
    
    print("✅ CrossModal注意力测试通过\n")


def test_crossmodal_model():
    """测试CrossModal模型"""
    print("="*60)
    print("测试CrossModal MAMBAFORMER模型...")
    
    # 创建模型
    model = CrossModalSequentialMAMBAFORMER(
        n_classes=5,
        d_model=128,
        n_heads=8,
        n_layers=4,
        seq_len=5
    )
    
    # 测试输入 - 5通道 (EEG(3) + EOG(1) + EMG(1))
    batch_size = 2
    seq_len = 5
    time_steps = 3000
    channels = 5
    
    x = torch.randn(batch_size, seq_len, time_steps, channels)
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        main_output, aux_output = model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"主输出形状: {main_output.shape}")  # 应该是 (2, 5, 5)
    print(f"辅助输出形状: {aux_output.shape}")  # 应该是 (2, 5, 2)
    
    assert main_output.shape == (batch_size, seq_len, 5), f"主输出形状错误: {main_output.shape}"
    assert aux_output.shape == (batch_size, seq_len, 2), f"辅助输出形状错误: {aux_output.shape}"
    
    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\n模型参数:")
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    print("✅ CrossModal模型测试通过\n")


def test_4channel_compatibility():
    """测试4通道兼容性"""
    print("="*60)
    print("测试4通道兼容性...")
    
    model = CrossModalSequentialMAMBAFORMER()
    
    # 测试4通道输入
    batch_size = 2
    seq_len = 5
    time_steps = 3000
    channels = 4  # 只有4通道
    
    x = torch.randn(batch_size, seq_len, time_steps, channels)
    
    model.eval()
    with torch.no_grad():
        main_output, aux_output = model(x)
    
    print(f"4通道输入形状: {x.shape}")
    print(f"主输出形状: {main_output.shape}")
    print(f"辅助输出形状: {aux_output.shape}")
    
    assert main_output.shape == (batch_size, seq_len, 5), f"主输出形状错误"
    assert aux_output.shape == (batch_size, seq_len, 2), f"辅助输出形状错误"
    
    print("✅ 4通道兼容性测试通过\n")


def test_crossmodal_dataset():
    """测试CrossModal数据集加载"""
    print("="*60)
    print("测试CrossModal数据集...")
    
    # 测试文件
    test_files = [
        "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4001E0.npz"
    ]
    
    # 创建数据集 - 使用4通道
    dataset = SequenceSleepDataset(
        test_files[:1],
        max_samples_per_file=10,
        seq_len=5,
        use_channels=4  # 4通道
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 获取一个样本
    x, y = dataset[0]
    print(f"输入形状: {x.shape}")  # 应该是 (5, 3000, 4)
    print(f"标签形状: {y.shape}")  # 应该是 (5,)
    
    assert x.shape == (5, 3000, 4), f"输入形状错误: {x.shape}"
    assert y.shape == (5,), f"标签形状错误: {y.shape}"
    
    # 检查通道分布
    print(f"EEG通道(前3): shape={x[:, :, :3].shape}")
    print(f"EOG通道(第4): shape={x[:, :, 3:4].shape}")
    print("注意: EMG通道将在模型内部复制EOG通道")
    
    print("✅ CrossModal数据集测试通过\n")


def test_memory_usage():
    """测试内存使用"""
    print("="*60)
    print("测试CrossModal模型内存使用...")
    
    if torch.cuda.is_available():
        # 清空缓存
        torch.cuda.empty_cache()
        
        # 获取初始内存
        initial_memory = torch.cuda.memory_allocated() / 1024**2  # MB
        
        # 创建模型
        model = CrossModalSequentialMAMBAFORMER().cuda()
        model_memory = torch.cuda.memory_allocated() / 1024**2 - initial_memory
        
        # 测试不同batch size
        for batch_size in [4, 8]:
            torch.cuda.empty_cache()
            
            x = torch.randn(batch_size, 5, 3000, 5).cuda()
            
            try:
                with torch.no_grad():
                    output, _ = model(x)
                
                memory_used = torch.cuda.memory_allocated() / 1024**2
                print(f"Batch size {batch_size}: {memory_used:.2f} MB")
                
            except RuntimeError as e:
                print(f"Batch size {batch_size}: 内存不足 - {e}")
        
        print(f"模型大小: {model_memory:.2f} MB")
    else:
        print("CUDA不可用，跳过内存测试")
    
    print()


def main():
    print("🧪 测试CrossModal MAMBAFORMER实现")
    print("="*60)
    
    # 运行所有测试
    test_crossmodal_attention()
    test_crossmodal_dataset()
    test_crossmodal_model()
    test_4channel_compatibility()
    test_memory_usage()
    
    print("="*60)
    print("✅ 所有测试通过！")
    print("\n现在可以运行训练脚本:")
    print("python training/train_crossmodal_mambaformer.py")


if __name__ == "__main__":
    main()