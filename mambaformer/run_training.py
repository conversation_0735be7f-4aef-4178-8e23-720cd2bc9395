#!/usr/bin/env python
"""
MAMBAFORMER项目的快速运行脚本
"""

import os
import sys
import subprocess

# 添加mambaformer目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_command(cmd, description):
    """运行命令并显示描述"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    print(f"命令: {cmd}")
    print()
    
    result = subprocess.run(cmd, shell=True, cwd=os.path.dirname(os.path.abspath(__file__)))
    if result.returncode != 0:
        print(f"❌ 错误: {description} 失败")
        sys.exit(1)
    print(f"✅ {description} 完成")

def main():
    """主函数"""
    print("🚀 MAMBAFORMER 睡眠分期分类实验")
    print("=" * 60)
    
    # 1. 生成K折分割
    if not os.path.exists("../configs/subject_aware_folds.json"):
        run_command(
            "python evaluation/subject_aware_kfold.py",
            "生成受试者感知K折分割"
        )
    else:
        print("✅ K折分割文件已存在")
    
    # 2. 运行数据泄露审计
    run_command(
        "python evaluation/data_leakage_audit.py",
        "运行数据泄露审计"
    )
    
    # 3. 开始训练
    run_command(
        "python training/train_subject_aware_mambaformer.py",
        "开始5折交叉验证训练"
    )
    
    print("\n✅ 实验完成！")
    print(f"日志文件位于: ../logs/")
    print(f"结果文件位于: ../configs/subject_aware_mambaformer_results.json")

if __name__ == "__main__":
    main()