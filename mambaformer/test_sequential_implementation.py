"""
测试序列MAMBAFORMER实现的正确性
"""

import os
import sys
import torch
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.sequential_mambaformer import SequentialMAMBAFORMER
from utils.sequence_dataset import SequenceSleepDataset, create_sequence_dataloaders


def test_sequence_dataset():
    """测试序列数据集"""
    print("="*60)
    print("测试序列数据集...")
    
    # 测试文件
    test_files = [
        "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4001E0.npz",
        "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4002E0.npz"
    ]
    
    # 创建数据集
    dataset = SequenceSleepDataset(
        test_files[:1],  # 只用一个文件测试
        max_samples_per_file=20,
        seq_len=5,
        use_channels=3
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 获取一个样本
    x, y = dataset[0]
    print(f"输入形状: {x.shape}")  # 应该是 (5, 3000, 3)
    print(f"标签形状: {y.shape}")  # 应该是 (5,)
    print(f"标签内容: {y}")
    
    assert x.shape == (5, 3000, 3), f"输入形状错误: {x.shape}"
    assert y.shape == (5,), f"标签形状错误: {y.shape}"
    
    print("✅ 序列数据集测试通过\n")


def test_sequential_model():
    """测试序列模型"""
    print("="*60)
    print("测试序列MAMBAFORMER模型...")
    
    # 创建模型
    model = SequentialMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        seq_len=5
    )
    
    # 测试输入
    batch_size = 2
    seq_len = 5
    time_steps = 3000
    channels = 3
    
    x = torch.randn(batch_size, seq_len, time_steps, channels)
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        main_output, aux_output = model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"主输出形状: {main_output.shape}")  # 应该是 (2, 5, 5)
    print(f"辅助输出形状: {aux_output.shape}")  # 应该是 (2, 5, 2)
    
    assert main_output.shape == (batch_size, seq_len, 5), f"主输出形状错误: {main_output.shape}"
    assert aux_output.shape == (batch_size, seq_len, 2), f"辅助输出形状错误: {aux_output.shape}"
    
    # 测试预测
    predictions = torch.argmax(main_output, dim=-1)
    print(f"预测形状: {predictions.shape}")
    print(f"预测示例: {predictions[0]}")
    
    print("✅ 序列模型测试通过\n")


def test_dataloader():
    """测试数据加载器"""
    print("="*60)
    print("测试数据加载器...")
    
    # 模拟fold信息
    fold_info = {
        'train_files': [
            "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4001E0.npz",
            "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4002E0.npz"
        ],
        'test_files': [
            "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4011E0.npz"
        ]
    }
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_sequence_dataloaders(
        fold_info,
        batch_size=4,
        seq_len=5,
        use_channels=3,
        max_samples_per_file=20
    )
    
    print(f"训练批次数: {len(train_loader)}")
    print(f"验证批次数: {len(val_loader)}")
    print(f"测试批次数: {len(test_loader)}")
    
    # 获取一个批次
    for x, y in train_loader:
        print(f"批次输入形状: {x.shape}")
        print(f"批次标签形状: {y.shape}")
        break
    
    print("✅ 数据加载器测试通过\n")


def test_memory_usage():
    """测试内存使用"""
    print("="*60)
    print("测试内存使用...")
    
    if torch.cuda.is_available():
        # 清空缓存
        torch.cuda.empty_cache()
        
        # 获取初始内存
        initial_memory = torch.cuda.memory_allocated() / 1024**2  # MB
        
        # 创建模型
        model = SequentialMAMBAFORMER().cuda()
        model_memory = torch.cuda.memory_allocated() / 1024**2 - initial_memory
        
        # 测试不同batch size
        for batch_size in [4, 8, 16]:
            torch.cuda.empty_cache()
            
            x = torch.randn(batch_size, 5, 3000, 3).cuda()
            y = torch.randint(0, 5, (batch_size, 5)).cuda()
            
            try:
                with torch.no_grad():
                    output, _ = model(x)
                
                memory_used = torch.cuda.memory_allocated() / 1024**2
                print(f"Batch size {batch_size}: {memory_used:.2f} MB")
                
            except RuntimeError as e:
                print(f"Batch size {batch_size}: 内存不足 - {e}")
        
        print(f"模型大小: {model_memory:.2f} MB")
    else:
        print("CUDA不可用，跳过内存测试")
    
    print()


def main():
    print("🧪 测试序列MAMBAFORMER实现")
    print("="*60)
    
    # 运行所有测试
    test_sequence_dataset()
    test_sequential_model()
    test_dataloader()
    test_memory_usage()
    
    print("="*60)
    print("✅ 所有测试通过！")
    print("\n现在可以运行训练脚本:")
    print("python training/train_sequential_mambaformer.py")


if __name__ == "__main__":
    main()