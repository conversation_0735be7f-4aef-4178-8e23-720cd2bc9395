#!/usr/bin/env python3
"""
使用数据增强和正则化技术训练MAMBAFORMER
解决过拟合问题，目标达到80%+准确率
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import logging
from datetime import datetime
import json
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import random

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet

def setup_logging(log_dir, experiment_name):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f'{experiment_name}_{timestamp}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__), log_file

class SleepEDFDatasetAugmented(Dataset):
    """Sleep-EDF数据集加载器 - 带数据增强"""
    def __init__(self, eeg_files, eog_files, label_files, 
                 mean_files=None, std_files=None,
                 mean_eog_files=None, std_eog_files=None,
                 use_normalization=True, augment=True):
        
        # 加载所有数据
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        self.means_eeg = []
        self.stds_eeg = []
        self.means_eog = []
        self.stds_eog = []
        
        for i, (eeg_f, eog_f, label_f) in enumerate(zip(eeg_files, eog_files, label_files)):
            if os.path.exists(eeg_f):
                with h5py.File(eeg_f, 'r') as f:
                    eeg = f['data'][:]
                    self.eeg_data.append(eeg)
                    
                with h5py.File(eog_f, 'r') as f:
                    eog = f['data'][:]
                    self.eog_data.append(eog)
                    
                with h5py.File(label_f, 'r') as f:
                    labels = f['data'][:]
                    self.labels.append(labels)
                
                # 加载归一化参数
                if mean_files and std_files:
                    with h5py.File(mean_files[i], 'r') as f:
                        mean_eeg = f['data'][:]
                        self.means_eeg.append(mean_eeg)
                        
                    with h5py.File(std_files[i], 'r') as f:
                        std_eeg = f['data'][:]
                        self.stds_eeg.append(std_eeg)
                        
                    with h5py.File(mean_eog_files[i], 'r') as f:
                        mean_eog = f['data'][:]
                        self.means_eog.append(mean_eog)
                        
                    with h5py.File(std_eog_files[i], 'r') as f:
                        std_eog = f['data'][:]
                        self.stds_eog.append(std_eog)
        
        # 合并所有数据
        self.eeg_data = np.concatenate(self.eeg_data, axis=0)
        self.eog_data = np.concatenate(self.eog_data, axis=0)
        self.labels = np.concatenate(self.labels, axis=0)
        
        if mean_files and std_files:
            self.means_eeg = np.concatenate(self.means_eeg, axis=0)
            self.stds_eeg = np.concatenate(self.stds_eeg, axis=0)
            self.means_eog = np.concatenate(self.means_eog, axis=0)
            self.stds_eog = np.concatenate(self.stds_eog, axis=0)
        
        self.use_normalization = use_normalization
        self.augment = augment
        
        print(f"加载数据集: {len(eeg_files)} subjects, {len(self.labels)} epochs")
        print(f"EEG形状: {self.eeg_data.shape}, EOG形状: {self.eog_data.shape}")
        print(f"标签分布: {np.bincount(self.labels.astype(int))}")
        
    def __len__(self):
        return len(self.labels)
    
    def augment_signal(self, signal):
        """数据增强"""
        if not self.augment or random.random() > 0.5:
            return signal
            
        # 随机选择增强方法
        aug_type = random.choice(['noise', 'scale', 'shift', 'time_mask'])
        
        if aug_type == 'noise':
            # 添加高斯噪声
            noise = np.random.normal(0, 0.01, signal.shape)
            signal = signal + noise
            
        elif aug_type == 'scale':
            # 随机缩放
            scale = random.uniform(0.8, 1.2)
            signal = signal * scale
            
        elif aug_type == 'shift':
            # 时间平移
            shift = random.randint(-50, 50)
            signal = np.roll(signal, shift)
            
        elif aug_type == 'time_mask':
            # 随机遮盖时间段
            mask_len = random.randint(10, 100)
            mask_start = random.randint(0, len(signal) - mask_len)
            signal[mask_start:mask_start+mask_len] = 0
            
        return signal
    
    def __getitem__(self, idx):
        eeg = self.eeg_data[idx].copy()
        eog = self.eog_data[idx].copy()
        label = self.labels[idx]
        
        # 标准化（使用原始方式）
        if self.use_normalization and hasattr(self, 'means_eeg'):
            # 每个样本使用自己的均值和标准差
            eeg = (eeg - self.means_eeg[idx]) / (self.stds_eeg[idx] + 1e-8)
            eog = (eog - self.means_eog[idx]) / (self.stds_eog[idx] + 1e-8)
        
        # 数据增强（仅训练时）
        if self.augment:
            eeg = self.augment_signal(eeg)
            eog = self.augment_signal(eog)
        
        # 转换为张量并添加通道维度
        eeg = torch.FloatTensor(eeg).unsqueeze(0)  # [1, seq_len]
        eog = torch.FloatTensor(eog).unsqueeze(0)  # [1, seq_len]
        label = torch.LongTensor([label]).squeeze()
        
        return eeg, eog, label

def mixup_data(x_eeg, x_eog, y, alpha=1.0):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x_eeg.size()[0]
    index = torch.randperm(batch_size).to(x_eeg.device)

    mixed_x_eeg = lam * x_eeg + (1 - lam) * x_eeg[index, :]
    mixed_x_eog = lam * x_eog + (1 - lam) * x_eog[index, :]
    y_a, y_b = y, y[index]
    
    return mixed_x_eeg, mixed_x_eog, y_a, y_b, lam

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    """Mixup损失函数"""
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def train_epoch(model, train_loader, optimizer, criterion, device, epoch, logger, use_mixup=True):
    """训练一个epoch - 带数据增强"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} [Train]')
    for batch_idx, (eeg_data, eog_data, labels) in enumerate(pbar):
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # Mixup增强
        if use_mixup and random.random() > 0.5:
            eeg_data, eog_data, labels_a, labels_b, lam = mixup_data(
                eeg_data, eog_data, labels, alpha=0.2
            )
            
            # 前向传播
            outputs = model(eeg_data, eog_data, stage="both")
            
            # 计算损失
            if 'fine_logits' in outputs:
                loss = mixup_criterion(criterion, outputs['fine_logits'], labels_a, labels_b, lam)
                predictions = outputs['fine_logits'].argmax(dim=1)
            else:
                loss = mixup_criterion(criterion, outputs['logits'], labels_a, labels_b, lam)
                predictions = outputs['logits'].argmax(dim=1)
                
            # 计算准确率（使用原始标签）
            correct += (lam * (predictions == labels_a).sum().item() + 
                       (1 - lam) * (predictions == labels_b).sum().item())
        else:
            # 正常训练
            outputs = model(eeg_data, eog_data, stage="both")
            
            if 'fine_logits' in outputs:
                loss = criterion(outputs['fine_logits'], labels)
                predictions = outputs['fine_logits'].argmax(dim=1)
            else:
                loss = criterion(outputs['logits'], labels)
                predictions = outputs['logits'].argmax(dim=1)
                
            correct += (predictions == labels).sum().item()
        
        # L2正则化
        l2_lambda = 0.001
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    avg_loss = total_loss / len(train_loader)
    accuracy = correct / total
    
    return avg_loss, accuracy

def validate(model, val_loader, criterion, device):
    """验证模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc='Validating')
        for eeg_data, eog_data, labels in pbar:
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(eeg_data, eog_data, stage="fine")
            
            # 计算损失
            if 'fine_logits' in outputs:
                loss = criterion(outputs['fine_logits'], labels)
                predictions = outputs['fine_logits'].argmax(dim=1)
            else:
                loss = criterion(outputs['logits'], labels)
                predictions = outputs['logits'].argmax(dim=1)
            
            total_loss += loss.item()
            all_preds.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def main():
    # 配置
    config = {
        'data_path': './processed_data_fixed',
        'train_subjects': list(range(1, 16)),  # 使用15个受试者训练
        'val_subjects': [16, 17, 18],          # 3个验证
        'test_subjects': [19, 20],             # 2个测试
        'batch_size': 32,
        'epochs': 300,
        'lr': 5e-4,
        'weight_decay': 1e-4,
        'patience': 50,
        'experiment_name': 'mambaformer_augmented',
        'log_dir': './log',
        'save_dir': './checkpoints',
        
        # 模型参数
        'd_model': 128,
        'dim_feedforward': 512,
        'window_size': 50,
        'num_layers': 4,
        'nhead': 8,
        'dropout': 0.3,  # 增加dropout
        
        # 加权损失
        'weights': [1., 2., 1., 2., 2.],  # W, N1, N2, N3, REM
        
        # 学习率调度器
        'step_size': 50,
        'gamma': 0.5,
        
        # 数据增强
        'use_mixup': True,
        'augment_train': True
    }
    
    # 创建目录
    os.makedirs(config['log_dir'], exist_ok=True)
    os.makedirs(config['save_dir'], exist_ok=True)
    
    # 设置日志
    logger, log_file = setup_logging(config['log_dir'], config['experiment_name'])
    logger.info("🧪 MAMBAFORMER Sleep Stage Classification - With Augmentation")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 准备数据文件
    def get_subject_files(subjects, data_path):
        eeg_files = []
        eog_files = []
        label_files = []
        mean_files = []
        std_files = []
        mean_eog_files = []
        std_eog_files = []
        
        for s in subjects:
            eeg_f = os.path.join(data_path, f'x{int(s):02d}.h5')
            if os.path.exists(eeg_f):
                eeg_files.append(eeg_f)
                eog_files.append(os.path.join(data_path, f'eog{int(s):02d}.h5'))
                label_files.append(os.path.join(data_path, f'y{int(s):02d}.h5'))
                mean_files.append(os.path.join(data_path, f'mean{int(s):02d}.h5'))
                std_files.append(os.path.join(data_path, f'std{int(s):02d}.h5'))
                mean_eog_files.append(os.path.join(data_path, f'eog_m{int(s):02d}.h5'))
                std_eog_files.append(os.path.join(data_path, f'eog_s{int(s):02d}.h5'))
        
        return eeg_files, eog_files, label_files, mean_files, std_files, mean_eog_files, std_eog_files
    
    # 创建数据集
    train_files = get_subject_files(config['train_subjects'], config['data_path'])
    val_files = get_subject_files(config['val_subjects'], config['data_path'])
    test_files = get_subject_files(config['test_subjects'], config['data_path'])
    
    train_dataset = SleepEDFDatasetAugmented(*train_files, use_normalization=True, 
                                             augment=config['augment_train'])
    val_dataset = SleepEDFDatasetAugmented(*val_files, use_normalization=True, 
                                          augment=False)
    test_dataset = SleepEDFDatasetAugmented(*test_files, use_normalization=True, 
                                           augment=False)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], 
                             shuffle=True, num_workers=4, pin_memory=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], 
                           shuffle=False, num_workers=4, pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], 
                            shuffle=False, num_workers=4, pin_memory=True)
    
    logger.info(f"训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    logger.info(f"验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    logger.info(f"测试集: {len(test_dataset)} 样本, {len(test_loader)} 批次")
    
    # 创建模型
    model = MambaFormerSleepNet(
        d_model=config['d_model'],
        num_mambaformer_layers=config['num_layers'],
        nhead=config['nhead'],
        window_size=config['window_size'],
        use_progressive=True,
        use_adaptive_mambaformer=True,
        dropout=config['dropout'],
        device=device
    ).to(device)
    
    logger.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数（使用加权交叉熵）
    weights = torch.tensor(config['weights']).to(device)
    criterion = nn.CrossEntropyLoss(weight=weights)
    
    # 优化器
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=config['lr'],
        betas=(0.9, 0.999),
        eps=1e-9,
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer,
        T_max=config['epochs'],
        eta_min=1e-6
    )
    
    # 训练循环
    best_val_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['epochs'] + 1):
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, criterion, device, epoch, logger,
            use_mixup=config['use_mixup']
        )
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, criterion, device)
        
        # 更新学习率
        scheduler.step()
        
        logger.info(f"Epoch {epoch}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} | "
                   f"LR: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'config': config
            }
            checkpoint_path = os.path.join(config['save_dir'], 
                                         f"{config['experiment_name']}_best.pth")
            torch.save(checkpoint, checkpoint_path)
            logger.info(f"✓ 新的最佳验证准确率: {val_acc:.4f}")
            patience_counter = 0
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logger.info("早停")
            break
        
        # 定期保存
        if epoch % 50 == 0:
            checkpoint_path = os.path.join(config['save_dir'], 
                                         f"{config['experiment_name']}_epoch{epoch}.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_acc': val_acc
            }, checkpoint_path)
    
    logger.info(f"训练完成，最佳验证准确率: {best_val_acc:.4f}")
    
    # 测试评估
    logger.info("\n📊 测试集评估")
    
    # 加载最佳模型
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, criterion, device)
    logger.info(f"测试准确率: {test_acc:.4f}")
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, 
                                 target_names=class_names)
    logger.info(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    # 保存结果
    results = {
        'best_val_acc': best_val_acc,
        'test_acc': test_acc,
        'config': config,
        'classification_report': report,
        'confusion_matrix': cm.tolist()
    }
    
    result_file = os.path.join(config['log_dir'], 
                              f"{config['experiment_name']}_results.json")
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ 实验完成！结果保存在: {result_file}")

if __name__ == "__main__":
    main()