"""
序列MAMBAFORMER V5训练脚本 - 正确的数据集划分版本
修复：按被试级别划分训练/验证/测试集，避免数据泄露
策略：基于V2架构（最佳性能），使用正确的三集划分
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import (
    SequentialMAMBAFORMER_V2,
    SequentialFocalLoss,
    TemporalConsistencyLoss,
    MildDataAugmentation
)
from utils.sequence_dataset import create_sequence_dataloaders, SequenceSleepDataset
from utils.enhanced_metrics import get_comprehensive_metrics, log_detailed_metrics


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"sequential_v5_correct_split_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


def create_auxiliary_labels(labels):
    """创建辅助任务标签 - 检测REM和N3"""
    aux_labels = ((labels == 4) | (labels == 3)).long()
    return aux_labels


def train_epoch_v5(model, train_loader, criterion, temp_loss_fn, aux_criterion,
                   optimizer, device, epoch, config, data_aug):
    """V5训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device)
        labels = labels.to(device)
        
        # 数据增强
        data, labels = data_aug(data, labels)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_output, aux_output = model(data)
        
        # 损失计算
        main_loss = criterion(main_output, labels)
        
        # 辅助损失
        aux_labels = create_auxiliary_labels(labels)
        aux_loss = aux_criterion(
            aux_output.view(-1, 2), 
            aux_labels.view(-1)
        )
        
        # 时序一致性损失
        temp_loss = temp_loss_fn(main_output)
        
        # 总损失
        loss = main_loss + 0.2 * aux_loss + temp_loss
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1


def evaluate_v5(model, data_loader, criterion, aux_criterion, device, phase='Val'):
    """V5评估函数"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    seq_preds = []
    seq_labels = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc=f'{phase}')
        
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            main_output, aux_output = model(data)
            
            # 损失计算
            main_loss = criterion(main_output, labels)
            aux_labels = create_auxiliary_labels(labels)
            aux_loss = aux_criterion(
                aux_output.view(-1, 2),
                aux_labels.view(-1)
            )
            loss = main_loss + 0.2 * aux_loss
            
            total_loss += loss.item()
            
            # 收集预测
            preds = torch.argmax(main_output, dim=-1)
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            seq_preds.append(preds.cpu().numpy())
            seq_labels.append(labels.cpu().numpy())
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    # 位置准确率
    seq_preds = np.concatenate(seq_preds, axis=0)
    seq_labels = np.concatenate(seq_labels, axis=0)
    
    position_acc = []
    for pos in range(seq_preds.shape[1]):
        pos_acc = accuracy_score(seq_labels[:, pos], seq_preds[:, pos])
        position_acc.append(pos_acc)
    
    avg_loss = total_loss / len(data_loader)
    
    # 计算综合指标
    metrics = get_comprehensive_metrics(
        np.array(all_labels), 
        np.array(all_preds), 
        n_classes=5
    )
    
    # 详细分析
    if phase == 'Test':
        report = classification_report(all_labels, all_preds, 
                                     target_names=['Wake', 'N1', 'N2', 'N3', 'REM'],
                                     output_dict=True)
        cm = confusion_matrix(all_labels, all_preds)
        return avg_loss, acc, f1, position_acc, report, cm, metrics
    
    return avg_loss, acc, f1, position_acc, metrics


def load_correct_split_data(split_config_path, data_dir):
    """加载正确划分的数据集"""
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    train_files = []
    val_files = []
    test_files = []
    
    # 构建完整路径
    for file in splits['splits']['train']['files']:
        train_files.append(os.path.join(data_dir, file))
    
    for file in splits['splits']['val']['files']:
        val_files.append(os.path.join(data_dir, file))
        
    for file in splits['splits']['test']['files']:
        test_files.append(os.path.join(data_dir, file))
    
    return train_files, val_files, test_files


def train_v5_correct_split(config, device):
    """V5训练主函数 - 使用正确的数据划分"""
    logging.info("\n" + "="*60)
    logging.info("🚀 开始训练 Sequential MAMBAFORMER V5 - 正确数据集划分版本")
    logging.info("📋 修复数据泄露：按被试级别划分，同一被试不会同时出现在不同集合")
    
    # 加载正确划分的数据
    split_config_path = '../../configs/subject_aware_splits.json'
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files, val_files, test_files = load_correct_split_data(split_config_path, data_dir)
    
    logging.info(f"📊 数据分布（按被试划分）:")
    logging.info(f"  训练集: {len(train_files)} 个文件 (14个被试)")
    logging.info(f"  验证集: {len(val_files)} 个文件 (2个被试)")
    logging.info(f"  测试集: {len(test_files)} 个文件 (4个被试)")
    
    # 创建数据集
    train_dataset = SequenceSleepDataset(
        train_files,
        max_samples_per_file=config['max_samples_per_file'],
        seq_len=config['seq_len'],
        use_channels=3  # 使用V2的3通道配置
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        max_samples_per_file=config['max_samples_per_file'],
        seq_len=config['seq_len'],
        use_channels=3
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        max_samples_per_file=config['max_samples_per_file'],
        seq_len=config['seq_len'],
        use_channels=3
    )
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=config['batch_size'],
        shuffle=True, 
        num_workers=4, 
        pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    logging.info(f"📊 数据集大小:")
    logging.info(f"  训练样本数: {len(train_dataset)}")
    logging.info(f"  验证样本数: {len(val_dataset)}")
    logging.info(f"  测试样本数: {len(test_dataset)}")
    logging.info(f"  训练批次数: {len(train_loader)}")
    logging.info(f"  验证批次数: {len(val_loader)}")
    logging.info(f"  测试批次数: {len(test_loader)}")
    
    # 创建V5模型 - 基于V2架构
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'], 
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 损失函数
    criterion = SequentialFocalLoss(alpha=1, gamma=2)
    aux_criterion = nn.CrossEntropyLoss()
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    # 数据增强
    data_aug = MildDataAugmentation(p=0.5)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 3,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos'
    )
    
    # 训练循环
    best_val_f1 = 0
    best_val_kappa = 0
    patience_counter = 0
    best_model_state = None
    train_losses = []
    val_losses = []
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch_v5(
            model, train_loader, criterion, temp_loss_fn, aux_criterion,
            optimizer, device, epoch, config, data_aug
        )
        
        # 验证
        val_loss, val_acc, val_f1, val_pos_acc, val_metrics = evaluate_v5(
            model, val_loader, criterion, aux_criterion, device, 'Val'
        )
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step()
        
        # 记录 - 包含Kappa和Macro-F1
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, F1: {train_f1:.4f}")
        logging.info(f"Val   - Loss: {val_loss:.4f}, Acc: {val_acc:.4f}, F1: {val_f1:.4f}, Kappa: {val_metrics['kappa']:.4f}, Macro-F1: {val_metrics['macro_f1']:.4f}")
        logging.info(f"Val Position Acc: {[f'{acc:.4f}' for acc in val_pos_acc]}")
        logging.info(f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # 过拟合监控
        if len(train_losses) > 3:
            recent_train_loss = np.mean(train_losses[-3:])
            recent_val_loss = np.mean(val_losses[-3:])
            gap = recent_val_loss - recent_train_loss
            if gap > 0.15:
                logging.warning(f"⚠️  过拟合警告: Gap = {gap:.4f}")
        
        # 保存最佳模型 - 同时考虑F1和Kappa
        if val_f1 > best_val_f1 or (val_f1 == best_val_f1 and val_metrics['kappa'] > best_val_kappa):
            best_val_f1 = val_f1
            best_val_kappa = val_metrics['kappa']
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳模型: Val F1={best_val_f1:.4f}, Kappa={best_val_kappa:.4f}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: 验证指标已{config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
        logging.info("✅ 已加载最佳模型进行测试")
    
    test_loss, test_acc, test_f1, test_pos_acc, report, cm, test_metrics = evaluate_v5(
        model, test_loader, criterion, aux_criterion, device, 'Test'
    )
    
    # 详细测试结果输出
    logging.info(f"\n🎯 Sequential V5 - 正确数据集划分 - 完整测试结果:")
    logging.info(f"="*60)
    logging.info(f"测试准确率: {test_acc:.4f}")
    logging.info(f"测试F1分数: {test_f1:.4f}")
    logging.info(f"测试Kappa系数: {test_metrics['kappa']:.4f}")
    logging.info(f"测试Macro-F1: {test_metrics['macro_f1']:.4f}")
    logging.info(f"测试G-Mean: {test_metrics['g_mean']:.4f}")
    logging.info(f"最佳验证F1: {best_val_f1:.4f}")
    logging.info(f"最佳验证Kappa: {best_val_kappa:.4f}")
    logging.info(f"验证-测试F1差距: {best_val_f1 - test_f1:.4f}")
    logging.info(f"各位置准确率: {[f'{acc:.4f}' for acc in test_pos_acc]}")
    
    # 记录详细的每类指标
    log_detailed_metrics(test_metrics, phase='Test', logger=logging)
    
    logging.info(f"\n📊 详细分类报告:")
    for class_name, metrics in report.items():
        if isinstance(metrics, dict) and class_name not in ['accuracy', 'macro avg', 'weighted avg']:
            logging.info(f"{class_name:>5}: Prec={metrics['precision']:.3f}, "
                        f"Recall={metrics['recall']:.3f}, F1={metrics['f1-score']:.3f}, "
                        f"Support={metrics['support']}")
    
    logging.info(f"Macro Avg: Prec={report['macro avg']['precision']:.3f}, "
                f"Recall={report['macro avg']['recall']:.3f}, F1={report['macro avg']['f1-score']:.3f}")
    
    logging.info(f"\n🔄 混淆矩阵:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("     " + " ".join(f"{name:>6}" for name in class_names))
    for i, row in enumerate(cm):
        logging.info(f"{class_names[i]:>4} " + " ".join(f"{val:>6}" for val in row))
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), '../../checkpoints/sequential_v5_correct_split.pth')
    
    return {
        'test_acc': test_acc,
        'test_f1': test_f1,
        'test_kappa': test_metrics['kappa'],
        'test_macro_f1': test_metrics['macro_f1'],
        'val_f1': best_val_f1,
        'val_kappa': best_val_kappa,
        'val_test_gap': best_val_f1 - test_f1,
        'confusion_matrix': cm.tolist(),
        'classification_report': report
    }


def main():
    # 配置 - 基于V2的成功经验
    config = {
        'batch_size': 32,
        'seq_len': 5,
        'learning_rate': 2e-5,
        'weight_decay': 1e-4,
        'num_epochs': 50,
        'patience': 8,
        'max_samples_per_file': 150,
        'd_model': 128,
        'n_heads': 8,
        'n_layers': 4,
        'dropout': 0.15,
        'temp_loss_weight': 0.1
    }
    
    log_file = setup_logging()
    
    logging.info("📈 序列MAMBAFORMER V5训练 - 正确数据集划分版本")
    logging.info("=" * 60)
    logging.info("🎯 V5策略:")
    logging.info("  • 修复数据泄露：按被试级别划分训练/验证/测试集")
    logging.info("  • 基于V2架构（最佳性能）")
    logging.info("  • 训练集: 14个被试 (70%)")
    logging.info("  • 验证集: 2个被试 (10%)")
    logging.info("  • 测试集: 4个被试 (20%)")
    logging.info("  • 同一被试的所有数据在同一集合中")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 训练
    result = train_v5_correct_split(config, device)
    
    # 汇总结果
    logging.info("\n" + "="*60)
    logging.info("🏆 序列MAMBAFORMER V5训练完成")
    logging.info(f"📈 测试准确率: {result['test_acc']:.4f}")
    logging.info(f"📈 测试F1分数: {result['test_f1']:.4f}")
    logging.info(f"📈 测试Kappa: {result['test_kappa']:.4f}")
    logging.info(f"📈 测试Macro-F1: {result['test_macro_f1']:.4f}")
    logging.info(f"📊 验证-测试F1差距: {result['val_test_gap']:.4f}")
    logging.info("\n📋 关键改进:")
    logging.info("  ✅ 修复了数据泄露问题")
    logging.info("  ✅ 使用固定的被试级别划分")
    logging.info("  ✅ 所有后续实验应使用相同的划分")
    
    # 保存结果
    results = {
        'config': config,
        'result': result,
        'log_file': log_file,
        'data_split': '../../configs/subject_aware_splits.json'
    }
    
    with open('../../configs/sequential_v5_correct_split_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"💾 结果已保存至: ../../configs/sequential_v5_correct_split_results.json")


if __name__ == "__main__":
    main()