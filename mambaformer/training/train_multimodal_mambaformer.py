"""
多模态MAMBAFORMER训练脚本
Phase 2: 4通道EEG+EOG融合
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.multimodal_mambaformer import MultiModalSequentialMAMBAFORMER
from models.sequential_mambaformer import SequentialFocalLoss, TemporalConsistencyLoss
from utils.sequence_dataset import create_sequence_dataloaders


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"multimodal_mambaformer_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


def create_auxiliary_labels(labels):
    """创建辅助任务标签"""
    # REM=4, SWS(N3)=3 -> 1, 其他 -> 0
    aux_labels = ((labels == 4) | (labels == 3)).long()
    return aux_labels


def train_epoch(model, train_loader, criterion, temp_loss_fn,
                optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        # data: (batch, seq_len, time_steps, channels)
        data = data.to(device)
        labels = labels.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        main_output, aux_output = model(data)
        
        # 计算损失
        main_loss = criterion(main_output, labels)
        
        # 辅助任务损失
        aux_labels = create_auxiliary_labels(labels)
        aux_loss = F.cross_entropy(
            aux_output.view(-1, 2), 
            aux_labels.view(-1)
        )
        
        # 时序一致性损失
        temp_loss = temp_loss_fn(main_output)
        
        # 总损失
        loss = main_loss + 0.2 * aux_loss + temp_loss
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        # 更新进度条
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    # 计算指标
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1


def evaluate(model, data_loader, criterion, device, phase='Val'):
    """评估模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    # 序列级别的预测
    seq_preds = []
    seq_labels = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc=f'{phase}')
        
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            main_output, aux_output = model(data)
            
            # 计算损失
            main_loss = criterion(main_output, labels)
            aux_labels = create_auxiliary_labels(labels)
            aux_loss = F.cross_entropy(
                aux_output.view(-1, 2),
                aux_labels.view(-1)
            )
            loss = main_loss + 0.2 * aux_loss
            
            total_loss += loss.item()
            
            # 收集预测
            preds = torch.argmax(main_output, dim=-1)
            
            # 逐epoch预测
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            # 序列级别预测
            seq_preds.append(preds.cpu().numpy())
            seq_labels.append(labels.cpu().numpy())
    
    # 计算逐epoch指标
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    # 计算每个位置的准确率
    seq_preds = np.concatenate(seq_preds, axis=0)
    seq_labels = np.concatenate(seq_labels, axis=0)
    
    position_acc = []
    for pos in range(seq_preds.shape[1]):
        pos_acc = accuracy_score(seq_labels[:, pos], seq_preds[:, pos])
        position_acc.append(pos_acc)
    
    avg_loss = total_loss / len(data_loader)
    
    return avg_loss, acc, f1, position_acc


def train_fold(fold_id, fold_info, config, device):
    """训练单个fold"""
    logging.info(f"\n{'='*60}")
    logging.info(f"开始训练 Fold {fold_id + 1}")
    logging.info(f"训练文件数: {len(fold_info['train_files'])}")
    logging.info(f"测试文件数: {len(fold_info['test_files'])}")
    
    # 创建数据加载器 - 使用4通道
    train_loader, val_loader, test_loader = create_sequence_dataloaders(
        fold_info,
        batch_size=config['batch_size'],
        seq_len=config['seq_len'],
        use_channels=4,  # 使用全部4通道
        max_samples_per_file=config['max_samples_per_file']
    )
    
    logging.info(f"训练批次数: {len(train_loader)}")
    logging.info(f"验证批次数: {len(val_loader)}")
    logging.info(f"测试批次数: {len(test_loader)}")
    
    # 创建多模态模型
    model = MultiModalSequentialMAMBAFORMER(
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 损失函数
    criterion = SequentialFocalLoss(alpha=1, gamma=2)
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 10,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos'
    )
    
    # 训练循环
    best_val_f1 = 0
    patience_counter = 0
    best_model_state = None
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, temp_loss_fn,
            optimizer, device, epoch
        )
        
        # 验证
        val_loss, val_acc, val_f1, val_pos_acc = evaluate(
            model, val_loader, criterion, device, 'Val'
        )
        
        # 更新学习率
        scheduler.step()
        
        # 记录
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, F1: {train_f1:.4f}")
        logging.info(f"Val   - Loss: {val_loss:.4f}, Acc: {val_acc:.4f}, F1: {val_f1:.4f}")
        logging.info(f"Val Position Acc: {[f'{acc:.4f}' for acc in val_pos_acc]}")
        
        # 保存最佳模型
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳Val F1: {best_val_f1:.4f}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"早停: 验证F1已{config['patience']}轮未改善")
            break
    
    # 加载最佳模型
    if best_model_state:
        model.load_state_dict(best_model_state)
    
    # 测试评估
    test_loss, test_acc, test_f1, test_pos_acc = evaluate(
        model, test_loader, criterion, device, 'Test'
    )
    
    # 详细评估
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data, labels in test_loader:
            data = data.to(device)
            main_output, _ = model(data)
            preds = torch.argmax(main_output, dim=-1)
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
    
    # 分类报告
    report = classification_report(all_labels, all_preds, 
                                 target_names=['Wake', 'N1', 'N2', 'N3', 'REM'])
    cm = confusion_matrix(all_labels, all_preds)
    
    logging.info(f"\n🎯 Fold {fold_id + 1} 测试结果:")
    logging.info(f"测试准确率: {test_acc:.4f}")
    logging.info(f"测试F1分数: {test_f1:.4f}")
    logging.info(f"各位置准确率: {[f'{acc:.4f}' for acc in test_pos_acc]}")
    logging.info(f"\n分类报告:\n{report}")
    logging.info(f"\n混淆矩阵:\n{cm}")
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), f'../../checkpoints/multimodal_fold_{fold_id}.pth')
    
    return {
        'fold_id': fold_id,
        'test_acc': test_acc,
        'test_f1': test_f1,
        'position_acc': test_pos_acc,
        'val_f1': best_val_f1,
        'confusion_matrix': cm.tolist(),
        'classification_report': report
    }


def main():
    # 配置
    config = {
        'batch_size': 8,  # 保持较小batch size
        'seq_len': 5,     # 序列长度
        'learning_rate': 3e-5,
        'weight_decay': 1e-4,
        'num_epochs': 50,
        'patience': 10,
        'max_samples_per_file': 150,
        'd_model': 128,
        'n_heads': 8,
        'n_layers': 4,
        'dropout': 0.15,
        'temp_loss_weight': 0.1
    }
    
    # 设置日志
    log_file = setup_logging()
    
    logging.info("多模态MAMBAFORMER训练 - Phase 2")
    logging.info("=" * 60)
    logging.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 加载K折配置
    with open('../../configs/subject_aware_folds.json', 'r') as f:
        fold_data = json.load(f)
    
    # 训练所有fold
    all_results = []
    
    for fold_id, fold_info in enumerate(fold_data['folds'].values()):
        if fold_id >= 1:  # 先只训练第一个fold测试
            break
        
        result = train_fold(fold_id, fold_info, config, device)
        all_results.append(result)
    
    # 汇总结果
    avg_acc = np.mean([r['test_acc'] for r in all_results])
    std_acc = np.std([r['test_acc'] for r in all_results])
    avg_f1 = np.mean([r['test_f1'] for r in all_results])
    std_f1 = np.std([r['test_f1'] for r in all_results])
    
    logging.info("\n" + "="*60)
    logging.info("🏆 多模态MAMBAFORMER训练完成")
    logging.info(f"平均测试准确率: {avg_acc:.4f} ± {std_acc:.4f}")
    logging.info(f"平均测试F1分数: {avg_f1:.4f} ± {std_f1:.4f}")
    
    # 保存结果
    results = {
        'config': config,
        'fold_results': all_results,
        'summary': {
            'mean_accuracy': avg_acc,
            'std_accuracy': std_acc,
            'mean_f1_score': avg_f1,
            'std_f1_score': std_f1,
            'log_file': log_file
        }
    }
    
    with open('../../configs/multimodal_mambaformer_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"结果已保存至: ../../configs/multimodal_mambaformer_results.json")


if __name__ == "__main__":
    main()