#!/usr/bin/env python3
"""
V14_Progressive_Step2: 添加渐进式分类
创新点: 粗分类(3类) → 细分类(5类)
目标: 突破87%准确率
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


class ProgressiveMAMBAFORMER(nn.Module):
    """渐进式分类的MAMBAFORMER"""
    
    def __init__(self, config):
        super().__init__()
        
        # 基础MAMBAFORMER编码器
        self.encoder = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,  # 内部仍然是5类，但我们会替换分类头
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 移除原始分类头，使用自定义的
        self.encoder.classifier = nn.Identity()
        
        # 粗分类头 (3类: Wake, NREM, REM)
        self.coarse_classifier = nn.Sequential(
            nn.Linear(config['d_model'], 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 3)
        )
        
        # 细分类头 (5类完整分类)
        self.fine_classifier = nn.Sequential(
            nn.Linear(config['d_model'], 256),
            nn.ReLU(),
            nn.Dropout(0.15),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 5)
        )
        
        # 不确定性阈值（可学习或固定）
        self.uncertainty_threshold = 0.3
        self.training_mode = True
        
    def compute_uncertainty(self, logits):
        """计算预测的不确定性（使用熵）"""
        probs = F.softmax(logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        # 归一化熵到[0,1]
        max_entropy = torch.log(torch.tensor(logits.shape[-1], dtype=torch.float32))
        normalized_entropy = entropy / max_entropy
        return normalized_entropy
    
    def forward(self, x, return_all=False):
        # 获取特征表示
        features, _ = self.encoder(x)
        
        # 如果是3D输出，取中间时间步
        if features.dim() == 3:
            features = features[:, features.shape[1]//2, :]
        
        # 粗分类
        coarse_logits = self.coarse_classifier(features)
        
        # 计算不确定性
        uncertainty = self.compute_uncertainty(coarse_logits)
        
        # 细分类
        fine_logits = self.fine_classifier(features)
        
        if self.training or return_all:
            # 训练时返回所有输出
            return coarse_logits, fine_logits, uncertainty
        else:
            # 推理时的渐进式决策
            return self.progressive_inference(coarse_logits, fine_logits, uncertainty)
    
    def progressive_inference(self, coarse_logits, fine_logits, uncertainty):
        """渐进式推理：根据不确定性决定是否需要细分类"""
        batch_size = coarse_logits.shape[0]
        final_predictions = torch.zeros(batch_size, 5).to(coarse_logits.device)
        
        # 粗分类预测
        coarse_preds = torch.argmax(coarse_logits, dim=1)
        
        for i in range(batch_size):
            if uncertainty[i] < self.uncertainty_threshold:
                # 低不确定性：使用粗分类映射
                if coarse_preds[i] == 0:  # Wake
                    final_predictions[i, 0] = 1.0
                elif coarse_preds[i] == 1:  # NREM
                    # 使用细分类器的N1,N2,N3概率分布
                    nrem_probs = F.softmax(fine_logits[i, 1:4], dim=0)
                    final_predictions[i, 1:4] = nrem_probs
                else:  # REM
                    final_predictions[i, 4] = 1.0
            else:
                # 高不确定性：直接使用细分类
                final_predictions[i] = F.softmax(fine_logits[i], dim=0)
        
        return final_predictions


class ProgressiveLoss(nn.Module):
    """渐进式分类的损失函数"""
    
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 粗分类的类权重
        self.coarse_weights = torch.tensor([2.0, 1.0, 1.5]).to(device)  # Wake, NREM, REM
        
        # 细分类的类权重（继承V14的配置）
        self.fine_weights = torch.tensor([3.0, 1.5, 1.0, 1.0, 2.5]).to(device)
        
        # 损失权重
        self.coarse_weight = 0.3  # 粗分类损失权重
        self.fine_weight = 0.7    # 细分类损失权重
        
    def forward(self, coarse_logits, fine_logits, targets):
        """
        Args:
            coarse_logits: (batch_size, 3) 粗分类输出
            fine_logits: (batch_size, 5) 细分类输出
            targets: (batch_size,) 真实标签 (0-4)
        """
        # 将5类标签映射到3类
        coarse_targets = self.map_to_coarse(targets)
        
        # 粗分类损失 (Focal Loss)
        coarse_ce = F.cross_entropy(coarse_logits, coarse_targets, reduction='none')
        coarse_pt = torch.exp(-coarse_ce)
        coarse_focal = (1 - coarse_pt) ** 2.0 * coarse_ce
        coarse_loss = (coarse_focal * self.coarse_weights[coarse_targets]).mean()
        
        # 细分类损失 (Focal Loss)
        fine_ce = F.cross_entropy(fine_logits, targets, reduction='none')
        fine_pt = torch.exp(-fine_ce)
        fine_focal = (1 - fine_pt) ** 2.0 * fine_ce
        fine_loss = (fine_focal * self.fine_weights[targets]).mean()
        
        # 组合损失
        total_loss = self.coarse_weight * coarse_loss + self.fine_weight * fine_loss
        
        return total_loss, coarse_loss, fine_loss
    
    def map_to_coarse(self, targets):
        """将5类标签映射到3类"""
        # 0: Wake -> 0
        # 1,2,3: N1,N2,N3 -> 1 (NREM)
        # 4: REM -> 2
        coarse = torch.zeros_like(targets)
        coarse[targets == 0] = 0  # Wake
        coarse[(targets >= 1) & (targets <= 3)] = 1  # NREM
        coarse[targets == 4] = 2  # REM
        return coarse


def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_progressive_step2_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Progressive - Step 2: Progressive Classification")
    logging.info("="*80)
    
    return log_dir


def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    model.training_mode = True
    
    total_loss = 0
    total_coarse_loss = 0
    total_fine_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for data, target in pbar:
        data, target = data.to(device), target.to(device)
        
        # 处理序列标签
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        optimizer.zero_grad()
        
        # 前向传播
        coarse_logits, fine_logits, uncertainty = model(data, return_all=True)
        
        # 计算损失
        loss, coarse_loss, fine_loss = criterion(coarse_logits, fine_logits, target)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += loss.item()
        total_coarse_loss += coarse_loss.item()
        total_fine_loss += fine_loss.item()
        
        # 收集预测（使用细分类输出作为最终预测）
        preds = fine_logits.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        pbar.set_postfix({
            'loss': loss.item(),
            'coarse': coarse_loss.item(),
            'fine': fine_loss.item()
        })
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    avg_loss = total_loss / len(train_loader)
    avg_coarse = total_coarse_loss / len(train_loader)
    avg_fine = total_fine_loss / len(train_loader)
    
    return avg_loss, avg_coarse, avg_fine, accuracy, f1


def evaluate(model, data_loader, device, use_progressive=True):
    model.eval()
    model.training_mode = False
    
    all_preds = []
    all_targets = []
    all_uncertainties = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            if use_progressive:
                # 使用渐进式推理
                coarse_logits, fine_logits, uncertainty = model(data, return_all=True)
                predictions = model.progressive_inference(coarse_logits, fine_logits, uncertainty)
                preds = predictions.argmax(dim=1)
                all_uncertainties.extend(uncertainty.cpu().numpy())
            else:
                # 直接使用细分类
                _, fine_logits, _ = model(data, return_all=True)
                preds = fine_logits.argmax(dim=1)
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class F1
    class_f1 = f1_score(all_targets, all_preds, average=None)
    
    # 计算使用粗分类的比例
    if all_uncertainties:
        low_uncertainty_ratio = np.mean(np.array(all_uncertainties) < model.uncertainty_threshold)
    else:
        low_uncertainty_ratio = 0
    
    return accuracy, f1, kappa, class_f1, low_uncertainty_ratio


def main():
    # 配置
    config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 1.5e-4,  # 稍微降低学习率
        'weight_decay': 1e-4,
        'num_epochs': 40,  # 增加epoch数
        'patience': 12
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4
    )
    
    # 创建模型
    model = ProgressiveMAMBAFORMER(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 尝试加载Step1的预训练权重
    pretrained_path = '../logs/v14_progressive_*/best_model_baseline.pth'
    import glob
    pretrained_files = glob.glob(pretrained_path)
    if pretrained_files:
        logging.info(f"Loading pretrained weights from {pretrained_files[0]}")
        checkpoint = torch.load(pretrained_files[0], map_location=device, weights_only=False)
        # 只加载encoder部分
        encoder_state = {k.replace('encoder.', ''): v 
                        for k, v in checkpoint['model_state_dict'].items() 
                        if 'encoder' in k or not k.startswith('classifier')}
        model.encoder.load_state_dict(encoder_state, strict=False)
        logging.info("✅ Loaded encoder weights from baseline")
    
    # 损失函数和优化器
    criterion = ProgressiveLoss(device)
    optimizer = optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2, eta_min=1e-6)
    
    # 训练
    best_val_acc = 0
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("\n🎯 Starting Progressive Training...")
    logging.info("Innovation: Coarse (3-class) → Fine (5-class) classification")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, coarse_loss, fine_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        
        # 验证（使用渐进式推理）
        val_acc, val_f1, val_kappa, val_class_f1, val_coarse_ratio = evaluate(
            model, val_loader, device, use_progressive=True
        )
        
        # 测试（同时测试渐进式和直接细分类）
        test_acc_prog, test_f1_prog, test_kappa_prog, test_class_f1_prog, test_coarse_ratio = evaluate(
            model, test_loader, device, use_progressive=True
        )
        
        test_acc_direct, test_f1_direct, test_kappa_direct, test_class_f1_direct, _ = evaluate(
            model, test_loader, device, use_progressive=False
        )
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Train: Loss={train_loss:.4f} (C:{coarse_loss:.4f}, F:{fine_loss:.4f}), "
                    f"Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Coarse Usage: {val_coarse_ratio:.1%}")
        logging.info(f"  Test (Progressive): Acc={test_acc_prog:.4f}, F1={test_f1_prog:.4f}, Kappa={test_kappa_prog:.4f}")
        logging.info(f"  Test (Direct): Acc={test_acc_direct:.4f}, F1={test_f1_direct:.4f}, Kappa={test_kappa_direct:.4f}")
        logging.info(f"  Test Coarse Usage: {test_coarse_ratio:.1%}")
        logging.info(f"  Test Class F1: W={test_class_f1_prog[0]:.3f}, N1={test_class_f1_prog[1]:.3f}, "
                    f"N2={test_class_f1_prog[2]:.3f}, N3={test_class_f1_prog[3]:.3f}, REM={test_class_f1_prog[4]:.3f}")
        
        # 选择更好的测试结果
        test_acc = max(test_acc_prog, test_acc_direct)
        test_f1 = max(test_f1_prog, test_f1_direct)
        test_kappa = max(test_kappa_prog, test_kappa_direct)
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1_prog.tolist(),
                'progressive_acc': test_acc_prog,
                'direct_acc': test_acc_direct,
                'coarse_usage': test_coarse_ratio
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model_progressive.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎉 ACHIEVED 87% TARGET!")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (Progressive Classification)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Progressive: {best_test_metrics['progressive_acc']:.4f}")
    logging.info(f"Direct: {best_test_metrics['direct_acc']:.4f}")
    logging.info(f"Coarse Usage: {best_test_metrics['coarse_usage']:.1%}")
    
    if best_test_acc >= 0.87:
        logging.info("✅ SUCCESS: Achieved 87% target!")
    else:
        gap = 0.87 - best_test_acc
        logging.info(f"Gap to 87%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results_progressive.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)
    
    logging.info(f"\n✅ Step 2 Complete: Progressive classification implemented")
    logging.info("Ready for Step 3: Adding uncertainty calibration if needed")

if __name__ == "__main__":
    main()