#!/usr/bin/env python3
"""
V14 Ultimate - 极限优化版本，目标90%
基于V14成功经验，整合所有有效策略：
1. 更深的网络（10层）
2. 更优的损失函数
3. 标签平滑
4. 高级数据增强
5. 集成学习思想（多头输出）
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_ultimate_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Ultimate - Target: 90% Accuracy")
    logging.info("="*80)
    
    return log_dir

class AdaptiveFocalLoss(nn.Module):
    """自适应Focal Loss with label smoothing"""
    def __init__(self, device='cuda', gamma=2.0, label_smoothing=0.1):
        super().__init__()
        self.device = device
        self.gamma = gamma
        self.label_smoothing = label_smoothing
        
        # 基于历史表现的权重
        self.class_weights = torch.tensor([4.0, 3.5, 1.0, 1.0, 3.0]).to(device)
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            inputs = inputs.reshape(-1, inputs.shape[-1])
            targets = targets.reshape(-1)
        
        n_classes = inputs.size(1)
        
        # 标签平滑
        with torch.no_grad():
            targets_one_hot = torch.zeros_like(inputs).scatter_(1, targets.unsqueeze(1), 1)
            targets_one_hot = targets_one_hot * (1 - self.label_smoothing) + self.label_smoothing / n_classes
        
        # 计算交叉熵
        log_probs = F.log_softmax(inputs, dim=1)
        ce_loss = -(targets_one_hot * log_probs).sum(dim=1)
        
        # Focal term
        probs = F.softmax(inputs, dim=1)
        p_t = probs.gather(1, targets.unsqueeze(1)).squeeze(1)
        focal_weight = (1 - p_t) ** self.gamma
        
        # 应用类权重
        weights = self.class_weights[targets]
        
        loss = focal_weight * ce_loss * weights
        
        return loss.mean()

class UltimateMAMBAFORMER(nn.Module):
    """终极版MAMBAFORMER - 多策略融合"""
    def __init__(self, config):
        super().__init__()
        
        # 主干网络 - 10层深度
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 简化的注意力增强层
        self.attention_enhancer = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model']),
            nn.LayerNorm(config['d_model']),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 特征金字塔
        self.pyramid_layers = nn.ModuleList([
            nn.Linear(config['d_model'], config['d_model']),
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.Linear(config['d_model'] // 2, config['d_model'] // 4)
        ])
        
        # 多头分类器（集成思想）
        self.classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(config['d_model'], 256),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Linear(128, 5)
            ) for _ in range(3)  # 3个分类头
        ])
        
        # 特征融合层
        fusion_dim = config['d_model'] + config['d_model'] // 2 + config['d_model'] // 4
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_dim, config['d_model']),
            nn.LayerNorm(config['d_model']),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 最终分类器
        self.final_classifier = nn.Sequential(
            nn.Linear(config['d_model'], 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Dropout(0.15),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 5)
        )
        
    def forward(self, x):
        # 主干特征提取
        features, _ = self.backbone(x)
        
        # 处理序列输出
        if features.dim() == 3:
            batch_size, seq_len, d_model = features.shape
            
            # 取中间时间步作为主特征
            main_features = features[:, seq_len//2, :]
            
            # 注意力增强 - 对主特征应用
            enhanced_features = self.attention_enhancer(main_features)
            main_features = main_features + 0.5 * enhanced_features  # 残差连接
            
            # 特征金字塔
            pyramid_features = []
            current_feat = main_features
            for layer in self.pyramid_layers:
                current_feat = layer(current_feat)
                pyramid_features.append(current_feat)
            
            # 特征融合
            fused_features = torch.cat(pyramid_features, dim=-1)
            fused_features = self.feature_fusion(fused_features)
            
        else:
            main_features = features
            fused_features = features
        
        # 多头预测（训练时）
        if self.training:
            outputs = []
            for classifier in self.classifiers:
                outputs.append(classifier(main_features))
            
            # 主分类器输出
            main_output = self.final_classifier(fused_features)
            
            return main_output, outputs
        else:
            # 推理时集成所有分类器
            all_outputs = []
            for classifier in self.classifiers:
                all_outputs.append(F.softmax(classifier(main_features), dim=1))
            
            main_output = F.softmax(self.final_classifier(fused_features), dim=1)
            
            # 加权平均（主分类器权重更高）
            ensemble_output = 0.5 * main_output + 0.5 * torch.stack(all_outputs).mean(dim=0)
            
            return ensemble_output

def cutmix_data(x, y, alpha=1.0):
    """CutMix数据增强 - 适配序列数据"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        return x, y, y, 1
    
    batch_size = x.size(0)
    index = torch.randperm(batch_size).to(x.device)
    
    # 对序列数据进行CutMix
    seq_len = x.size(1)
    cut_pos = int(seq_len * (1 - lam))
    
    mixed_x = x.clone()
    mixed_x[:, :cut_pos] = x[index, :cut_pos]
    
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        # 处理序列标签
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        # 数据增强策略
        augment_prob = random.random()
        
        if augment_prob < 0.3 and epoch > 5:  # 30%概率使用CutMix
            data, target_a, target_b, lam = cutmix_data(data, target)
            
            optimizer.zero_grad()
            outputs = model(data)
            
            if isinstance(outputs, tuple):
                main_output, aux_outputs = outputs
                # 主损失
                loss = lam * criterion(main_output, target_a) + (1 - lam) * criterion(main_output, target_b)
                # 辅助损失
                for aux_out in aux_outputs:
                    loss += 0.1 * (lam * criterion(aux_out, target_a) + (1 - lam) * criterion(aux_out, target_b))
            else:
                loss = lam * criterion(outputs, target_a) + (1 - lam) * criterion(outputs, target_b)
        
        elif augment_prob < 0.5 and epoch > 10:  # 20%概率使用MixUp
            # MixUp
            lam = np.random.beta(0.2, 0.2)
            index = torch.randperm(data.size(0)).to(device)
            mixed_data = lam * data + (1 - lam) * data[index]
            target_a, target_b = target, target[index]
            
            optimizer.zero_grad()
            outputs = model(mixed_data)
            
            if isinstance(outputs, tuple):
                main_output, aux_outputs = outputs
                loss = lam * criterion(main_output, target_a) + (1 - lam) * criterion(main_output, target_b)
                for aux_out in aux_outputs:
                    loss += 0.1 * (lam * criterion(aux_out, target_a) + (1 - lam) * criterion(aux_out, target_b))
            else:
                loss = lam * criterion(outputs, target_a) + (1 - lam) * criterion(outputs, target_b)
        
        else:  # 正常训练
            optimizer.zero_grad()
            outputs = model(data)
            
            if isinstance(outputs, tuple):
                main_output, aux_outputs = outputs
                loss = criterion(main_output, target)
                # 辅助损失
                for aux_out in aux_outputs:
                    loss += 0.1 * criterion(aux_out, target)
            else:
                loss = criterion(outputs, target)
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if isinstance(outputs, tuple):
            preds = main_output.argmax(dim=1)
        else:
            preds = outputs.argmax(dim=1)
        
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy() if augment_prob >= 0.3 or epoch <= 5 else target_a.cpu().numpy())
        
        pbar.set_postfix({'loss': loss.item()})
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            output = model(data)
            
            # 处理多头输出
            if isinstance(output, tuple):
                output = output[0]
            
            preds = output.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # Ultimate配置
    config = {
        'd_model': 288,  # 平衡的模型维度
        'n_heads': 18,
        'n_layers': 10,  # 10层深度网络
        'dropout': 0.1,
        'seq_len': 5,
        'batch_size': 28,
        'learning_rate': 1.8e-4,
        'weight_decay': 3e-5,
        'num_epochs': 60,
        'patience': 20,
        'label_smoothing': 0.1
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 扩展训练集
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz'
    ]
    
    val_files = [
        'SC4032E0.npz', 'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = UltimateMAMBAFORMER(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = AdaptiveFocalLoss(device, label_smoothing=config['label_smoothing'])
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度 - Cosine Annealing with Warm Restarts
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 训练
    best_val_acc = 0
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting Ultimate training...")
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
                # 执行git commit
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ Achieved 90% accuracy: {test_acc:.4f}'")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (Ultimate)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: Achieved 90% target!")
        logging.info(f"MF1: {best_test_metrics['f1']:.4f} (Target: 0.82)")
        logging.info(f"Kappa: {best_test_metrics['kappa']:.4f} (Target: 0.82)")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()