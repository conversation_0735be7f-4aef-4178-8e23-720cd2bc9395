WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.

Epoch 1:   0%|          | 0/868 [00:00<?, ?it/s]
Epoch 1:   0%|          | 0/868 [00:01<?, ?it/s, loss=3.9771, acc=0.1062, REM_acc=0.000, Wake_acc=0.029]
Epoch 1:   0%|          | 1/868 [00:01<15:26,  1.07s/it, loss=3.9771, acc=0.1062, REM_acc=0.000, Wake_acc=0.029]
Epoch 1:   0%|          | 3/868 [00:01<04:32,  3.18it/s, loss=3.9771, acc=0.1062, REM_acc=0.000, Wake_acc=0.029]
Epoch 1:   1%|          | 6/868 [00:01<02:06,  6.81it/s, loss=3.9771, acc=0.1062, REM_acc=0.000, Wake_acc=0.029]
Epoch 1:   1%|          | 9/868 [00:01<01:21, 10.50it/s, loss=3.9771, acc=0.1062, REM_acc=0.000, Wake_acc=0.029]
Epoch 1:   1%|          | 9/868 [00:01<01:21, 10.50it/s, loss=1.0789, acc=0.3506, REM_acc=0.576, Wake_acc=0.470]
Epoch 1:   1%|▏         | 13/868 [00:01<00:55, 15.31it/s, loss=1.0789, acc=0.3506, REM_acc=0.576, Wake_acc=0.470]
Epoch 1:   2%|▏         | 17/868 [00:01<00:44, 19.22it/s, loss=1.0789, acc=0.3506, REM_acc=0.576, Wake_acc=0.470]
Epoch 1:   2%|▏         | 17/868 [00:01<00:44, 19.22it/s, loss=1.1784, acc=0.4098, REM_acc=0.586, Wake_acc=0.594]
Epoch 1:   2%|▏         | 21/868 [00:01<00:37, 22.51it/s, loss=1.1784, acc=0.4098, REM_acc=0.586, Wake_acc=0.594]
Epoch 1:   3%|▎         | 25/868 [00:01<00:34, 24.54it/s, loss=1.1784, acc=0.4098, REM_acc=0.586, Wake_acc=0.594]
Epoch 1:   3%|▎         | 28/868 [00:02<00:33, 25.37it/s, loss=1.1784, acc=0.4098, REM_acc=0.586, Wake_acc=0.594]
Epoch 1:   3%|▎         | 28/868 [00:02<00:33, 25.37it/s, loss=1.0152, acc=0.4530, REM_acc=0.612, Wake_acc=0.615]
Epoch 1:   4%|▎         | 32/868 [00:02<00:31, 26.90it/s, loss=1.0152, acc=0.4530, REM_acc=0.612, Wake_acc=0.615]
Epoch 1:   4%|▍         | 35/868 [00:02<00:32, 25.38it/s, loss=1.0152, acc=0.4530, REM_acc=0.612, Wake_acc=0.615]
Epoch 1:   4%|▍         | 38/868 [00:02<00:35, 23.21it/s, loss=1.0152, acc=0.4530, REM_acc=0.612, Wake_acc=0.615]
Epoch 1:   4%|▍         | 38/868 [00:02<00:35, 23.21it/s, loss=0.8383, acc=0.4899, REM_acc=0.617, Wake_acc=0.640]
Epoch 1:   5%|▍         | 41/868 [00:02<00:35, 23.60it/s, loss=0.8383, acc=0.4899, REM_acc=0.617, Wake_acc=0.640]
Epoch 1:   5%|▌         | 44/868 [00:02<00:35, 23.39it/s, loss=0.8383, acc=0.4899, REM_acc=0.617, Wake_acc=0.640]
Epoch 1:   5%|▌         | 47/868 [00:02<00:34, 23.72it/s, loss=0.8383, acc=0.4899, REM_acc=0.617, Wake_acc=0.640]
Epoch 1:   6%|▌         | 50/868 [00:02<00:32, 24.81it/s, loss=0.8383, acc=0.4899, REM_acc=0.617, Wake_acc=0.640]
Epoch 1:   6%|▌         | 50/868 [00:02<00:32, 24.81it/s, loss=0.6854, acc=0.4998, REM_acc=0.619, Wake_acc=0.687]
Epoch 1:   6%|▌         | 53/868 [00:03<00:33, 24.26it/s, loss=0.6854, acc=0.4998, REM_acc=0.619, Wake_acc=0.687]
Epoch 1:   6%|▋         | 56/868 [00:03<00:32, 24.99it/s, loss=0.6854, acc=0.4998, REM_acc=0.619, Wake_acc=0.687]
Epoch 1:   7%|▋         | 59/868 [00:03<00:31, 25.77it/s, loss=0.6854, acc=0.4998, REM_acc=0.619, Wake_acc=0.687]
Epoch 1:   7%|▋         | 59/868 [00:03<00:31, 25.77it/s, loss=0.7116, acc=0.5265, REM_acc=0.640, Wake_acc=0.706]
Epoch 1:   7%|▋         | 62/868 [00:03<00:33, 24.17it/s, loss=0.7116, acc=0.5265, REM_acc=0.640, Wake_acc=0.706]
Epoch 1:   7%|▋         | 65/868 [00:03<00:36, 22.30it/s, loss=0.7116, acc=0.5265, REM_acc=0.640, Wake_acc=0.706]
Epoch 1:   8%|▊         | 68/868 [00:03<00:35, 22.61it/s, loss=0.7116, acc=0.5265, REM_acc=0.640, Wake_acc=0.706]
Epoch 1:   8%|▊         | 68/868 [00:03<00:35, 22.61it/s, loss=0.8058, acc=0.5339, REM_acc=0.648, Wake_acc=0.730]
Epoch 1:   8%|▊         | 71/868 [00:03<00:33, 23.49it/s, loss=0.8058, acc=0.5339, REM_acc=0.648, Wake_acc=0.730]
Epoch 1:   9%|▊         | 74/868 [00:03<00:33, 23.71it/s, loss=0.8058, acc=0.5339, REM_acc=0.648, Wake_acc=0.730]
Epoch 1:   9%|▉         | 77/868 [00:04<00:33, 23.67it/s, loss=0.8058, acc=0.5339, REM_acc=0.648, Wake_acc=0.730]
Epoch 1:   9%|▉         | 80/868 [00:04<00:33, 23.64it/s, loss=0.8058, acc=0.5339, REM_acc=0.648, Wake_acc=0.730]
Epoch 1:   9%|▉         | 80/868 [00:04<00:33, 23.64it/s, loss=1.0223, acc=0.5442, REM_acc=0.673, Wake_acc=0.743]
Epoch 1:  10%|▉         | 83/868 [00:04<00:32, 24.46it/s, loss=1.0223, acc=0.5442, REM_acc=0.673, Wake_acc=0.743]
Epoch 1:  10%|▉         | 86/868 [00:04<00:33, 23.48it/s, loss=1.0223, acc=0.5442, REM_acc=0.673, Wake_acc=0.743]
Epoch 1:  10%|█         | 89/868 [00:04<00:36, 21.58it/s, loss=1.0223, acc=0.5442, REM_acc=0.673, Wake_acc=0.743]
Epoch 1:  10%|█         | 89/868 [00:04<00:36, 21.58it/s, loss=0.5680, acc=0.5582, REM_acc=0.689, Wake_acc=0.750]
Epoch 1:  11%|█         | 92/868 [00:04<00:39, 19.86it/s, loss=0.5680, acc=0.5582, REM_acc=0.689, Wake_acc=0.750]
Epoch 1:  11%|█         | 95/868 [00:04<00:38, 19.96it/s, loss=0.5680, acc=0.5582, REM_acc=0.689, Wake_acc=0.750]
Epoch 1:  11%|█▏        | 98/868 [00:05<00:37, 20.38it/s, loss=0.5680, acc=0.5582, REM_acc=0.689, Wake_acc=0.750]
Epoch 1:  11%|█▏        | 98/868 [00:05<00:37, 20.38it/s, loss=0.4743, acc=0.5628, REM_acc=0.695, Wake_acc=0.745]
Epoch 1:  12%|█▏        | 101/868 [00:05<00:36, 20.99it/s, loss=0.4743, acc=0.5628, REM_acc=0.695, Wake_acc=0.745]
Epoch 1:  12%|█▏        | 104/868 [00:05<00:33, 22.67it/s, loss=0.4743, acc=0.5628, REM_acc=0.695, Wake_acc=0.745]
Epoch 1:  12%|█▏        | 107/868 [00:05<00:31, 24.16it/s, loss=0.4743, acc=0.5628, REM_acc=0.695, Wake_acc=0.745]
Epoch 1:  13%|█▎        | 110/868 [00:05<00:30, 24.66it/s, loss=0.4743, acc=0.5628, REM_acc=0.695, Wake_acc=0.745]
Epoch 1:  13%|█▎        | 110/868 [00:05<00:30, 24.66it/s, loss=0.5413, acc=0.5767, REM_acc=0.704, Wake_acc=0.748]
Epoch 1:  13%|█▎        | 113/868 [00:05<00:31, 24.05it/s, loss=0.5413, acc=0.5767, REM_acc=0.704, Wake_acc=0.748]
Epoch 1:  13%|█▎        | 116/868 [00:05<00:32, 23.41it/s, loss=0.5413, acc=0.5767, REM_acc=0.704, Wake_acc=0.748]
Epoch 1:  14%|█▎        | 119/868 [00:05<00:34, 21.91it/s, loss=0.5413, acc=0.5767, REM_acc=0.704, Wake_acc=0.748]
Epoch 1:  14%|█▎        | 119/868 [00:06<00:34, 21.91it/s, loss=0.5426, acc=0.5835, REM_acc=0.712, Wake_acc=0.758]
Epoch 1:  14%|█▍        | 122/868 [00:06<00:34, 21.58it/s, loss=0.5426, acc=0.5835, REM_acc=0.712, Wake_acc=0.758]
Epoch 1:  14%|█▍        | 125/868 [00:06<00:32, 22.97it/s, loss=0.5426, acc=0.5835, REM_acc=0.712, Wake_acc=0.758]
Epoch 1:  15%|█▍        | 128/868 [00:06<00:32, 22.98it/s, loss=0.5426, acc=0.5835, REM_acc=0.712, Wake_acc=0.758]
Epoch 1:  15%|█▍        | 128/868 [00:06<00:32, 22.98it/s, loss=0.3655, acc=0.5948, REM_acc=0.729, Wake_acc=0.763]
Epoch 1:  15%|█▌        | 131/868 [00:06<00:29, 24.65it/s, loss=0.3655, acc=0.5948, REM_acc=0.729, Wake_acc=0.763]
Epoch 1:  15%|█▌        | 134/868 [00:06<00:28, 25.68it/s, loss=0.3655, acc=0.5948, REM_acc=0.729, Wake_acc=0.763]
Epoch 1:  16%|█▌        | 137/868 [00:06<00:28, 25.80it/s, loss=0.3655, acc=0.5948, REM_acc=0.729, Wake_acc=0.763]
Epoch 1:  16%|█▌        | 140/868 [00:06<00:28, 25.67it/s, loss=0.3655, acc=0.5948, REM_acc=0.729, Wake_acc=0.763]
Epoch 1:  16%|█▌        | 140/868 [00:06<00:28, 25.67it/s, loss=0.7613, acc=0.6005, REM_acc=0.736, Wake_acc=0.768]
Epoch 1:  16%|█▋        | 143/868 [00:06<00:27, 26.24it/s, loss=0.7613, acc=0.6005, REM_acc=0.736, Wake_acc=0.768]
Epoch 1:  17%|█▋        | 146/868 [00:07<00:30, 23.30it/s, loss=0.7613, acc=0.6005, REM_acc=0.736, Wake_acc=0.768]
Epoch 1:  17%|█▋        | 149/868 [00:07<00:32, 21.81it/s, loss=0.7613, acc=0.6005, REM_acc=0.736, Wake_acc=0.768]
Epoch 1:  17%|█▋        | 149/868 [00:07<00:32, 21.81it/s, loss=0.8478, acc=0.6060, REM_acc=0.745, Wake_acc=0.771]
Epoch 1:  18%|█▊        | 152/868 [00:07<00:30, 23.12it/s, loss=0.8478, acc=0.6060, REM_acc=0.745, Wake_acc=0.771]
Epoch 1:  18%|█▊        | 156/868 [00:07<00:28, 25.29it/s, loss=0.8478, acc=0.6060, REM_acc=0.745, Wake_acc=0.771]
Epoch 1:  18%|█▊        | 159/868 [00:07<00:26, 26.45it/s, loss=0.8478, acc=0.6060, REM_acc=0.745, Wake_acc=0.771]
Epoch 1:  18%|█▊        | 159/868 [00:07<00:26, 26.45it/s, loss=0.7401, acc=0.6114, REM_acc=0.754, Wake_acc=0.777]
Epoch 1:  19%|█▊        | 162/868 [00:07<00:25, 27.31it/s, loss=0.7401, acc=0.6114, REM_acc=0.754, Wake_acc=0.777]
Epoch 1:  19%|█▉        | 165/868 [00:07<00:25, 27.75it/s, loss=0.7401, acc=0.6114, REM_acc=0.754, Wake_acc=0.777]
Epoch 1:  19%|█▉        | 169/868 [00:07<00:24, 28.58it/s, loss=0.7401, acc=0.6114, REM_acc=0.754, Wake_acc=0.777]
Epoch 1:  19%|█▉        | 169/868 [00:07<00:24, 28.58it/s, loss=0.5161, acc=0.6142, REM_acc=0.763, Wake_acc=0.785]
Epoch 1:  20%|█▉        | 172/868 [00:08<00:24, 28.63it/s, loss=0.5161, acc=0.6142, REM_acc=0.763, Wake_acc=0.785]
Epoch 1:  20%|██        | 175/868 [00:08<00:25, 27.65it/s, loss=0.5161, acc=0.6142, REM_acc=0.763, Wake_acc=0.785]
Epoch 1:  21%|██        | 178/868 [00:08<00:27, 25.00it/s, loss=0.5161, acc=0.6142, REM_acc=0.763, Wake_acc=0.785]
Epoch 1:  21%|██        | 178/868 [00:08<00:27, 25.00it/s, loss=0.5984, acc=0.6175, REM_acc=0.769, Wake_acc=0.792]
Epoch 1:  21%|██        | 181/868 [00:08<00:29, 23.34it/s, loss=0.5984, acc=0.6175, REM_acc=0.769, Wake_acc=0.792]
Epoch 1:  21%|██        | 184/868 [00:08<00:28, 24.05it/s, loss=0.5984, acc=0.6175, REM_acc=0.769, Wake_acc=0.792]
Epoch 1:  22%|██▏       | 187/868 [00:08<00:27, 25.20it/s, loss=0.5984, acc=0.6175, REM_acc=0.769, Wake_acc=0.792]
Epoch 1:  22%|██▏       | 187/868 [00:08<00:27, 25.20it/s, loss=0.5971, acc=0.6229, REM_acc=0.775, Wake_acc=0.795]
Epoch 1:  22%|██▏       | 191/868 [00:08<00:25, 26.79it/s, loss=0.5971, acc=0.6229, REM_acc=0.775, Wake_acc=0.795]
Epoch 1:  22%|██▏       | 195/868 [00:08<00:23, 28.07it/s, loss=0.5971, acc=0.6229, REM_acc=0.775, Wake_acc=0.795]
Epoch 1:  23%|██▎       | 198/868 [00:09<00:23, 27.97it/s, loss=0.5971, acc=0.6229, REM_acc=0.775, Wake_acc=0.795]
Epoch 1:  23%|██▎       | 198/868 [00:09<00:23, 27.97it/s, loss=0.2958, acc=0.6246, REM_acc=0.782, Wake_acc=0.802]
Epoch 1:  23%|██▎       | 201/868 [00:09<00:23, 28.38it/s, loss=0.2958, acc=0.6246, REM_acc=0.782, Wake_acc=0.802]
Epoch 1:  24%|██▎       | 204/868 [00:09<00:24, 27.59it/s, loss=0.2958, acc=0.6246, REM_acc=0.782, Wake_acc=0.802]
Epoch 1:  24%|██▍       | 207/868 [00:09<00:26, 24.88it/s, loss=0.2958, acc=0.6246, REM_acc=0.782, Wake_acc=0.802]
Epoch 1:  24%|██▍       | 210/868 [00:09<00:27, 23.73it/s, loss=0.2958, acc=0.6246, REM_acc=0.782, Wake_acc=0.802]
Epoch 1:  24%|██▍       | 210/868 [00:09<00:27, 23.73it/s, loss=0.4000, acc=0.6305, REM_acc=0.789, Wake_acc=0.808]
Epoch 1:  25%|██▍       | 213/868 [00:09<00:27, 24.10it/s, loss=0.4000, acc=0.6305, REM_acc=0.789, Wake_acc=0.808]
Epoch 1:  25%|██▍       | 216/868 [00:09<00:26, 24.90it/s, loss=0.4000, acc=0.6305, REM_acc=0.789, Wake_acc=0.808]
Epoch 1:  25%|██▌       | 220/868 [00:09<00:24, 26.74it/s, loss=0.4000, acc=0.6305, REM_acc=0.789, Wake_acc=0.808]
Epoch 1:  25%|██▌       | 220/868 [00:09<00:24, 26.74it/s, loss=0.4833, acc=0.6284, REM_acc=0.795, Wake_acc=0.805]
Epoch 1:  26%|██▌       | 223/868 [00:10<00:24, 26.05it/s, loss=0.4833, acc=0.6284, REM_acc=0.795, Wake_acc=0.805]
Epoch 1:  26%|██▌       | 226/868 [00:10<00:24, 25.87it/s, loss=0.4833, acc=0.6284, REM_acc=0.795, Wake_acc=0.805]
Epoch 1:  26%|██▋       | 229/868 [00:10<00:24, 26.34it/s, loss=0.4833, acc=0.6284, REM_acc=0.795, Wake_acc=0.805]
Epoch 1:  26%|██▋       | 229/868 [00:10<00:24, 26.34it/s, loss=0.4078, acc=0.6311, REM_acc=0.800, Wake_acc=0.805]
Epoch 1:  27%|██▋       | 232/868 [00:10<00:23, 27.29it/s, loss=0.4078, acc=0.6311, REM_acc=0.800, Wake_acc=0.805]
Epoch 1:  27%|██▋       | 235/868 [00:10<00:24, 26.02it/s, loss=0.4078, acc=0.6311, REM_acc=0.800, Wake_acc=0.805]
Epoch 1:  27%|██▋       | 238/868 [00:10<00:24, 25.59it/s, loss=0.4078, acc=0.6311, REM_acc=0.800, Wake_acc=0.805]
Epoch 1:  27%|██▋       | 238/868 [00:10<00:24, 25.59it/s, loss=0.5264, acc=0.6337, REM_acc=0.806, Wake_acc=0.809]
Epoch 1:  28%|██▊       | 241/868 [00:10<00:25, 24.58it/s, loss=0.5264, acc=0.6337, REM_acc=0.806, Wake_acc=0.809]
Epoch 1:  28%|██▊       | 244/868 [00:10<00:24, 25.73it/s, loss=0.5264, acc=0.6337, REM_acc=0.806, Wake_acc=0.809]
Epoch 1:  29%|██▊       | 248/868 [00:10<00:22, 27.27it/s, loss=0.5264, acc=0.6337, REM_acc=0.806, Wake_acc=0.809]
Epoch 1:  29%|██▊       | 248/868 [00:11<00:22, 27.27it/s, loss=0.5705, acc=0.6379, REM_acc=0.810, Wake_acc=0.810]
Epoch 1:  29%|██▉       | 251/868 [00:11<00:22, 27.38it/s, loss=0.5705, acc=0.6379, REM_acc=0.810, Wake_acc=0.810]
Epoch 1:  29%|██▉       | 254/868 [00:11<00:22, 27.20it/s, loss=0.5705, acc=0.6379, REM_acc=0.810, Wake_acc=0.810]
Epoch 1:  30%|██▉       | 258/868 [00:11<00:21, 28.08it/s, loss=0.5705, acc=0.6379, REM_acc=0.810, Wake_acc=0.810]
Epoch 1:  30%|██▉       | 258/868 [00:11<00:21, 28.08it/s, loss=0.3088, acc=0.6429, REM_acc=0.813, Wake_acc=0.810]
Epoch 1:  30%|███       | 261/868 [00:11<00:21, 27.79it/s, loss=0.3088, acc=0.6429, REM_acc=0.813, Wake_acc=0.810]
Epoch 1:  30%|███       | 264/868 [00:11<00:22, 26.91it/s, loss=0.3088, acc=0.6429, REM_acc=0.813, Wake_acc=0.810]
Epoch 1:  31%|███       | 267/868 [00:11<00:24, 25.03it/s, loss=0.3088, acc=0.6429, REM_acc=0.813, Wake_acc=0.810]
Epoch 1:  31%|███       | 270/868 [00:11<00:24, 24.06it/s, loss=0.3088, acc=0.6429, REM_acc=0.813, Wake_acc=0.810]
Epoch 1:  31%|███       | 270/868 [00:11<00:24, 24.06it/s, loss=0.3856, acc=0.6457, REM_acc=0.818, Wake_acc=0.815]
Epoch 1:  31%|███▏      | 273/868 [00:11<00:25, 23.72it/s, loss=0.3856, acc=0.6457, REM_acc=0.818, Wake_acc=0.815]
Epoch 1:  32%|███▏      | 276/868 [00:12<00:24, 23.79it/s, loss=0.3856, acc=0.6457, REM_acc=0.818, Wake_acc=0.815]
Epoch 1:  32%|███▏      | 279/868 [00:12<00:24, 23.56it/s, loss=0.3856, acc=0.6457, REM_acc=0.818, Wake_acc=0.815]
Epoch 1:  32%|███▏      | 279/868 [00:12<00:24, 23.56it/s, loss=0.3600, acc=0.6483, REM_acc=0.823, Wake_acc=0.817]
Epoch 1:  32%|███▏      | 282/868 [00:12<00:24, 23.86it/s, loss=0.3600, acc=0.6483, REM_acc=0.823, Wake_acc=0.817]
Epoch 1:  33%|███▎      | 285/868 [00:12<00:24, 24.21it/s, loss=0.3600, acc=0.6483, REM_acc=0.823, Wake_acc=0.817]
Epoch 1:  33%|███▎      | 288/868 [00:12<00:24, 24.09it/s, loss=0.3600, acc=0.6483, REM_acc=0.823, Wake_acc=0.817]
Epoch 1:  33%|███▎      | 288/868 [00:12<00:24, 24.09it/s, loss=0.2463, acc=0.6513, REM_acc=0.828, Wake_acc=0.820]
Epoch 1:  34%|███▎      | 291/868 [00:12<00:25, 22.98it/s, loss=0.2463, acc=0.6513, REM_acc=0.828, Wake_acc=0.820]
Epoch 1:  34%|███▍      | 294/868 [00:12<00:25, 22.24it/s, loss=0.2463, acc=0.6513, REM_acc=0.828, Wake_acc=0.820]
Epoch 1:  34%|███▍      | 297/868 [00:13<00:25, 22.26it/s, loss=0.2463, acc=0.6513, REM_acc=0.828, Wake_acc=0.820]
Epoch 1:  35%|███▍      | 300/868 [00:13<00:24, 22.99it/s, loss=0.2463, acc=0.6513, REM_acc=0.828, Wake_acc=0.820]
Epoch 1:  35%|███▍      | 300/868 [00:13<00:24, 22.99it/s, loss=0.2758, acc=0.6543, REM_acc=0.832, Wake_acc=0.821]
Epoch 1:  35%|███▍      | 303/868 [00:13<00:23, 23.91it/s, loss=0.2758, acc=0.6543, REM_acc=0.832, Wake_acc=0.821]
Epoch 1:  35%|███▌      | 306/868 [00:13<00:22, 25.16it/s, loss=0.2758, acc=0.6543, REM_acc=0.832, Wake_acc=0.821]
Epoch 1:  36%|███▌      | 309/868 [00:13<00:21, 25.82it/s, loss=0.2758, acc=0.6543, REM_acc=0.832, Wake_acc=0.821]
Epoch 1:  36%|███▌      | 309/868 [00:13<00:21, 25.82it/s, loss=0.3556, acc=0.6579, REM_acc=0.835, Wake_acc=0.825]
Epoch 1:  36%|███▌      | 312/868 [00:13<00:20, 26.52it/s, loss=0.3556, acc=0.6579, REM_acc=0.835, Wake_acc=0.825]
Epoch 1:  36%|███▋      | 315/868 [00:13<00:21, 26.30it/s, loss=0.3556, acc=0.6579, REM_acc=0.835, Wake_acc=0.825]
Epoch 1:  37%|███▋      | 318/868 [00:13<00:20, 26.52it/s, loss=0.3556, acc=0.6579, REM_acc=0.835, Wake_acc=0.825]
Epoch 1:  37%|███▋      | 318/868 [00:13<00:20, 26.52it/s, loss=0.3262, acc=0.6617, REM_acc=0.838, Wake_acc=0.825]
Epoch 1:  37%|███▋      | 321/868 [00:13<00:22, 23.80it/s, loss=0.3262, acc=0.6617, REM_acc=0.838, Wake_acc=0.825]
Epoch 1:  37%|███▋      | 324/868 [00:14<00:24, 22.01it/s, loss=0.3262, acc=0.6617, REM_acc=0.838, Wake_acc=0.825]
Epoch 1:  38%|███▊      | 327/868 [00:14<00:23, 22.70it/s, loss=0.3262, acc=0.6617, REM_acc=0.838, Wake_acc=0.825]
Epoch 1:  38%|███▊      | 330/868 [00:14<00:22, 24.43it/s, loss=0.3262, acc=0.6617, REM_acc=0.838, Wake_acc=0.825]
Epoch 1:  38%|███▊      | 330/868 [00:14<00:22, 24.43it/s, loss=0.4558, acc=0.6643, REM_acc=0.842, Wake_acc=0.828]
Epoch 1:  38%|███▊      | 334/868 [00:14<00:20, 26.53it/s, loss=0.4558, acc=0.6643, REM_acc=0.842, Wake_acc=0.828]
Epoch 1:  39%|███▉      | 338/868 [00:14<00:19, 27.68it/s, loss=0.4558, acc=0.6643, REM_acc=0.842, Wake_acc=0.828]
Epoch 1:  39%|███▉      | 338/868 [00:14<00:19, 27.68it/s, loss=0.2918, acc=0.6672, REM_acc=0.845, Wake_acc=0.830]
Epoch 1:  39%|███▉      | 341/868 [00:14<00:18, 27.84it/s, loss=0.2918, acc=0.6672, REM_acc=0.845, Wake_acc=0.830]
Epoch 1:  40%|███▉      | 344/868 [00:14<00:18, 28.32it/s, loss=0.2918, acc=0.6672, REM_acc=0.845, Wake_acc=0.830]
Epoch 1:  40%|███▉      | 347/868 [00:14<00:18, 28.12it/s, loss=0.2918, acc=0.6672, REM_acc=0.845, Wake_acc=0.830]
Epoch 1:  40%|████      | 350/868 [00:15<00:19, 26.25it/s, loss=0.2918, acc=0.6672, REM_acc=0.845, Wake_acc=0.830]
Epoch 1:  40%|████      | 350/868 [00:15<00:19, 26.25it/s, loss=0.3069, acc=0.6694, REM_acc=0.848, Wake_acc=0.832]
Epoch 1:  41%|████      | 353/868 [00:15<00:21, 24.17it/s, loss=0.3069, acc=0.6694, REM_acc=0.848, Wake_acc=0.832]
Epoch 1:  41%|████      | 356/868 [00:15<00:21, 24.13it/s, loss=0.3069, acc=0.6694, REM_acc=0.848, Wake_acc=0.832]
Epoch 1:  41%|████▏     | 360/868 [00:15<00:19, 26.03it/s, loss=0.3069, acc=0.6694, REM_acc=0.848, Wake_acc=0.832]
Epoch 1:  41%|████▏     | 360/868 [00:15<00:19, 26.03it/s, loss=0.4952, acc=0.6729, REM_acc=0.851, Wake_acc=0.834]
Epoch 1:  42%|████▏     | 363/868 [00:15<00:18, 26.58it/s, loss=0.4952, acc=0.6729, REM_acc=0.851, Wake_acc=0.834]
Epoch 1:  42%|████▏     | 366/868 [00:15<00:18, 26.77it/s, loss=0.4952, acc=0.6729, REM_acc=0.851, Wake_acc=0.834]
Epoch 1:  43%|████▎     | 369/868 [00:15<00:18, 26.31it/s, loss=0.4952, acc=0.6729, REM_acc=0.851, Wake_acc=0.834]
Epoch 1:  43%|████▎     | 369/868 [00:15<00:18, 26.31it/s, loss=0.3891, acc=0.6770, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  43%|████▎     | 372/868 [00:15<00:18, 26.17it/s, loss=0.3891, acc=0.6770, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  43%|████▎     | 375/868 [00:16<00:20, 24.52it/s, loss=0.3891, acc=0.6770, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  44%|████▎     | 378/868 [00:16<00:20, 23.71it/s, loss=0.3891, acc=0.6770, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  44%|████▎     | 378/868 [00:16<00:20, 23.71it/s, loss=0.5058, acc=0.6788, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  44%|████▍     | 381/868 [00:16<00:20, 23.21it/s, loss=0.5058, acc=0.6788, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  44%|████▍     | 384/868 [00:16<00:20, 24.11it/s, loss=0.5058, acc=0.6788, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  45%|████▍     | 387/868 [00:16<00:18, 25.54it/s, loss=0.5058, acc=0.6788, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  45%|████▍     | 390/868 [00:16<00:18, 26.18it/s, loss=0.5058, acc=0.6788, REM_acc=0.855, Wake_acc=0.837]
Epoch 1:  45%|████▍     | 390/868 [00:16<00:18, 26.18it/s, loss=0.2970, acc=0.6804, REM_acc=0.856, Wake_acc=0.837]
Epoch 1:  45%|████▌     | 393/868 [00:16<00:18, 25.86it/s, loss=0.2970, acc=0.6804, REM_acc=0.856, Wake_acc=0.837]
Epoch 1:  46%|████▌     | 396/868 [00:16<00:18, 25.30it/s, loss=0.2970, acc=0.6804, REM_acc=0.856, Wake_acc=0.837]
Epoch 1:  46%|████▌     | 399/868 [00:17<00:19, 24.21it/s, loss=0.2970, acc=0.6804, REM_acc=0.856, Wake_acc=0.837]
Epoch 1:  46%|████▌     | 399/868 [00:17<00:19, 24.21it/s, loss=0.4708, acc=0.6829, REM_acc=0.857, Wake_acc=0.839]
Epoch 1:  46%|████▋     | 402/868 [00:17<00:20, 23.21it/s, loss=0.4708, acc=0.6829, REM_acc=0.857, Wake_acc=0.839]
Epoch 1:  47%|████▋     | 405/868 [00:17<00:19, 23.72it/s, loss=0.4708, acc=0.6829, REM_acc=0.857, Wake_acc=0.839]
Epoch 1:  47%|████▋     | 408/868 [00:17<00:18, 24.22it/s, loss=0.4708, acc=0.6829, REM_acc=0.857, Wake_acc=0.839]
Epoch 1:  47%|████▋     | 408/868 [00:17<00:18, 24.22it/s, loss=0.2179, acc=0.6860, REM_acc=0.860, Wake_acc=0.842]
Epoch 1:  47%|████▋     | 411/868 [00:17<00:18, 24.54it/s, loss=0.2179, acc=0.6860, REM_acc=0.860, Wake_acc=0.842]
Epoch 1:  48%|████▊     | 414/868 [00:17<00:18, 24.77it/s, loss=0.2179, acc=0.6860, REM_acc=0.860, Wake_acc=0.842]
Epoch 1:  48%|████▊     | 417/868 [00:17<00:17, 25.06it/s, loss=0.2179, acc=0.6860, REM_acc=0.860, Wake_acc=0.842]
Epoch 1:  48%|████▊     | 420/868 [00:17<00:18, 24.03it/s, loss=0.2179, acc=0.6860, REM_acc=0.860, Wake_acc=0.842]
Epoch 1:  48%|████▊     | 420/868 [00:17<00:18, 24.03it/s, loss=0.6679, acc=0.6879, REM_acc=0.861, Wake_acc=0.843]
Epoch 1:  49%|████▊     | 423/868 [00:17<00:17, 25.24it/s, loss=0.6679, acc=0.6879, REM_acc=0.861, Wake_acc=0.843]
Epoch 1:  49%|████▉     | 426/868 [00:18<00:17, 25.11it/s, loss=0.6679, acc=0.6879, REM_acc=0.861, Wake_acc=0.843]
Epoch 1:  49%|████▉     | 429/868 [00:18<00:17, 25.12it/s, loss=0.6679, acc=0.6879, REM_acc=0.861, Wake_acc=0.843]
Epoch 1:  49%|████▉     | 429/868 [00:18<00:17, 25.12it/s, loss=0.5202, acc=0.6901, REM_acc=0.863, Wake_acc=0.845]
Epoch 1:  50%|████▉     | 432/868 [00:18<00:17, 24.50it/s, loss=0.5202, acc=0.6901, REM_acc=0.863, Wake_acc=0.845]
Epoch 1:  50%|█████     | 435/868 [00:18<00:17, 24.21it/s, loss=0.5202, acc=0.6901, REM_acc=0.863, Wake_acc=0.845]
Epoch 1:  50%|█████     | 438/868 [00:18<00:17, 24.67it/s, loss=0.5202, acc=0.6901, REM_acc=0.863, Wake_acc=0.845]
Epoch 1:  50%|█████     | 438/868 [00:18<00:17, 24.67it/s, loss=0.5558, acc=0.6919, REM_acc=0.865, Wake_acc=0.845]
Epoch 1:  51%|█████     | 441/868 [00:18<00:17, 24.99it/s, loss=0.5558, acc=0.6919, REM_acc=0.865, Wake_acc=0.845]
Epoch 1:  51%|█████     | 444/868 [00:18<00:16, 26.03it/s, loss=0.5558, acc=0.6919, REM_acc=0.865, Wake_acc=0.845]
Epoch 1:  51%|█████▏    | 447/868 [00:18<00:16, 26.08it/s, loss=0.5558, acc=0.6919, REM_acc=0.865, Wake_acc=0.845]
Epoch 1:  52%|█████▏    | 450/868 [00:19<00:16, 24.90it/s, loss=0.5558, acc=0.6919, REM_acc=0.865, Wake_acc=0.845]
Epoch 1:  52%|█████▏    | 450/868 [00:19<00:16, 24.90it/s, loss=0.3606, acc=0.6937, REM_acc=0.867, Wake_acc=0.846]
Epoch 1:  52%|█████▏    | 453/868 [00:19<00:17, 23.69it/s, loss=0.3606, acc=0.6937, REM_acc=0.867, Wake_acc=0.846]
Epoch 1:  53%|█████▎    | 456/868 [00:19<00:17, 23.54it/s, loss=0.3606, acc=0.6937, REM_acc=0.867, Wake_acc=0.846]
Epoch 1:  53%|█████▎    | 459/868 [00:19<00:17, 24.03it/s, loss=0.3606, acc=0.6937, REM_acc=0.867, Wake_acc=0.846]
Epoch 1:  53%|█████▎    | 459/868 [00:19<00:17, 24.03it/s, loss=0.7287, acc=0.6954, REM_acc=0.869, Wake_acc=0.848]
Epoch 1:  53%|█████▎    | 462/868 [00:19<00:15, 25.38it/s, loss=0.7287, acc=0.6954, REM_acc=0.869, Wake_acc=0.848]
Epoch 1:  54%|█████▎    | 465/868 [00:19<00:16, 25.16it/s, loss=0.7287, acc=0.6954, REM_acc=0.869, Wake_acc=0.848]
Epoch 1:  54%|█████▍    | 468/868 [00:19<00:16, 24.35it/s, loss=0.7287, acc=0.6954, REM_acc=0.869, Wake_acc=0.848]
Epoch 1:  54%|█████▍    | 468/868 [00:19<00:16, 24.35it/s, loss=0.4991, acc=0.6969, REM_acc=0.870, Wake_acc=0.849]
Epoch 1:  54%|█████▍    | 471/868 [00:19<00:16, 24.79it/s, loss=0.4991, acc=0.6969, REM_acc=0.870, Wake_acc=0.849]
Epoch 1:  55%|█████▍    | 474/868 [00:20<00:15, 24.98it/s, loss=0.4991, acc=0.6969, REM_acc=0.870, Wake_acc=0.849]
Epoch 1:  55%|█████▍    | 477/868 [00:20<00:15, 25.35it/s, loss=0.4991, acc=0.6969, REM_acc=0.870, Wake_acc=0.849]
Epoch 1:  55%|█████▌    | 480/868 [00:20<00:15, 25.80it/s, loss=0.4991, acc=0.6969, REM_acc=0.870, Wake_acc=0.849]
Epoch 1:  55%|█████▌    | 480/868 [00:20<00:15, 25.80it/s, loss=0.4372, acc=0.6987, REM_acc=0.872, Wake_acc=0.850]
Epoch 1:  56%|█████▌    | 484/868 [00:20<00:13, 27.60it/s, loss=0.4372, acc=0.6987, REM_acc=0.872, Wake_acc=0.850]
Epoch 1:  56%|█████▌    | 487/868 [00:20<00:13, 28.08it/s, loss=0.4372, acc=0.6987, REM_acc=0.872, Wake_acc=0.850]
Epoch 1:  56%|█████▋    | 490/868 [00:20<00:13, 28.02it/s, loss=0.4372, acc=0.6987, REM_acc=0.872, Wake_acc=0.850]
Epoch 1:  56%|█████▋    | 490/868 [00:20<00:13, 28.02it/s, loss=0.3142, acc=0.7011, REM_acc=0.872, Wake_acc=0.852]
Epoch 1:  57%|█████▋    | 493/868 [00:20<00:13, 28.05it/s, loss=0.3142, acc=0.7011, REM_acc=0.872, Wake_acc=0.852]
Epoch 1:  57%|█████▋    | 496/868 [00:20<00:14, 25.98it/s, loss=0.3142, acc=0.7011, REM_acc=0.872, Wake_acc=0.852]
Epoch 1:  57%|█████▋    | 499/868 [00:20<00:15, 23.88it/s, loss=0.3142, acc=0.7011, REM_acc=0.872, Wake_acc=0.852]
Epoch 1:  57%|█████▋    | 499/868 [00:21<00:15, 23.88it/s, loss=0.2603, acc=0.7039, REM_acc=0.873, Wake_acc=0.853]
Epoch 1:  58%|█████▊    | 502/868 [00:21<00:14, 24.79it/s, loss=0.2603, acc=0.7039, REM_acc=0.873, Wake_acc=0.853]
Epoch 1:  58%|█████▊    | 505/868 [00:21<00:13, 26.08it/s, loss=0.2603, acc=0.7039, REM_acc=0.873, Wake_acc=0.853]
Epoch 1:  59%|█████▊    | 508/868 [00:21<00:13, 26.72it/s, loss=0.2603, acc=0.7039, REM_acc=0.873, Wake_acc=0.853]
Epoch 1:  59%|█████▊    | 508/868 [00:21<00:13, 26.72it/s, loss=0.2622, acc=0.7054, REM_acc=0.875, Wake_acc=0.854]
Epoch 1:  59%|█████▉    | 511/868 [00:21<00:13, 27.33it/s, loss=0.2622, acc=0.7054, REM_acc=0.875, Wake_acc=0.854]
Epoch 1:  59%|█████▉    | 514/868 [00:21<00:13, 27.11it/s, loss=0.2622, acc=0.7054, REM_acc=0.875, Wake_acc=0.854]
Epoch 1:  60%|█████▉    | 517/868 [00:21<00:12, 27.29it/s, loss=0.2622, acc=0.7054, REM_acc=0.875, Wake_acc=0.854]
Epoch 1:  60%|█████▉    | 520/868 [00:21<00:12, 27.55it/s, loss=0.2622, acc=0.7054, REM_acc=0.875, Wake_acc=0.854]
Epoch 1:  60%|█████▉    | 520/868 [00:21<00:12, 27.55it/s, loss=0.3367, acc=0.7072, REM_acc=0.875, Wake_acc=0.853]
Epoch 1:  60%|██████    | 523/868 [00:21<00:12, 27.06it/s, loss=0.3367, acc=0.7072, REM_acc=0.875, Wake_acc=0.853]
Epoch 1:  61%|██████    | 526/868 [00:21<00:13, 26.28it/s, loss=0.3367, acc=0.7072, REM_acc=0.875, Wake_acc=0.853]
Epoch 1:  61%|██████    | 529/868 [00:22<00:14, 23.90it/s, loss=0.3367, acc=0.7072, REM_acc=0.875, Wake_acc=0.853]
Epoch 1:  61%|██████    | 529/868 [00:22<00:14, 23.90it/s, loss=0.9257, acc=0.7081, REM_acc=0.876, Wake_acc=0.854]
Epoch 1:  61%|██████▏   | 532/868 [00:22<00:14, 23.31it/s, loss=0.9257, acc=0.7081, REM_acc=0.876, Wake_acc=0.854]
Epoch 1:  62%|██████▏   | 535/868 [00:22<00:13, 24.97it/s, loss=0.9257, acc=0.7081, REM_acc=0.876, Wake_acc=0.854]
Epoch 1:  62%|██████▏   | 538/868 [00:22<00:12, 26.23it/s, loss=0.9257, acc=0.7081, REM_acc=0.876, Wake_acc=0.854]
Epoch 1:  62%|██████▏   | 538/868 [00:22<00:12, 26.23it/s, loss=0.3962, acc=0.7099, REM_acc=0.878, Wake_acc=0.855]
Epoch 1:  62%|██████▏   | 541/868 [00:22<00:12, 27.23it/s, loss=0.3962, acc=0.7099, REM_acc=0.878, Wake_acc=0.855]
Epoch 1:  63%|██████▎   | 544/868 [00:22<00:11, 27.73it/s, loss=0.3962, acc=0.7099, REM_acc=0.878, Wake_acc=0.855]
Epoch 1:  63%|██████▎   | 547/868 [00:22<00:11, 27.54it/s, loss=0.3962, acc=0.7099, REM_acc=0.878, Wake_acc=0.855]
Epoch 1:  63%|██████▎   | 550/868 [00:22<00:11, 27.59it/s, loss=0.3962, acc=0.7099, REM_acc=0.878, Wake_acc=0.855]
Epoch 1:  63%|██████▎   | 550/868 [00:22<00:11, 27.59it/s, loss=0.2967, acc=0.7119, REM_acc=0.879, Wake_acc=0.857]
Epoch 1:  64%|██████▍   | 554/868 [00:23<00:11, 28.53it/s, loss=0.2967, acc=0.7119, REM_acc=0.879, Wake_acc=0.857]
Epoch 1:  64%|██████▍   | 557/868 [00:23<00:10, 28.54it/s, loss=0.2967, acc=0.7119, REM_acc=0.879, Wake_acc=0.857]
Epoch 1:  65%|██████▍   | 560/868 [00:23<00:10, 28.44it/s, loss=0.2967, acc=0.7119, REM_acc=0.879, Wake_acc=0.857]
Epoch 1:  65%|██████▍   | 560/868 [00:23<00:10, 28.44it/s, loss=0.6072, acc=0.7134, REM_acc=0.880, Wake_acc=0.858]
Epoch 1:  65%|██████▍   | 563/868 [00:23<00:10, 27.78it/s, loss=0.6072, acc=0.7134, REM_acc=0.880, Wake_acc=0.858]
Epoch 1:  65%|██████▌   | 566/868 [00:23<00:11, 27.11it/s, loss=0.6072, acc=0.7134, REM_acc=0.880, Wake_acc=0.858]
Epoch 1:  66%|██████▌   | 569/868 [00:23<00:10, 27.42it/s, loss=0.6072, acc=0.7134, REM_acc=0.880, Wake_acc=0.858]
Epoch 1:  66%|██████▌   | 569/868 [00:23<00:10, 27.42it/s, loss=0.4371, acc=0.7152, REM_acc=0.881, Wake_acc=0.857]
Epoch 1:  66%|██████▌   | 572/868 [00:23<00:11, 26.76it/s, loss=0.4371, acc=0.7152, REM_acc=0.881, Wake_acc=0.857]
Epoch 1:  66%|██████▌   | 575/868 [00:23<00:11, 25.19it/s, loss=0.4371, acc=0.7152, REM_acc=0.881, Wake_acc=0.857]
Epoch 1:  67%|██████▋   | 578/868 [00:23<00:12, 23.50it/s, loss=0.4371, acc=0.7152, REM_acc=0.881, Wake_acc=0.857]
Epoch 1:  67%|██████▋   | 578/868 [00:24<00:12, 23.50it/s, loss=0.5760, acc=0.7171, REM_acc=0.882, Wake_acc=0.860]
Epoch 1:  67%|██████▋   | 581/868 [00:24<00:13, 21.88it/s, loss=0.5760, acc=0.7171, REM_acc=0.882, Wake_acc=0.860]
Epoch 1:  67%|██████▋   | 584/868 [00:24<00:13, 21.17it/s, loss=0.5760, acc=0.7171, REM_acc=0.882, Wake_acc=0.860]
Epoch 1:  68%|██████▊   | 587/868 [00:24<00:14, 19.39it/s, loss=0.5760, acc=0.7171, REM_acc=0.882, Wake_acc=0.860]
Epoch 1:  68%|██████▊   | 589/868 [00:24<00:15, 18.58it/s, loss=0.5760, acc=0.7171, REM_acc=0.882, Wake_acc=0.860]
Epoch 1:  68%|██████▊   | 589/868 [00:24<00:15, 18.58it/s, loss=0.3171, acc=0.7183, REM_acc=0.882, Wake_acc=0.861]
Epoch 1:  68%|██████▊   | 591/868 [00:24<00:15, 17.85it/s, loss=0.3171, acc=0.7183, REM_acc=0.882, Wake_acc=0.861]
Epoch 1:  68%|██████▊   | 593/868 [00:24<00:15, 18.27it/s, loss=0.3171, acc=0.7183, REM_acc=0.882, Wake_acc=0.861]
Epoch 1:  69%|██████▊   | 595/868 [00:24<00:15, 17.84it/s, loss=0.3171, acc=0.7183, REM_acc=0.882, Wake_acc=0.861]
Epoch 1:  69%|██████▉   | 597/868 [00:25<00:14, 18.26it/s, loss=0.3171, acc=0.7183, REM_acc=0.882, Wake_acc=0.861]
Epoch 1:  69%|██████▉   | 599/868 [00:25<00:15, 17.92it/s, loss=0.3171, acc=0.7183, REM_acc=0.882, Wake_acc=0.861]
Epoch 1:  69%|██████▉   | 599/868 [00:25<00:15, 17.92it/s, loss=0.4488, acc=0.7198, REM_acc=0.883, Wake_acc=0.862]
Epoch 1:  69%|██████▉   | 601/868 [00:25<00:14, 18.04it/s, loss=0.4488, acc=0.7198, REM_acc=0.883, Wake_acc=0.862]
Epoch 1:  70%|██████▉   | 604/868 [00:25<00:12, 20.53it/s, loss=0.4488, acc=0.7198, REM_acc=0.883, Wake_acc=0.862]
Epoch 1:  70%|██████▉   | 607/868 [00:25<00:11, 21.80it/s, loss=0.4488, acc=0.7198, REM_acc=0.883, Wake_acc=0.862]
Epoch 1:  70%|███████   | 610/868 [00:25<00:11, 21.71it/s, loss=0.4488, acc=0.7198, REM_acc=0.883, Wake_acc=0.862]
Epoch 1:  70%|███████   | 610/868 [00:25<00:11, 21.71it/s, loss=0.3805, acc=0.7213, REM_acc=0.884, Wake_acc=0.863]
Epoch 1:  71%|███████   | 613/868 [00:25<00:11, 21.74it/s, loss=0.3805, acc=0.7213, REM_acc=0.884, Wake_acc=0.863]
Epoch 1:  71%|███████   | 616/868 [00:25<00:10, 22.92it/s, loss=0.3805, acc=0.7213, REM_acc=0.884, Wake_acc=0.863]
Epoch 1:  71%|███████▏  | 619/868 [00:26<00:10, 23.97it/s, loss=0.3805, acc=0.7213, REM_acc=0.884, Wake_acc=0.863]
Epoch 1:  71%|███████▏  | 619/868 [00:26<00:10, 23.97it/s, loss=0.2112, acc=0.7227, REM_acc=0.885, Wake_acc=0.862]
Epoch 1:  72%|███████▏  | 622/868 [00:26<00:09, 24.77it/s, loss=0.2112, acc=0.7227, REM_acc=0.885, Wake_acc=0.862]
Epoch 1:  72%|███████▏  | 625/868 [00:26<00:09, 24.36it/s, loss=0.2112, acc=0.7227, REM_acc=0.885, Wake_acc=0.862]
Epoch 1:  72%|███████▏  | 628/868 [00:26<00:09, 25.66it/s, loss=0.2112, acc=0.7227, REM_acc=0.885, Wake_acc=0.862]
Epoch 1:  72%|███████▏  | 628/868 [00:26<00:09, 25.66it/s, loss=0.3118, acc=0.7242, REM_acc=0.887, Wake_acc=0.864]
Epoch 1:  73%|███████▎  | 631/868 [00:26<00:09, 26.27it/s, loss=0.3118, acc=0.7242, REM_acc=0.887, Wake_acc=0.864]
Epoch 1:  73%|███████▎  | 634/868 [00:26<00:08, 27.24it/s, loss=0.3118, acc=0.7242, REM_acc=0.887, Wake_acc=0.864]
Epoch 1:  73%|███████▎  | 637/868 [00:26<00:08, 27.80it/s, loss=0.3118, acc=0.7242, REM_acc=0.887, Wake_acc=0.864]
Epoch 1:  74%|███████▎  | 640/868 [00:26<00:08, 27.23it/s, loss=0.3118, acc=0.7242, REM_acc=0.887, Wake_acc=0.864]
Epoch 1:  74%|███████▎  | 640/868 [00:26<00:08, 27.23it/s, loss=0.3849, acc=0.7258, REM_acc=0.888, Wake_acc=0.865]
Epoch 1:  74%|███████▍  | 643/868 [00:26<00:09, 24.45it/s, loss=0.3849, acc=0.7258, REM_acc=0.888, Wake_acc=0.865]
Epoch 1:  74%|███████▍  | 646/868 [00:27<00:09, 24.37it/s, loss=0.3849, acc=0.7258, REM_acc=0.888, Wake_acc=0.865]
Epoch 1:  75%|███████▍  | 649/868 [00:27<00:08, 25.74it/s, loss=0.3849, acc=0.7258, REM_acc=0.888, Wake_acc=0.865]
Epoch 1:  75%|███████▍  | 649/868 [00:27<00:08, 25.74it/s, loss=0.2985, acc=0.7272, REM_acc=0.888, Wake_acc=0.866]
Epoch 1:  75%|███████▌  | 653/868 [00:27<00:07, 27.70it/s, loss=0.2985, acc=0.7272, REM_acc=0.888, Wake_acc=0.866]
Epoch 1:  76%|███████▌  | 656/868 [00:27<00:07, 27.46it/s, loss=0.2985, acc=0.7272, REM_acc=0.888, Wake_acc=0.866]
Epoch 1:  76%|███████▌  | 659/868 [00:27<00:07, 27.15it/s, loss=0.2985, acc=0.7272, REM_acc=0.888, Wake_acc=0.866]
Epoch 1:  76%|███████▌  | 659/868 [00:27<00:07, 27.15it/s, loss=0.6403, acc=0.7274, REM_acc=0.889, Wake_acc=0.865]
Epoch 1:  76%|███████▋  | 662/868 [00:27<00:07, 26.19it/s, loss=0.6403, acc=0.7274, REM_acc=0.889, Wake_acc=0.865]
Epoch 1:  77%|███████▋  | 665/868 [00:27<00:08, 25.04it/s, loss=0.6403, acc=0.7274, REM_acc=0.889, Wake_acc=0.865]
Epoch 1:  77%|███████▋  | 668/868 [00:27<00:08, 24.04it/s, loss=0.6403, acc=0.7274, REM_acc=0.889, Wake_acc=0.865]
Epoch 1:  77%|███████▋  | 668/868 [00:28<00:08, 24.04it/s, loss=0.4795, acc=0.7283, REM_acc=0.890, Wake_acc=0.867]
Epoch 1:  77%|███████▋  | 671/868 [00:28<00:07, 25.28it/s, loss=0.4795, acc=0.7283, REM_acc=0.890, Wake_acc=0.867]
Epoch 1:  78%|███████▊  | 674/868 [00:28<00:07, 25.59it/s, loss=0.4795, acc=0.7283, REM_acc=0.890, Wake_acc=0.867]
Epoch 1:  78%|███████▊  | 677/868 [00:28<00:07, 24.65it/s, loss=0.4795, acc=0.7283, REM_acc=0.890, Wake_acc=0.867]
Epoch 1:  78%|███████▊  | 680/868 [00:28<00:07, 23.98it/s, loss=0.4795, acc=0.7283, REM_acc=0.890, Wake_acc=0.867]
Epoch 1:  78%|███████▊  | 680/868 [00:28<00:07, 23.98it/s, loss=0.2581, acc=0.7290, REM_acc=0.892, Wake_acc=0.866]
Epoch 1:  79%|███████▉  | 684/868 [00:28<00:07, 25.94it/s, loss=0.2581, acc=0.7290, REM_acc=0.892, Wake_acc=0.866]
Epoch 1:  79%|███████▉  | 687/868 [00:28<00:06, 26.59it/s, loss=0.2581, acc=0.7290, REM_acc=0.892, Wake_acc=0.866]
Epoch 1:  79%|███████▉  | 687/868 [00:28<00:06, 26.59it/s, loss=0.4130, acc=0.7300, REM_acc=0.892, Wake_acc=0.867]
Epoch 1:  80%|███████▉  | 691/868 [00:28<00:06, 27.96it/s, loss=0.4130, acc=0.7300, REM_acc=0.892, Wake_acc=0.867]
Epoch 1:  80%|████████  | 695/868 [00:28<00:06, 28.73it/s, loss=0.4130, acc=0.7300, REM_acc=0.892, Wake_acc=0.867]
Epoch 1:  80%|████████  | 698/868 [00:28<00:05, 28.77it/s, loss=0.4130, acc=0.7300, REM_acc=0.892, Wake_acc=0.867]
Epoch 1:  80%|████████  | 698/868 [00:29<00:05, 28.77it/s, loss=0.3511, acc=0.7311, REM_acc=0.893, Wake_acc=0.868]
Epoch 1:  81%|████████  | 701/868 [00:29<00:05, 28.79it/s, loss=0.3511, acc=0.7311, REM_acc=0.893, Wake_acc=0.868]
Epoch 1:  81%|████████  | 704/868 [00:29<00:06, 26.35it/s, loss=0.3511, acc=0.7311, REM_acc=0.893, Wake_acc=0.868]
Epoch 1:  81%|████████▏ | 707/868 [00:29<00:06, 23.65it/s, loss=0.3511, acc=0.7311, REM_acc=0.893, Wake_acc=0.868]
Epoch 1:  82%|████████▏ | 710/868 [00:29<00:06, 23.16it/s, loss=0.3511, acc=0.7311, REM_acc=0.893, Wake_acc=0.868]
Epoch 1:  82%|████████▏ | 710/868 [00:29<00:06, 23.16it/s, loss=0.3181, acc=0.7325, REM_acc=0.894, Wake_acc=0.868]
Epoch 1:  82%|████████▏ | 713/868 [00:29<00:06, 24.03it/s, loss=0.3181, acc=0.7325, REM_acc=0.894, Wake_acc=0.868]
Epoch 1:  83%|████████▎ | 717/868 [00:29<00:05, 25.93it/s, loss=0.3181, acc=0.7325, REM_acc=0.894, Wake_acc=0.868]
Epoch 1:  83%|████████▎ | 720/868 [00:29<00:05, 26.87it/s, loss=0.3181, acc=0.7325, REM_acc=0.894, Wake_acc=0.868]
Epoch 1:  83%|████████▎ | 720/868 [00:29<00:05, 26.87it/s, loss=0.5403, acc=0.7334, REM_acc=0.895, Wake_acc=0.869]
Epoch 1:  83%|████████▎ | 723/868 [00:29<00:05, 27.49it/s, loss=0.5403, acc=0.7334, REM_acc=0.895, Wake_acc=0.869]
Epoch 1:  84%|████████▎ | 726/868 [00:30<00:05, 27.33it/s, loss=0.5403, acc=0.7334, REM_acc=0.895, Wake_acc=0.869]
Epoch 1:  84%|████████▍ | 729/868 [00:30<00:05, 26.31it/s, loss=0.5403, acc=0.7334, REM_acc=0.895, Wake_acc=0.869]
Epoch 1:  84%|████████▍ | 729/868 [00:30<00:05, 26.31it/s, loss=0.1882, acc=0.7347, REM_acc=0.896, Wake_acc=0.869]
Epoch 1:  84%|████████▍ | 732/868 [00:30<00:05, 26.31it/s, loss=0.1882, acc=0.7347, REM_acc=0.896, Wake_acc=0.869]
Epoch 1:  85%|████████▍ | 735/868 [00:30<00:05, 24.81it/s, loss=0.1882, acc=0.7347, REM_acc=0.896, Wake_acc=0.869]
Epoch 1:  85%|████████▌ | 738/868 [00:30<00:05, 23.67it/s, loss=0.1882, acc=0.7347, REM_acc=0.896, Wake_acc=0.869]
Epoch 1:  85%|████████▌ | 738/868 [00:30<00:05, 23.67it/s, loss=0.1009, acc=0.7360, REM_acc=0.896, Wake_acc=0.870]
Epoch 1:  85%|████████▌ | 741/868 [00:30<00:05, 23.22it/s, loss=0.1009, acc=0.7360, REM_acc=0.896, Wake_acc=0.870]
Epoch 1:  86%|████████▌ | 744/868 [00:30<00:05, 24.36it/s, loss=0.1009, acc=0.7360, REM_acc=0.896, Wake_acc=0.870]
Epoch 1:  86%|████████▌ | 747/868 [00:30<00:04, 24.95it/s, loss=0.1009, acc=0.7360, REM_acc=0.896, Wake_acc=0.870]
Epoch 1:  86%|████████▋ | 750/868 [00:31<00:04, 25.11it/s, loss=0.1009, acc=0.7360, REM_acc=0.896, Wake_acc=0.870]
Epoch 1:  86%|████████▋ | 750/868 [00:31<00:04, 25.11it/s, loss=0.2398, acc=0.7365, REM_acc=0.897, Wake_acc=0.871]
Epoch 1:  87%|████████▋ | 754/868 [00:31<00:04, 26.59it/s, loss=0.2398, acc=0.7365, REM_acc=0.897, Wake_acc=0.871]
Epoch 1:  87%|████████▋ | 758/868 [00:31<00:03, 27.95it/s, loss=0.2398, acc=0.7365, REM_acc=0.897, Wake_acc=0.871]
Epoch 1:  87%|████████▋ | 758/868 [00:31<00:03, 27.95it/s, loss=0.3887, acc=0.7377, REM_acc=0.898, Wake_acc=0.872]
Epoch 1:  88%|████████▊ | 761/868 [00:31<00:03, 28.17it/s, loss=0.3887, acc=0.7377, REM_acc=0.898, Wake_acc=0.872]
Epoch 1:  88%|████████▊ | 764/868 [00:31<00:03, 26.63it/s, loss=0.3887, acc=0.7377, REM_acc=0.898, Wake_acc=0.872]
Epoch 1:  88%|████████▊ | 767/868 [00:31<00:04, 25.02it/s, loss=0.3887, acc=0.7377, REM_acc=0.898, Wake_acc=0.872]
Epoch 1:  89%|████████▊ | 770/868 [00:31<00:04, 24.09it/s, loss=0.3887, acc=0.7377, REM_acc=0.898, Wake_acc=0.872]
Epoch 1:  89%|████████▊ | 770/868 [00:31<00:04, 24.09it/s, loss=0.4337, acc=0.7388, REM_acc=0.898, Wake_acc=0.873]
Epoch 1:  89%|████████▉ | 773/868 [00:31<00:04, 23.67it/s, loss=0.4337, acc=0.7388, REM_acc=0.898, Wake_acc=0.873]
Epoch 1:  89%|████████▉ | 776/868 [00:32<00:04, 22.56it/s, loss=0.4337, acc=0.7388, REM_acc=0.898, Wake_acc=0.873]
Epoch 1:  90%|████████▉ | 779/868 [00:32<00:04, 21.11it/s, loss=0.4337, acc=0.7388, REM_acc=0.898, Wake_acc=0.873]
Epoch 1:  90%|████████▉ | 779/868 [00:32<00:04, 21.11it/s, loss=0.3575, acc=0.7399, REM_acc=0.899, Wake_acc=0.872]
Epoch 1:  90%|█████████ | 782/868 [00:32<00:04, 20.20it/s, loss=0.3575, acc=0.7399, REM_acc=0.899, Wake_acc=0.872]
Epoch 1:  90%|█████████ | 785/868 [00:32<00:04, 19.56it/s, loss=0.3575, acc=0.7399, REM_acc=0.899, Wake_acc=0.872]
Epoch 1:  91%|█████████ | 787/868 [00:32<00:04, 18.68it/s, loss=0.3575, acc=0.7399, REM_acc=0.899, Wake_acc=0.872]
Epoch 1:  91%|█████████ | 789/868 [00:32<00:04, 18.03it/s, loss=0.3575, acc=0.7399, REM_acc=0.899, Wake_acc=0.872]
Epoch 1:  91%|█████████ | 789/868 [00:32<00:04, 18.03it/s, loss=0.2349, acc=0.7398, REM_acc=0.899, Wake_acc=0.873]
Epoch 1:  91%|█████████ | 791/868 [00:32<00:04, 17.84it/s, loss=0.2349, acc=0.7398, REM_acc=0.899, Wake_acc=0.873]
Epoch 1:  91%|█████████▏| 793/868 [00:33<00:04, 18.22it/s, loss=0.2349, acc=0.7398, REM_acc=0.899, Wake_acc=0.873]
Epoch 1:  92%|█████████▏| 796/868 [00:33<00:03, 20.09it/s, loss=0.2349, acc=0.7398, REM_acc=0.899, Wake_acc=0.873]
Epoch 1:  92%|█████████▏| 799/868 [00:33<00:03, 21.06it/s, loss=0.2349, acc=0.7398, REM_acc=0.899, Wake_acc=0.873]
Epoch 1:  92%|█████████▏| 799/868 [00:33<00:03, 21.06it/s, loss=0.2366, acc=0.7405, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  92%|█████████▏| 802/868 [00:33<00:03, 20.87it/s, loss=0.2366, acc=0.7405, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  93%|█████████▎| 806/868 [00:33<00:02, 23.68it/s, loss=0.2366, acc=0.7405, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  93%|█████████▎| 809/868 [00:33<00:02, 24.61it/s, loss=0.2366, acc=0.7405, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  93%|█████████▎| 809/868 [00:33<00:02, 24.61it/s, loss=0.3239, acc=0.7413, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  94%|█████████▎| 813/868 [00:33<00:02, 26.70it/s, loss=0.3239, acc=0.7413, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  94%|█████████▍| 817/868 [00:33<00:01, 28.00it/s, loss=0.3239, acc=0.7413, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  94%|█████████▍| 820/868 [00:34<00:01, 27.32it/s, loss=0.3239, acc=0.7413, REM_acc=0.900, Wake_acc=0.873]
Epoch 1:  94%|█████████▍| 820/868 [00:34<00:01, 27.32it/s, loss=0.3580, acc=0.7424, REM_acc=0.901, Wake_acc=0.873]
Epoch 1:  95%|█████████▍| 823/868 [00:34<00:01, 23.72it/s, loss=0.3580, acc=0.7424, REM_acc=0.901, Wake_acc=0.873]
Epoch 1:  95%|█████████▌| 826/868 [00:34<00:01, 22.68it/s, loss=0.3580, acc=0.7424, REM_acc=0.901, Wake_acc=0.873]
Epoch 1:  96%|█████████▌| 829/868 [00:34<00:01, 23.99it/s, loss=0.3580, acc=0.7424, REM_acc=0.901, Wake_acc=0.873]
Epoch 1:  96%|█████████▌| 829/868 [00:34<00:01, 23.99it/s, loss=0.1837, acc=0.7433, REM_acc=0.902, Wake_acc=0.874]
Epoch 1:  96%|█████████▌| 832/868 [00:34<00:01, 25.09it/s, loss=0.1837, acc=0.7433, REM_acc=0.902, Wake_acc=0.874]
Epoch 1:  96%|█████████▋| 836/868 [00:34<00:01, 27.18it/s, loss=0.1837, acc=0.7433, REM_acc=0.902, Wake_acc=0.874]
Epoch 1:  97%|█████████▋| 839/868 [00:34<00:01, 27.69it/s, loss=0.1837, acc=0.7433, REM_acc=0.902, Wake_acc=0.874]
Epoch 1:  97%|█████████▋| 839/868 [00:34<00:01, 27.69it/s, loss=0.4210, acc=0.7436, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  97%|█████████▋| 842/868 [00:34<00:00, 26.93it/s, loss=0.4210, acc=0.7436, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  97%|█████████▋| 845/868 [00:35<00:00, 26.01it/s, loss=0.4210, acc=0.7436, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  98%|█████████▊| 848/868 [00:35<00:00, 25.05it/s, loss=0.4210, acc=0.7436, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  98%|█████████▊| 848/868 [00:35<00:00, 25.05it/s, loss=0.4278, acc=0.7445, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  98%|█████████▊| 851/868 [00:35<00:00, 25.18it/s, loss=0.4278, acc=0.7445, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  98%|█████████▊| 854/868 [00:35<00:00, 23.03it/s, loss=0.4278, acc=0.7445, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  99%|█████████▊| 857/868 [00:35<00:00, 22.19it/s, loss=0.4278, acc=0.7445, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  99%|█████████▉| 860/868 [00:35<00:00, 21.68it/s, loss=0.4278, acc=0.7445, REM_acc=0.902, Wake_acc=0.875]
Epoch 1:  99%|█████████▉| 860/868 [00:35<00:00, 21.68it/s, loss=0.4091, acc=0.7455, REM_acc=0.903, Wake_acc=0.875]
Epoch 1: 100%|█████████▉| 864/868 [00:35<00:00, 24.48it/s, loss=0.4091, acc=0.7455, REM_acc=0.903, Wake_acc=0.875]
Epoch 1: 100%|█████████▉| 867/868 [00:36<00:00, 25.79it/s, loss=0.4091, acc=0.7455, REM_acc=0.903, Wake_acc=0.875]
Epoch 1: 100%|██████████| 868/868 [00:36<00:00, 24.03it/s, loss=0.4091, acc=0.7455, REM_acc=0.903, Wake_acc=0.875]

Evaluating:   0%|          | 0/147 [00:00<?, ?it/s]
Evaluating:   1%|          | 1/147 [00:00<00:32,  4.44it/s]
Evaluating:   5%|▍         | 7/147 [00:00<00:05, 25.29it/s]
Evaluating:  11%|█         | 16/147 [00:00<00:02, 47.32it/s]
Evaluating:  18%|█▊        | 26/147 [00:00<00:01, 63.57it/s]
Evaluating:  27%|██▋       | 39/147 [00:00<00:01, 83.09it/s]
Evaluating:  35%|███▌      | 52/147 [00:00<00:00, 96.62it/s]
Evaluating:  45%|████▍     | 66/147 [00:00<00:00, 108.18it/s]
Evaluating:  55%|█████▌    | 81/147 [00:00<00:00, 119.64it/s]
Evaluating:  65%|██████▌   | 96/147 [00:01<00:00, 127.94it/s]
Evaluating:  75%|███████▍  | 110/147 [00:01<00:00, 128.17it/s]
Evaluating:  85%|████████▌ | 125/147 [00:01<00:00, 132.87it/s]
Evaluating:  95%|█████████▍| 139/147 [00:01<00:00, 130.27it/s]
Evaluating: 100%|██████████| 147/147 [00:01<00:00, 95.65it/s] 
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_FIXED.py", line 490, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_FIXED.py", line 486, in main
    train_v14_fixed(config, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_FIXED.py", line 389, in train_v14_fixed
    torch.save(best_model_state, '../../checkpoints/v14_fixed.pth')
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/serialization.py", line 964, in save
    with _open_zipfile_writer(f) as opened_zipfile:
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/serialization.py", line 828, in _open_zipfile_writer
    return container(name_or_buffer)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/serialization.py", line 792, in __init__
    torch._C.PyTorchFileWriter(
RuntimeError: Parent directory ../../checkpoints does not exist.
