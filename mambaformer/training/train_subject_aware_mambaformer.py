"""
基于受试者感知K折的MAMBAFORMER训练脚本
避免数据泄露，确保跨受试者泛化性能评估的准确性
"""

import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import json
import logging
import datetime
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
from torch.utils.data import Dataset, DataLoader
import time
from tqdm import tqdm

# 导入我们的简化MAMBAFORMER模型
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.simple_attn_mambaformer import MultiChannelSimplifiedMAMBAFORMER, SimplifiedMAMBAFORMER

def setup_logging(log_dir="logs"):
    """设置日志记录"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"subject_aware_mambaformer_{timestamp}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件创建: {log_file}")
    return logger, log_file

def load_subject_aware_folds(fold_file='subject_aware_folds.json'):
    """加载受试者感知的K折数据"""
    with open(fold_file, 'r') as f:
        fold_data = json.load(f)
    
    folds = {}
    for fold_id_str, fold_info in fold_data['folds'].items():
        fold_id = int(fold_id_str)
        folds[fold_id] = {
            'train': fold_info['train_files'],
            'test': fold_info['test_files'],
            'train_subjects': fold_info['train_subjects'],
            'test_subjects': fold_info['test_subjects']
        }
    
    return folds, fold_data['subject_fold_map'], fold_data['total_subjects']

class NPZDataset(Dataset):
    """NPZ数据集加载器"""
    def __init__(self, npz_files, max_samples_per_file=None, use_channels=3, logger=None):
        self.data = []
        self.labels = []
        self.use_channels = use_channels
        
        if logger:
            logger.info(f"加载 {len(npz_files)} 个NPZ文件...")
        else:
            print(f"加载 {len(npz_files)} 个NPZ文件...")
        
        for file_path in tqdm(npz_files, desc="加载数据"):
            try:
                data = np.load(file_path)
                x = data['x']  # (N, 3000, 4)
                y = data['y']  # (N,)
                
                # 限制样本数量
                if max_samples_per_file and len(x) > max_samples_per_file:
                    indices = np.random.choice(len(x), max_samples_per_file, replace=False)
                    x = x[indices]
                    y = y[indices]
                
                # 只使用前N个通道
                if x.shape[2] > use_channels:
                    x = x[:, :, :use_channels]  # (N, 3000, use_channels)
                
                self.data.append(x)
                self.labels.append(y)
                
            except Exception as e:
                error_msg = f"加载文件 {file_path} 失败: {e}"
                if logger:
                    logger.warning(error_msg)
                else:
                    print(error_msg)
                continue
        
        if self.data:
            # 合并所有数据
            self.data = np.concatenate(self.data, axis=0)
            self.labels = np.concatenate(self.labels, axis=0)
            
            info_msg = f"总数据: {self.data.shape}, 标签: {self.labels.shape}"
            label_dist_msg = f"标签分布: {np.bincount(self.labels)}"
            
            if logger:
                logger.info(info_msg)
                logger.info(label_dist_msg)
            else:
                print(info_msg)
                print(label_dist_msg)
        else:
            self.data = np.array([]).reshape(0, 3000, use_channels)
            self.labels = np.array([])
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        # 转换数据维度: (3000, channels) -> (channels, 3000)
        x = torch.FloatTensor(self.data[idx]).transpose(0, 1)  # (channels, 3000)
        y = torch.LongTensor([self.labels[idx]]).squeeze()
        
        return x, y

class MultiTaskLoss(nn.Module):
    """多任务损失函数"""
    def __init__(self, alpha=None, gamma=2.0, aux_weight=0.2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.aux_weight = aux_weight
        
    def focal_loss(self, inputs, targets):
        """Focal Loss"""
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if self.alpha.device != focal_loss.device:
                self.alpha = self.alpha.to(focal_loss.device)
            focal_loss = self.alpha[targets] * focal_loss
        
        return focal_loss.mean()
    
    def forward(self, outputs, targets):
        """计算多任务损失"""
        if isinstance(outputs, dict):
            # 多任务输出
            main_loss = F.cross_entropy(outputs['stage_logits'], targets)
            
            # 辅助任务损失
            aux_loss = 0.0
            if 'rem_scores' in outputs:
                rem_targets = (targets == 4).float()  # REM标签是4
                rem_loss = F.binary_cross_entropy(outputs['rem_scores'], rem_targets)
                aux_loss += rem_loss
                
            if 'sws_scores' in outputs:
                sws_targets = (targets == 3).float()  # N3标签是3
                sws_loss = F.binary_cross_entropy(outputs['sws_scores'], sws_targets)
                aux_loss += sws_loss
            
            total_loss = main_loss + self.aux_weight * aux_loss
            return total_loss, main_loss, aux_loss
        else:
            # 单任务输出
            main_loss = F.cross_entropy(outputs, targets)
            return main_loss, main_loss, 0.0

def train_epoch(model, dataloader, criterion, optimizer, device, scheduler=None):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    total_main_loss = 0
    total_aux_loss = 0
    all_preds = []
    all_labels = []
    
    for batch_x, batch_y in tqdm(dataloader, desc="Training", leave=False):
        batch_x = batch_x.to(device)
        batch_y = batch_y.to(device)
        
        # 前向传播
        outputs = model(batch_x)
        
        # 计算损失
        loss, main_loss, aux_loss = criterion(outputs, batch_y)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        if scheduler:
            scheduler.step()
        
        # 记录
        total_loss += loss.item()
        total_main_loss += main_loss.item()
        if isinstance(aux_loss, torch.Tensor):
            total_aux_loss += aux_loss.item()
        else:
            total_aux_loss += aux_loss
        
        # 获取预测
        if isinstance(outputs, dict):
            preds = torch.argmax(outputs['stage_logits'], 1)
        else:
            preds = torch.argmax(outputs, 1)
            
        all_preds.extend(preds.cpu().numpy())
        all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    avg_main_loss = total_main_loss / len(dataloader)
    avg_aux_loss = total_aux_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, avg_main_loss, avg_aux_loss, accuracy

def evaluate(model, dataloader, criterion, device):
    """评估模型"""
    model.eval()
    total_loss = 0
    total_main_loss = 0
    total_aux_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_x, batch_y in tqdm(dataloader, desc="Evaluating", leave=False):
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            outputs = model(batch_x)
            loss, main_loss, aux_loss = criterion(outputs, batch_y)
            
            total_loss += loss.item()
            total_main_loss += main_loss.item()
            if isinstance(aux_loss, torch.Tensor):
                total_aux_loss += aux_loss.item()
            else:
                total_aux_loss += aux_loss
            
            # 获取预测
            if isinstance(outputs, dict):
                preds = torch.argmax(outputs['stage_logits'], 1)
            else:
                preds = torch.argmax(outputs, 1)
                
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    avg_main_loss = total_main_loss / len(dataloader)
    avg_aux_loss = total_aux_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    return avg_loss, avg_main_loss, avg_aux_loss, accuracy, f1, all_preds, all_labels

def train_fold(fold_id, train_files, test_files, train_subjects, test_subjects, config, logger=None):
    """训练单个fold"""
    if logger is None:
        logger = logging.getLogger(__name__)
    
    logger.info(f"\n开始训练 Fold {fold_id + 1}")
    logger.info(f"训练文件数: {len(train_files)}, 测试文件数: {len(test_files)}")
    logger.info(f"训练受试者: {train_subjects}")
    logger.info(f"测试受试者: {test_subjects}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据集
    logger.info("创建数据集...")
    train_dataset = NPZDataset(
        train_files, 
        max_samples_per_file=config.get('max_samples_per_file', 200),
        use_channels=config.get('use_channels', 3),
        logger=logger
    )
    test_dataset = NPZDataset(
        test_files, 
        max_samples_per_file=None,  # 测试集不限制
        use_channels=config.get('use_channels', 3),
        logger=logger
    )
    
    if len(train_dataset) < 50 or len(test_dataset) < 10:
        logger.warning(f"数据不足，跳过此fold")
        return None
    
    # 分出验证集
    train_size = int(0.9 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size]
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    if config.get('use_channels', 3) == 1:
        model = SimplifiedMAMBAFORMER().to(device)
    else:
        model = MultiChannelSimplifiedMAMBAFORMER().to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"模型参数量: {total_params:,}")
    
    # 损失函数和优化器
    criterion = MultiTaskLoss(gamma=2.0, aux_weight=0.2)
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        eps=1e-8
    )
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 5,
        steps_per_epoch=len(train_loader),
        epochs=config['num_epochs'],
        pct_start=0.1
    )
    
    # 训练循环
    best_val_f1 = 0
    best_model_state = None
    patience = 0
    max_patience = config.get('patience', 10)
    
    for epoch in range(config['num_epochs']):
        epoch_msg = f"\nEpoch {epoch + 1}/{config['num_epochs']}"
        logger.info(epoch_msg)
        
        # 训练
        train_loss, train_main_loss, train_aux_loss, train_acc = train_epoch(
            model, train_loader, criterion, optimizer, device, scheduler
        )
        
        # 验证
        val_loss, val_main_loss, val_aux_loss, val_acc, val_f1, _, _ = evaluate(
            model, val_loader, criterion, device
        )
        
        current_lr = optimizer.param_groups[0]['lr']
        train_msg = f"Train: Loss={train_loss:.4f} (Main={train_main_loss:.4f}, Aux={train_aux_loss:.4f}), Acc={train_acc:.4f}"
        val_msg = f"Val: Loss={val_loss:.4f} (Main={val_main_loss:.4f}, Aux={val_aux_loss:.4f}), Acc={val_acc:.4f}, F1={val_f1:.4f}"
        lr_msg = f"LR: {current_lr:.2e}"
        
        logger.info(train_msg)
        logger.info(val_msg)
        logger.info(lr_msg)
        
        # 保存最佳模型
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_model_state = model.state_dict().copy()
            patience = 0
            best_msg = f"💾 新的最佳F1: {best_val_f1:.4f}"
            logger.info(best_msg)
        else:
            patience += 1
            if patience >= max_patience:
                early_stop_msg = f"早停：验证F1已{patience}轮未改善"
                logger.info(early_stop_msg)
                break
    
    # 在测试集上评估最佳模型
    model.load_state_dict(best_model_state)
    test_loss, test_main_loss, test_aux_loss, test_acc, test_f1, test_preds, test_labels = evaluate(
        model, test_loader, criterion, device
    )
    
    final_result_msg = f"\n🎯 Fold {fold_id + 1} 最终结果:"
    best_val_msg = f"最佳验证F1: {best_val_f1:.4f}"
    test_acc_msg = f"测试准确率: {test_acc:.4f}"
    test_f1_msg = f"测试F1分数: {test_f1:.4f}"
    
    logger.info(final_result_msg)
    logger.info(best_val_msg)
    logger.info(test_acc_msg)
    logger.info(test_f1_msg)
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    cm = confusion_matrix(test_labels, test_preds)
    report = classification_report(test_labels, test_preds, target_names=class_names, digits=4)
    
    logger.info("\n混淆矩阵:")
    logger.info(str(cm))
    logger.info("\n分类报告:")
    logger.info(report)
    
    # 保存模型
    model_save_path = f'subject_aware_mambaformer_fold_{fold_id + 1}.pth'
    torch.save({
        'model_state_dict': best_model_state,
        'config': config,
        'fold_id': fold_id,
        'test_acc': test_acc,
        'test_f1': test_f1,
        'val_f1': best_val_f1,
        'model_params': total_params,
        'train_subjects': train_subjects,
        'test_subjects': test_subjects
    }, model_save_path)
    
    return {
        'fold_id': fold_id,
        'test_acc': test_acc,
        'test_f1': test_f1,
        'val_f1': best_val_f1,
        'confusion_matrix': cm.tolist(),
        'classification_report': report,
        'model_params': total_params,
        'train_subjects': train_subjects,
        'test_subjects': test_subjects
    }

def main():
    """主训练函数"""
    # 设置日志
    logger, log_file = setup_logging()
    
    # 配置
    config = {
        'batch_size': 32,
        'learning_rate': 5e-5,
        'weight_decay': 1e-4,
        'num_epochs': 30,
        'max_samples_per_file': 150,  # 限制每个文件的样本数以平衡内存和速度
        'use_channels': 3,  # 使用前3个通道
        'patience': 8
    }
    
    title_msg = "受试者感知MAMBAFORMER + 无数据泄露训练"
    separator = "=" * 60
    config_msgs = [
        f"使用通道数: {config['use_channels']}",
        f"批次大小: {config['batch_size']}",
        f"学习率: {config['learning_rate']}",
        f"日志文件: {log_file}",
        "✅ 采用受试者感知K折划分，避免数据泄露"
    ]
    
    logger.info(title_msg)
    logger.info(separator)
    for msg in config_msgs:
        logger.info(msg)
    
    # 加载受试者感知的K折数据
    try:
        folds, subject_fold_map, total_subjects = load_subject_aware_folds()
        logger.info(f"成功加载受试者感知K折数据: {total_subjects}个受试者, {len(folds)}个fold")
    except Exception as e:
        logger.error(f"加载K折数据失败: {e}")
        print("请先运行 python subject_aware_kfold.py 创建受试者感知的K折划分")
        return
    
    # 训练所有fold
    all_results = []
    
    for fold_id in range(len(folds)):
        logger.info(f"\n{'='*60}")
        logger.info(f"Fold {fold_id + 1}/{len(folds)}")
        logger.info(f"{'='*60}")
        
        try:
            result = train_fold(
                fold_id,
                folds[fold_id]['train'],
                folds[fold_id]['test'],
                folds[fold_id]['train_subjects'],
                folds[fold_id]['test_subjects'],
                config,
                logger
            )
            
            if result:
                all_results.append(result)
                logger.info(f"✅ Fold {fold_id + 1} 完成")
            else:
                logger.warning(f"⚠️ Fold {fold_id + 1} 跳过")
        
        except Exception as e:
            logger.error(f"❌ Fold {fold_id + 1} 训练失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 汇总结果
    if all_results:
        summary_separator = f"\n{'='*60}"
        summary_title = "🏆 受试者感知MAMBAFORMER K折交叉验证结果汇总"
        
        logger.info(summary_separator)
        logger.info(summary_title)
        logger.info("="*60)
        
        test_accs = [r['test_acc'] for r in all_results]
        test_f1s = [r['test_f1'] for r in all_results]
        
        mean_acc_msg = f"平均测试准确率: {np.mean(test_accs):.4f} ± {np.std(test_accs):.4f}"
        mean_f1_msg = f"平均测试F1分数: {np.mean(test_f1s):.4f} ± {np.std(test_f1s):.4f}"
        
        logger.info(mean_acc_msg)
        logger.info(mean_f1_msg)
        
        detail_msg = "\n各Fold详细结果:"
        logger.info(detail_msg)
        for result in all_results:
            fold_result_msg = f"Fold {result['fold_id']+1}: Acc={result['test_acc']:.4f}, F1={result['test_f1']:.4f} (测试受试者: {result['test_subjects']})"
            logger.info(fold_result_msg)
        
        # 保存汇总结果
        summary_result = {
            'config': config,
            'fold_results': all_results,
            'subject_fold_map': subject_fold_map,
            'summary_statistics': {
                'mean_accuracy': float(np.mean(test_accs)),
                'std_accuracy': float(np.std(test_accs)),
                'mean_f1_score': float(np.mean(test_f1s)),
                'std_f1_score': float(np.std(test_f1s)),
                'num_folds_completed': len(all_results),
                'total_subjects': total_subjects,
                'architecture': 'Subject-Aware-Simplified-MAMBAFORMER'
            }
        }
        
        with open('subject_aware_mambaformer_results.json', 'w') as f:
            json.dump(summary_result, f, indent=2, default=str)
        
        completion_msg = f"📊 完整结果已保存至: subject_aware_mambaformer_results.json"
        success_msg = f"🎉 受试者感知MAMBAFORMER训练完成！"
        
        logger.info(completion_msg)
        logger.info(success_msg)
        
        # 数据泄露对比分析
        logger.info(f"\n📈 数据泄露影响分析:")
        logger.info(f"受试者感知MAMBAFORMER: {np.mean(test_accs):.4f} ± {np.std(test_accs):.4f}")
        logger.info(f"这是真实的跨受试者泛化性能，没有数据泄露")
    else:
        logger.error("❌ 没有成功完成任何fold，请检查数据和配置")

if __name__ == "__main__":
    main()