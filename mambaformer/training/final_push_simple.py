"""
最终推进策略 - 简化版
基于现有最佳模型进行优化
目标：ACC=87%, Kappa=0.8, MF1=80%
"""

import os
import sys
import json
import torch
import torch.nn as nn
import numpy as np
import logging
from tqdm import tqdm
import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import create_sequence_dataloaders, SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"final_push_simple_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


def evaluate_model(model_path, model_config, test_dataset, test_loader, device, model_name):
    """评估单个模型"""
    logging.info(f"\n评估模型: {model_name}")
    
    # 加载模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=model_config['d_model'],
        n_heads=model_config['n_heads'],
        n_layers=model_config['n_layers'],
        dropout=model_config['dropout'],
        seq_len=model_config['seq_len']
    ).to(device)
    
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
    else:
        logging.warning(f"模型文件不存在: {model_path}")
        return None
    
    # Epoch级别评估
    evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (data, labels) in enumerate(tqdm(test_loader, desc=f"Evaluating {model_name}")):
            data = data.to(device)
            labels = labels.to(device)
            
            # 获取模型输出
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取序列信息
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        start_indices.append(seq_idx)
                else:
                    break
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    metrics = evaluator.evaluate()
    
    logging.info(f"{model_name} - ACC: {metrics['accuracy']:.4f}, "
                f"F1: {metrics['macro_f1']:.4f}, Kappa: {metrics['kappa']:.4f}")
    
    return metrics


def ensemble_predictions(models_predictions, weights=None):
    """集成多个模型的预测"""
    if weights is None:
        weights = np.ones(len(models_predictions)) / len(models_predictions)
    
    # 加权平均
    ensemble_pred = np.zeros_like(models_predictions[0])
    for pred, weight in zip(models_predictions, weights):
        ensemble_pred += pred * weight
    
    return ensemble_pred


def main():
    log_file = setup_logging()
    
    logging.info("🚀 最终推进 - 简化版")
    logging.info("=" * 80)
    logging.info("🎯 目标: ACC=87%, Kappa=0.8, MF1=80%")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 模型配置
    models_info = {
        'V7_Sequential': {
            'path': '../../checkpoints/sequential_v7_balanced.pth',
            'config': {
                'd_model': 128,
                'n_heads': 8,
                'n_layers': 4,
                'dropout': 0.1,
                'seq_len': 5
            }
        },
        'V8_Enhanced': {
            'path': '../../checkpoints/sequential_v8_enhanced.pth',
            'config': {
                'd_model': 128,
                'n_heads': 8,
                'n_layers': 4,
                'dropout': 0.1,
                'seq_len': 5
            }
        }
    }
    
    # 加载测试数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 创建测试数据集
    test_dataset = SequenceSleepDataset(test_files, seq_len=5, use_channels=3)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, shuffle=False,
        num_workers=4, pin_memory=True
    )
    
    logging.info(f"测试集大小: {len(test_dataset)}")
    
    # 评估各个模型
    all_results = {}
    
    for model_name, model_info in models_info.items():
        metrics = evaluate_model(
            model_info['path'],
            model_info['config'],
            test_dataset,
            test_loader,
            device,
            model_name
        )
        if metrics:
            all_results[model_name] = metrics
    
    # 分析当前结果
    logging.info("\n" + "="*80)
    logging.info("📊 当前结果分析")
    logging.info("="*80)
    
    # 目标
    target_acc = 0.87
    target_kappa = 0.8
    target_f1 = 0.8
    
    # 找出最佳模型
    best_f1 = 0
    best_model = None
    
    for model_name, metrics in all_results.items():
        if metrics['macro_f1'] > best_f1:
            best_f1 = metrics['macro_f1']
            best_model = model_name
    
    if best_model:
        best_metrics = all_results[best_model]
        logging.info(f"最佳模型: {best_model}")
        logging.info(f"ACC: {best_metrics['accuracy']:.4f} (目标: {target_acc}, 差距: {target_acc - best_metrics['accuracy']:.4f})")
        logging.info(f"F1:  {best_metrics['macro_f1']:.4f} (目标: {target_f1}, 差距: {target_f1 - best_metrics['macro_f1']:.4f})")
        logging.info(f"Kappa: {best_metrics['kappa']:.4f} (目标: {target_kappa}, 差距: {target_kappa - best_metrics['kappa']:.4f})")
    
    # 策略建议
    logging.info("\n🔧 优化策略建议:")
    
    if best_metrics['accuracy'] < target_acc:
        acc_gap = target_acc - best_metrics['accuracy']
        logging.info(f"1. 准确率差距 {acc_gap:.4f}:")
        logging.info("   - 增加模型容量 (d_model=256或更多层)")
        logging.info("   - 使用更强的数据增强")
        logging.info("   - 优化类别平衡策略")
    
    if best_metrics['macro_f1'] < target_f1:
        f1_gap = target_f1 - best_metrics['macro_f1']
        logging.info(f"2. F1分数差距 {f1_gap:.4f}:")
        logging.info("   - 使用更强的Focal Loss (γ=3.0)")
        logging.info("   - 增加困难类别的权重")
        logging.info("   - 使用集成学习")
    
    if best_metrics['kappa'] < target_kappa:
        kappa_gap = target_kappa - best_metrics['kappa']
        logging.info(f"3. Kappa系数差距 {kappa_gap:.4f}:")
        logging.info("   - 使用后处理平滑")
        logging.info("   - 增加时序一致性约束")
        logging.info("   - 优化序列建模")
    
    # 尝试后处理优化
    logging.info("\n📊 应用规则后处理...")
    rule_processor = RuleBasedPostProcessor()
    
    # 对最佳模型应用后处理
    if best_model:
        model_path = models_info[best_model]['path']
        model_config = models_info[best_model]['config']
        
        model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=model_config['d_model'],
            n_heads=model_config['n_heads'],
            n_layers=model_config['n_layers'],
            dropout=model_config['dropout'],
            seq_len=model_config['seq_len']
        ).to(device)
        
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
        
        # 收集所有预测
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for data, labels in test_loader:
                data = data.to(device)
                outputs, _ = model(data)
                preds = torch.argmax(outputs, dim=-1)
                all_preds.extend(preds.cpu().numpy().flatten())
                all_labels.extend(labels.cpu().numpy().flatten())
        
        # 应用后处理
        all_preds = np.array(all_preds)
        all_labels = np.array(all_labels)
        smoothed_preds = rule_processor.smooth(all_preds)
        
        # 计算后处理后的指标
        pp_metrics = get_comprehensive_metrics(all_labels, smoothed_preds)
        
        logging.info(f"\n后处理结果:")
        logging.info(f"ACC: {pp_metrics['accuracy']:.4f} (提升: {pp_metrics['accuracy'] - best_metrics['accuracy']:.4f})")
        logging.info(f"F1:  {pp_metrics['macro_f1']:.4f} (提升: {pp_metrics['macro_f1'] - best_metrics['macro_f1']:.4f})")
        logging.info(f"Kappa: {pp_metrics['kappa']:.4f} (提升: {pp_metrics['kappa'] - best_metrics['kappa']:.4f})")
    
    # 保存分析结果
    results = {
        'models_evaluated': all_results,
        'best_model': best_model,
        'target_gaps': {
            'acc_gap': float(target_acc - best_metrics['accuracy']),
            'f1_gap': float(target_f1 - best_metrics['macro_f1']),
            'kappa_gap': float(target_kappa - best_metrics['kappa'])
        },
        'log_file': log_file
    }
    
    with open('../../configs/final_push_analysis.json', 'w') as f:
        json.dump(results, f, indent=2, default=lambda x: float(x) if hasattr(x, 'item') else x)
    
    logging.info("\n💾 分析结果已保存")
    logging.info("🌟 最终推进分析完成！")


if __name__ == "__main__":
    main()