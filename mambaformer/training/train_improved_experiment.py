#!/usr/bin/env python3
"""
改进版实验 - 解决REM检测问题，目标85%+准确率
基于成功的81.33%模型进行优化
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
import h5py
import logging
from datetime import datetime
import json
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import gc

# 设置CUDA设备
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

class ImprovedSleepNet(nn.Module):
    """改进的睡眠分期网络 - 增强REM检测能力"""
    def __init__(self, input_channels=1, num_classes=5, seq_len=3000):
        super().__init__()
        
        # 多尺度CNN特征提取
        self.multi_scale_cnn = nn.ModuleList([
            # 短时特征（高频）
            nn.Sequential(
                nn.Conv1d(input_channels, 32, kernel_size=10, stride=5, padding=5),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.MaxPool1d(2)
            ),
            # 中时特征
            nn.Sequential(
                nn.Conv1d(input_channels, 32, kernel_size=50, stride=25, padding=25),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.MaxPool1d(2)
            ),
            # 长时特征（低频）
            nn.Sequential(
                nn.Conv1d(input_channels, 32, kernel_size=100, stride=50, padding=50),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.MaxPool1d(2)
            )
        ])
        
        # 先对齐多尺度特征的长度
        self.align_features = nn.ModuleList([
            nn.AdaptiveAvgPool1d(30),  # 统一到相同长度
            nn.AdaptiveAvgPool1d(30),
            nn.AdaptiveAvgPool1d(30)
        ])
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(96, 128, kernel_size=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, kernel_size=5, stride=2, padding=2),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(8)  # 固定输出长度
        )
        
        # REM专用特征提取器（EOG信号对REM更敏感）
        self.rem_detector = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=30, stride=15, padding=15),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 32, kernel_size=10, stride=5, padding=5),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(4)
        )
        
        # Transformer层
        self.transformer_layer = nn.TransformerEncoderLayer(
            d_model=256,
            nhead=8,
            dim_feedforward=1024,
            dropout=0.3,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(self.transformer_layer, num_layers=3)
        
        # 跨模态注意力
        self.cross_modal_attn = nn.MultiheadAttention(
            embed_dim=256,
            num_heads=8,
            dropout=0.3,
            batch_first=True
        )
        
        # 分阶段分类器
        # 第一阶段：Wake vs Sleep
        self.wake_classifier = nn.Linear(256 * 2 + 32, 2)
        
        # 第二阶段：NREM vs REM (针对睡眠样本)
        self.rem_classifier = nn.Linear(256 * 2 + 32, 2)
        
        # 第三阶段：N1 vs N2 vs N3 (针对NREM样本)
        self.nrem_classifier = nn.Linear(256 * 2 + 32, 3)
        
        # 最终分类器（端到端）
        self.final_classifier = nn.Sequential(
            nn.Linear(256 * 2 + 32, 512),
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, eeg, eog, stage='final'):
        batch_size = eeg.shape[0]
        
        # 多尺度特征提取
        multi_scale_feats = []
        for i, cnn in enumerate(self.multi_scale_cnn):
            feat = cnn(eeg)
            # 对齐特征长度
            feat = self.align_features[i](feat)
            multi_scale_feats.append(feat)
        
        # 拼接多尺度特征
        eeg_multi = torch.cat(multi_scale_feats, dim=1)  # [batch, 96, 30]
        
        # 特征融合
        eeg_feat = self.feature_fusion(eeg_multi)  # [batch, 256, 8]
        
        # EOG处理（相同方式）
        eog_multi_scale_feats = []
        for i, cnn in enumerate(self.multi_scale_cnn):
            feat = cnn(eog)
            # 对齐特征长度
            feat = self.align_features[i](feat)
            eog_multi_scale_feats.append(feat)
        eog_multi = torch.cat(eog_multi_scale_feats, dim=1)
        eog_feat = self.feature_fusion(eog_multi)  # [batch, 256, 8]
        
        # REM特征（主要从EOG提取）
        rem_feat = self.rem_detector(eog)  # [batch, 32, 4]
        rem_feat_global = rem_feat.mean(dim=2)  # [batch, 32]
        
        # 转置为transformer格式
        eeg_feat = eeg_feat.transpose(1, 2)  # [batch, 8, 256]
        eog_feat = eog_feat.transpose(1, 2)  # [batch, 8, 256]
        
        # Transformer编码
        eeg_encoded = self.transformer(eeg_feat)
        eog_encoded = self.transformer(eog_feat)
        
        # 跨模态注意力
        eeg_cross, _ = self.cross_modal_attn(eeg_encoded, eog_encoded, eog_encoded)
        eog_cross, _ = self.cross_modal_attn(eog_encoded, eeg_encoded, eeg_encoded)
        
        # 全局特征
        eeg_global = eeg_cross.mean(dim=1)  # [batch, 256]
        eog_global = eog_cross.mean(dim=1)  # [batch, 256]
        
        # 组合所有特征
        combined = torch.cat([eeg_global, eog_global, rem_feat_global], dim=1)  # [batch, 544]
        
        if stage == 'wake':
            return self.wake_classifier(combined)
        elif stage == 'rem':
            return self.rem_classifier(combined)
        elif stage == 'nrem':
            return self.nrem_classifier(combined)
        else:  # final
            return self.final_classifier(combined)

class BalancedSleepDataset(Dataset):
    """平衡的数据集 - 确保REM样本得到充分训练"""
    def __init__(self, data_path, subjects, max_epochs_per_subject=200, augment=True):
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        self.augment = augment
        
        print(f"加载数据集...")
        
        for subj in subjects:
            eeg_file = os.path.join(data_path, f'x{int(subj):02d}.h5')
            eog_file = os.path.join(data_path, f'eog{int(subj):02d}.h5')
            label_file = os.path.join(data_path, f'y{int(subj):02d}.h5')
            
            if os.path.exists(eeg_file):
                with h5py.File(eeg_file, 'r') as f:
                    data = f['data'][:]
                    n_samples = min(len(data), max_epochs_per_subject)
                    self.eeg_data.append(data[:n_samples])
                    
                with h5py.File(eog_file, 'r') as f:
                    data = f['data'][:]
                    self.eog_data.append(data[:n_samples])
                    
                with h5py.File(label_file, 'r') as f:
                    data = f['data'][:]
                    self.labels.append(data[:n_samples])
        
        # 合并数据
        self.eeg_data = np.concatenate(self.eeg_data, axis=0).astype(np.float32)
        self.eog_data = np.concatenate(self.eog_data, axis=0).astype(np.float32)
        self.labels = np.concatenate(self.labels, axis=0).astype(np.int64)
        
        # 标准化
        self.eeg_data = (self.eeg_data - self.eeg_data.mean()) / (self.eeg_data.std() + 1e-6)
        self.eog_data = (self.eog_data - self.eog_data.mean()) / (self.eog_data.std() + 1e-6)
        
        # 统计类别分布
        self.class_counts = np.bincount(self.labels)
        print(f"数据集大小: {len(self.labels)} 样本")
        print(f"标签分布: {self.class_counts}")
        
        # 计算类别权重（用于采样）
        self.weights = 1.0 / self.class_counts[self.labels]
        
    def __len__(self):
        return len(self.labels)
    
    def augment_signal(self, signal, label):
        """针对性的数据增强 - 特别增强REM样本"""
        if not self.augment:
            return signal
            
        # REM类别（标签4）进行更强的增强
        if label == 4:
            # REM特征：快速眼动，低幅度EEG
            if np.random.rand() > 0.3:
                # 添加眼动伪迹
                t = np.linspace(0, len(signal)/100, len(signal))
                eye_movement = 0.1 * np.sin(2 * np.pi * np.random.uniform(0.5, 2) * t)
                signal = signal + eye_movement
        
        # 通用增强
        if np.random.rand() > 0.5:
            # 时间偏移
            shift = np.random.randint(-100, 100)
            signal = np.roll(signal, shift)
            
        if np.random.rand() > 0.5:
            # 幅度缩放
            scale = np.random.uniform(0.8, 1.2)
            signal = signal * scale
            
        if np.random.rand() > 0.7:
            # 添加噪声
            noise = np.random.normal(0, 0.05, signal.shape)
            signal = signal + noise
            
        return signal
    
    def __getitem__(self, idx):
        eeg = self.eeg_data[idx].copy()
        eog = self.eog_data[idx].copy()
        label = self.labels[idx]
        
        # 数据增强
        if self.augment:
            eeg = self.augment_signal(eeg, label)
            eog = self.augment_signal(eog, label)
        
        eeg = torch.FloatTensor(eeg).unsqueeze(0)
        eog = torch.FloatTensor(eog).unsqueeze(0)
        label = torch.LongTensor([label]).squeeze()
        
        return eeg, eog, label

def focal_loss(outputs, targets, alpha=None, gamma=2.0):
    """Focal Loss - 解决类别不平衡问题"""
    ce_loss = F.cross_entropy(outputs, targets, reduction='none', weight=alpha)
    pt = torch.exp(-ce_loss)
    focal_loss = (1 - pt) ** gamma * ce_loss
    return focal_loss.mean()

def train_epoch(model, train_loader, optimizer, device, alpha, gamma=2.0):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (eeg, eog, labels) in enumerate(tqdm(train_loader, desc='Training')):
        eeg = eeg.to(device)
        eog = eog.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 多阶段训练
        outputs = model(eeg, eog, stage='final')
        loss = focal_loss(outputs, labels, alpha=alpha, gamma=gamma)
        
        # 额外的阶段性损失（辅助训练）
        if batch_idx % 3 == 0:
            # Wake分类损失
            wake_labels = (labels == 0).long()
            wake_outputs = model(eeg, eog, stage='wake')
            wake_loss = F.cross_entropy(wake_outputs, wake_labels)
            loss += 0.2 * wake_loss
        
        # L2正则化
        l2_lambda = 0.0005
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()
    
    return total_loss / len(train_loader), correct / total

def validate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg, eog, labels in tqdm(val_loader, desc='Validating'):
            eeg = eeg.to(device)
            eog = eog.to(device)
            labels = labels.to(device)
            
            outputs = model(eeg, eog, stage='final')
            _, predicted = outputs.max(1)
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(all_labels, all_preds)
    return accuracy, all_preds, all_labels

def main():
    # 清理内存
    gc.collect()
    torch.cuda.empty_cache()
    
    # 配置
    config = {
        'data_path': './processed_data_fixed',
        'train_subjects': [1, 2, 3, 4, 5, 6],   # 6个训练受试者
        'val_subjects': [7, 8],                 # 2个验证受试者
        'test_subjects': [9],                   # 1个测试受试者
        'max_epochs_per_subject': 200,
        'batch_size': 48,
        'epochs': 50,
        'lr': 0.0005,
        'patience': 10,
        'focal_gamma': 2.0,
        # 加强REM权重
        'weights': [1.0, 2.0, 1.0, 2.0, 3.0]  # [W, N1, N2, N3, REM]
    }
    
    # 设置日志
    os.makedirs('./log', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f'./log/improved_experiment_{timestamp}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 改进版实验 - 目标85%+准确率")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据集
    train_dataset = BalancedSleepDataset(
        config['data_path'], 
        config['train_subjects'],
        config['max_epochs_per_subject'],
        augment=True
    )
    val_dataset = BalancedSleepDataset(
        config['data_path'], 
        config['val_subjects'],
        config['max_epochs_per_subject'],
        augment=False
    )
    test_dataset = BalancedSleepDataset(
        config['data_path'], 
        config['test_subjects'],
        config['max_epochs_per_subject'],
        augment=False
    )
    
    # 使用加权采样器平衡类别
    sampler = WeightedRandomSampler(
        weights=train_dataset.weights,
        num_samples=len(train_dataset),
        replacement=True
    )
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config['batch_size'], 
        sampler=sampler,
        num_workers=4,
        pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    
    # 模型
    model = ImprovedSleepNet().to(device)
    logger.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # Focal Loss权重
    alpha = torch.tensor(config['weights']).to(device)
    
    # 优化器 - 使用AdamW
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=config['lr'],
        weight_decay=0.01
    )
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['lr'] * 10,
        epochs=config['epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos'
    )
    
    # 训练
    best_val_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['epochs'] + 1):
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, device, alpha, config['focal_gamma']
        )
        
        # 学习率调度
        scheduler.step()
        
        # 验证
        val_acc, val_preds, val_labels = validate(model, val_loader, device)
        
        # 计算各类别准确率
        val_cm = confusion_matrix(val_labels, val_preds)
        class_acc = val_cm.diagonal() / val_cm.sum(axis=1)
        
        logger.info(f"Epoch {epoch}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Acc: {val_acc:.4f}")
        logger.info(f"类别准确率: W={class_acc[0]:.2f}, N1={class_acc[1]:.2f}, "
                   f"N2={class_acc[2]:.2f}, N3={class_acc[3]:.2f}, REM={class_acc[4]:.2f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), './checkpoints/improved_experiment_best.pth')
            logger.info(f"✓ 新的最佳准确率: {val_acc:.4f}")
            patience_counter = 0
            
            # 如果达到85%，立即测试
            if val_acc >= 0.85:
                logger.info("🎉 达到85%准确率目标！")
                break
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logger.info("早停")
            break
    
    # 测试评估
    logger.info("\n📊 测试集评估")
    model.load_state_dict(torch.load('./checkpoints/improved_experiment_best.pth'))
    test_acc, test_preds, test_labels = validate(model, test_loader, device)
    
    logger.info(f"测试准确率: {test_acc:.4f}")
    
    # 详细的分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, target_names=class_names, digits=3)
    logger.info(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    logger.info(f"\n✅ 实验完成！最佳验证准确率: {best_val_acc:.4f}, 测试准确率: {test_acc:.4f}")

if __name__ == "__main__":
    main()