#!/usr/bin/env python3
"""
Monitor V13 and V14 training progress
"""

import os
import time
import subprocess
import re
from datetime import datetime

def get_latest_metrics(log_file, model_name):
    """Extract latest metrics from log file"""
    if not os.path.exists(log_file):
        return None
    
    try:
        # Get last 100 lines
        result = subprocess.run(
            f"tail -n 100 {log_file}",
            shell=True,
            capture_output=True,
            text=True
        )
        lines = result.stdout.strip().split('\n')
        
        metrics = {
            'model': model_name,
            'epoch': None,
            'acc': None,
            'f1': None,
            'kappa': None,
            'rem_acc': None,
            'wake_acc': None,
            'loss': None
        }
        
        for line in reversed(lines):
            # Check for epoch progress
            if 'Epoch' in line and '%' in line:
                epoch_match = re.search(r'Epoch (\d+)', line)
                if epoch_match:
                    metrics['epoch'] = int(epoch_match.group(1))
                
                # Extract metrics from progress bar
                if 'acc=' in line:
                    acc_match = re.search(r'acc=([0-9.]+)', line)
                    if acc_match:
                        metrics['acc'] = float(acc_match.group(1))
                
                if 'loss=' in line:
                    loss_match = re.search(r'loss=([0-9.]+)', line)
                    if loss_match:
                        metrics['loss'] = float(loss_match.group(1))
                
                if 'REM_acc=' in line:
                    rem_match = re.search(r'REM_acc=([0-9.]+)', line)
                    if rem_match:
                        metrics['rem_acc'] = float(rem_match.group(1))
                
                if 'Wake_acc=' in line:
                    wake_match = re.search(r'Wake_acc=([0-9.]+)', line)
                    if wake_match:
                        metrics['wake_acc'] = float(wake_match.group(1))
                
                if metrics['epoch'] is not None:
                    break
            
            # Check for validation metrics
            if 'INFO' in line:
                if 'F1:' in line:
                    f1_match = re.search(r'F1:\s*([0-9.]+)', line)
                    if f1_match:
                        metrics['f1'] = float(f1_match.group(1))
                
                if 'Kappa:' in line:
                    kappa_match = re.search(r'Kappa:\s*([0-9.]+)', line)
                    if kappa_match:
                        metrics['kappa'] = float(kappa_match.group(1))
        
        return metrics
    except Exception as e:
        print(f"Error reading {log_file}: {e}")
        return None

def check_process_status(model_name):
    """Check if training process is running"""
    result = subprocess.run(
        f"ps aux | grep 'train_{model_name}_FIXED.py' | grep -v grep",
        shell=True,
        capture_output=True,
        text=True
    )
    return len(result.stdout.strip()) > 0

def main():
    v13_log = "../logs/v13_fixed_running.log"
    v14_log = "../logs/v14_fixed_running.log"
    
    print("\n" + "="*80)
    print("MAMBAFORMER Training Monitor")
    print("="*80)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nPress Ctrl+C to stop monitoring\n")
    
    while True:
        try:
            # Clear screen for clean display
            os.system('clear')
            
            print("\n" + "="*80)
            print(f"Training Status - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*80)
            
            # Check V13
            v13_running = check_process_status("v13")
            v13_metrics = get_latest_metrics(v13_log, "V13")
            
            print("\n📊 V13 Model (General Enhancement):")
            print(f"   Status: {'🟢 Running' if v13_running else '🔴 Stopped'}")
            
            if v13_metrics and v13_metrics['epoch']:
                print(f"   Epoch: {v13_metrics['epoch']}")
                if v13_metrics['acc']:
                    print(f"   Training Acc: {v13_metrics['acc']:.4f} ({v13_metrics['acc']*100:.2f}%)")
                if v13_metrics['loss']:
                    print(f"   Loss: {v13_metrics['loss']:.4f}")
                if v13_metrics['f1']:
                    print(f"   Validation F1: {v13_metrics['f1']:.4f}")
                if v13_metrics['kappa']:
                    print(f"   Validation Kappa: {v13_metrics['kappa']:.4f}")
            
            # Check V14
            v14_running = check_process_status("v14")
            v14_metrics = get_latest_metrics(v14_log, "V14")
            
            print("\n📊 V14 Model (REM/Wake Focus):")
            print(f"   Status: {'🟢 Running' if v14_running else '🔴 Stopped'}")
            
            if v14_metrics and v14_metrics['epoch']:
                print(f"   Epoch: {v14_metrics['epoch']}")
                if v14_metrics['acc']:
                    print(f"   Training Acc: {v14_metrics['acc']:.4f} ({v14_metrics['acc']*100:.2f}%)")
                if v14_metrics['loss']:
                    print(f"   Loss: {v14_metrics['loss']:.4f}")
                if v14_metrics['rem_acc']:
                    print(f"   REM Acc: {v14_metrics['rem_acc']:.3f} ({v14_metrics['rem_acc']*100:.1f}%)")
                if v14_metrics['wake_acc']:
                    print(f"   Wake Acc: {v14_metrics['wake_acc']:.3f} ({v14_metrics['wake_acc']*100:.1f}%)")
                if v14_metrics['f1']:
                    print(f"   Validation F1: {v14_metrics['f1']:.4f}")
                if v14_metrics['kappa']:
                    print(f"   Validation Kappa: {v14_metrics['kappa']:.4f}")
            
            # Target metrics
            print("\n🎯 Target Metrics:")
            print("   Accuracy: ≥87%")
            print("   Kappa: ≥0.80")
            print("   Macro F1: ≥80%")
            
            # Check if targets achieved
            targets_v13 = False
            targets_v14 = False
            
            if v13_metrics and v13_metrics['f1'] and v13_metrics['kappa']:
                if v13_metrics['f1'] >= 0.80 and v13_metrics['kappa'] >= 0.80:
                    targets_v13 = True
            
            if v14_metrics and v14_metrics['f1'] and v14_metrics['kappa']:
                if v14_metrics['f1'] >= 0.80 and v14_metrics['kappa'] >= 0.80:
                    targets_v14 = True
            
            if targets_v13 or targets_v14:
                print("\n🎉 TARGETS ACHIEVED:")
                if targets_v13:
                    print("   ✅ V13 has reached target metrics!")
                if targets_v14:
                    print("   ✅ V14 has reached target metrics!")
            
            print("\n" + "-"*80)
            print("Refreshing every 30 seconds... Press Ctrl+C to stop")
            
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped by user.")
            break
        except Exception as e:
            print(f"\nError: {e}")
            time.sleep(5)

if __name__ == "__main__":
    main()