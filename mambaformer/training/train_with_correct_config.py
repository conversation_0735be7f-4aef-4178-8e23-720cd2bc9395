#!/usr/bin/env python3
"""
使用正确配置训练MAMBAFORMER
参考原始Cross-Modal Transformer的设置
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import logging
from datetime import datetime
import json
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet

def setup_logging(log_dir, experiment_name):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f'{experiment_name}_{timestamp}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__), log_file

class SleepEDFDataset(Dataset):
    """Sleep-EDF数据集加载器 - 兼容原始格式"""
    def __init__(self, eeg_files, eog_files, label_files, 
                 mean_files=None, std_files=None,
                 mean_eog_files=None, std_eog_files=None,
                 use_normalization=True):
        
        # 加载所有数据
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        self.means_eeg = []
        self.stds_eeg = []
        self.means_eog = []
        self.stds_eog = []
        
        for i, (eeg_f, eog_f, label_f) in enumerate(zip(eeg_files, eog_files, label_files)):
            with h5py.File(eeg_f, 'r') as f:
                eeg = f['data'][:]
                self.eeg_data.append(eeg)
                
            with h5py.File(eog_f, 'r') as f:
                eog = f['data'][:]
                self.eog_data.append(eog)
                
            with h5py.File(label_f, 'r') as f:
                labels = f['data'][:]
                self.labels.append(labels)
            
            # 加载归一化参数
            if mean_files and std_files:
                with h5py.File(mean_files[i], 'r') as f:
                    mean_eeg = f['data'][:]
                    self.means_eeg.append(mean_eeg)
                    
                with h5py.File(std_files[i], 'r') as f:
                    std_eeg = f['data'][:]
                    self.stds_eeg.append(std_eeg)
                    
                with h5py.File(mean_eog_files[i], 'r') as f:
                    mean_eog = f['data'][:]
                    self.means_eog.append(mean_eog)
                    
                with h5py.File(std_eog_files[i], 'r') as f:
                    std_eog = f['data'][:]
                    self.stds_eog.append(std_eog)
        
        # 合并所有数据
        self.eeg_data = np.concatenate(self.eeg_data, axis=0)
        self.eog_data = np.concatenate(self.eog_data, axis=0)
        self.labels = np.concatenate(self.labels, axis=0)
        
        if mean_files and std_files:
            self.means_eeg = np.concatenate(self.means_eeg, axis=0)
            self.stds_eeg = np.concatenate(self.stds_eeg, axis=0)
            self.means_eog = np.concatenate(self.means_eog, axis=0)
            self.stds_eog = np.concatenate(self.stds_eog, axis=0)
        
        self.use_normalization = use_normalization
        
        print(f"加载数据集: {len(eeg_files)} subjects, {len(self.labels)} epochs")
        print(f"EEG形状: {self.eeg_data.shape}, EOG形状: {self.eog_data.shape}")
        print(f"标签分布: {np.bincount(self.labels.astype(int))}")
        
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        eeg = self.eeg_data[idx]
        eog = self.eog_data[idx]
        label = self.labels[idx]
        
        # 标准化（使用原始方式）
        if self.use_normalization and hasattr(self, 'means_eeg'):
            # 每个样本使用自己的均值和标准差
            eeg = (eeg - self.means_eeg[idx]) / (self.stds_eeg[idx] + 1e-8)
            eog = (eog - self.means_eog[idx]) / (self.stds_eog[idx] + 1e-8)
        
        # 转换为张量并添加通道维度
        eeg = torch.FloatTensor(eeg).unsqueeze(0)  # [1, seq_len]
        eog = torch.FloatTensor(eog).unsqueeze(0)  # [1, seq_len]
        label = torch.LongTensor([label]).squeeze()
        
        return eeg, eog, label

def calculate_class_weights(labels):
    """计算类别权重（逆频率加权）"""
    class_counts = np.bincount(labels)
    total = len(labels)
    
    # 避免除零
    class_counts = np.maximum(class_counts, 1)
    
    # 逆频率权重
    weights = total / (len(class_counts) * class_counts)
    
    # 归一化权重
    weights = weights / weights.mean()
    
    return torch.FloatTensor(weights)

def train_epoch(model, train_loader, optimizer, criterion, device, epoch, logger):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} [Train]')
    for batch_idx, (eeg_data, eog_data, labels) in enumerate(pbar):
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(eeg_data, eog_data, stage="both")
        
        # 计算损失
        if 'fine_logits' in outputs:
            loss = criterion(outputs['fine_logits'], labels)
            predictions = outputs['fine_logits'].argmax(dim=1)
        else:
            loss = criterion(outputs['logits'], labels)
            predictions = outputs['logits'].argmax(dim=1)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        correct += (predictions == labels).sum().item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    avg_loss = total_loss / len(train_loader)
    accuracy = correct / total
    
    return avg_loss, accuracy

def validate(model, val_loader, criterion, device):
    """验证模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc='Validating')
        for eeg_data, eog_data, labels in pbar:
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(eeg_data, eog_data, stage="fine")
            
            # 计算损失
            if 'fine_logits' in outputs:
                loss = criterion(outputs['fine_logits'], labels)
                predictions = outputs['fine_logits'].argmax(dim=1)
            else:
                loss = criterion(outputs['logits'], labels)
                predictions = outputs['logits'].argmax(dim=1)
            
            total_loss += loss.item()
            all_preds.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def main():
    # 配置（参考原始设置）
    config = {
        'data_path': './processed_data_fixed',
        'train_subjects': [str(i) for i in range(1, 16)],  # 15个训练受试者
        'val_subjects': ['16', '17', '18'],                # 3个验证受试者
        'test_subjects': ['19', '20'],                     # 2个测试受试者
        'batch_size': 32,  # 原始设置
        'epochs': 200,     # 原始设置
        'lr': 1e-3,        # 原始设置
        'weight_decay': 1e-4,  # 原始设置
        'patience': 30,
        'experiment_name': 'mambaformer_sleepEDF_correct',
        'log_dir': './log',
        'save_dir': './checkpoints',
        
        # 模型参数（原始设置）
        'd_model': 128,    # 原始设置
        'dim_feedforward': 512,  # 原始设置
        'window_size': 50,       # 原始设置
        'num_layers': 4,
        'nhead': 8,
        'dropout': 0.1,
        
        # 加权损失（原始设置）
        'weights': [1., 2., 1., 2., 2.],  # W, N1, N2, N3, REM
        
        # 学习率调度器（原始设置）
        'step_size': 30,
        'gamma': 0.5
    }
    
    # 创建目录
    os.makedirs(config['log_dir'], exist_ok=True)
    os.makedirs(config['save_dir'], exist_ok=True)
    
    # 设置日志
    logger, log_file = setup_logging(config['log_dir'], config['experiment_name'])
    logger.info("🧪 MAMBAFORMER Sleep Stage Classification - Correct Config")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 准备数据文件
    def get_subject_files(subjects, data_path):
        eeg_files = [os.path.join(data_path, f'x{int(s):02d}.h5') for s in subjects]
        eog_files = [os.path.join(data_path, f'eog{int(s):02d}.h5') for s in subjects]
        label_files = [os.path.join(data_path, f'y{int(s):02d}.h5') for s in subjects]
        mean_files = [os.path.join(data_path, f'mean{int(s):02d}.h5') for s in subjects]
        std_files = [os.path.join(data_path, f'std{int(s):02d}.h5') for s in subjects]
        mean_eog_files = [os.path.join(data_path, f'eog_m{int(s):02d}.h5') for s in subjects]
        std_eog_files = [os.path.join(data_path, f'eog_s{int(s):02d}.h5') for s in subjects]
        
        return eeg_files, eog_files, label_files, mean_files, std_files, mean_eog_files, std_eog_files
    
    # 创建数据集
    train_files = get_subject_files(config['train_subjects'], config['data_path'])
    val_files = get_subject_files(config['val_subjects'], config['data_path'])
    test_files = get_subject_files(config['test_subjects'], config['data_path'])
    
    train_dataset = SleepEDFDataset(*train_files, use_normalization=True)
    val_dataset = SleepEDFDataset(*val_files, use_normalization=True)
    test_dataset = SleepEDFDataset(*test_files, use_normalization=True)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], 
                             shuffle=True, num_workers=2, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], 
                           shuffle=False, num_workers=2, pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], 
                            shuffle=False, num_workers=2, pin_memory=True)
    
    logger.info(f"训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    logger.info(f"验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    logger.info(f"测试集: {len(test_dataset)} 样本, {len(test_loader)} 批次")
    
    # 创建模型
    model = MambaFormerSleepNet(
        d_model=config['d_model'],
        num_mambaformer_layers=config['num_layers'],
        nhead=config['nhead'],
        window_size=config['window_size'],
        use_progressive=True,
        use_adaptive_mambaformer=True,
        dropout=config['dropout'],
        device=device
    ).to(device)
    
    logger.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数（使用加权交叉熵）
    weights = torch.tensor(config['weights']).to(device)
    criterion = nn.CrossEntropyLoss(weight=weights)
    
    # 优化器（使用原始设置）
    optimizer = torch.optim.Adam(
        model.parameters(), 
        lr=config['lr'],
        betas=(0.9, 0.999),  # 原始设置
        eps=1e-9,            # 原始设置
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器（使用原始设置）
    scheduler = torch.optim.lr_scheduler.StepLR(
        optimizer,
        step_size=config['step_size'],
        gamma=config['gamma']
    )
    
    # 训练循环
    best_val_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['epochs'] + 1):
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device, epoch, logger)
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, criterion, device)
        
        # 更新学习率
        scheduler.step()
        
        logger.info(f"Epoch {epoch}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} | "
                   f"LR: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'config': config
            }
            checkpoint_path = os.path.join(config['save_dir'], 
                                         f"{config['experiment_name']}_best.pth")
            torch.save(checkpoint, checkpoint_path)
            logger.info(f"✓ 新的最佳验证准确率: {val_acc:.4f}")
            patience_counter = 0
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logger.info("早停")
            break
    
    logger.info(f"训练完成，最佳验证准确率: {best_val_acc:.4f}")
    
    # 测试评估
    logger.info("\n📊 测试集评估")
    
    # 加载最佳模型
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, criterion, device)
    logger.info(f"测试准确率: {test_acc:.4f}")
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, 
                                 target_names=class_names)
    logger.info(f"\n分类报告:\n{report}")
    
    # 保存结果
    results = {
        'best_val_acc': best_val_acc,
        'test_acc': test_acc,
        'config': config,
        'classification_report': report
    }
    
    result_file = os.path.join(config['log_dir'], 
                              f"{config['experiment_name']}_results.json")
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ 实验完成！结果保存在: {result_file}")

if __name__ == "__main__":
    main()