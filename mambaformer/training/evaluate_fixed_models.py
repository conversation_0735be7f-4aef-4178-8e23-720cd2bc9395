#!/usr/bin/env python3
"""
Evaluate V13 and V14 fixed models on test set
"""

import os
import sys
import json
import numpy as np
import torch
import logging
from datetime import datetime
from tqdm import tqdm

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from torch.utils.data import DataLoader


def evaluate_model(model_path, model_name, config, device):
    """Evaluate a single model"""
    logging.info(f"\n{'='*80}")
    logging.info(f"Evaluating {model_name}")
    logging.info(f"{'='*80}")
    
    # Load model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(model_path, map_location=device)
    if isinstance(checkpoint, dict):
        model.load_state_dict(checkpoint.get('model_state_dict', checkpoint))
    else:
        model.load_state_dict(checkpoint)
    
    logging.info(f"✅ Model loaded from {model_path}")
    
    # Load test data
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    test_dataset = SequenceSleepDataset(
        test_files, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None  # 使用所有数据！
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config['num_workers'],
        pin_memory=True
    )
    
    logging.info(f"📊 Test set: {test_dataset.total_epochs} epochs")
    
    # Evaluate
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    evaluator.total_epochs = test_dataset.total_epochs
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for data, labels in tqdm(test_loader, desc=f'Evaluating {model_name}'):
            data = data.to(device)
            labels = labels.to(device)
            
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    metrics = evaluator.evaluate()
    
    # Convert numpy arrays to lists for JSON serialization
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        else:
            return obj
    
    metrics = convert_numpy(metrics)
    
    # Log results
    logging.info(f"\n📊 {model_name} Results:")
    logging.info(f"  Accuracy: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
    logging.info(f"  Macro F1: {metrics['macro_f1']:.4f} ({metrics['macro_f1']*100:.2f}%)")
    logging.info(f"  Kappa: {metrics['kappa']:.4f}")
    
    # Per-class F1
    logging.info(f"\n  Per-class F1:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    for cls_name in class_names:
        if cls_name in metrics['per_class_metrics']:
            f1 = metrics['per_class_metrics'][cls_name]['f1']
            logging.info(f"    {cls_name}: {f1:.4f} ({f1*100:.2f}%)")
    
    # Check targets
    targets_met = []
    if metrics['accuracy'] >= 0.87:
        targets_met.append("Accuracy ≥ 87%")
    if metrics['macro_f1'] >= 0.80:
        targets_met.append("Macro F1 ≥ 80%")
    if metrics['kappa'] >= 0.80:
        targets_met.append("Kappa ≥ 0.80")
    
    if targets_met:
        logging.info(f"\n✅ Targets Met: {', '.join(targets_met)}")
    else:
        logging.info(f"\n⚠️  Below target - Acc: {metrics['accuracy']:.4f}, F1: {metrics['macro_f1']:.4f}, Kappa: {metrics['kappa']:.4f}")
    
    return metrics


def main():
    # Setup logging
    log_file = f"../logs/evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V13 & V14 Fixed Models Evaluation")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🔧 Device: {device}")
    
    # Configurations
    v13_config = {
        'd_model': 256,
        'n_heads': 8,
        'n_layers': 6,
        'dropout': 0.1,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4
    }
    
    v14_config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4
    }
    
    results = {}
    
    # Evaluate V13
    if os.path.exists('../../checkpoints/v13_fixed.pth'):
        v13_metrics = evaluate_model(
            '../../checkpoints/v13_fixed.pth',
            'V13 Fixed',
            v13_config,
            device
        )
        results['v13_fixed'] = v13_metrics
    
    # Evaluate V14
    if os.path.exists('../../checkpoints/v14_fixed.pth'):
        v14_metrics = evaluate_model(
            '../../checkpoints/v14_fixed.pth',
            'V14 Fixed (REM/Wake Focus)',
            v14_config,
            device
        )
        results['v14_fixed'] = v14_metrics
    
    # Save results
    results_file = '../../configs/fixed_models_evaluation.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n📊 Results saved to {results_file}")
    
    # Summary
    logging.info("\n" + "="*80)
    logging.info("📊 SUMMARY")
    logging.info("="*80)
    
    for model_name, metrics in results.items():
        logging.info(f"\n{model_name}:")
        logging.info(f"  Accuracy: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
        logging.info(f"  Macro F1: {metrics['macro_f1']:.4f} ({metrics['macro_f1']*100:.2f}%)")
        logging.info(f"  Kappa: {metrics['kappa']:.4f}")
        
        # Check if targets met
        targets_met = []
        if metrics['accuracy'] >= 0.87:
            targets_met.append("✅ Acc")
        if metrics['macro_f1'] >= 0.80:
            targets_met.append("✅ F1")
        if metrics['kappa'] >= 0.80:
            targets_met.append("✅ Kappa")
        
        if targets_met:
            logging.info(f"  {' '.join(targets_met)}")
        else:
            logging.info(f"  ⚠️ Below targets")
    
    logging.info("\n✅ Evaluation complete!")
    
    return results


if __name__ == "__main__":
    main()