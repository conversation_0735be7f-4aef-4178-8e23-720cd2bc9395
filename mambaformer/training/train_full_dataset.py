"""
完整Sleep-EDF数据集MAMBAFORMER训练脚本
支持日志记录和完整实验管理
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
import os
import json
import logging
from pathlib import Path
from datetime import datetime
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import time
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet
from models.epoch_cmt import Epoch_Cross_Transformer_Network
from datasets.sleep_edf import get_dataset

def setup_logging(log_dir, experiment_name):
    """设置日志系统"""
    # 创建带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"{experiment_name}_{timestamp}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    
    return logger, log_file

def parse_args():
    parser = argparse.ArgumentParser('MAMBAFORMER Sleep Stage Classification - Full Dataset')
    
    # 数据相关
    parser.add_argument('--data_path', type=str, default='./processed_data',
                       help='预处理数据路径')
    parser.add_argument('--train_subjects', type=str, default='1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16',
                       help='训练集subject列表')
    parser.add_argument('--val_subjects', type=str, default='17,18', 
                       help='验证集subject列表')
    parser.add_argument('--test_subjects', type=str, default='19,20',
                       help='测试集subject列表')
    parser.add_argument('--batch_size', type=int, default=128, 
                       help='批大小')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载器进程数')
    
    # 模型相关
    parser.add_argument('--model_name', type=str, default='mambaformer',
                       choices=['mambaformer', 'baseline'],
                       help='模型选择')
    parser.add_argument('--model_type', type=str, default='Epoch',
                       help='模型类型: Epoch/Seq')
    parser.add_argument('--d_model', type=int, default=64,
                       help='模型维度')
    parser.add_argument('--num_layers', type=int, default=4, 
                       help='MambaFormer层数')
    parser.add_argument('--nhead', type=int, default=8,
                       help='注意力头数')
    parser.add_argument('--window_size', type=int, default=50,
                       help='CNN窗口大小')
    
    # 训练相关
    parser.add_argument('--epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                       help='权重衰减')
    parser.add_argument('--patience', type=int, default=15,
                       help='早停耐心值')
    parser.add_argument('--lr_patience', type=int, default=10,
                       help='学习率调度器耐心值')
    
    # 实验相关
    parser.add_argument('--use_progressive', action='store_true', default=True,
                       help='使用渐进式分类器')
    parser.add_argument('--save_dir', type=str, default='./checkpoints',
                       help='模型保存目录')
    parser.add_argument('--log_dir', type=str, default='./log',
                       help='日志保存目录')
    parser.add_argument('--experiment_name', type=str, default='mambaformer_full',
                       help='实验名称')
    parser.add_argument('--resume', type=str, default=None,
                       help='从检查点恢复训练')
    
    # GPU设置
    parser.add_argument('--device', type=str, default='auto',
                       help='设备选择: auto/cpu/cuda:0/cuda:1')
    parser.add_argument('--use_amp', action='store_true', default=False,
                       help='使用混合精度训练')
    
    return parser.parse_args()

def setup_device(device_str):
    """设置计算设备"""
    if device_str == 'auto':
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
            print(f"✓ 使用GPU: {torch.cuda.get_device_name(0)}")
        else:
            device = torch.device('cpu')
            print("✓ 使用CPU")
    else:
        device = torch.device(device_str)
        if device.type == 'cuda':
            print(f"✓ 使用指定GPU: {torch.cuda.get_device_name(device)}")
        else:
            print("✓ 使用CPU")
    
    return device

def create_data_loaders(args, device, logger):
    """创建数据加载器"""
    logger.info("📊 加载完整数据集...")
    
    # 解析subject列表
    args.train_data_list = args.train_subjects.split(',')
    args.val_data_list = args.val_subjects.split(',')
    args.test_data_list = args.test_subjects.split(',')
    
    logger.info(f"训练集subjects: {args.train_data_list}")
    logger.info(f"验证集subjects: {args.val_data_list}")
    logger.info(f"测试集subjects: {args.test_data_list}")
    
    # 使用原始的数据加载函数
    train_loader, val_loader, test_loader = get_dataset(device, args, only_val=False)
    
    logger.info(f"✓ 训练集: {len(train_loader.dataset)} 样本, {len(train_loader)} 批次")
    logger.info(f"✓ 验证集: {len(val_loader.dataset)} 样本, {len(val_loader)} 批次") 
    logger.info(f"✓ 测试集: {len(test_loader.dataset)} 样本, {len(test_loader)} 批次")
    
    return train_loader, val_loader, test_loader

def create_model(args, device, logger):
    """创建模型"""
    if args.model_name == 'mambaformer':
        logger.info("🏗️ 创建MAMBAFORMER模型...")
        model = MambaFormerSleepNet(
            d_model=args.d_model,
            num_mambaformer_layers=args.num_layers,
            nhead=args.nhead,
            window_size=args.window_size,
            use_wica_online=False,  # 数据已预处理
            use_progressive=args.use_progressive,
            use_adaptive_mambaformer=True,
            dropout=0.1
        )
    else:
        logger.info("🏗️ 创建Cross-Modal Transformer基准模型...")
        model = Epoch_Cross_Transformer_Network(
            d_model=args.d_model,
            dim_feedforward=args.d_model * 4,
            window_size=args.window_size
        )
    
    model = model.to(device)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"✓ 模型参数: {total_params:,} (可训练: {trainable_params:,})")
    
    return model

def train_epoch(model, train_loader, optimizer, device, epoch, logger, args, scaler=None):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} [Train]")
    
    for batch_idx, batch_data in enumerate(pbar):
        # 解包数据 - 适配不同的数据格式
        if len(batch_data) == 7:
            eeg_data, eog_data, labels, _, _, _, _ = batch_data
        else:
            eeg_data, eog_data, labels = batch_data[:3]
            
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device) 
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 混合精度训练
        if args.use_amp and scaler is not None:
            with torch.cuda.amp.autocast():
                if args.model_name == 'mambaformer':
                    outputs = model(eeg_data, eog_data, stage="both")
                    losses = model.compute_loss(outputs, labels, stage="both")
                    loss = losses['total_loss']
                else:
                    outputs, _, _ = model(eeg_data, eog_data, finetune=True)
                    criterion = nn.CrossEntropyLoss()
                    loss = criterion(outputs, labels)
            
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            # 普通训练
            if args.model_name == 'mambaformer':
                outputs = model(eeg_data, eog_data, stage="both")
                losses = model.compute_loss(outputs, labels, stage="both")
                loss = losses['total_loss']
            else:
                outputs, _, _ = model(eeg_data, eog_data, finetune=True)
                criterion = nn.CrossEntropyLoss()
                loss = criterion(outputs, labels)
                
            loss.backward()
            optimizer.step()
        
        # 统计
        total_loss += loss.item()
        
        # 获取预测结果
        if args.model_name == 'mambaformer':
            if 'fine_probs' in outputs:
                pred = outputs['fine_probs'].argmax(dim=1)
            elif 'logits' in outputs:
                pred = outputs['logits'].argmax(dim=1)
            else:
                pred = outputs['fine_logits'].argmax(dim=1)
        else:
            pred = outputs.argmax(dim=1)
            
        correct += (pred == labels).sum().item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
        
        # 定期记录到日志
        if batch_idx % 100 == 0:
            logger.info(f"Epoch {epoch+1}, Batch {batch_idx}/{len(train_loader)}, "
                       f"Loss: {loss.item():.4f}, Acc: {100.*correct/total:.2f}%")
    
    avg_loss = total_loss / len(train_loader)
    avg_acc = correct / total
    
    return avg_loss, avg_acc

def validate(model, val_loader, device, logger, args):
    """验证模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_data in tqdm(val_loader, desc="Validating"):
            # 解包数据
            if len(batch_data) == 7:
                eeg_data, eog_data, labels, _, _, _, _ = batch_data
            else:
                eeg_data, eog_data, labels = batch_data[:3]
                
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            if args.model_name == 'mambaformer':
                outputs = model(eeg_data, eog_data, stage="fine")
                losses = model.compute_loss(outputs, labels, stage="fine")
                total_loss += losses['total_loss'].item()
                
                if 'fine_probs' in outputs:
                    pred = outputs['fine_probs'].argmax(dim=1)
                elif 'logits' in outputs:
                    pred = outputs['logits'].argmax(dim=1)
                else:
                    pred = outputs['fine_logits'].argmax(dim=1)
            else:
                outputs, _, _ = model(eeg_data, eog_data, finetune=True)
                criterion = nn.CrossEntropyLoss()
                loss = criterion(outputs, labels)
                total_loss += loss.item()
                pred = outputs.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def save_checkpoint(model, optimizer, epoch, best_val_acc, args, logger, filename):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'best_val_acc': best_val_acc,
        'args': args
    }
    
    save_path = os.path.join(args.save_dir, filename)
    torch.save(checkpoint, save_path)
    logger.info(f"💾 检查点保存到: {save_path}")

def train_model(model, train_loader, val_loader, args, device, logger):
    """完整的训练流程"""
    logger.info(f"\n🚀 开始训练 {args.model_name.upper()} 模型")
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', patience=args.lr_patience, factor=0.5, verbose=True
    )
    
    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler() if args.use_amp else None
    
    # 恢复训练
    start_epoch = 0
    best_val_acc = 0
    if args.resume:
        logger.info(f"📂 从检查点恢复: {args.resume}")
        checkpoint = torch.load(args.resume)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_val_acc = checkpoint['best_val_acc']
        logger.info(f"✓ 从epoch {start_epoch}继续训练，最佳验证准确率: {best_val_acc:.4f}")
    
    # 训练历史
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': [],
        'lr': []
    }
    
    # 早停
    patience_counter = 0
    
    start_time = time.time()
    
    for epoch in range(start_epoch, args.epochs):
        epoch_start_time = time.time()
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, device, epoch, logger, args, scaler
        )
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, device, logger, args)
        
        # 获取当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        
        # 更新历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        history['lr'].append(current_lr)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 记录epoch结果
        epoch_time = time.time() - epoch_start_time
        logger.info(f"\nEpoch {epoch+1}/{args.epochs} 完成 (用时: {epoch_time:.2f}s)")
        logger.info(f"  训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}")
        logger.info(f"  验证 - Loss: {val_loss:.4f}, Acc: {val_acc:.4f}")
        logger.info(f"  学习率: {current_lr:.6f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            save_checkpoint(
                model, optimizer, epoch, best_val_acc, args, logger,
                f"{args.experiment_name}_best.pth"
            )
            logger.info(f"🏆 新的最佳验证准确率: {best_val_acc:.4f}")
        else:
            patience_counter += 1
            logger.info(f"  耐心计数: {patience_counter}/{args.patience}")
            
            if patience_counter >= args.patience:
                logger.info(f"🛑 早停触发，最佳验证准确率: {best_val_acc:.4f}")
                break
        
        # 定期保存检查点
        if (epoch + 1) % 10 == 0:
            save_checkpoint(
                model, optimizer, epoch, best_val_acc, args, logger,
                f"{args.experiment_name}_epoch_{epoch+1}.pth"
            )
    
    training_time = time.time() - start_time
    logger.info(f"\n✅ 训练完成，总用时: {training_time/60:.2f}分钟")
    logger.info(f"🏆 最佳验证准确率: {best_val_acc:.4f}")
    
    # 保存训练历史
    history_file = os.path.join(args.log_dir, f"{args.experiment_name}_history.json")
    with open(history_file, 'w') as f:
        json.dump(history, f, indent=2)
    logger.info(f"📊 训练历史保存到: {history_file}")
    
    return history, best_val_acc

def evaluate_model(model, test_loader, device, logger, args):
    """评估模型"""
    logger.info(f"\n📊 评估 {args.model_name.upper()} 模型")
    
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_data in tqdm(test_loader, desc="Testing"):
            # 解包数据
            if len(batch_data) == 7:
                eeg_data, eog_data, labels, _, _, _, _ = batch_data
            else:
                eeg_data, eog_data, labels = batch_data[:3]
                
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            if args.model_name == 'mambaformer':
                outputs = model(eeg_data, eog_data, stage="fine")
                
                if 'fine_probs' in outputs:
                    pred = outputs['fine_probs'].argmax(dim=1)
                elif 'logits' in outputs:
                    pred = outputs['logits'].argmax(dim=1)
                else:
                    pred = outputs['fine_logits'].argmax(dim=1)
            else:
                outputs, _, _ = model(eeg_data, eog_data, finetune=True)
                pred = outputs.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # 计算指标
    test_acc = accuracy_score(all_labels, all_preds)
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(all_labels, all_preds, target_names=class_names, digits=4)
    
    # 混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    
    logger.info(f"\n✓ 测试准确率: {test_acc:.4f}")
    logger.info(f"\n分类报告:\n{report}")
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    # 保存评估结果
    results = {
        'test_acc': test_acc,
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'predictions': all_preds,
        'labels': all_labels
    }
    
    results_file = os.path.join(args.log_dir, f"{args.experiment_name}_test_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    logger.info(f"📊 测试结果保存到: {results_file}")
    
    return results

def main():
    args = parse_args()
    
    # 创建必要的目录
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    
    # 设置日志
    logger, log_file = setup_logging(args.log_dir, args.experiment_name)
    
    logger.info("🧪 MAMBAFORMER Sleep Stage Classification - 完整数据集实验")
    logger.info("=" * 60)
    logger.info(f"实验配置:\n{json.dumps(vars(args), indent=2)}")
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(args, device, logger)
    
    # 创建模型
    model = create_model(args, device, logger)
    
    # 训练模型
    history, best_val_acc = train_model(
        model, train_loader, val_loader, args, device, logger
    )
    
    # 加载最佳模型进行测试
    logger.info("\n📂 加载最佳模型进行测试...")
    best_checkpoint = torch.load(
        os.path.join(args.save_dir, f"{args.experiment_name}_best.pth")
    )
    model.load_state_dict(best_checkpoint['model_state_dict'])
    
    # 评估模型
    test_results = evaluate_model(model, test_loader, device, logger, args)
    
    logger.info("\n✅ 实验完成！")
    logger.info(f"📊 最佳验证准确率: {best_val_acc:.4f}")
    logger.info(f"📊 测试准确率: {test_results['test_acc']:.4f}")
    logger.info(f"📝 完整日志保存在: {log_file}")

if __name__ == '__main__':
    main()