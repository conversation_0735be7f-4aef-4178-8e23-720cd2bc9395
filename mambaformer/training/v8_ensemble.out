2025-08-12 02:15:58,775 - INFO - ================================================================================
2025-08-12 02:15:58,775 - INFO - 🎯 V8 Ensemble - Multi-Head Voting for 90% Target
2025-08-12 02:15:58,775 - INFO - ================================================================================
2025-08-12 02:15:58,776 - INFO - Configuration: {
  "d_model": 256,
  "n_heads": 16,
  "n_layers": 6,
  "dropout": 0.15,
  "seq_len": 5,
  "batch_size": 24,
  "learning_rate": 0.00015,
  "weight_decay": 4e-05,
  "num_epochs": 60,
  "patience": 15,
  "use_fusion": false
}
2025-08-12 02:15:58,944 - INFO - Device: cuda
2025-08-12 02:15:58,944 - INFO - Loading datasets...
2025-08-12 02:16:00,469 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 02:16:00,469 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 02:16:00,889 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 02:16:00,889 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 02:16:01,600 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 02:16:01,601 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 02:16:01,601 - INFO - Dataset sizes: Train=25266, Val=4931, Test=11955
2025-08-12 02:16:02,099 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-12 02:16:02,323 - INFO - Model parameters: 5,680,299
2025-08-12 02:16:03,734 - INFO - Starting V8 Ensemble training...

Training:   0%|          | 0/1053 [00:00<?, ?it/s]
Training:   0%|          | 0/1053 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ensemble.py", line 539, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ensemble.py", line 461, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ensemble.py", line 295, in train_epoch
    output, all_heads = model(data, return_all_heads=True)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ensemble.py", line 153, in forward
    features = self.attention_pool(output)  # (batch, d_model)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ensemble.py", line 59, in forward
    weights = F.softmax(self.attention(x), dim=1)  # (batch, seq_len, 1)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (120x5 and 256x1)
