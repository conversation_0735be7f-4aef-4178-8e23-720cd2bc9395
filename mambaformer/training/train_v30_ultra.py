#!/usr/bin/env python3
"""
🚀 V30 ULTRA MODEL - BREAKTHROUGH TO 90%
Ultra-deep MAMBAFORMER with advanced training strategies
Target: 90% accuracy on test set
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import OneCycleLR, CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, f1_score, cohen_kappa_score
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class MixupDataset(Dataset):
    """Dataset with mixup augmentation"""
    def __init__(self, original_dataset, alpha=0.2):
        self.dataset = original_dataset
        self.alpha = alpha
        
    def __len__(self):
        return len(self.dataset)
    
    def __getitem__(self, idx):
        data1, label1 = self.dataset[idx]
        
        # Random mixup with another sample
        if np.random.random() < 0.5:
            idx2 = np.random.randint(0, len(self.dataset))
            data2, label2 = self.dataset[idx2]
            
            # Mixup ratio
            lam = np.random.beta(self.alpha, self.alpha)
            
            # Mix data
            data = lam * data1 + (1 - lam) * data2
            
            # For classification, we'll return the stronger label
            if lam > 0.5:
                return data, label1
            else:
                return data, label2
        
        return data1, label1


class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance"""
    def __init__(self, alpha=None, gamma=2.0, device='cuda'):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        self.device = device
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss
        
        return focal_loss.mean()


class LabelSmoothingLoss(nn.Module):
    """Label smoothing for better generalization"""
    def __init__(self, n_classes=5, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)
        
        return torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))


def train_epoch(model, train_loader, criterion, optimizer, scheduler, device, use_mixup=True):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        # Handle sequence labels
        if target.dim() > 1:
            target_eval = target[:, target.shape[1]//2]
        else:
            target_eval = target
        
        # Cutmix augmentation (similar to mixup but spatial)
        if use_mixup and np.random.random() < 0.3:
            # Random noise augmentation
            noise = torch.randn_like(data) * 0.01
            data = data + noise
        
        optimizer.zero_grad()
        
        # Forward pass
        output, _ = model(data)
        
        # Calculate loss
        loss = criterion(output, target)
        
        # Add L2 regularization
        l2_lambda = 0.001
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        if scheduler is not None and batch_idx % 10 == 0:
            scheduler.step()
        
        total_loss += loss.item()
        
        # Track predictions
        if output.dim() == 3:
            pred = output[:, output.shape[1]//2, :].argmax(dim=1)
        else:
            pred = output.argmax(dim=1)
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target_eval.cpu().numpy())
        
        # Update progress bar
        acc = (pred == target_eval).float().mean().item()
        pbar.set_postfix({
            'loss': loss.item(),
            'acc': acc,
            'lr': optimizer.param_groups[0]['lr']
        })
    
    # Calculate epoch metrics
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, macro_f1


def evaluate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(val_loader, desc='Evaluating'):
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            # Test-time augmentation
            predictions = []
            
            # Original
            output, _ = model(data)
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            predictions.append(F.softmax(output, dim=1))
            
            # With noise
            for _ in range(2):
                noise = torch.randn_like(data) * 0.005
                output, _ = model(data + noise)
                if output.dim() == 3:
                    output = output[:, output.shape[1]//2, :]
                predictions.append(F.softmax(output, dim=1))
            
            # Average predictions
            avg_pred = torch.stack(predictions).mean(dim=0)
            pred = avg_pred.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)
    
    # Calculate metrics
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    
    cm = confusion_matrix(all_targets, all_preds, labels=[0, 1, 2, 3, 4])
    
    return accuracy, macro_f1, kappa, class_f1, cm


def main():
    # Ultra configuration for breakthrough
    config = {
        'd_model': 512,  # Large model
        'n_heads': 32,   # More attention heads
        'n_layers': 12,  # Ultra deep
        'dropout': 0.25,
        'seq_len': 7,
        'batch_size': 16,
        'learning_rate': 3e-4,
        'min_lr': 1e-6,
        'num_epochs': 100,
        'patience': 20,
        'gradient_clip': 1.0,
        'weight_decay': 0.05,
        'warmup_epochs': 5,
        'label_smoothing': 0.1,
        'mixup_alpha': 0.2
    }
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'../logs/v30_ultra_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    # Save logs to file
    log_file = os.path.join(log_dir, 'training.log')
    with open(log_file, 'w') as f:
        f.write("="*80 + "\n")
        f.write("🚀 V30 ULTRA MODEL TRAINING\n")
        f.write("Target: 90% Accuracy\n")
        f.write("="*80 + "\n")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V30 ULTRA MODEL - BREAKTHROUGH TO 90%")
    logging.info("Current Best: 85.42% → Target: 90%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Log configuration
    logging.info("\n📋 Ultra Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # Prepare file paths
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        is_training=True
    )
    
    # Wrap with mixup
    train_dataset = MixupDataset(train_dataset, alpha=config['mixup_alpha'])
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        is_training=False
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        is_training=False
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create ultra model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Initialize with pretrained weights if available
    pretrained_path = '../logs/v22_deep_20250811_030913/best_model.pth'
    if os.path.exists(pretrained_path):
        checkpoint = torch.load(pretrained_path, map_location=device, weights_only=False)
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint
        
        # Load matching layers (only those with matching dimensions)
        model_dict = model.state_dict()
        pretrained_dict = {}
        
        for k, v in state_dict.items():
            if k in model_dict:
                if model_dict[k].shape == v.shape:
                    pretrained_dict[k] = v
                    
        if pretrained_dict:
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict, strict=False)
            logging.info(f"✅ Loaded {len(pretrained_dict)} matching layers from pretrained")
        else:
            logging.info("⚠️ No matching layers found, training from scratch")
    
    logging.info(f"\nModel Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Calculate class weights
    class_weights = [1.0, 3.0, 1.0, 1.0, 5.0]  # Boost N1 and REM
    
    # Loss functions
    focal_loss = FocalLoss(alpha=class_weights, gamma=2.0, device=device)
    label_smooth_loss = LabelSmoothingLoss(n_classes=5, smoothing=config['label_smoothing'])
    
    # Combined loss
    def combined_loss(outputs, targets):
        return 0.7 * focal_loss(outputs, targets) + 0.3 * label_smooth_loss(outputs, targets)
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # Scheduler
    total_steps = len(train_loader) * config['num_epochs']
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'],
        total_steps=total_steps,
        pct_start=0.1,
        anneal_strategy='cos',
        div_factor=25,
        final_div_factor=1000
    )
    
    # Training loop
    logging.info("\n🏋️ Starting Ultra Training...")
    logging.info("="*80)
    
    best_accuracy = 0
    best_macro_f1 = 0
    patience_counter = 0
    
    for epoch in range(config['num_epochs']):
        # Train
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, combined_loss, optimizer, scheduler, device,
            use_mixup=(epoch >= config['warmup_epochs'])
        )
        
        # Validate
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device
        )
        
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # Save best model
        if val_acc > best_accuracy:
            best_accuracy = val_acc
            best_macro_f1 = val_f1
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Acc: {val_acc:.4f})")
            
            # Early test if close to target
            if val_acc >= 0.88:
                logging.info("\n🎯 Approaching target! Running test evaluation...")
                test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
                    model, test_loader, device
                )
                logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
                
                if test_acc >= 0.90:
                    logging.info("\n🎉 TARGET ACHIEVED! 90% ACCURACY!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⛔ Early stopping at epoch {epoch+1}")
                break
    
    # Final test evaluation
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL TEST EVALUATION")
    logging.info("="*80)
    
    # Load best model
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'),
                          map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
        model, test_loader, device
    )
    
    logging.info("\n🎯 FINAL TEST RESULTS:")
    logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"  Macro F1: {test_f1:.4f}")
    logging.info(f"  Kappa: {test_kappa:.4f}")
    logging.info(f"  Class F1 Scores:")
    logging.info(f"    Wake: {test_class_f1[0]:.4f}")
    logging.info(f"    N1: {test_class_f1[1]:.4f}")
    logging.info(f"    N2: {test_class_f1[2]:.4f}")
    logging.info(f"    N3: {test_class_f1[3]:.4f}")
    logging.info(f"    REM: {test_class_f1[4]:.4f}")
    
    gap_to_90 = 0.90 - test_acc
    if gap_to_90 > 0:
        logging.info(f"\n📊 Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    else:
        logging.info(f"\n🎉 EXCEEDED TARGET BY {-gap_to_90:.4f} ({-gap_to_90*100:.2f}%)")
    
    # Save results
    results = {
        'test_accuracy': float(test_acc),
        'test_macro_f1': float(test_f1),
        'test_kappa': float(test_kappa),
        'test_class_f1': test_class_f1.tolist(),
        'confusion_matrix': test_cm.tolist(),
        'best_val_acc': float(best_accuracy),
        'best_val_f1': float(best_macro_f1),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {log_dir}/results.json")
    
if __name__ == '__main__':
    main()