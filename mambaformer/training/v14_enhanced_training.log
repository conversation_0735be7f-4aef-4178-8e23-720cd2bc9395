2025-08-12 01:06:42,998 - INFO - ================================================================================
2025-08-12 01:06:42,999 - INFO - 🎯 V14 Enhanced - Target: 90% Accuracy
2025-08-12 01:06:42,999 - INFO - ================================================================================
2025-08-12 01:06:43,000 - INFO - Configuration: {
  "d_model": 320,
  "n_heads": 20,
  "n_layers": 8,
  "dropout": 0.12,
  "seq_len": 5,
  "batch_size": 24,
  "learning_rate": 0.00015,
  "weight_decay": 5e-05,
  "num_epochs": 50,
  "patience": 15,
  "mixup_alpha": 0.2
}
2025-08-12 01:06:43,031 - INFO - Device: cuda
2025-08-12 01:06:43,032 - INFO - Loading datasets...
2025-08-12 01:06:44,862 - INFO - 从 27 个文件加载了 28450 个epochs, 创建了 28342 个序列
2025-08-12 01:06:44,863 - INFO - 创建序列数据集: 28342个序列, 序列长度=5, 通道数=3, 总epochs=28450
2025-08-12 01:06:45,076 - INFO - 从 4 个文件加载了 4112 个epochs, 创建了 4096 个序列
2025-08-12 01:06:45,077 - INFO - 创建序列数据集: 4096个序列, 序列长度=5, 通道数=3, 总epochs=4112
2025-08-12 01:06:45,680 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-12 01:06:45,681 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-12 01:06:45,681 - INFO - Dataset sizes: Train=28342, Val=4096, Test=9714
2025-08-12 01:06:46,166 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=10,183,479, d_model=320, n_heads=20, n_layers=8
2025-08-12 01:06:46,433 - INFO - Model parameters: 11,150,332
/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_enhanced_90target.py:391: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = amp.GradScaler()
2025-08-12 01:06:47,971 - INFO - Starting training with enhanced architecture...

Training:   0%|          | 0/1181 [00:00<?, ?it/s]/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_enhanced_90target.py:221: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with amp.autocast():

Training:   0%|          | 0/1181 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_enhanced_90target.py", line 477, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_enhanced_90target.py", line 405, in main
    train_loss, train_acc, train_f1, train_cm = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_enhanced_90target.py", line 222, in train_epoch
    output = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_enhanced_90target.py", line 165, in forward
    multi_scale_features.append(conv(features_conv))
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Given groups=1, weight of size [80, 320, 1], expected input[24, 5, 5] to have 320 channels, but got 5 channels instead
