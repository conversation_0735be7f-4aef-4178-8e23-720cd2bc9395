#!/usr/bin/env python3
"""
简化版MAMBAFORMER训练脚本
使用20个受试者数据，目标达到80%+准确率
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import logging
from datetime import datetime
import json
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm

# 创建简化版的MAMBAFORMER模型
class SimpleMambaFormer(nn.Module):
    def __init__(self, input_dim=1, d_model=128, num_classes=5, 
                 num_layers=4, nhead=8, dropout=0.1):
        super().__init__()
        
        # 输入投影
        self.input_proj_eeg = nn.Conv1d(input_dim, d_model, kernel_size=1)
        self.input_proj_eog = nn.Conv1d(input_dim, d_model, kernel_size=1)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=512,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 跨模态注意力
        self.cross_attn = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=nhead,
            dropout=dropout,
            batch_first=True
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(d_model * 2, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, num_classes)
        )
        
    def forward(self, eeg, eog):
        # 输入形状: [batch, 1, seq_len]
        batch_size = eeg.shape[0]
        
        # 投影到d_model维度
        eeg_feat = self.input_proj_eeg(eeg)  # [batch, d_model, seq_len]
        eog_feat = self.input_proj_eog(eog)  # [batch, d_model, seq_len]
        
        # 转置为transformer格式
        eeg_feat = eeg_feat.transpose(1, 2)  # [batch, seq_len, d_model]
        eog_feat = eog_feat.transpose(1, 2)  # [batch, seq_len, d_model]
        
        # 自注意力编码
        eeg_encoded = self.transformer(eeg_feat)
        eog_encoded = self.transformer(eog_feat)
        
        # 跨模态注意力
        eeg_cross, _ = self.cross_attn(eeg_encoded, eog_encoded, eog_encoded)
        eog_cross, _ = self.cross_attn(eog_encoded, eeg_encoded, eeg_encoded)
        
        # 全局池化
        eeg_global = eeg_cross.mean(dim=1)  # [batch, d_model]
        eog_global = eog_cross.mean(dim=1)  # [batch, d_model]
        
        # 拼接特征
        combined = torch.cat([eeg_global, eog_global], dim=1)  # [batch, d_model*2]
        
        # 分类
        logits = self.classifier(combined)
        
        return logits

class SleepEDFDataset(Dataset):
    """Sleep-EDF数据集加载器"""
    def __init__(self, data_path, subjects):
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        
        for subj in subjects:
            # 加载数据
            eeg_file = os.path.join(data_path, f'x{int(subj):02d}.h5')
            eog_file = os.path.join(data_path, f'eog{int(subj):02d}.h5')
            label_file = os.path.join(data_path, f'y{int(subj):02d}.h5')
            
            if os.path.exists(eeg_file):
                with h5py.File(eeg_file, 'r') as f:
                    self.eeg_data.append(f['data'][:])
                with h5py.File(eog_file, 'r') as f:
                    self.eog_data.append(f['data'][:])
                with h5py.File(label_file, 'r') as f:
                    self.labels.append(f['data'][:])
        
        # 合并所有数据
        self.eeg_data = np.concatenate(self.eeg_data, axis=0)
        self.eog_data = np.concatenate(self.eog_data, axis=0)
        self.labels = np.concatenate(self.labels, axis=0).astype(np.int64)
        
        print(f"数据集大小: {len(self.labels)} 样本")
        print(f"标签分布: {np.bincount(self.labels)}")
        print(f"数据形状: EEG {self.eeg_data.shape}, EOG {self.eog_data.shape}")
        
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        eeg = torch.FloatTensor(self.eeg_data[idx]).unsqueeze(0)
        eog = torch.FloatTensor(self.eog_data[idx]).unsqueeze(0)
        label = torch.LongTensor([self.labels[idx]]).squeeze()
        return eeg, eog, label

def train_epoch(model, train_loader, optimizer, criterion, device):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for eeg, eog, labels in tqdm(train_loader, desc='Training'):
        eeg = eeg.to(device)
        eog = eog.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        outputs = model(eeg, eog)
        loss = criterion(outputs, labels)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()
    
    return total_loss / len(train_loader), correct / total

def validate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg, eog, labels in val_loader:
            eeg = eeg.to(device)
            eog = eog.to(device)
            labels = labels.to(device)
            
            outputs = model(eeg, eog)
            loss = criterion(outputs, labels)
            
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(all_labels, all_preds)
    return total_loss / len(val_loader), accuracy, all_preds, all_labels

def main():
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 配置
    config = {
        'data_path': './processed_data_fixed',
        'train_subjects': list(range(1, 16)),  # 15个训练
        'val_subjects': [16, 17, 18],          # 3个验证
        'test_subjects': [19, 20],             # 2个测试
        'batch_size': 2,  # 极小批次大小以避免GPU内存不足
        'epochs': 200,
        'lr': 1e-3,
        'patience': 30,
        'd_model': 64,  # 减少模型大小
        'num_layers': 2,  # 减少层数
        'nhead': 4,  # 减少注意力头数
        'dropout': 0.2,
        'weights': [1., 2., 1., 2., 2.]  # 类别权重
    }
    
    # 设置日志
    os.makedirs('./log', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f'./log/simple_mambaformer_{timestamp}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Simple MAMBAFORMER Training")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据集
    train_dataset = SleepEDFDataset(config['data_path'], config['train_subjects'])
    val_dataset = SleepEDFDataset(config['data_path'], config['val_subjects'])
    test_dataset = SleepEDFDataset(config['data_path'], config['test_subjects'])
    
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], 
                             shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], 
                           shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], 
                            shuffle=False, num_workers=4)
    
    # 模型
    model = SimpleMambaFormer(
        d_model=config['d_model'],
        num_layers=config['num_layers'],
        nhead=config['nhead'],
        dropout=config['dropout']
    ).to(device)
    
    logger.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失和优化器
    weights = torch.tensor(config['weights']).to(device)
    criterion = nn.CrossEntropyLoss(weight=weights)
    optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'])
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=10
    )
    
    # 训练
    best_val_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['epochs'] + 1):
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, criterion, device)
        
        # 调整学习率
        scheduler.step(val_acc)
        
        logger.info(f"Epoch {epoch}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), './checkpoints/simple_mambaformer_best.pth')
            logger.info(f"✓ 新的最佳准确率: {val_acc:.4f}")
            patience_counter = 0
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logger.info("早停")
            break
    
    # 测试
    logger.info("\n📊 测试集评估")
    model.load_state_dict(torch.load('./checkpoints/simple_mambaformer_best.pth'))
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, criterion, device)
    
    logger.info(f"测试准确率: {test_acc:.4f}")
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, target_names=class_names)
    logger.info(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    logger.info(f"\n✅ 训练完成！最佳验证准确率: {best_val_acc:.4f}, 测试准确率: {test_acc:.4f}")

if __name__ == "__main__":
    main()