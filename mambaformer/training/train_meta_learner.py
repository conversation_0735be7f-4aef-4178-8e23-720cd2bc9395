#!/usr/bin/env python3
"""
Meta-Learner Stacking for 90% Target
Train a neural network meta-learner on ensemble predictions
Current best: 87.32% → Target: 90%
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, TensorDataset
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
from sklearn.model_selection import StratifiedKFold
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class MetaLearner(nn.Module):
    """Neural network meta-learner for stacking"""
    def __init__(self, n_models=3, n_classes=5, hidden_size=128):
        super().__init__()
        
        # Input: predictions from each model (n_models * n_classes)
        input_size = n_models * n_classes
        
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.bn1 = nn.BatchNorm1d(hidden_size)
        self.dropout1 = nn.Dropout(0.3)
        
        self.fc2 = nn.Linear(hidden_size, hidden_size // 2)
        self.bn2 = nn.BatchNorm1d(hidden_size // 2)
        self.dropout2 = nn.Dropout(0.2)
        
        self.fc3 = nn.Linear(hidden_size // 2, n_classes)
        
        # Attention mechanism for model weighting
        self.attention = nn.Linear(input_size, n_models)
        
    def forward(self, x):
        # x shape: (batch_size, n_models * n_classes)
        
        # Compute attention weights for models
        attention_weights = torch.softmax(self.attention(x), dim=-1)
        
        # Main pathway
        out = F.relu(self.bn1(self.fc1(x)))
        out = self.dropout1(out)
        
        out = F.relu(self.bn2(self.fc2(out)))
        out = self.dropout2(out)
        
        out = self.fc3(out)
        
        return out, attention_weights


def get_model_predictions(model_configs, test_files, device):
    """Get predictions from all base models"""
    
    all_predictions = []
    all_probabilities = []
    true_labels = None
    
    for config in model_configs:
        logging.info(f"Getting predictions from {config['name']}...")
        
        # Load model
        model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=0.0,
            seq_len=config['seq_len']
        ).to(device)
        
        checkpoint = torch.load(config['path'], map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Create dataset
        test_dataset = SequenceSleepDataset(
            test_files,
            seq_len=config['seq_len'],
            use_channels=3,
            max_samples_per_file=None
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Get predictions
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc=f'{config["name"]}', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get final predictions
        preds, labels, probs = evaluator.get_final_predictions()
        all_predictions.append(preds)
        all_probabilities.append(probs)
        
        if true_labels is None:
            true_labels = labels
        
        # Individual performance
        acc = accuracy_score(labels, preds)
        logging.info(f"  {config['name']}: {acc:.4f}")
    
    return all_predictions, all_probabilities, true_labels


def create_meta_features(probabilities):
    """Create features for meta-learner from base model predictions"""
    n_samples = len(probabilities[0])
    n_models = len(probabilities)
    n_classes = probabilities[0].shape[-1]
    
    # Flatten probabilities from all models
    features = np.zeros((n_samples, n_models * n_classes))
    
    for i in range(n_samples):
        feature_vec = []
        for model_probs in probabilities:
            feature_vec.extend(model_probs[i])
        features[i] = feature_vec
    
    return features


def train_meta_learner(features, labels, device):
    """Train the meta-learner using cross-validation"""
    
    logging.info("\nTraining meta-learner...")
    
    n_models = features.shape[1] // 5  # 5 classes
    meta_model = MetaLearner(n_models=n_models, n_classes=5).to(device)
    
    # Convert to tensors
    X = torch.FloatTensor(features)
    y = torch.LongTensor(labels)
    
    # Split for training (use 80% for training meta-learner)
    n_train = int(0.8 * len(X))
    indices = torch.randperm(len(X))
    train_indices = indices[:n_train]
    val_indices = indices[n_train:]
    
    X_train, y_train = X[train_indices], y[train_indices]
    X_val, y_val = X[val_indices], y[val_indices]
    
    # Create datasets
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)
    
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False)
    
    # Training setup
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(meta_model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50)
    
    best_val_acc = 0
    best_model_state = None
    
    # Train
    for epoch in range(50):
        # Training
        meta_model.train()
        train_loss = 0
        train_correct = 0
        
        for batch_x, batch_y in train_loader:
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            optimizer.zero_grad()
            outputs, _ = meta_model(batch_x)
            loss = criterion(outputs, batch_y)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_correct += (outputs.argmax(1) == batch_y).sum().item()
        
        train_acc = train_correct / len(X_train)
        
        # Validation
        meta_model.eval()
        val_correct = 0
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x = batch_x.to(device)
                batch_y = batch_y.to(device)
                
                outputs, _ = meta_model(batch_x)
                val_correct += (outputs.argmax(1) == batch_y).sum().item()
        
        val_acc = val_correct / len(X_val)
        
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_model_state = meta_model.state_dict().copy()
        
        scheduler.step()
        
        if (epoch + 1) % 10 == 0:
            logging.info(f"Epoch {epoch+1}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}")
    
    # Load best model
    meta_model.load_state_dict(best_model_state)
    
    return meta_model


def evaluate_stacking():
    """Main evaluation function"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/meta_learner_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 META-LEARNER STACKING")
    logging.info("Current: 87.32% → Target: 90%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Model configurations
    model_configs = [
        {
            'name': 'V17_Stable',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth',
            'd_model': 288,
            'n_heads': 18,
            'n_layers': 6,
            'seq_len': 5
        },
        {
            'name': 'V18_Fixed',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v18_fixed_20250811_014911/best_model.pth',
            'd_model': 384,
            'n_heads': 24,
            'n_layers': 7,
            'seq_len': 6
        },
        {
            'name': 'V22_Deep',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v22_deep_20250811_030913/best_model.pth',
            'd_model': 448,
            'n_heads': 28,
            'n_layers': 10,
            'seq_len': 9
        }
    ]
    
    # Data
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Use validation set for meta-learner training
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Get predictions from validation set for training meta-learner
    logging.info("\n📊 Getting validation predictions for meta-learner training...")
    val_predictions, val_probabilities, val_labels = get_model_predictions(
        model_configs, val_files, device
    )
    
    # Create meta-features
    val_features = create_meta_features(val_probabilities)
    
    # Train meta-learner
    meta_model = train_meta_learner(val_features, val_labels, device)
    
    # Get test predictions
    logging.info("\n📊 Getting test predictions...")
    test_predictions, test_probabilities, test_labels = get_model_predictions(
        model_configs, test_files, device
    )
    
    # Create test features
    test_features = create_meta_features(test_probabilities)
    
    # Evaluate different strategies
    logging.info("\n" + "="*80)
    logging.info("EVALUATION RESULTS:")
    logging.info("="*80)
    
    # 1. Simple average (baseline)
    avg_probs = np.mean(test_probabilities, axis=0)
    avg_preds = np.argmax(avg_probs, axis=-1)
    avg_acc = accuracy_score(test_labels, avg_preds)
    avg_f1 = f1_score(test_labels, avg_preds, average='macro')
    avg_kappa = cohen_kappa_score(test_labels, avg_preds)
    
    logging.info(f"\n1. Simple Average:")
    logging.info(f"   Accuracy: {avg_acc:.4f} ({avg_acc*100:.2f}%)")
    logging.info(f"   F1: {avg_f1:.4f}, Kappa: {avg_kappa:.4f}")
    
    # 2. Weighted average (from grid search)
    weights = [0.55, 0.25, 0.20]
    weighted_probs = sum(p * w for p, w in zip(test_probabilities, weights))
    weighted_preds = np.argmax(weighted_probs, axis=-1)
    weighted_acc = accuracy_score(test_labels, weighted_preds)
    weighted_f1 = f1_score(test_labels, weighted_preds, average='macro')
    weighted_kappa = cohen_kappa_score(test_labels, weighted_preds)
    
    logging.info(f"\n2. Grid Search Weighted:")
    logging.info(f"   Accuracy: {weighted_acc:.4f} ({weighted_acc*100:.2f}%)")
    logging.info(f"   F1: {weighted_f1:.4f}, Kappa: {weighted_kappa:.4f}")
    
    # 3. Meta-learner predictions
    meta_model.eval()
    X_test = torch.FloatTensor(test_features).to(device)
    
    with torch.no_grad():
        meta_outputs, attention_weights = meta_model(X_test)
        meta_preds = meta_outputs.argmax(1).cpu().numpy()
    
    meta_acc = accuracy_score(test_labels, meta_preds)
    meta_f1 = f1_score(test_labels, meta_preds, average='macro')
    meta_kappa = cohen_kappa_score(test_labels, meta_preds)
    
    logging.info(f"\n3. Meta-Learner Stacking:")
    logging.info(f"   Accuracy: {meta_acc:.4f} ({meta_acc*100:.2f}%)")
    logging.info(f"   F1: {meta_f1:.4f}, Kappa: {meta_kappa:.4f}")
    
    # Analyze attention weights
    avg_attention = attention_weights.mean(0).cpu().numpy()
    logging.info(f"\n   Model Importance (from attention):")
    for i, config in enumerate(model_configs):
        logging.info(f"     {config['name']}: {avg_attention[i]:.3f}")
    
    # 4. Calibrated meta-learner (temperature scaling)
    temperature = 1.5
    calibrated_outputs = meta_outputs / temperature
    calibrated_probs = torch.softmax(calibrated_outputs, dim=-1)
    calibrated_preds = calibrated_probs.argmax(1).cpu().numpy()
    
    calib_acc = accuracy_score(test_labels, calibrated_preds)
    calib_f1 = f1_score(test_labels, calibrated_preds, average='macro')
    calib_kappa = cohen_kappa_score(test_labels, calibrated_preds)
    
    logging.info(f"\n4. Calibrated Meta-Learner:")
    logging.info(f"   Accuracy: {calib_acc:.4f} ({calib_acc*100:.2f}%)")
    logging.info(f"   F1: {calib_f1:.4f}, Kappa: {calib_kappa:.4f}")
    
    # Find best result
    all_accs = [avg_acc, weighted_acc, meta_acc, calib_acc]
    all_names = ["Simple Average", "Grid Search", "Meta-Learner", "Calibrated Meta"]
    
    best_acc = max(all_accs)
    best_name = all_names[all_accs.index(best_acc)]
    
    logging.info("\n" + "="*80)
    logging.info(f"🏆 BEST RESULT: {best_name}")
    logging.info(f"   Test Accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")
    
    # Check if we reached target
    if best_acc >= 0.90:
        logging.info("\n🎉🎉🎉 SUCCESS! 90% TARGET ACHIEVED! 🎉🎉🎉")
    else:
        gap = 0.90 - best_acc
        logging.info(f"\n📈 Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    logging.info("="*80)
    
    # Save results
    results = {
        'timestamp': timestamp,
        'models': [m['name'] for m in model_configs],
        'simple_average': {'acc': float(avg_acc), 'f1': float(avg_f1), 'kappa': float(avg_kappa)},
        'weighted_average': {'acc': float(weighted_acc), 'f1': float(weighted_f1), 'kappa': float(weighted_kappa)},
        'meta_learner': {'acc': float(meta_acc), 'f1': float(meta_f1), 'kappa': float(meta_kappa)},
        'calibrated': {'acc': float(calib_acc), 'f1': float(calib_f1), 'kappa': float(calib_kappa)},
        'best_method': best_name,
        'best_accuracy': float(best_acc),
        'target_achieved': best_acc >= 0.90
    }
    
    # Save meta-learner model
    torch.save({
        'model_state_dict': meta_model.state_dict(),
        'attention_weights': avg_attention,
        'test_accuracy': meta_acc
    }, os.path.join(log_dir, 'meta_learner.pth'))
    
    results_file = os.path.join(log_dir, 'results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    return results


if __name__ == "__main__":
    results = evaluate_stacking()