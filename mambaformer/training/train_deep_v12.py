"""
深度MAMBAFORMER V12训练脚本 - 更深网络架构
基于当前最佳结果，探索更深的网络架构以突破性能瓶颈

核心思想：
- 6-8层深度Transformer架构
- 渐进式深度训练（层级预训练）
- 深度残差连接和层归一化
- 自适应深度（动态层级激活）
- 更强的表征学习能力
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.multimodal_mambaformer import (
    MultiModalMAMBAFORMER, 
    ProgressiveMultiModalTrainer
)
from models.sequential_mambaformer_v2 import (
    SequentialFocalLoss,
    TemporalConsistencyLoss
)
from utils.multimodal_dataset import create_multimodal_dataloaders
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics
from utils.enhanced_metrics import get_comprehensive_metrics


class DeepMultiModalMAMBAFORMER(nn.Module):
    """深度多模态MAMBAFORMER - V12架构"""
    
    def __init__(self, n_classes=5, d_model=128, n_heads=8, n_layers=6, 
                 dropout=0.1, seq_len=5, use_eog=True, use_emg=False):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        self.use_eog = use_eog
        self.use_emg = use_emg
        
        # 复用现有的epoch编码器
        from models.multimodal_mambaformer import MultiModalEpochEncoder
        self.epoch_encoder = MultiModalEpochEncoder(
            d_model=d_model, 
            n_heads=n_heads,
            dropout=dropout,
            use_eog=use_eog,
            use_emg=use_emg
        )
        
        # 位置编码
        self.pos_encoding = self._create_positional_encoding(seq_len, d_model)
        
        # 深度Transformer编码器 - 增加到6-8层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,  # 扩大前馈网络
            dropout=dropout,
            activation='gelu',  # GELU激活函数
            batch_first=True,
            norm_first=True     # Pre-LN架构更稳定
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, num_layers=n_layers,
            enable_nested_tensor=False  # 禁用嵌套张量优化以提高稳定性
        )
        
        # 深度分类器（更多层级）
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.3),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)
        )
        
        # 深度特定的正则化
        self.layer_dropout = nn.Dropout(dropout * 0.1)  # 层级dropout
        
        # 权重初始化
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建深度MultiModalMAMBAFORMER V12 ({n_layers}层): 参数量={total_params:,}")
        
    def _create_positional_encoding(self, seq_len, d_model):
        """创建位置编码"""
        import math
        pe = torch.zeros(seq_len, d_model)
        position = torch.arange(0, seq_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pe.unsqueeze(0), requires_grad=False)
        
    def _init_weights(self):
        """深度网络权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # 使用Xavier初始化，针对深度网络调整
                nn.init.xavier_uniform_(m.weight, gain=0.5)  # 减小初始化scale
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, eeg_data, eog_data=None, emg_data=None):
        """
        深度网络前向传播
        Args:
            eeg_data: (batch, seq_len, time_steps)
            eog_data: (batch, seq_len, time_steps) - optional
            emg_data: (batch, seq_len, time_steps) - optional
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len = eeg_data.shape[:2]
        
        # Stage 1: 各epoch的多模态特征提取
        sequence_features = []
        
        for t in range(seq_len):
            eeg_t = eeg_data[:, t, :]  # (batch, time_steps)
            eog_t = eog_data[:, t, :] if eog_data is not None else None
            emg_t = emg_data[:, t, :] if emg_data is not None else None
            
            epoch_feat, _ = self.epoch_encoder(eeg_t, eog_t, emg_t)
            sequence_features.append(epoch_feat)
        
        # Stage 2: 组成序列特征
        sequence_features = torch.stack(sequence_features, dim=1)  # (B, seq_len, d_model)
        
        # Stage 3: 添加位置编码
        sequence_features = sequence_features + self.pos_encoding[:, :seq_len, :]
        
        # Stage 4: 深度层级dropout（正则化）
        sequence_features = self.layer_dropout(sequence_features)
        
        # Stage 5: 深度Transformer序列编码
        encoded_sequence = self.transformer_encoder(sequence_features)
        
        # Stage 6: 分类预测
        main_output = self.classifier(encoded_sequence)
        aux_output = self.auxiliary_head(encoded_sequence)
        
        return main_output, aux_output


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"deep_v12_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


class LabelSmoothingLoss(nn.Module):
    """标签平滑损失"""
    def __init__(self, n_classes, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
        
    def forward(self, pred, target):
        pred = pred.log_softmax(dim=-1)
        true_dist = torch.zeros_like(pred)
        true_dist.fill_(self.smoothing / (self.n_classes - 1))
        true_dist.scatter_(1, target.data.unsqueeze(1), self.confidence)
        return torch.mean(torch.sum(-true_dist * pred, dim=-1))


def create_auxiliary_labels(labels):
    """创建辅助任务标签"""
    deep_sleep = ((labels == 3) | (labels == 4)).long()
    return deep_sleep


def get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps, num_cycles=0.5):
    """余弦退火学习率调度器，带预热"""
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        progress = float(current_step - num_warmup_steps) / float(max(1, num_training_steps - num_warmup_steps))
        return max(0.0, 0.5 * (1.0 + np.cos(np.pi * float(num_cycles) * 2.0 * progress)))
    
    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)


def train_epoch_v12(model, train_loader, criterion, aux_criterion, temp_loss_fn, 
                   optimizer, device, epoch, config, scaler):
    """V12深度网络训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (eeg_data, eog_data, emg_data, labels) in enumerate(pbar):
        eeg_data = eeg_data.to(device, non_blocking=True)
        eog_data = eog_data.to(device, non_blocking=True) if eog_data is not None else None
        emg_data = emg_data.to(device, non_blocking=True) if emg_data is not None else None
        labels = labels.to(device, non_blocking=True)
        
        optimizer.zero_grad()
        
        # 混合精度前向传播
        with torch.amp.autocast('cuda', enabled=config.get('use_amp', False)):
            # 深度网络前向传播
            main_output, aux_output = model(eeg_data, eog_data, emg_data)
            
            # 主损失（标签平滑）
            main_loss = criterion(main_output.view(-1, 5), labels.view(-1))
            
            # 辅助损失
            aux_labels = create_auxiliary_labels(labels)
            aux_loss = aux_criterion(aux_output.view(-1, 2), aux_labels.view(-1))
            
            # 时序一致性损失
            temp_loss = temp_loss_fn(main_output)
            
            # 总损失
            loss = (main_loss + 
                   0.15 * aux_loss + 
                   config['temp_loss_weight'] * temp_loss)
        
        # 混合精度反向传播
        if config.get('use_amp', False):
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)  # 深度网络需要更强的梯度裁剪
            scaler.step(optimizer)
            scaler.update()
        else:
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    # 计算指标
    metrics = get_comprehensive_metrics(np.array(all_labels), np.array(all_preds))
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, metrics


def evaluate_epoch_level_v12(model, test_dataset, test_loader, device, config):
    """V12正确的epoch级别评估"""
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (eeg_data, eog_data, emg_data, labels) in enumerate(tqdm(test_loader, desc="Evaluating")):
            eeg_data = eeg_data.to(device, non_blocking=True)
            eog_data = eog_data.to(device, non_blocking=True) if eog_data is not None else None
            emg_data = emg_data.to(device, non_blocking=True) if emg_data is not None else None
            labels = labels.to(device, non_blocking=True)
            
            # 获取模型输出
            outputs, _ = model(eeg_data, eog_data, emg_data)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取序列信息
            batch_size = eeg_data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        start_indices.append(seq_idx)
                else:
                    break
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    return evaluator.evaluate()


def train_v12_deep(config, device):
    """V12深度网络训练主函数"""
    logging.info("\n" + "="*80)
    logging.info("🚀 开始训练深度MAMBAFORMER V12 - 突破性能瓶颈")
    logging.info("📋 深度架构：6-8层Transformer + GELU + Pre-LN + 深度分类器")
    
    # 加载数据 - 使用最佳的多模态配置
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 创建数据加载器 - 根据配置选择模态
    (train_loader, val_loader, test_loader, 
     train_dataset, val_dataset, test_dataset) = create_multimodal_dataloaders(
        train_files, val_files, test_files, 
        config,
        use_eog=config.get('use_eog', True),   
        use_emg=config.get('use_emg', False)   
    )
    
    logging.info(f"📊 数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 创建深度模型
    model = DeepMultiModalMAMBAFORMER(
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],  # 深度网络
        dropout=config['dropout'],
        seq_len=config['seq_len'],
        use_eog=config.get('use_eog', True),
        use_emg=config.get('use_emg', False)
    )
    model = model.to(device)
    
    # 损失函数
    criterion = LabelSmoothingLoss(n_classes=5, smoothing=config['label_smoothing'])
    aux_criterion = nn.CrossEntropyLoss()
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    # 优化器 - 深度网络使用AdamW
    optimizer = optim.AdamW(model.parameters(), lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'], betas=(0.9, 0.95))  # beta2调整为0.95
    
    # 学习率调度器 - 更长的预热
    num_training_steps = config['num_epochs'] * len(train_loader)
    num_warmup_steps = int(0.2 * num_training_steps)  # 20%预热步数，深度网络需要更长预热
    scheduler = get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps)
    
    # 混合精度训练
    scaler = torch.amp.GradScaler('cuda', enabled=config.get('use_amp', False))
    
    logging.info(f"🎯 深度网络配置: {config['n_layers']}层 Transformer")
    logging.info(f"🧠 GELU激活 + Pre-LN架构 + 深度分类器")
    logging.info(f"⚡ 混合精度: {config.get('use_amp', False)}")
    logging.info(f"🚀 学习率调度: 预热{num_warmup_steps}步 (20%), 总{num_training_steps}步")
    
    # 训练循环
    best_val_metrics = {'macro_f1': 0, 'kappa': 0}
    best_model_state = None
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_metrics = train_epoch_v12(
            model, train_loader, criterion, aux_criterion, 
            temp_loss_fn, optimizer, device, epoch, config, scaler
        )
        
        # 验证（使用正确的epoch级别评估）
        val_metrics = evaluate_epoch_level_v12(model, val_dataset, val_loader, device, config)
        
        # 学习率调度
        if scheduler:
            for _ in range(len(train_loader)):
                scheduler.step()
        
        # 记录
        current_lr = optimizer.param_groups[0]['lr']
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_metrics['accuracy']:.4f}, F1: {train_metrics['macro_f1']:.4f}, Kappa: {train_metrics['kappa']:.4f}")
        logging.info(f"Val   - Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}, Kappa: {val_metrics['kappa']:.4f}")
        logging.info(f"Val REM F1: {val_metrics['per_class_metrics']['REM']['f1']:.4f}")
        logging.info(f"LR: {current_lr:.2e}")
        
        # 保存最佳模型
        val_score = val_metrics['macro_f1'] + 0.05 * val_metrics['per_class_metrics']['REM']['f1']
        best_score = best_val_metrics['macro_f1'] + 0.05 * best_val_metrics.get('rem_f1', 0)
        
        if val_score > best_score:
            best_val_metrics = val_metrics.copy()
            best_val_metrics['rem_f1'] = val_metrics['per_class_metrics']['REM']['f1']
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳模型: F1={val_metrics['macro_f1']:.4f}, Kappa={val_metrics['kappa']:.4f}")
        else:
            patience_counter += 1
        
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: {config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
    
    test_metrics = evaluate_epoch_level_v12(model, test_dataset, test_loader, device, config)
    
    # 详细测试结果
    log_epoch_level_metrics(test_metrics, phase='Test V12 Deep', logger=logging)
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), '../../checkpoints/deep_v12.pth')
    
    return {
        'test_metrics': test_metrics,
        'best_val_metrics': best_val_metrics,
        'config': config
    }


def main():
    # V12深度配置
    config = {
        'batch_size': 20,        # 深度网络降低batch size
        'seq_len': 5,
        'learning_rate': 8e-6,   # 更小的学习率，深度网络需要谨慎训练
        'weight_decay': 2e-4,    # 增加正则化
        'num_epochs': 80,        # 更多epochs让深度网络收敛
        'patience': 18,          # 增加patience
        'd_model': 128,
        'n_heads': 8,
        'n_layers': 7,           # 7层深度架构！
        'dropout': 0.18,         # 增加dropout防过拟合
        'temp_loss_weight': 0.08, 
        'label_smoothing': 0.07,  # 增加标签平滑
        'use_amp': True,
        'use_channels': 2,
        'num_workers': 4,
        # 模态配置
        'use_eog': True,         # 使用EOG
        'use_emg': False         # 不使用EMG，专注于EEG+EOG
    }
    
    log_file = setup_logging()
    
    logging.info("🚀 深度MAMBAFORMER V12训练 - 突破性能极限")
    logging.info("=" * 80)
    logging.info("🎯 V12深度特性:")
    logging.info(f"  • {config['n_layers']}层深度Transformer架构")
    logging.info("  • GELU激活函数 + Pre-LN稳定训练")
    logging.info("  • 深度分类器 + 强化正则化")
    logging.info("  • 扩大前馈网络 + 层级dropout")
    logging.info("  • 20%预热步数 + 强梯度裁剪")
    logging.info("  • EEG+EOG双模态专注")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 训练
    result = train_v12_deep(config, device)
    
    # 对比结果
    logging.info("\n" + "="*80)
    logging.info("📊 深度网络 vs 标准网络性能对比")
    logging.info("="*80)
    
    # 已知基线结果
    baselines = {
        'V7_EEG_4层': {'accuracy': 0.8564, 'macro_f1': 0.7890, 'kappa': 0.8051},
        'V8_EEG_4层': {'accuracy': 0.8374, 'macro_f1': 0.7880, 'kappa': 0.7815},
        'V10_EEG_EOG_4层': {'accuracy': 0.85, 'macro_f1': 0.80, 'kappa': 0.82}  # 估计
    }
    
    v12_metrics = result['test_metrics']
    baselines[f'V12_EEG_EOG_{config["n_layers"]}层'] = {
        'accuracy': v12_metrics['accuracy'],
        'macro_f1': v12_metrics['macro_f1'],
        'kappa': v12_metrics['kappa']
    }
    
    logging.info("深度vs标准架构对比:")
    for version, metrics in baselines.items():
        layers = version.split('_')[-1]
        modality = version.split('_')[1:-1]
        modality_str = '+'.join(modality)
        logging.info(f"{version:>18} ({modality_str}) - Acc: {metrics['accuracy']:.4f}, F1: {metrics['macro_f1']:.4f}, Kappa: {metrics['kappa']:.4f}")
    
    # 深度网络改进分析
    best_4layer_f1 = max([baselines[k]['macro_f1'] for k in baselines if '4层' in k])
    v12_f1 = v12_metrics['macro_f1']
    depth_improvement = v12_f1 - best_4layer_f1
    
    logging.info(f"\n🏗️  深度架构分析:")
    logging.info(f"最佳4层F1: {best_4layer_f1:.4f}")
    logging.info(f"V12 {config['n_layers']}层F1: {v12_f1:.4f}")
    logging.info(f"深度改进: {depth_improvement:+.4f} F1")
    
    if depth_improvement > 0.01:
        logging.info("🎉 深度架构显著改进！网络容量提升有效")
    elif depth_improvement > 0:
        logging.info("✅ 深度架构轻微改进，可以进一步调优")
    else:
        logging.info("⚠️  深度架构未显著改进，可能需要调整训练策略")
    
    # 保存结果
    results = {
        'version': 'V12_Deep',
        'result': result,
        'log_file': log_file,
        'architecture': f'{config["n_layers"]}-layer Deep MultiModal MAMBAFORMER',
        'depth_improvement': depth_improvement,
        'config': config
    }
    
    with open('../../configs/deep_v12_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=lambda x: float(x) if hasattr(x, 'item') else x)
    
    logging.info(f"\n💾 V12深度网络结果已保存")
    logging.info("🌟 深度网络探索完成！架构优化里程碑达成！")


if __name__ == "__main__":
    main()