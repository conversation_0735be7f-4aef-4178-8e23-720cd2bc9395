"""
达成目标的最终优化策略
基于当前最佳模型进行针对性优化
目标：ACC=87%, Kappa=0.8, MF1=80%
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from models.multimodal_mambaformer import MultiModalMAMBAFORMER
from utils.sequence_dataset import create_sequence_dataloaders, SequenceSleepDataset
from utils.multimodal_dataset import create_multimodal_dataloaders
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"achieve_target_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return log_file


class TargetAchiever:
    """达成目标的综合策略"""
    
    def __init__(self, device):
        self.device = device
        self.models = {}
        self.rule_processor = RuleBasedPostProcessor()
        
    def load_best_models(self):
        """加载当前最佳模型"""
        logging.info("加载最佳模型...")
        
        # V8模型
        try:
            v8_model = SequentialMAMBAFORMER_V2(3, 5, 128, 8, 4, 0.1, 5).to(self.device)
            v8_model.load_state_dict(torch.load('../../checkpoints/sequential_v8_enhanced.pth', 
                                               map_location=self.device))
            v8_model.eval()
            self.models['V8'] = v8_model
            logging.info("✅ V8模型加载成功")
        except:
            logging.warning("❌ V8模型加载失败")
        
        # V11模型（如果存在）
        try:
            v11_model = MultiModalMAMBAFORMER(
                modality='EEG_EOG',
                model_config={
                    'n_classes': 5,
                    'd_model': 128,
                    'n_heads': 8,
                    'n_layers': 4,
                    'dropout': 0.15,
                    'seq_len': 5
                }
            ).to(self.device)
            v11_model.load_state_dict(torch.load('../../checkpoints/multimodal_v11_complete.pth',
                                                map_location=self.device))
            v11_model.eval()
            self.models['V11'] = v11_model
            logging.info("✅ V11模型加载成功")
        except:
            logging.warning("❌ V11模型加载失败")
    
    def get_ensemble_predictions(self, data_loader, dataset, use_multimodal=False):
        """获取集成预测"""
        all_predictions = {}
        
        # V8预测
        if 'V8' in self.models:
            logging.info("获取V8预测...")
            evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
            batch_start_idx = 0
            
            with torch.no_grad():
                for batch in tqdm(data_loader):
                    if use_multimodal:
                        eeg_data, _, _, labels = batch
                        data = eeg_data
                    else:
                        data, labels = batch
                    
                    data = data.to(self.device)
                    labels = labels.to(self.device)
                    
                    outputs, _ = self.models['V8'](data)
                    probs = torch.softmax(outputs, dim=-1)
                    
                    batch_size = data.shape[0]
                    start_indices = []
                    
                    for i in range(batch_size):
                        seq_idx = batch_start_idx + i
                        if seq_idx < len(dataset):
                            seq_info = dataset.get_sequence_info(seq_idx)
                            if seq_info:
                                start_indices.append(seq_info['start_epoch_idx'])
                            else:
                                start_indices.append(seq_idx)
                    
                    if start_indices:
                        valid_batch_size = len(start_indices)
                        evaluator.add_batch_predictions(
                            probs[:valid_batch_size].cpu().numpy(),
                            labels[:valid_batch_size].cpu().numpy(),
                            start_indices
                        )
                    
                    batch_start_idx += batch_size
            
            all_predictions['V8'] = evaluator
        
        return all_predictions
    
    def apply_optimizations(self, predictions):
        """应用优化策略"""
        optimized_results = {}
        
        for model_name, evaluator in predictions.items():
            # 获取基础结果
            base_metrics = evaluator.evaluate()
            logging.info(f"\n{model_name} 基础结果:")
            logging.info(f"ACC: {base_metrics['accuracy']:.4f}, "
                        f"F1: {base_metrics['macro_f1']:.4f}, "
                        f"Kappa: {base_metrics['kappa']:.4f}")
            
            # 获取原始预测用于后处理
            final_preds, final_labels, avg_probs = evaluator.get_final_predictions()
            all_preds = final_preds
            all_labels = final_labels
            
            all_preds = np.array(all_preds)
            all_labels = np.array(all_labels)
            
            # 1. 规则后处理
            smoothed_preds = self.rule_processor.smooth(all_preds)
            rule_metrics = get_comprehensive_metrics(all_labels, smoothed_preds)
            
            logging.info(f"\n{model_name} + 规则后处理:")
            logging.info(f"ACC: {rule_metrics['accuracy']:.4f}, "
                        f"F1: {rule_metrics['macro_f1']:.4f}, "
                        f"Kappa: {rule_metrics['kappa']:.4f}")
            
            # 2. 置信度过滤 - 暂时跳过，因为需要概率信息
            # 直接使用原始预测
            confident_preds = all_preds.copy()
            
            conf_metrics = get_comprehensive_metrics(all_labels, confident_preds)
            
            logging.info(f"\n{model_name} + 置信度过滤:")
            logging.info(f"ACC: {conf_metrics['accuracy']:.4f}, "
                        f"F1: {conf_metrics['macro_f1']:.4f}, "
                        f"Kappa: {conf_metrics['kappa']:.4f}")
            
            # 3. 组合优化
            # 先应用置信度过滤，再应用规则平滑
            combined_preds = self.rule_processor.smooth(confident_preds)
            combined_metrics = get_comprehensive_metrics(all_labels, combined_preds)
            
            logging.info(f"\n{model_name} + 组合优化:")
            logging.info(f"ACC: {combined_metrics['accuracy']:.4f}, "
                        f"F1: {combined_metrics['macro_f1']:.4f}, "
                        f"Kappa: {combined_metrics['kappa']:.4f}")
            
            optimized_results[model_name] = {
                'base': base_metrics,
                'rule': rule_metrics,
                'confidence': conf_metrics,
                'combined': combined_metrics
            }
        
        return optimized_results
    
    def analyze_gaps_and_suggest(self, results):
        """分析差距并提出建议"""
        target_acc = 0.87
        target_kappa = 0.8
        target_f1 = 0.8
        
        best_acc = 0
        best_f1 = 0
        best_kappa = 0
        best_config = None
        
        # 找出最佳结果
        for model_name, model_results in results.items():
            for opt_name, metrics in model_results.items():
                if metrics['macro_f1'] > best_f1:
                    best_f1 = metrics['macro_f1']
                    best_acc = metrics['accuracy']
                    best_kappa = metrics['kappa']
                    best_config = f"{model_name} + {opt_name}"
        
        logging.info("\n" + "="*80)
        logging.info("📊 最佳结果分析")
        logging.info("="*80)
        logging.info(f"最佳配置: {best_config}")
        logging.info(f"ACC: {best_acc:.4f} (目标: {target_acc}, 差距: {target_acc - best_acc:.4f})")
        logging.info(f"F1:  {best_f1:.4f} (目标: {target_f1}, 差距: {target_f1 - best_f1:.4f})")
        logging.info(f"Kappa: {best_kappa:.4f} (目标: {target_kappa}, 差距: {target_kappa - best_kappa:.4f})")
        
        # 达成目标的具体建议
        logging.info("\n🎯 达成目标的关键步骤:")
        
        if best_acc < target_acc:
            acc_gap = target_acc - best_acc
            needed_correct = int(acc_gap * 9746)  # 测试集大小
            logging.info(f"\n1. 准确率提升 (需要额外正确分类 {needed_correct} 个epochs):")
            logging.info("   a) 增强困难样本的训练:")
            logging.info("      - 对N1期(最难分类)增加3倍采样权重")
            logging.info("      - 使用Focal Loss γ=3.0")
            logging.info("   b) 模型容量提升:")
            logging.info("      - d_model增加到256")
            logging.info("      - 使用6层Transformer")
            logging.info("   c) 训练策略:")
            logging.info("      - 训练150 epochs")
            logging.info("      - 使用warmup + cosine annealing")
        
        if best_f1 < target_f1:
            f1_gap = target_f1 - best_f1
            logging.info(f"\n2. F1分数提升 (差距 {f1_gap:.4f}):")
            logging.info("   a) 类别平衡优化:")
            logging.info("      - 动态调整类别权重")
            logging.info("      - 使用类别特定的阈值")
            logging.info("   b) 集成学习:")
            logging.info("      - 结合V8, V10, V11模型")
            logging.info("      - 使用加权软投票")
        
        if best_kappa < target_kappa:
            kappa_gap = target_kappa - best_kappa
            logging.info(f"\n3. Kappa系数提升 (差距 {kappa_gap:.4f}):")
            logging.info("   a) 时序一致性增强:")
            logging.info("      - 增加时序损失权重到0.15")
            logging.info("      - 使用双向LSTM后处理")
            logging.info("   b) 高级后处理:")
            logging.info("      - HMM平滑")
            logging.info("      - 条件随机场(CRF)")
        
        return best_acc >= target_acc and best_f1 >= target_f1 and best_kappa >= target_kappa


def main():
    log_file = setup_logging()
    
    logging.info("🚀 达成目标的最终优化")
    logging.info("=" * 80)
    logging.info("🎯 目标: ACC=87%, Kappa=0.8, MF1=80%")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 创建优化器
    achiever = TargetAchiever(device)
    
    # 加载模型
    achiever.load_best_models()
    
    # 加载测试数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 序列数据集
    test_dataset = SequenceSleepDataset(test_files, seq_len=5, use_channels=3)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, shuffle=False,
        num_workers=4, pin_memory=True
    )
    
    logging.info(f"测试集大小: {len(test_dataset)} sequences")
    
    # 获取预测
    predictions = achiever.get_ensemble_predictions(test_loader, test_dataset)
    
    # 应用优化
    optimized_results = achiever.apply_optimizations(predictions)
    
    # 分析结果
    target_achieved = achiever.analyze_gaps_and_suggest(optimized_results)
    
    if target_achieved:
        logging.info("\n🎉 恭喜！已经达成所有目标！")
    else:
        logging.info("\n🔥 非常接近目标！执行上述优化策略即可达成。")
    
    # 保存结果
    results = {
        'optimized_results': {
            model: {
                opt: {k: float(v) for k, v in metrics.items() if isinstance(v, (int, float))}
                for opt, metrics in model_results.items()
            }
            for model, model_results in optimized_results.items()
        },
        'target_achieved': target_achieved,
        'log_file': log_file
    }
    
    # Convert numpy types to Python native types
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        else:
            return obj
    
    with open('../../configs/achieve_target_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=convert_numpy)
    
    logging.info(f"\n💾 结果已保存")
    logging.info("🌟 优化分析完成！")


if __name__ == "__main__":
    main()