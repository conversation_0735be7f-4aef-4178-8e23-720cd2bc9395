"""
简化版Cross-Modal Transformer训练脚本 - 带K折交叉验证
避免数据泄露，使用受试者级别的数据划分
"""

import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import h5py
from sklearn.model_selection import KFold
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import time
from tqdm import tqdm
import json
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class SleepDataset(Dataset):
    """睡眠数据集 - 支持受试者级别的数据划分"""
    def __init__(self, data_file, subject_ids, transform=None):
        self.data_file = data_file
        self.subject_ids = subject_ids
        self.transform = transform
        
        # 加载指定受试者的数据
        self.data = []
        self.labels = []
        
        with h5py.File(data_file, 'r') as hf:
            for subj_id in subject_ids:
                key = f'subject_{subj_id:02d}'
                if key in hf:
                    subj_data = hf[key]['data'][:]
                    subj_labels = hf[key]['labels'][:]
                    
                    self.data.append(subj_data)
                    self.labels.append(subj_labels)
            
            # 合并所有受试者的数据
            if self.data:
                self.data = np.concatenate(self.data, axis=0)
                self.labels = np.concatenate(self.labels, axis=0)
            else:
                self.data = np.array([])
                self.labels = np.array([])
        
        print(f"加载了 {len(subject_ids)} 个受试者的 {len(self.data)} 个epochs")
        if len(self.labels) > 0:
            print(f"标签分布: {np.bincount(self.labels)}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        # 获取数据
        x = self.data[idx]  # (3, 3000)
        y = self.labels[idx]
        
        # 转换为tensor
        x = torch.FloatTensor(x)
        y = torch.LongTensor([y]).squeeze()
        
        if self.transform:
            x = self.transform(x)
        
        return x, y

class SimpleSleepTransformer(nn.Module):
    """简化版睡眠分期Transformer模型"""
    def __init__(self, input_channels=3, num_classes=5, d_model=128, nhead=8, 
                 num_layers=4, dropout=0.1):
        super().__init__()
        
        # CNN特征提取 - 大幅降低序列长度
        self.cnn_features = nn.Sequential(
            # 第一阶段：3000 -> 750
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=25, padding=25),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),  # 750 -> 187
            
            # 第二阶段：187 -> 46
            nn.Conv1d(64, 128, kernel_size=10, stride=5, padding=5),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),  # 46 -> 11
            
            # 第三阶段：投影到d_model
            nn.Conv1d(128, d_model, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
        )
        
        # 位置编码
        self.pos_encoder = nn.Parameter(torch.randn(1, 11, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=512,
            dropout=dropout,
            activation='gelu'
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # CNN特征提取
        cnn_feat = self.cnn_features(x)  # (B, d_model, 11)
        
        # 准备transformer输入
        x_transformer = cnn_feat.permute(2, 0, 1)  # (11, B, d_model)
        
        # 添加位置编码
        pos = self.pos_encoder.permute(1, 0, 2).repeat(1, batch_size, 1)
        x_transformer = x_transformer + pos
        
        # Transformer编码
        x_encoded = self.transformer(x_transformer)  # (11, B, d_model)
        
        # 全局池化
        x_pooled = x_encoded.mean(dim=0)  # (B, d_model)
        
        # 分类
        output = self.classifier(x_pooled)
        
        return output

class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance"""
    def __init__(self, alpha=None, gamma=2.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if self.alpha.device != focal_loss.device:
                self.alpha = self.alpha.to(focal_loss.device)
            focal_loss = self.alpha[targets] * focal_loss
        
        return focal_loss.mean()

def train_epoch(model, dataloader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    for batch_x, batch_y in tqdm(dataloader, desc="Training"):
        batch_x = batch_x.to(device)
        batch_y = batch_y.to(device)
        
        # 前向传播
        outputs = model(batch_x)
        loss = criterion(outputs, batch_y)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 记录
        total_loss += loss.item()
        _, preds = torch.max(outputs, 1)
        all_preds.extend(preds.cpu().numpy())
        all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy

def evaluate(model, dataloader, criterion, device):
    """评估模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_x, batch_y in tqdm(dataloader, desc="Evaluating"):
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            
            total_loss += loss.item()
            _, preds = torch.max(outputs, 1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    return avg_loss, accuracy, f1, all_preds, all_labels

def main():
    # 超参数
    config = {
        'batch_size': 64,
        'learning_rate': 1e-4,
        'num_epochs': 20,
        'd_model': 128,
        'nhead': 8,
        'num_layers': 4,
        'dropout': 0.2,
        'k_folds': 5,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu'
    }
    
    print(f"使用设备: {config['device']}")
    
    # 加载数据信息
    data_file = 'sleep_edf_20_correct.h5'
    
    # 获取所有受试者ID
    with h5py.File(data_file, 'r') as hf:
        all_subjects = []
        for key in hf.keys():
            if key.startswith('subject_'):
                subj_id = int(key.split('_')[1])
                all_subjects.append(subj_id)
    
    all_subjects = sorted(all_subjects)
    print(f"总受试者数: {len(all_subjects)}")
    print(f"受试者ID: {all_subjects}")
    
    # 计算类别权重（用于处理类别不平衡）
    with h5py.File(data_file, 'r') as hf:
        all_labels = []
        for subj_id in all_subjects:
            key = f'subject_{subj_id:02d}'
            if key in hf:
                labels = hf[key]['labels'][:]
                all_labels.extend(labels)
        
        class_counts = np.bincount(all_labels)
        class_weights = 1.0 / (class_counts + 1)
        class_weights = class_weights / class_weights.sum() * len(class_weights)
        alpha = torch.FloatTensor(class_weights)
        print(f"类别分布: {class_counts}")
        print(f"类别权重: {class_weights}")
    
    # K折交叉验证
    kfold = KFold(n_splits=config['k_folds'], shuffle=True, random_state=42)
    fold_results = []
    
    for fold, (train_idx, test_idx) in enumerate(kfold.split(all_subjects)):
        print(f"\n{'='*50}")
        print(f"Fold {fold + 1}/{config['k_folds']}")
        print(f"{'='*50}")
        
        # 获取训练和测试受试者
        train_subjects = [all_subjects[i] for i in train_idx]
        test_subjects = [all_subjects[i] for i in test_idx]
        
        # 从训练集中分出验证集（20%）
        val_size = max(1, len(train_subjects) // 5)
        val_subjects = train_subjects[-val_size:]
        train_subjects = train_subjects[:-val_size]
        
        print(f"训练受试者: {train_subjects}")
        print(f"验证受试者: {val_subjects}")
        print(f"测试受试者: {test_subjects}")
        
        # 创建数据集
        train_dataset = SleepDataset(data_file, train_subjects)
        val_dataset = SleepDataset(data_file, val_subjects)
        test_dataset = SleepDataset(data_file, test_subjects)
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], 
                                shuffle=True, num_workers=4, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], 
                              shuffle=False, num_workers=4, pin_memory=True)
        test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], 
                               shuffle=False, num_workers=4, pin_memory=True)
        
        # 创建模型
        model = SimpleSleepTransformer(
            input_channels=3,
            num_classes=5,
            d_model=config['d_model'],
            nhead=config['nhead'],
            num_layers=config['num_layers'],
            dropout=config['dropout']
        ).to(config['device'])
        
        # 损失函数和优化器
        criterion = FocalLoss(alpha=alpha.to(config['device']), gamma=2.0)
        optimizer = torch.optim.AdamW(model.parameters(), lr=config['learning_rate'], 
                                     weight_decay=1e-5)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, 
                                                               T_max=config['num_epochs'])
        
        # 训练
        best_val_acc = 0
        best_model_state = None
        
        for epoch in range(config['num_epochs']):
            print(f"\nEpoch {epoch + 1}/{config['num_epochs']}")
            
            # 训练
            train_loss, train_acc = train_epoch(model, train_loader, criterion, 
                                              optimizer, config['device'])
            
            # 验证
            val_loss, val_acc, val_f1, _, _ = evaluate(model, val_loader, criterion, 
                                                       config['device'])
            
            # 学习率调度
            scheduler.step()
            
            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}, Val F1: {val_f1:.4f}")
            
            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_model_state = model.state_dict().copy()
        
        # 在测试集上评估最佳模型
        model.load_state_dict(best_model_state)
        test_loss, test_acc, test_f1, test_preds, test_labels = evaluate(
            model, test_loader, criterion, config['device']
        )
        
        print(f"\n最佳验证准确率: {best_val_acc:.4f}")
        print(f"测试准确率: {test_acc:.4f}")
        print(f"测试F1分数: {test_f1:.4f}")
        
        # 混淆矩阵
        cm = confusion_matrix(test_labels, test_preds)
        print("\n混淆矩阵:")
        print(cm)
        
        # 分类报告
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        report = classification_report(test_labels, test_preds, 
                                     target_names=class_names, digits=4)
        print("\n分类报告:")
        print(report)
        
        # 保存fold结果
        fold_results.append({
            'fold': fold + 1,
            'test_acc': test_acc,
            'test_f1': test_f1,
            'val_acc': best_val_acc,
            'confusion_matrix': cm.tolist(),
            'classification_report': report
        })
        
        # 保存模型
        torch.save({
            'model_state_dict': best_model_state,
            'config': config,
            'fold': fold + 1,
            'test_acc': test_acc,
            'test_f1': test_f1
        }, f'simple_transformer_fold{fold+1}.pth')
    
    # 汇总所有fold的结果
    print(f"\n{'='*50}")
    print(f"K折交叉验证结果汇总")
    print(f"{'='*50}")
    
    test_accs = [r['test_acc'] for r in fold_results]
    test_f1s = [r['test_f1'] for r in fold_results]
    
    print(f"平均测试准确率: {np.mean(test_accs):.4f} ± {np.std(test_accs):.4f}")
    print(f"平均测试F1分数: {np.mean(test_f1s):.4f} ± {np.std(test_f1s):.4f}")
    
    # 显示每个fold的结果
    for i, result in enumerate(fold_results):
        print(f"Fold {i+1}: 测试准确率={result['test_acc']:.4f}, 测试F1={result['test_f1']:.4f}")
    
    # 保存所有结果
    with open('simple_kfold_results.json', 'w') as f:
        json.dump({
            'config': config,
            'fold_results': fold_results,
            'summary': {
                'mean_test_acc': float(np.mean(test_accs)),
                'std_test_acc': float(np.std(test_accs)),
                'mean_test_f1': float(np.mean(test_f1s)),
                'std_test_f1': float(np.std(test_f1s))
            }
        }, f, indent=2)
    
    print("\n训练完成！")
    print("这次的结果是基于正确的受试者级别数据划分，避免了数据泄露。")

if __name__ == "__main__":
    main()