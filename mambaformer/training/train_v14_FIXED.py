#!/usr/bin/env python3
"""
训练V14模型 - REM专注版修复（使用完整数据）
修复了数据加载问题：显式设置max_samples_per_file=None

专门改善REM分类性能
目标：ACC=87%, Kappa=0.8, MF1=80%
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import (
    SequentialMAMBAFORMER_V2,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


class REMFocusedLoss(nn.Module):
    """专注于改善REM和Wake分类的损失函数"""
    def __init__(self, alpha=None, gamma=2.0, rem_weight=3.0, wake_weight=5.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.rem_weight = rem_weight
        self.wake_weight = wake_weight
        self.ce_loss = nn.CrossEntropyLoss(reduction='none')
        
    def forward(self, input, target):
        # 基础交叉熵损失
        ce_loss = self.ce_loss(input, target)
        
        # Focal损失计算
        p = torch.exp(-ce_loss)
        focal_loss = (1 - p) ** self.gamma * ce_loss
        
        # REM类(0)和Wake类(4)额外加权
        rem_mask = (target == 0).float()
        wake_mask = (target == 4).float()
        
        focal_loss = focal_loss * (1 + rem_mask * (self.rem_weight - 1) + wake_mask * (self.wake_weight - 1))
        
        # REM置信度正则化
        if rem_mask.any():
            rem_outputs = input[rem_mask.bool()]
            rem_probs = torch.softmax(rem_outputs, dim=-1)
            rem_confidence_loss = -torch.log(rem_probs[:, 0] + 1e-8).mean()
            return focal_loss.mean() + 0.1 * rem_confidence_loss
        
        return focal_loss.mean()


def setup_logging(config):
    """设置日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"../logs/v14_fixed_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V14 训练 - REM/Wake专注修复版（使用完整数据）")
    logging.info("="*80)
    logging.info(f"📝 日志文件: {log_file}")
    logging.info(f"🔧 配置: {json.dumps(config, indent=2)}")
    
    return log_file


def train_epoch_v14(model, train_loader, criterion, temp_loss_fn,
                   optimizer, device, epoch, config):
    """训练一个epoch - REM专注"""
    model.train()
    total_loss = 0
    total_correct = 0
    total_samples = 0
    all_preds = []
    all_labels = []
    
    # REM和Wake类的统计
    rem_correct = 0
    rem_total = 0
    wake_correct = 0
    wake_total = 0
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch}')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device)
        labels = labels.to(device)
        
        # REM数据增强（增强REM样本）
        rem_mask = (labels == 0).any(dim=1)
        if rem_mask.any() and np.random.random() < 0.3:
            # 对REM样本添加轻微噪声
            data[rem_mask] = data[rem_mask] + torch.randn_like(data[rem_mask]) * 0.02
        
        # 前向传播
        outputs, temporal_features = model(data)
        
        # 计算损失
        main_loss = criterion(outputs.view(-1, 5), labels.view(-1))
        temp_loss = temp_loss_fn(outputs) if temp_loss_fn else 0
        
        # 动态权重
        temp_weight = min(0.2, 0.1 * (1 + epoch / 50))
        
        loss = main_loss + temp_weight * temp_loss
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        preds = torch.argmax(outputs, dim=-1)
        
        # 整体准确率
        correct = (preds.view(-1) == labels.view(-1))
        total_correct += correct.sum().item()
        total_samples += labels.numel()
        
        # REM和Wake准确率
        labels_flat = labels.view(-1)
        preds_flat = preds.view(-1)
        
        rem_mask_flat = (labels_flat == 0)
        if rem_mask_flat.any():
            rem_correct += (preds_flat[rem_mask_flat] == labels_flat[rem_mask_flat]).sum().item()
            rem_total += rem_mask_flat.sum().item()
        
        wake_mask_flat = (labels_flat == 4)
        if wake_mask_flat.any():
            wake_correct += (preds_flat[wake_mask_flat] == labels_flat[wake_mask_flat]).sum().item()
            wake_total += wake_mask_flat.sum().item()
        
        all_preds.extend(preds_flat.cpu().numpy())
        all_labels.extend(labels_flat.cpu().numpy())
        
        # 更新进度条
        if batch_idx % 10 == 0:
            rem_acc = rem_correct / rem_total if rem_total > 0 else 0
            wake_acc = wake_correct / wake_total if wake_total > 0 else 0
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{total_correct/total_samples:.4f}',
                'REM_acc': f'{rem_acc:.3f}',
                'Wake_acc': f'{wake_acc:.3f}'
            })
    
    # 计算指标
    avg_loss = total_loss / len(train_loader)
    accuracy = total_correct / total_samples
    macro_f1 = f1_score(all_labels, all_preds, average='macro', zero_division=0)
    kappa = cohen_kappa_score(all_labels, all_preds)
    
    # 计算每类F1
    per_class_f1 = f1_score(all_labels, all_preds, average=None, zero_division=0)
    
    return avg_loss, accuracy, macro_f1, kappa, per_class_f1


def evaluate_epoch_level_v14(model, val_dataset, val_loader, device, seq_len):
    """Epoch级别评估"""
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=seq_len, n_classes=5)
    evaluator.total_epochs = val_dataset.total_epochs
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for data, labels in tqdm(val_loader, desc='Evaluating'):
            data = data.to(device)
            labels = labels.to(device)
            
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(val_dataset):
                    seq_info = val_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    return evaluator.evaluate()


def train_v14_fixed(config, device):
    """V14训练主函数 - REM/Wake专注修复版"""
    logging.info("\n" + "="*80)
    logging.info("🚀 开始训练 MAMBAFORMER V14 - REM/Wake专注版（完整数据）")
    logging.info(f"🎯 目标: ACC=87%, Kappa=0.8, MF1=80%")
    logging.info("🔍 重点: 改善REM和Wake分类")
    
    # 加载数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 🚨 关键修复：显式设置max_samples_per_file=None
    logging.info("📊 创建数据集（使用完整数据）...")
    train_dataset = SequenceSleepDataset(
        train_files, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None  # 使用所有数据！
    )
    val_dataset = SequenceSleepDataset(
        val_files, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None  # 使用所有数据！
    )
    test_dataset = SequenceSleepDataset(
        test_files, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None  # 使用所有数据！
    )
    
    logging.info(f"  训练集: {len(train_dataset)} 序列, {train_dataset.total_epochs} epochs")
    logging.info(f"  验证集: {len(val_dataset)} 序列, {val_dataset.total_epochs} epochs")
    logging.info(f"  测试集: {len(test_dataset)} 序列, {test_dataset.total_epochs} epochs")
    
    # 统计类别分布
    logging.info("\n📊 验证数据集类别分布...")
    val_labels = []
    for i in range(min(100, len(val_dataset))):
        _, labels = val_dataset[i]
        val_labels.extend(labels.numpy())
    
    unique, counts = np.unique(val_labels, return_counts=True)
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    for cls_id, count in zip(unique, counts):
        if cls_id < 5:
            pct = count / len(val_labels) * 100
            logging.info(f"  {class_names[cls_id]}: {pct:.2f}%")
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], shuffle=True,
        num_workers=config['num_workers'], pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], shuffle=False,
        num_workers=config['num_workers'], pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], shuffle=False,
        num_workers=config['num_workers'], pin_memory=True
    )
    
    # 创建模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logging.info(f"📊 模型参数: {total_params:,} (可训练: {trainable_params:,})")
    
    # REM/Wake专注损失函数
    class_weights = torch.tensor([3.0, 1.5, 1.0, 1.2, 5.0]).to(device)  # REM和Wake权重更高
    criterion = REMFocusedLoss(alpha=class_weights, gamma=2.5, rem_weight=3.0, wake_weight=5.0)
    temp_loss_fn = TemporalConsistencyLoss(weight=0.2)
    
    # 优化器和调度器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer,
        T_0=15,
        T_mult=2,
        eta_min=1e-6
    )
    
    # 规则后处理器
    rule_processor = RuleBasedPostProcessor()
    
    logging.info("\n📋 V14核心策略:")
    logging.info("  • 使用完整数据（修复）")
    logging.info("  • REM专注损失（3x权重）")
    logging.info("  • Wake专注损失（5x权重）")
    logging.info("  • REM数据增强")
    logging.info("  • 增强架构 (d_model=256)")
    
    # 训练循环
    best_val_metrics = {'macro_f1': 0, 'kappa': 0}
    best_model_state = None
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc, train_f1, train_kappa, per_class_f1 = train_epoch_v14(
            model, train_loader, criterion, temp_loss_fn,
            optimizer, device, epoch, config
        )
        
        # 验证
        val_metrics = evaluate_epoch_level_v14(
            model, val_dataset, val_loader, device, config['seq_len']
        )
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, "
                    f"F1: {train_f1:.4f}, Kappa: {train_kappa:.4f}")
        logging.info(f"训练类别F1 - REM: {per_class_f1[0]:.3f}, Wake: {per_class_f1[4]:.3f}")
        logging.info(f"验证 - Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}, "
                    f"Kappa: {val_metrics['kappa']:.4f}")
        
        # 记录每类F1
        if 'per_class_metrics' in val_metrics:
            class_f1_scores = []
            for cls_name in class_names:
                if cls_name in val_metrics['per_class_metrics']:
                    f1 = val_metrics['per_class_metrics'][cls_name]['f1']
                    class_f1_scores.append(f"{cls_name}: {f1:.3f}")
            logging.info(f"验证类别F1: {', '.join(class_f1_scores)}")
        
        logging.info(f"LR: {current_lr:.2e}")
        
        # 保存最佳模型
        if val_metrics['macro_f1'] > best_val_metrics['macro_f1']:
            best_val_metrics = val_metrics
            best_model_state = model.state_dict()
            patience_counter = 0
            
            torch.save(best_model_state, '../../checkpoints/v14_fixed.pth')
            logging.info(f"✅ 保存最佳模型 (F1: {val_metrics['macro_f1']:.4f})")
            
            # 检查是否达到目标
            if (val_metrics['accuracy'] >= 0.87 and 
                val_metrics['kappa'] >= 0.8 and 
                val_metrics['macro_f1'] >= 0.8):
                logging.info("🎉 达到目标性能！")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"⚠️ Early stopping at epoch {epoch}")
                break
    
    # 最终测试
    logging.info("\n" + "="*80)
    logging.info("📊 最终测试评估")
    
    model.load_state_dict(best_model_state)
    test_metrics = evaluate_epoch_level_v14(
        model, test_dataset, test_loader, device, config['seq_len']
    )
    
    logging.info(f"测试集性能:")
    logging.info(f"  Accuracy: {test_metrics['accuracy']:.4f}")
    logging.info(f"  Macro F1: {test_metrics['macro_f1']:.4f}")
    logging.info(f"  Kappa: {test_metrics['kappa']:.4f}")
    
    if 'per_class_metrics' in test_metrics:
        logging.info("测试集各类F1:")
        for cls_name in class_names:
            if cls_name in test_metrics['per_class_metrics']:
                f1 = test_metrics['per_class_metrics'][cls_name]['f1']
                logging.info(f"  {cls_name}: {f1:.4f}")
    
    # 保存结果 - 转换numpy数组为列表
    def convert_numpy(obj):
        """递归转换numpy数组为列表"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        else:
            return obj
    
    results = {
        'config': config,
        'best_val_metrics': convert_numpy(best_val_metrics),
        'test_metrics': convert_numpy(test_metrics),
        'data_info': {
            'train_epochs': train_dataset.total_epochs,
            'val_epochs': val_dataset.total_epochs,
            'test_epochs': test_dataset.total_epochs
        }
    }
    
    with open('../../configs/v14_fixed_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info("\n✅ 训练完成！")
    return test_metrics


def main():
    # 配置
    config = {
        # 模型配置
        'd_model': 256,  # 增强版
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        
        # 训练配置
        'batch_size': 32,
        'num_epochs': 100,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'patience': 15,
        'num_workers': 4,
        
        # 数据配置（关键）
        'max_samples_per_file': None,  # 使用所有数据！
    }
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️ 设备: {device}")
    
    # 设置日志
    setup_logging(config)
    
    # 训练
    train_v14_fixed(config, device)


if __name__ == "__main__":
    main()