"""
MAMBAFORMER训练脚本 - 基于AttnSleep成熟框架
利用已有的数据处理、K折交叉验证和训练系统
"""

import argparse
import collections
import numpy as np
import os
import sys
import json
import torch
import torch.nn as nn
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# 添加AttnSleep路径
sys.path.append('../AttnSleep')

# 导入AttnSleep框架组件
from data_loader.data_loaders import data_generator_np
import model.loss as module_loss
import model.metric as module_metric
from parse_config import ConfigParser
from trainer import Trainer
from utils.util import load_folds_data, calc_class_weight

# 导入我们的MAMBAFORMER模型
from attn_mambaformer import MAMBAFORMER, MultiChannelMAMBAFORMER, AttnMAMBAFORMER

def weights_init_normal(m):
    """权重初始化"""
    if type(m) == nn.Conv2d:
        torch.nn.init.normal_(m.weight.data, 0.0, 0.02)
    elif type(m) == nn.Conv1d:
        torch.nn.init.normal_(m.weight.data, 0.0, 0.02)
    elif type(m) == nn.BatchNorm1d:
        torch.nn.init.normal_(m.weight.data, 1.0, 0.02)
        torch.nn.init.constant_(m.bias.data, 0.0)
    elif type(m) == nn.Linear:
        torch.nn.init.normal_(m.weight.data, 0.0, 0.02)
        if m.bias is not None:
            torch.nn.init.constant_(m.bias.data, 0.0)

def get_mambaformer_model(model_type="single_channel"):
    """获取MAMBAFORMER模型"""
    if model_type == "single_channel":
        return MAMBAFORMER()
    elif model_type == "multi_channel":
        return MultiChannelMAMBAFORMER()
    else:
        raise ValueError(f"Unknown model type: {model_type}")

class MAMBAFORMERTrainer:
    """
    MAMBAFORMER训练器
    基于AttnSleep框架但进行了优化
    """
    def __init__(self, model, criterion, metrics, optimizer, config, 
                 data_loader, fold_id, valid_data_loader, class_weights, scheduler=None):
        self.model = model
        self.criterion = criterion
        self.metrics = metrics
        self.optimizer = optimizer
        self.config = config
        self.fold_id = fold_id
        self.scheduler = scheduler
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = self.model.to(self.device)
        
        self.data_loader = data_loader
        self.valid_data_loader = valid_data_loader
        self.class_weights = class_weights
        
        # 训练配置
        self.epochs = config['trainer']['epochs']
        self.save_period = config['trainer']['save_period']
        self.monitor = config['trainer']['monitor']
        self.early_stop = config['trainer'].get('early_stop', 10)
        
        # 监控指标
        self.monitor_mode = 'max' if 'max' in self.monitor else 'min'
        self.best_metric = float('-inf') if self.monitor_mode == 'max' else float('inf')
        self.early_stop_counter = 0
        
        # 日志和保存
        self.checkpoint_dir = f"checkpoints/fold_{fold_id}"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        self.train_history = {'loss': [], 'acc': [], 'f1': []}
        self.val_history = {'loss': [], 'acc': [], 'f1': []}
    
    def train(self):
        """主训练循环"""
        print(f"\n开始训练 Fold {self.fold_id + 1}")
        print(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"训练样本: {len(self.data_loader.dataset)}, 验证样本: {len(self.valid_data_loader.dataset)}")
        
        for epoch in range(self.epochs):
            # 训练阶段
            train_result = self.train_epoch()
            
            # 验证阶段
            val_result = self.validate_epoch()
            
            # 学习率调度
            if self.scheduler:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_result['loss'])
                else:
                    self.scheduler.step()
            
            # 记录历史
            self.train_history['loss'].append(train_result['loss'])
            self.train_history['acc'].append(train_result['accuracy'])
            self.train_history['f1'].append(train_result.get('macro_f1', 0))
            
            self.val_history['loss'].append(val_result['loss'])
            self.val_history['acc'].append(val_result['accuracy'])
            self.val_history['f1'].append(val_result.get('macro_f1', 0))
            
            # 打印进度
            current_lr = self.optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{self.epochs}")
            print(f"  Train: Loss={train_result['loss']:.4f}, Acc={train_result['accuracy']:.4f}, F1={train_result.get('macro_f1', 0):.4f}")
            print(f"  Val:   Loss={val_result['loss']:.4f}, Acc={val_result['accuracy']:.4f}, F1={val_result.get('macro_f1', 0):.4f}")
            print(f"  LR: {current_lr:.2e}")
            
            # 检查是否是最佳模型
            current_metric = val_result['macro_f1'] if 'macro_f1' in val_result else val_result['accuracy']
            is_best = False
            
            if self.monitor_mode == 'max':
                if current_metric > self.best_metric:
                    self.best_metric = current_metric
                    is_best = True
                    self.early_stop_counter = 0
                else:
                    self.early_stop_counter += 1
            else:
                if current_metric < self.best_metric:
                    self.best_metric = current_metric
                    is_best = True
                    self.early_stop_counter = 0
                else:
                    self.early_stop_counter += 1
            
            # 保存检查点
            if is_best:
                self.save_checkpoint(epoch, is_best=True)
                print(f"  💾 新的最佳模型! {self.monitor}={current_metric:.4f}")
            
            if (epoch + 1) % self.save_period == 0:
                self.save_checkpoint(epoch)
            
            # 早停检查
            if self.early_stop_counter >= self.early_stop:
                print(f"  🛑 早停: {self.monitor}已{self.early_stop}轮未改善")
                break
        
        # 训练完成，加载最佳模型进行最终评估
        best_checkpoint = os.path.join(self.checkpoint_dir, 'model_best.pth')
        if os.path.exists(best_checkpoint):
            checkpoint = torch.load(best_checkpoint)
            self.model.load_state_dict(checkpoint['state_dict'])
            print(f"已加载最佳模型 (epoch {checkpoint['epoch']}, {self.monitor}={checkpoint['monitor_metric']:.4f})")
        
        # 最终验证
        final_result = self.validate_epoch(detailed=True)
        
        return {
            'best_metric': self.best_metric,
            'final_result': final_result,
            'train_history': self.train_history,
            'val_history': self.val_history
        }
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, (data, target) in enumerate(tqdm(self.data_loader, desc="Training", leave=False)):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            output = self.model(data)
            
            # 处理多任务输出
            if isinstance(output, dict):
                stage_logits = output['stage_logits']
            else:
                stage_logits = output
            
            # 计算损失
            loss = self.criterion(stage_logits, target)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 记录
            total_loss += loss.item()
            
            preds = torch.argmax(stage_logits, dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(target.cpu().numpy())
        
        # 计算指标
        avg_loss = total_loss / len(self.data_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        macro_f1 = f1_score(all_labels, all_preds, average='macro')
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'macro_f1': macro_f1
        }
    
    def validate_epoch(self, detailed=False):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for data, target in tqdm(self.valid_data_loader, desc="Validating", leave=False):
                data, target = data.to(self.device), target.to(self.device)
                
                # 前向传播
                output = self.model(data)
                
                # 处理多任务输出
                if isinstance(output, dict):
                    stage_logits = output['stage_logits']
                else:
                    stage_logits = output
                
                # 计算损失
                loss = self.criterion(stage_logits, target)
                total_loss += loss.item()
                
                # 预测
                preds = torch.argmax(stage_logits, dim=1)
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(target.cpu().numpy())
        
        # 计算指标
        avg_loss = total_loss / len(self.valid_data_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        macro_f1 = f1_score(all_labels, all_preds, average='macro')
        
        result = {
            'loss': avg_loss,
            'accuracy': accuracy,
            'macro_f1': macro_f1
        }
        
        if detailed:
            # 详细的分类报告和混淆矩阵
            class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
            result['classification_report'] = classification_report(
                all_labels, all_preds, target_names=class_names, digits=4
            )
            result['confusion_matrix'] = confusion_matrix(all_labels, all_preds)
            result['predictions'] = all_preds
            result['labels'] = all_labels
        
        return result
    
    def save_checkpoint(self, epoch, is_best=False):
        """保存检查点"""
        state = {
            'epoch': epoch + 1,
            'state_dict': self.model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'monitor_metric': self.best_metric,
            'config': self.config,
            'fold_id': self.fold_id
        }
        
        if self.scheduler:
            state['scheduler'] = self.scheduler.state_dict()
        
        # 保存常规检查点
        filename = os.path.join(self.checkpoint_dir, f'checkpoint_epoch_{epoch+1}.pth')
        torch.save(state, filename)
        
        # 保存最佳模型
        if is_best:
            best_filename = os.path.join(self.checkpoint_dir, 'model_best.pth')
            torch.save(state, best_filename)

def main(config, fold_id):
    """主训练函数"""
    batch_size = config["data_loader"]["args"]["batch_size"]
    
    # 设置随机种子
    torch.manual_seed(123)
    np.random.seed(123)
    torch.backends.cudnn.deterministic = False
    torch.backends.cudnn.benchmark = False
    
    print(f"开始训练 Fold {fold_id + 1}")
    
    # 构建模型
    model = get_mambaformer_model("single_channel")
    model.apply(weights_init_normal)
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数
    criterion = getattr(module_loss, config['loss'])
    
    # 优化器
    trainable_params = filter(lambda p: p.requires_grad, model.parameters())
    optimizer = torch.optim.AdamW(
        trainable_params, 
        lr=config['optimizer']['args']['lr'],
        weight_decay=config['optimizer']['args']['weight_decay'],
        eps=config['optimizer']['args']['eps']
    )
    
    # 数据加载
    data_loader, valid_data_loader, data_count = data_generator_np(
        folds_data[fold_id][0], folds_data[fold_id][1], batch_size
    )
    
    # 类别权重
    weights_for_each_class = calc_class_weight(data_count)
    print(f"类别分布: {data_count}")
    print(f"类别权重: {weights_for_each_class}")
    
    # 学习率调度器
    scheduler = None
    if 'lr_scheduler' in config:
        if config['lr_scheduler']['type'] == 'OneCycleLR':
            scheduler = torch.optim.lr_scheduler.OneCycleLR(
                optimizer,
                max_lr=config['lr_scheduler']['args']['max_lr'],
                steps_per_epoch=len(data_loader),
                epochs=config['trainer']['epochs'],
                pct_start=config['lr_scheduler']['args']['pct_start'],
                div_factor=config['lr_scheduler']['args']['div_factor'],
                final_div_factor=config['lr_scheduler']['args']['final_div_factor']
            )
        elif config['lr_scheduler']['type'] == 'ReduceLROnPlateau':
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode='min', factor=0.5, patience=5, verbose=True
            )
    
    # 创建训练器
    trainer = MAMBAFORMERTrainer(
        model=model,
        criterion=criterion,
        metrics=None,
        optimizer=optimizer,
        config=config,
        data_loader=data_loader,
        fold_id=fold_id,
        valid_data_loader=valid_data_loader,
        class_weights=weights_for_each_class,
        scheduler=scheduler
    )
    
    # 开始训练
    result = trainer.train()
    
    return result

if __name__ == '__main__':
    # 命令行参数
    args = argparse.ArgumentParser(description='MAMBAFORMER Training with AttnSleep Framework')
    args.add_argument('-c', '--config', default="mambaformer_config.json", type=str,
                      help='config file path (default: mambaformer_config.json)')
    args.add_argument('-f', '--fold_id', type=int, default=0,
                      help='fold_id for cross-validation')
    args.add_argument('-da', '--np_data_dir', type=str, 
                      default="/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20",
                      help='Directory containing numpy files')
    args.add_argument('--all_folds', action='store_true',
                      help='Train on all folds')
    
    parsed_args = args.parse_args()
    
    # 加载配置
    with open(parsed_args.config, 'r') as f:
        config = json.load(f)
    
    # 加载数据折叠
    print(f"加载数据从: {parsed_args.np_data_dir}")
    folds_data = load_folds_data(parsed_args.np_data_dir, config["data_loader"]["args"]["num_folds"])
    print(f"成功加载 {config['data_loader']['args']['num_folds']} 折数据")
    
    if parsed_args.all_folds:
        # 运行所有折叠
        all_results = []
        
        for fold_id in range(config["data_loader"]["args"]["num_folds"]):
            print(f"\n{'='*60}")
            print(f"开始训练 Fold {fold_id + 1}/{config['data_loader']['args']['num_folds']}")
            print(f"{'='*60}")
            
            try:
                result = main(config, fold_id)
                result['fold_id'] = fold_id
                all_results.append(result)
                
                print(f"\nFold {fold_id + 1} 完成:")
                print(f"  最佳指标: {result['best_metric']:.4f}")
                print(f"  最终准确率: {result['final_result']['accuracy']:.4f}")
                print(f"  最终F1分数: {result['final_result']['macro_f1']:.4f}")
                
            except Exception as e:
                print(f"Fold {fold_id + 1} 训练失败: {e}")
                continue
        
        # 汇总结果
        if all_results:
            print(f"\n{'='*60}")
            print("所有Fold训练完成 - 结果汇总")
            print(f"{'='*60}")
            
            accuracies = [r['final_result']['accuracy'] for r in all_results]
            f1_scores = [r['final_result']['macro_f1'] for r in all_results]
            
            print(f"平均准确率: {np.mean(accuracies):.4f} ± {np.std(accuracies):.4f}")
            print(f"平均F1分数: {np.mean(f1_scores):.4f} ± {np.std(f1_scores):.4f}")
            
            print(f"\n各Fold详细结果:")
            for i, result in enumerate(all_results):
                print(f"Fold {i+1}: Acc={result['final_result']['accuracy']:.4f}, "
                      f"F1={result['final_result']['macro_f1']:.4f}")
            
            # 保存汇总结果
            summary_result = {
                'config': config,
                'fold_results': all_results,
                'summary_statistics': {
                    'mean_accuracy': float(np.mean(accuracies)),
                    'std_accuracy': float(np.std(accuracies)),
                    'mean_f1_score': float(np.mean(f1_scores)),
                    'std_f1_score': float(np.std(f1_scores)),
                    'num_folds_completed': len(all_results),
                    'architecture': 'wICA-ICLabel-CrossModal-MAMBA'
                }
            }
            
            with open('mambaformer_all_folds_results.json', 'w') as f:
                json.dump(summary_result, f, indent=2, default=str)
            
            print(f"\n完整结果已保存至: mambaformer_all_folds_results.json")
    else:
        # 运行单个折叠
        result = main(config, parsed_args.fold_id)
        print(f"\n训练完成!")
        print(f"最佳指标: {result['best_metric']:.4f}")
        print(f"最终准确率: {result['final_result']['accuracy']:.4f}")
        print(f"最终F1分数: {result['final_result']['macro_f1']:.4f}")