"""
完整MAMBAFORMER训练脚本 - 带K折交叉验证
使用wICA-ICLabel-CrossModal-MAMBA完整架构
"""

import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import h5py
from sklearn.model_selection import KFold
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import time
from tqdm import tqdm
import json
import warnings
warnings.filterwarnings('ignore')

# 导入完整MAMBAFORMER模型
from mambaformer_model import create_mambaformer

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class SleepDataset(Dataset):
    """睡眠数据集 - 支持受试者级别的数据划分"""
    def __init__(self, data_file, subject_ids, transform=None):
        self.data_file = data_file
        self.subject_ids = subject_ids
        self.transform = transform
        
        # 加载指定受试者的数据
        self.data = []
        self.labels = []
        
        with h5py.File(data_file, 'r') as hf:
            for subj_id in subject_ids:
                key = f'subject_{subj_id:02d}'
                if key in hf:
                    subj_data = hf[key]['data'][:]
                    subj_labels = hf[key]['labels'][:]
                    
                    self.data.append(subj_data)
                    self.labels.append(subj_labels)
            
            # 合并所有受试者的数据
            if self.data:
                self.data = np.concatenate(self.data, axis=0)
                self.labels = np.concatenate(self.labels, axis=0)
            else:
                self.data = np.array([])
                self.labels = np.array([])
        
        print(f"加载了 {len(subject_ids)} 个受试者的 {len(self.data)} 个epochs")
        if len(self.labels) > 0:
            print(f"标签分布: {np.bincount(self.labels)}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        # 获取数据 (channels, seq_len)
        x = self.data[idx]  # (3, 3000)
        y = self.labels[idx]
        
        # 转换为tensor
        x = torch.FloatTensor(x)
        y = torch.LongTensor([y]).squeeze()
        
        if self.transform:
            x = self.transform(x)
        
        return x, y

class MultiTaskLoss(nn.Module):
    """
    多任务损失函数
    结合主任务（睡眠分期）和辅助任务（REM/纺锤波/慢波检测）
    """
    
    def __init__(self, alpha=None, gamma=2.0, aux_weight=0.2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.aux_weight = aux_weight
        
    def focal_loss(self, inputs, targets):
        """Focal Loss for main task"""
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if self.alpha.device != focal_loss.device:
                self.alpha = self.alpha.to(focal_loss.device)
            focal_loss = self.alpha[targets] * focal_loss
        
        return focal_loss.mean()
    
    def forward(self, outputs, targets):
        """
        计算多任务损失
        
        Args:
            outputs: 模型输出字典
            targets: 目标标签
        """
        # 主任务：睡眠分期
        main_loss = self.focal_loss(outputs['stage_logits'], targets)
        
        # 辅助任务损失（基于目标标签推导）
        aux_loss = 0.0
        
        if 'rem_scores' in outputs:
            # REM检测：标签4是REM
            rem_targets = (targets == 4).float()
            rem_loss = F.binary_cross_entropy(outputs['rem_scores'], rem_targets)
            aux_loss += rem_loss
            
        if 'sws_scores' in outputs:
            # 慢波睡眠检测：标签3是N3
            sws_targets = (targets == 3).float()
            sws_loss = F.binary_cross_entropy(outputs['sws_scores'], sws_targets)
            aux_loss += sws_loss
            
        # 总损失
        total_loss = main_loss + self.aux_weight * aux_loss
        
        return total_loss, main_loss, aux_loss

def train_epoch(model, dataloader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    total_main_loss = 0
    total_aux_loss = 0
    all_preds = []
    all_labels = []
    
    for batch_x, batch_y in tqdm(dataloader, desc="Training", leave=False):
        batch_x = batch_x.to(device)
        batch_y = batch_y.to(device)
        
        # 前向传播
        outputs = model(batch_x, preprocessed=True)
        
        # 计算损失
        loss, main_loss, aux_loss = criterion(outputs, batch_y)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 记录
        total_loss += loss.item()
        total_main_loss += main_loss.item()
        total_aux_loss += aux_loss.item() if isinstance(aux_loss, torch.Tensor) else aux_loss
        
        _, preds = torch.max(outputs['stage_logits'], 1)
        all_preds.extend(preds.cpu().numpy())
        all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    avg_main_loss = total_main_loss / len(dataloader)
    avg_aux_loss = total_aux_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, avg_main_loss, avg_aux_loss, accuracy

def evaluate(model, dataloader, criterion, device):
    """评估模型"""
    model.eval()
    total_loss = 0
    total_main_loss = 0
    total_aux_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_x, batch_y in tqdm(dataloader, desc="Evaluating", leave=False):
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            outputs = model(batch_x, preprocessed=True)
            loss, main_loss, aux_loss = criterion(outputs, batch_y)
            
            total_loss += loss.item()
            total_main_loss += main_loss.item()
            total_aux_loss += aux_loss.item() if isinstance(aux_loss, torch.Tensor) else aux_loss
            
            _, preds = torch.max(outputs['stage_logits'], 1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(batch_y.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    avg_main_loss = total_main_loss / len(dataloader)
    avg_aux_loss = total_aux_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    return avg_loss, avg_main_loss, avg_aux_loss, accuracy, f1, all_preds, all_labels

def main():
    # 超参数配置
    config = {
        'batch_size': 32,
        'learning_rate': 5e-5,  # 更小的学习率，适应复杂模型
        'num_epochs': 25,
        'd_model': 128,
        'n_crossmodal_layers': 2,
        'n_mamba_layers': 4,
        'n_heads': 8,
        'dropout': 0.15,
        'use_bidirectional_mamba': True,
        'sleep_stage_conditioning': True,
        'k_folds': 5,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu'
    }
    
    print("=" * 60)
    print("完整MAMBAFORMER训练 - K折交叉验证")
    print("=" * 60)
    print(f"使用设备: {config['device']}")
    print(f"模型配置: d_model={config['d_model']}, CrossModal层={config['n_crossmodal_layers']}, MAMBA层={config['n_mamba_layers']}")
    
    # 加载数据信息
    data_file = 'sleep_edf_20_conservative_preprocessed.h5'
    
    if not os.path.exists(data_file):
        print(f"错误：找不到数据文件 {data_file}")
        print("请先运行conservative_preprocessing.py")
        return
    
    # 获取所有受试者ID
    with h5py.File(data_file, 'r') as hf:
        all_subjects = []
        for key in hf.keys():
            if key.startswith('subject_'):
                subj_id = int(key.split('_')[1])
                all_subjects.append(subj_id)
    
    all_subjects = sorted(all_subjects)
    print(f"总受试者数: {len(all_subjects)}")
    print(f"受试者ID: {all_subjects}")
    
    # 计算类别权重
    with h5py.File(data_file, 'r') as hf:
        all_labels = []
        for subj_id in all_subjects:
            key = f'subject_{subj_id:02d}'
            if key in hf:
                labels = hf[key]['labels'][:]
                all_labels.extend(labels)
        
        class_counts = np.bincount(all_labels)
        class_weights = 1.0 / (class_counts + 1)
        class_weights = class_weights / class_weights.sum() * len(class_weights)
        alpha = torch.FloatTensor(class_weights)
        
        print(f"类别分布: {class_counts}")
        print(f"类别权重: {class_weights}")
    
    # K折交叉验证
    kfold = KFold(n_splits=config['k_folds'], shuffle=True, random_state=42)
    fold_results = []
    
    for fold, (train_idx, test_idx) in enumerate(kfold.split(all_subjects)):
        print(f"\n{'='*50}")
        print(f"Fold {fold + 1}/{config['k_folds']}")
        print(f"{'='*50}")
        
        # 获取训练和测试受试者
        train_subjects = [all_subjects[i] for i in train_idx]
        test_subjects = [all_subjects[i] for i in test_idx]
        
        # 从训练集中分出验证集
        val_size = max(1, len(train_subjects) // 5)
        val_subjects = train_subjects[-val_size:]
        train_subjects = train_subjects[:-val_size]
        
        print(f"训练受试者 ({len(train_subjects)}): {train_subjects}")
        print(f"验证受试者 ({len(val_subjects)}): {val_subjects}")
        print(f"测试受试者 ({len(test_subjects)}): {test_subjects}")
        
        # 创建数据集
        train_dataset = SleepDataset(data_file, train_subjects)
        val_dataset = SleepDataset(data_file, val_subjects)
        test_dataset = SleepDataset(data_file, test_subjects)
        
        # 检查数据集大小
        if len(train_dataset) < 50:
            print(f"训练数据太少 ({len(train_dataset)})，跳过此fold")
            continue
            
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], 
                                shuffle=True, num_workers=4, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], 
                              shuffle=False, num_workers=4, pin_memory=True)
        test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], 
                               shuffle=False, num_workers=4, pin_memory=True)
        
        # 创建完整MAMBAFORMER模型
        model = create_mambaformer(
            input_channels=3,
            num_classes=5,
            d_model=config['d_model'],
            n_crossmodal_layers=config['n_crossmodal_layers'],
            n_mamba_layers=config['n_mamba_layers'],
            n_heads=config['n_heads'],
            dropout=config['dropout'],
            use_bidirectional_mamba=config['use_bidirectional_mamba'],
            sleep_stage_conditioning=config['sleep_stage_conditioning'],
            use_preprocessing=True
        ).to(config['device'])
        
        # 统计参数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数量: {total_params:,}")
        
        # 多任务损失函数和优化器
        criterion = MultiTaskLoss(alpha=alpha.to(config['device']), gamma=2.0, aux_weight=0.2)
        
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=config['learning_rate'],
            weight_decay=1e-4,
            eps=1e-8
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=config['learning_rate'] * 5,
            steps_per_epoch=len(train_loader),
            epochs=config['num_epochs'],
            pct_start=0.1
        )
        
        # 训练
        best_val_f1 = 0
        best_model_state = None
        patience = 0
        max_patience = 5
        
        print(f"开始训练...")
        for epoch in range(config['num_epochs']):
            print(f"\nEpoch {epoch + 1}/{config['num_epochs']}")
            
            # 训练
            train_loss, train_main_loss, train_aux_loss, train_acc = train_epoch(
                model, train_loader, criterion, optimizer, config['device']
            )
            
            # 验证
            val_loss, val_main_loss, val_aux_loss, val_acc, val_f1, _, _ = evaluate(
                model, val_loader, criterion, config['device']
            )
            
            # 更新学习率
            scheduler.step()
            current_lr = scheduler.get_last_lr()[0]
            
            print(f"Train: Loss={train_loss:.4f} (Main={train_main_loss:.4f}, Aux={train_aux_loss:.4f}), Acc={train_acc:.4f}")
            print(f"Val: Loss={val_loss:.4f} (Main={val_main_loss:.4f}, Aux={val_aux_loss:.4f}), Acc={val_acc:.4f}, F1={val_f1:.4f}")
            print(f"LR: {current_lr:.2e}")
            
            # 保存最佳模型
            if val_f1 > best_val_f1:
                best_val_f1 = val_f1
                best_model_state = model.state_dict().copy()
                patience = 0
                print(f"💾 新的最佳F1: {best_val_f1:.4f}")
            else:
                patience += 1
                if patience >= max_patience:
                    print(f"早停：验证F1已{patience}轮未改善")
                    break
        
        # 在测试集上评估最佳模型
        model.load_state_dict(best_model_state)
        test_loss, test_main_loss, test_aux_loss, test_acc, test_f1, test_preds, test_labels = evaluate(
            model, test_loader, criterion, config['device']
        )
        
        print(f"\n🎯 最终结果:")
        print(f"最佳验证F1: {best_val_f1:.4f}")
        print(f"测试准确率: {test_acc:.4f}")
        print(f"测试F1分数: {test_f1:.4f}")
        
        # 混淆矩阵和分类报告
        cm = confusion_matrix(test_labels, test_preds)
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        report = classification_report(test_labels, test_preds, 
                                     target_names=class_names, digits=4)
        
        print("\n混淆矩阵:")
        print(cm)
        print("\n分类报告:")
        print(report)
        
        # 保存fold结果
        fold_results.append({
            'fold': fold + 1,
            'test_acc': test_acc,
            'test_f1': test_f1,
            'val_f1': best_val_f1,
            'confusion_matrix': cm.tolist(),
            'classification_report': report,
            'model_params': total_params
        })
        
        # 保存模型
        torch.save({
            'model_state_dict': best_model_state,
            'config': config,
            'fold': fold + 1,
            'test_acc': test_acc,
            'test_f1': test_f1,
            'val_f1': best_val_f1
        }, f'mambaformer_fold{fold+1}.pth')
        
        print(f"✅ Fold {fold+1} 完成，模型已保存")
    
    # 汇总K折交叉验证结果
    if fold_results:
        print(f"\n{'='*60}")
        print(f"🏆 完整MAMBAFORMER K折交叉验证结果汇总")
        print(f"{'='*60}")
        
        test_accs = [r['test_acc'] for r in fold_results]
        test_f1s = [r['test_f1'] for r in fold_results]
        
        print(f"平均测试准确率: {np.mean(test_accs):.4f} ± {np.std(test_accs):.4f}")
        print(f"平均测试F1分数: {np.mean(test_f1s):.4f} ± {np.std(test_f1s):.4f}")
        
        print(f"\n各Fold详细结果:")
        for i, result in enumerate(fold_results):
            print(f"Fold {i+1}: 测试准确率={result['test_acc']:.4f}, 测试F1={result['test_f1']:.4f}")
        
        # 保存完整结果
        with open('mambaformer_kfold_results.json', 'w') as f:
            json.dump({
                'config': config,
                'fold_results': fold_results,
                'summary': {
                    'mean_test_acc': float(np.mean(test_accs)),
                    'std_test_acc': float(np.std(test_accs)),
                    'mean_test_f1': float(np.mean(test_f1s)),
                    'std_test_f1': float(np.std(test_f1s)),
                    'architecture': 'wICA-ICLabel-CrossModal-MAMBA'
                }
            }, f, indent=2)
        
        print(f"\n🎉 完整MAMBAFORMER训练完成！")
        print(f"📊 结果已保存至 mambaformer_kfold_results.json")
        
        # 与之前结果的对比
        prev_acc = 0.5830  # 简化版本的结果
        improvement = np.mean(test_accs) - prev_acc
        
        print(f"\n📈 性能对比:")
        print(f"简化Transformer: {prev_acc:.4f}")
        print(f"完整MAMBAFORMER: {np.mean(test_accs):.4f}")
        print(f"性能提升: {improvement:+.4f} ({improvement/prev_acc*100:+.1f}%)")
        
    else:
        print("❌ 没有成功完成任何fold，请检查数据和配置")

if __name__ == "__main__":
    main()