#!/usr/bin/env python3
"""
完整Sleep-EDF-20数据集的训练配置
"""

import os
import json
import argparse
from datetime import datetime

def load_data_split(processed_dir):
    """加载数据分割信息"""
    split_file = os.path.join(processed_dir, 'data_split.json')
    if os.path.exists(split_file):
        with open(split_file, 'r') as f:
            return json.load(f)
    return None

def generate_config(processed_dir='./processed_data_full', model_type='mambaformer'):
    """生成训练配置"""
    
    # 加载数据分割
    split_info = load_data_split(processed_dir)
    if split_info is None:
        print("❌ 未找到数据分割信息，请先运行预处理")
        return None
    
    # 基础配置
    config = {
        # 数据配置
        'data_path': processed_dir,
        'train_subjects': split_info['train_subjects'],
        'val_subjects': split_info['val_subjects'],
        'test_subjects': split_info['test_subjects'],
        
        # 训练配置
        'batch_size': 64,  # 可根据GPU内存调整
        'epochs': 100,     # 完整数据集可以训练更多epochs
        'lr': 1e-4,
        'weight_decay': 1e-5,
        'patience': 10,    # 早停耐心值
        
        # 模型配置
        'model_type': model_type,
        'd_model': 64,
        'num_layers': 4,
        'nhead': 8,
        'dropout': 0.1,
        'use_progressive': True,
        'use_adaptive_mambaformer': True,
        
        # 优化配置
        'use_amp': True,  # 混合精度训练
        'gradient_clip': 1.0,
        'scheduler': 'cosine',  # 学习率调度器
        'warmup_epochs': 5,
        
        # 实验配置
        'experiment_name': f'{model_type}_sleepEDF_full',
        'log_dir': './log',
        'save_dir': './checkpoints',
        'save_freq': 5,  # 每5个epoch保存一次
        
        # 数据增强
        'augmentation': {
            'time_shift': 0.1,  # 时间偏移
            'noise_level': 0.05,  # 噪声水平
            'scale_range': (0.9, 1.1)  # 幅度缩放
        },
        
        # 评估配置
        'eval_metrics': ['accuracy', 'f1_macro', 'cohen_kappa', 'confusion_matrix'],
        'class_names': ['Wake', 'N1', 'N2', 'N3', 'REM'],
        
        # 训练技巧
        'label_smoothing': 0.1,
        'mixup_alpha': 0.2,
        
        # 数据集信息
        'dataset_info': {
            'name': 'Sleep-EDF-20',
            'n_train': split_info['n_train'],
            'n_val': split_info['n_val'],
            'n_test': split_info['n_test'],
            'n_classes': 5,
            'seq_len': 3000,  # 30s * 100Hz
            'sampling_rate': 100
        }
    }
    
    # 保存配置
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    config_file = f'config_{model_type}_full_{timestamp}.json'
    
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ 配置文件已生成: {config_file}")
    print(f"\n📊 数据集信息:")
    print(f"  训练集: {config['dataset_info']['n_train']} subjects")
    print(f"  验证集: {config['dataset_info']['n_val']} subjects")
    print(f"  测试集: {config['dataset_info']['n_test']} subjects")
    
    return config, config_file

def generate_baseline_config(processed_dir='./processed_data_full'):
    """生成基线模型（Cross-Modal Transformer）的配置"""
    config = generate_config(processed_dir, model_type='crossmodal')
    if config:
        # 调整基线模型特定配置
        config[0]['use_progressive'] = False
        config[0]['use_adaptive_mambaformer'] = False
        config[0]['model_type'] = 'crossmodal_baseline'
        return config

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成训练配置')
    parser.add_argument('--processed_dir', type=str, 
                        default='./processed_data_full',
                        help='预处理数据目录')
    parser.add_argument('--model_type', type=str, 
                        default='mambaformer',
                        choices=['mambaformer', 'crossmodal'],
                        help='模型类型')
    
    args = parser.parse_args()
    
    if args.model_type == 'crossmodal':
        generate_baseline_config(args.processed_dir)
    else:
        generate_config(args.processed_dir, args.model_type)