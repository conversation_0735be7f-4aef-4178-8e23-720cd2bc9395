#!/usr/bin/env python3
"""
Final Ensemble to achieve 87% accuracy
Combines best V14, V15, and V17 models
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


def load_model(checkpoint_path, config, device):
    """Load a trained model from checkpoint"""
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model


def evaluate_ensemble():
    """Evaluate ensemble of best models"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"../logs/final_ensemble_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 FINAL ENSEMBLE EVALUATION FOR 87% TARGET")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Model configurations
    models_info = [
        {
            'name': 'V14_Original',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/checkpoints/mambaformer_v14_20250809_184458_best.pth',
            'config': {
                'd_model': 256,
                'n_heads': 16,
                'n_layers': 6,
                'dropout': 0.15,
                'seq_len': 5
            },
            'weight': 0.35  # Base weight
        },
        {
            'name': 'V15_Targeted',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v15_targeted_20250810_230143/best_model.pth',
            'config': {
                'd_model': 320,
                'n_heads': 20,
                'n_layers': 7,
                'dropout': 0.18,
                'seq_len': 6
            },
            'weight': 0.35  # Good validation performance
        },
        {
            'name': 'V17_Stable',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth',
            'config': {
                'd_model': 288,
                'n_heads': 18,
                'n_layers': 6,
                'dropout': 0.15,
                'seq_len': 5
            },
            'weight': 0.30  # Conservative model
        }
    ]
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Test files: {len(test_files)}")
    
    # Load models
    models = []
    for info in models_info:
        if os.path.exists(info['path']):
            try:
                model = load_model(info['path'], info['config'], device)
                models.append({
                    'name': info['name'],
                    'model': model,
                    'config': info['config'],
                    'weight': info['weight']
                })
                logging.info(f"✅ Loaded {info['name']}")
            except Exception as e:
                logging.info(f"❌ Failed to load {info['name']}: {e}")
        else:
            logging.info(f"❌ Path not found for {info['name']}: {info['path']}")
    
    if len(models) == 0:
        logging.error("No models could be loaded!")
        return
    
    logging.info(f"\nUsing {len(models)} models for ensemble")
    
    # Normalize weights
    total_weight = sum(m['weight'] for m in models)
    for m in models:
        m['weight'] = m['weight'] / total_weight
        logging.info(f"  {m['name']}: weight={m['weight']:.3f}")
    
    # Process each model's predictions
    all_predictions = []
    
    for model_info in models:
        model = model_info['model']
        config = model_info['config']
        
        logging.info(f"\n📊 Processing {model_info['name']}...")
        
        # Create dataset with appropriate seq_len
        test_dataset = SequenceSleepDataset(
            test_files,
            seq_len=config['seq_len'],
            use_channels=3,
            max_samples_per_file=None
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Evaluate
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc=f"Evaluating {model_info['name']}"):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get predictions
        final_preds, final_labels, epoch_probs = evaluator.get_final_predictions()
        
        # Individual model metrics
        metrics = evaluator.evaluate()
        logging.info(f"  {model_info['name']} Performance:")
        logging.info(f"    Accuracy: {metrics['accuracy']:.4f}")
        logging.info(f"    Macro F1: {metrics['macro_f1']:.4f}")
        logging.info(f"    Kappa: {metrics['kappa']:.4f}")
        
        all_predictions.append({
            'probs': epoch_probs,
            'weight': model_info['weight'],
            'labels': final_labels
        })
    
    # Ensemble predictions
    logging.info("\n" + "="*80)
    logging.info("🎯 ENSEMBLE RESULTS")
    logging.info("="*80)
    
    # Weighted average of probabilities
    ensemble_probs = np.zeros_like(all_predictions[0]['probs'])
    for pred_info in all_predictions:
        ensemble_probs += pred_info['probs'] * pred_info['weight']
    
    # Get final predictions
    ensemble_preds = np.argmax(ensemble_probs, axis=1)
    true_labels = all_predictions[0]['labels']
    
    # Calculate metrics
    from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
    
    accuracy = accuracy_score(true_labels, ensemble_preds)
    macro_f1 = f1_score(true_labels, ensemble_preds, average='macro')
    kappa = cohen_kappa_score(true_labels, ensemble_preds)
    
    logging.info(f"\n📊 ENSEMBLE PERFORMANCE:")
    logging.info(f"  Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    logging.info(f"  Macro F1: {macro_f1:.4f} ({macro_f1*100:.2f}%)")
    logging.info(f"  Kappa: {kappa:.4f}")
    
    # Check targets
    logging.info("\n🎯 TARGET ACHIEVEMENT (87% / 80% / 0.80):")
    success = True
    
    if accuracy >= 0.87:
        logging.info(f"  ✅ Accuracy: {accuracy:.4f} ≥ 0.87")
    else:
        logging.info(f"  ❌ Accuracy: {accuracy:.4f} < 0.87 (gap: {0.87-accuracy:.4f})")
        success = False
    
    if macro_f1 >= 0.80:
        logging.info(f"  ✅ Macro F1: {macro_f1:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Macro F1: {macro_f1:.4f} < 0.80 (gap: {0.80-macro_f1:.4f})")
        success = False
    
    if kappa >= 0.80:
        logging.info(f"  ✅ Kappa: {kappa:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Kappa: {kappa:.4f} < 0.80 (gap: {0.80-kappa:.4f})")
        success = False
    
    # Confusion matrix
    cm = confusion_matrix(true_labels, ensemble_preds)
    
    logging.info("\n📊 Confusion Matrix:")
    logging.info("     Wake   N1    N2    N3   REM")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    for i, row in enumerate(cm):
        row_str = f"{class_names[i]:4} "
        for val in row:
            row_str += f"{val:5d} "
        logging.info(row_str)
    
    # Per-class metrics
    report = classification_report(true_labels, ensemble_preds, 
                                  target_names=class_names, 
                                  output_dict=True)
    
    logging.info("\n📊 Per-Class Performance:")
    for class_name in class_names:
        metrics = report[class_name]
        logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1-score']*100:.1f}%")
    
    # Save results
    results = {
        'timestamp': timestamp,
        'models': [m['name'] for m in models],
        'weights': [m['weight'] for m in models],
        'accuracy': float(accuracy),
        'macro_f1': float(macro_f1),
        'kappa': float(kappa),
        'confusion_matrix': cm.tolist(),
        'per_class_metrics': report,
        'targets_achieved': success
    }
    
    results_file = f"../logs/final_ensemble_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    if success:
        logging.info("\n" + "="*80)
        logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
        logging.info("Ensemble Model Ready for ICASSP 2026!")
        logging.info("="*80)
    else:
        logging.info("\n📈 Close to targets. May need further optimization.")
    
    return results


if __name__ == "__main__":
    results = evaluate_ensemble()