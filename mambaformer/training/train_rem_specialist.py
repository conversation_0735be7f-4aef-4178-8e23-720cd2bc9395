#!/usr/bin/env python3
"""
🎯 REM SPECIALIST MODEL TRAINING
Focus on improving REM stage classification (currently ~26% F1)
Strategy: Train specialized model for REM detection with heavy class weighting
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class REMFocusedLoss(nn.Module):
    """Custom loss heavily weighted for REM classification"""
    def __init__(self, rem_weight=15.0, device='cuda'):
        super().__init__()
        # REM is class 4
        weights = torch.tensor([1.0, 2.0, 1.0, 1.0, rem_weight]).to(device)
        self.ce_loss = nn.CrossEntropyLoss(weight=weights)
        
    def forward(self, outputs, targets):
        # Handle sequential output
        if outputs.dim() == 3:
            # Reshape for loss computation
            batch_size, seq_len, n_classes = outputs.shape
            outputs = outputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        loss = self.ce_loss(outputs, targets)
        
        # Add focal loss component for hard examples
        with torch.no_grad():
            probs = F.softmax(outputs, dim=1)
            correct_probs = probs.gather(1, targets.unsqueeze(1)).squeeze()
            focal_weight = (1 - correct_probs) ** 2
        
        # Extra penalty for REM misclassification
        rem_mask = (targets == 4).float()
        rem_penalty = rem_mask * focal_weight * 2.0
        
        total_loss = loss + rem_penalty.mean()
        
        return total_loss


def augment_rem_samples(dataset, rem_augment_factor=3):
    """Augment REM samples in the dataset"""
    rem_indices = []
    other_indices = []
    
    for idx in range(len(dataset)):
        _, label = dataset[idx]
        # Check if sequence contains significant REM
        if label.dim() > 0:
            rem_count = (label == 4).sum().item()
            if rem_count >= 3:  # At least 3 REM epochs in sequence
                rem_indices.append(idx)
            else:
                other_indices.append(idx)
        else:
            if label == 4:
                rem_indices.append(idx)
            else:
                other_indices.append(idx)
    
    # Create augmented indices
    augmented_indices = other_indices + rem_indices * rem_augment_factor
    np.random.shuffle(augmented_indices)
    
    return augmented_indices


def create_rem_focused_dataloader(dataset, batch_size=16, is_training=True):
    """Create dataloader with REM oversampling"""
    if is_training:
        augmented_indices = augment_rem_samples(dataset)
        sampler = torch.utils.data.SubsetRandomSampler(augmented_indices)
        return DataLoader(dataset, batch_size=batch_size, sampler=sampler, 
                         num_workers=4, pin_memory=True)
    else:
        return DataLoader(dataset, batch_size=batch_size, shuffle=False,
                         num_workers=4, pin_memory=True)


def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    rem_correct = 0
    rem_total = 0
    
    pbar = tqdm(train_loader, desc='Training')
    for data, target in pbar:
        data, target = data.to(device), target.to(device)
        
        # Handle sequence labels
        if target.dim() > 1:
            target_eval = target[:, target.shape[1]//2]
        else:
            target_eval = target
        
        optimizer.zero_grad()
        output, _ = model(data)
        
        loss = criterion(output, target)
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)
        optimizer.step()
        
        total_loss += loss.item()
        
        # Calculate accuracy
        if output.dim() == 3:
            pred = output[:, output.shape[1]//2, :].argmax(dim=1)
        else:
            pred = output.argmax(dim=1)
        
        correct += pred.eq(target_eval).sum().item()
        total += target_eval.size(0)
        
        # Track REM accuracy
        rem_mask = target_eval == 4
        if rem_mask.any():
            rem_correct += pred[rem_mask].eq(target_eval[rem_mask]).sum().item()
            rem_total += rem_mask.sum().item()
        
        pbar.set_postfix({
            'loss': loss.item(),
            'acc': 100. * correct / total,
            'rem_acc': 100. * rem_correct / rem_total if rem_total > 0 else 0
        })
    
    return total_loss / len(train_loader), correct / total, rem_correct / rem_total if rem_total > 0 else 0


def evaluate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)
            
            # Handle sequence labels
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            output, _ = model(data)
            
            if output.dim() == 3:
                pred = output[:, output.shape[1]//2, :].argmax(dim=1)
            else:
                pred = output.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)
    
    # Calculate metrics
    # Get unique labels in the data
    unique_labels = np.unique(np.concatenate([all_targets, all_preds]))
    
    cm = confusion_matrix(all_targets, all_preds, labels=[0, 1, 2, 3, 4])
    
    # Only include target names for labels that exist
    target_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    existing_target_names = [target_names[i] for i in unique_labels if i < len(target_names)]
    
    report = classification_report(all_targets, all_preds, 
                                 labels=unique_labels,
                                 target_names=existing_target_names,
                                 output_dict=True, 
                                 zero_division=0)
    
    accuracy = (all_preds == all_targets).mean()
    
    # Check if REM class exists in the report
    if 'REM' in report:
        rem_f1 = report['REM']['f1-score']
        rem_precision = report['REM']['precision']
        rem_recall = report['REM']['recall']
    else:
        # If REM class not predicted, return 0 scores
        rem_f1 = 0.0
        rem_precision = 0.0
        rem_recall = 0.0
    
    return accuracy, rem_f1, rem_precision, rem_recall, cm


def main():
    # Configuration
    config = {
        'd_model': 384,
        'n_heads': 24,
        'n_layers': 10,
        'dropout': 0.3,
        'seq_len': 7,
        'batch_size': 16,
        'learning_rate': 1e-5,
        'num_epochs': 30,
        'patience': 10,
        'gradient_clip': 0.5,
        'weight_decay': 0.05,
        'rem_augment_factor': 5,
        'rem_loss_weight': 20.0
    }
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'../logs/rem_specialist_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 REM SPECIALIST MODEL TRAINING")
    logging.info("Current REM F1: ~26% → Target: >60%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Log configuration
    logging.info("\n📋 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # Prepare file paths
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        is_training=True
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        is_training=False
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        is_training=False
    )
    
    # Create REM-focused dataloaders
    train_loader = create_rem_focused_dataloader(
        train_dataset, 
        batch_size=config['batch_size'],
        is_training=True
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Load pretrained weights if available
    pretrained_path = '../logs/v22_deep_20250811_030913/best_model.pth'
    if os.path.exists(pretrained_path):
        checkpoint = torch.load(pretrained_path, map_location=device, weights_only=False)
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint
        
        # Try to load matching weights
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in state_dict.items() 
                          if k in model_dict and model_dict[k].shape == v.shape}
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)
        logging.info(f"✅ Loaded {len(pretrained_dict)}/{len(model_dict)} layers from pretrained")
    
    logging.info(f"\nModel Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Loss and optimizer
    criterion = REMFocusedLoss(rem_weight=config['rem_loss_weight'], device=device)
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    scheduler = CosineAnnealingWarmRestarts(
        optimizer, T_0=5, T_mult=2, eta_min=1e-7
    )
    
    # Training loop
    logging.info("\n🏋️ Starting REM-Focused Training...")
    logging.info("="*80)
    
    best_rem_f1 = 0
    patience_counter = 0
    
    for epoch in range(config['num_epochs']):
        # Train
        train_loss, train_acc, train_rem_acc = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        scheduler.step()
        
        # Validate
        val_acc, val_rem_f1, val_rem_prec, val_rem_rec, val_cm = evaluate(
            model, val_loader, device
        )
        
        logging.info(f"\nEpoch {epoch+1}:")
        logging.info(f"  Loss: {train_loss:.4f}")
        logging.info(f"  Train Acc: {train_acc:.4f}")
        logging.info(f"  Train REM Acc: {train_rem_acc:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f}")
        logging.info(f"  REM F1: {val_rem_f1:.4f} {'⭐' if val_rem_f1 > best_rem_f1 else ''}")
        logging.info(f"  REM Precision: {val_rem_prec:.4f}")
        logging.info(f"  REM Recall: {val_rem_rec:.4f}")
        
        # Save best model
        if val_rem_f1 > best_rem_f1:
            best_rem_f1 = val_rem_f1
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'rem_f1': val_rem_f1,
                'val_acc': val_acc,
                'config': config
            }, os.path.join(log_dir, 'best_rem_model.pth'))
            
            logging.info(f"  💾 Saved best REM model (F1: {val_rem_f1:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⛔ Early stopping at epoch {epoch+1}")
                break
    
    # Test evaluation
    logging.info("\n" + "="*80)
    logging.info("📊TEST EVALUATION")
    logging.info("="*80)
    
    # Load best model
    checkpoint = torch.load(os.path.join(log_dir, 'best_rem_model.pth'), 
                          map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    test_acc, test_rem_f1, test_rem_prec, test_rem_rec, test_cm = evaluate(
        model, test_loader, device
    )
    
    logging.info("\n🎯 TEST RESULTS:")
    logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"  REM F1: {test_rem_f1:.4f}")
    logging.info(f"  REM Precision: {test_rem_prec:.4f}")
    logging.info(f"  REM Recall: {test_rem_rec:.4f}")
    
    # Save results
    results = {
        'test_accuracy': test_acc,
        'rem_f1': test_rem_f1,
        'rem_precision': test_rem_prec,
        'rem_recall': test_rem_rec,
        'confusion_matrix': test_cm.tolist(),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {log_dir}/results.json")
    
if __name__ == '__main__':
    main()