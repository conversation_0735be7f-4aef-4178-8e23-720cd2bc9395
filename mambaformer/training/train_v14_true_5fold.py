#!/usr/bin/env python3
"""
真正的5折交叉验证训练 - V14 MAMBAFORMER
每个fold独立训练，确保无数据泄露
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import ReduceLROnPlateau
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.model_selection import KFold
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import time

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2, SequentialFocalLoss, TemporalConsistencyLoss
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class REMFocusedLoss(nn.Module):
    """REM and Wake focused loss for better class balance"""
    def __init__(self, alpha=None, gamma=2.0, rem_weight=3.0, wake_weight=5.0):
        super().__init__()
        self.focal_loss = SequentialFocalLoss(alpha=alpha, gamma=gamma)
        self.rem_weight = rem_weight
        self.wake_weight = wake_weight
        
    def forward(self, predictions, targets):
        focal = self.focal_loss(predictions, targets)
        
        # Apply extra weight to REM (class 4) and Wake (class 0)
        batch_size, seq_len = targets.shape
        weight_mask = torch.ones_like(targets, dtype=torch.float32)
        weight_mask[targets == 4] = self.rem_weight  # REM
        weight_mask[targets == 0] = self.wake_weight  # Wake
        
        weighted_focal = focal * weight_mask.mean()
        return weighted_focal


def get_subject_files():
    """获取所有被试的文件映射"""
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 所有被试及其对应文件
    subject_files = {
        '00': ['SC4001E0.npz', 'SC4002E0.npz'],
        '01': ['SC4011E0.npz', 'SC4012E0.npz'],
        '02': ['SC4021E0.npz', 'SC4022E0.npz'],
        '03': ['SC4031E0.npz', 'SC4032E0.npz'],
        '04': ['SC4041E0.npz', 'SC4042E0.npz'],
        '05': ['SC4051E0.npz', 'SC4052E0.npz'],
        '06': ['SC4061E0.npz', 'SC4062E0.npz'],
        '07': ['SC4071E0.npz', 'SC4072E0.npz'],
        '08': ['SC4081E0.npz', 'SC4082E0.npz'],
        '09': ['SC4091E0.npz', 'SC4092E0.npz'],
        '10': ['SC4101E0.npz', 'SC4102E0.npz'],
        '11': ['SC4111E0.npz', 'SC4112E0.npz'],
        '12': ['SC4121E0.npz', 'SC4122E0.npz'],
        '13': ['SC4131E0.npz'],  # 只有一个文件
        '14': ['SC4141E0.npz', 'SC4142E0.npz'],
        '15': ['SC4151E0.npz', 'SC4152E0.npz'],
        '16': ['SC4161E0.npz', 'SC4162E0.npz'],
        '17': ['SC4171E0.npz', 'SC4172E0.npz'],
        '18': ['SC4181E0.npz', 'SC4182E0.npz'],
        '19': ['SC4191E0.npz', 'SC4192E0.npz']
    }
    
    # 转换为完整路径
    for subject in subject_files:
        subject_files[subject] = [os.path.join(data_dir, f) for f in subject_files[subject]]
    
    return subject_files


def train_fold(fold_idx, train_subjects, test_subjects, subject_files, config, device, log_dir):
    """训练单个fold - 完整的训练流程"""
    
    fold_start_time = time.time()
    
    logging.info(f"\n{'='*80}")
    logging.info(f"🔄 FOLD {fold_idx + 1}/5 - INDEPENDENT TRAINING")
    logging.info(f"{'='*80}")
    logging.info(f"Train subjects ({len(train_subjects)}): {train_subjects}")
    logging.info(f"Test subjects ({len(test_subjects)}): {test_subjects}")
    
    # 收集训练和测试文件
    train_files = []
    for subject in train_subjects:
        train_files.extend(subject_files[subject])
    
    test_files = []
    for subject in test_subjects:
        test_files.extend(subject_files[subject])
    
    logging.info(f"Train files: {len(train_files)}")
    logging.info(f"Test files: {len(test_files)}")
    
    # 进一步划分训练集为训练和验证（80/20）
    np.random.seed(42 + fold_idx)  # 确保可重复
    np.random.shuffle(train_subjects)
    val_split = int(len(train_subjects) * 0.2)
    val_subjects = train_subjects[:val_split]
    train_subjects_final = train_subjects[val_split:]
    
    train_files_final = []
    for subject in train_subjects_final:
        train_files_final.extend(subject_files[subject])
    
    val_files = []
    for subject in val_subjects:
        val_files.extend(subject_files[subject])
    
    logging.info(f"Final train subjects: {train_subjects_final}")
    logging.info(f"Validation subjects: {val_subjects}")
    
    # Create datasets
    train_dataset = SequenceSleepDataset(
        train_files_final, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None  # 使用所有数据
    )
    
    val_dataset = SequenceSleepDataset(
        val_files, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {train_dataset.total_epochs} epochs")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=config['num_workers'],
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config['num_workers'],
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config['num_workers'],
        pin_memory=True
    )
    
    # Initialize NEW model for this fold
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"✅ Created new model for fold {fold_idx + 1}")
    
    # Loss functions
    class_weights = torch.tensor([1.0, 2.0, 1.0, 1.5, 1.5]).to(device)
    criterion = REMFocusedLoss(alpha=class_weights, gamma=2.0, rem_weight=3.0, wake_weight=5.0)
    temp_loss_fn = TemporalConsistencyLoss(weight=0.2)
    
    # Optimizer and scheduler
    optimizer = optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=0.01)
    scheduler = ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=3)
    
    # Training loop
    best_val_f1 = 0
    patience_counter = 0
    best_model_state = None
    training_history = []
    
    for epoch in range(config['num_epochs']):
        epoch_start_time = time.time()
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Fold {fold_idx+1} Epoch {epoch+1}/{config["num_epochs"]}')
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs, _ = model(data)
            
            # Combined loss
            main_loss = criterion(outputs, labels)
            temp_loss = temp_loss_fn(outputs)
            loss = main_loss + temp_loss
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        epoch_time = time.time() - epoch_start_time
        
        logging.info(f"Fold {fold_idx+1} Epoch {epoch+1}: "
                    f"Loss={avg_train_loss:.4f}, "
                    f"Val_Acc={val_acc:.4f}, "
                    f"Val_F1={val_f1:.4f}, "
                    f"Val_Kappa={val_kappa:.4f}, "
                    f"Time={epoch_time:.1f}s")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa
        })
        
        # Learning rate scheduling
        scheduler.step(val_f1)
        
        # Early stopping and best model saving
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            # Save best model for this fold
            checkpoint_path = os.path.join(log_dir, f'fold_{fold_idx+1}_best.pth')
            torch.save({
                'fold': fold_idx + 1,
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }, checkpoint_path)
            logging.info(f"💾 Saved best model for fold {fold_idx+1}: F1={val_f1:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"⏹️ Early stopping triggered for fold {fold_idx+1}")
                break
    
    # Load best model for final test evaluation
    model.load_state_dict(best_model_state)
    logging.info(f"📊 Evaluating best model on test set...")
    
    # Test evaluation
    model.eval()
    test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    test_evaluator.total_epochs = test_dataset.total_epochs
    
    with torch.no_grad():
        batch_start_idx = 0
        for data, labels in tqdm(test_loader, desc='Testing', leave=False):
            data = data.to(device)
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                test_evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    # Get test metrics
    test_metrics = test_evaluator.evaluate()
    
    # Get confusion matrix
    final_preds, final_labels, _ = test_evaluator.get_final_predictions()
    cm = confusion_matrix(final_labels, final_preds)
    
    fold_time = time.time() - fold_start_time
    
    fold_results = {
        'fold': fold_idx + 1,
        'train_subjects': train_subjects_final,
        'val_subjects': val_subjects,
        'test_subjects': test_subjects,
        'test_accuracy': float(test_metrics['accuracy']),
        'test_macro_f1': float(test_metrics['macro_f1']),
        'test_kappa': float(test_metrics['kappa']),
        'best_val_f1': float(best_val_f1),
        'confusion_matrix': cm.tolist(),
        'per_class_metrics': {},
        'training_history': training_history,
        'training_time': fold_time
    }
    
    # Add per-class metrics
    for class_name, class_metrics in test_metrics['per_class_metrics'].items():
        fold_results['per_class_metrics'][class_name] = {
            'precision': float(class_metrics['precision']),
            'recall': float(class_metrics['recall']),
            'f1': float(class_metrics['f1']),
            'support': int(class_metrics['support'])
        }
    
    logging.info(f"\n📊 Fold {fold_idx + 1} Test Results:")
    logging.info(f"  Accuracy: {fold_results['test_accuracy']:.4f} ({fold_results['test_accuracy']*100:.2f}%)")
    logging.info(f"  Macro F1: {fold_results['test_macro_f1']:.4f} ({fold_results['test_macro_f1']*100:.2f}%)")
    logging.info(f"  Kappa: {fold_results['test_kappa']:.4f}")
    logging.info(f"  Training time: {fold_time/60:.1f} minutes")
    
    # Check targets
    targets_met = []
    if fold_results['test_accuracy'] >= 0.87:
        targets_met.append("Acc")
    if fold_results['test_macro_f1'] >= 0.80:
        targets_met.append("F1")
    if fold_results['test_kappa'] >= 0.80:
        targets_met.append("Kappa")
    
    if targets_met:
        logging.info(f"  ✅ Targets met: {', '.join(targets_met)}")
    
    return fold_results


def main():
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/true_5fold_cv_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V14 - TRUE 5-FOLD CROSS-VALIDATION")
    logging.info("="*80)
    logging.info("⚠️ Each fold trains an INDEPENDENT model from scratch")
    logging.info("⚠️ NO data leakage - strict subject-level splits")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Configuration
    config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4,
        'learning_rate': 1e-4,
        'num_epochs': 20,  # Reasonable number for good training
        'patience': 5
    }
    
    logging.info("\nConfiguration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Get all subject files
    subject_files = get_subject_files()
    subjects = list(subject_files.keys())
    logging.info(f"\nTotal subjects: {len(subjects)}")
    
    # 5-Fold Cross-Validation Setup
    kfold = KFold(n_splits=5, shuffle=True, random_state=42)
    
    all_fold_results = []
    total_start_time = time.time()
    
    # Train each fold independently
    for fold_idx, (train_idx, test_idx) in enumerate(kfold.split(subjects)):
        # Get train and test subjects
        train_subjects = [subjects[i] for i in train_idx]
        test_subjects = [subjects[i] for i in test_idx]
        
        # Train this fold
        fold_results = train_fold(
            fold_idx, 
            train_subjects, 
            test_subjects, 
            subject_files, 
            config, 
            device, 
            log_dir
        )
        
        all_fold_results.append(fold_results)
        
        # Save intermediate results after each fold
        intermediate_results = {
            'timestamp': timestamp,
            'config': config,
            'completed_folds': all_fold_results
        }
        
        intermediate_file = os.path.join(log_dir, f'results_after_fold_{fold_idx+1}.json')
        with open(intermediate_file, 'w') as f:
            json.dump(intermediate_results, f, indent=2)
    
    total_time = time.time() - total_start_time
    
    # Calculate average metrics across all folds
    avg_accuracy = np.mean([r['test_accuracy'] for r in all_fold_results])
    avg_f1 = np.mean([r['test_macro_f1'] for r in all_fold_results])
    avg_kappa = np.mean([r['test_kappa'] for r in all_fold_results])
    
    std_accuracy = np.std([r['test_accuracy'] for r in all_fold_results])
    std_f1 = np.std([r['test_macro_f1'] for r in all_fold_results])
    std_kappa = np.std([r['test_kappa'] for r in all_fold_results])
    
    # Log final results
    logging.info("\n" + "="*80)
    logging.info("📊 TRUE 5-FOLD CROSS-VALIDATION RESULTS")
    logging.info("="*80)
    
    logging.info("\nPer-Fold Test Results:")
    for i, fold_result in enumerate(all_fold_results):
        logging.info(f"Fold {i+1}: Acc={fold_result['test_accuracy']:.4f}, "
                    f"F1={fold_result['test_macro_f1']:.4f}, "
                    f"Kappa={fold_result['test_kappa']:.4f}")
    
    logging.info("\n📊 Average Metrics (Mean ± Std):")
    logging.info(f"  Accuracy: {avg_accuracy:.4f} ± {std_accuracy:.4f} ({avg_accuracy*100:.2f}% ± {std_accuracy*100:.2f}%)")
    logging.info(f"  Macro F1: {avg_f1:.4f} ± {std_f1:.4f} ({avg_f1*100:.2f}% ± {std_f1*100:.2f}%)")
    logging.info(f"  Kappa: {avg_kappa:.4f} ± {std_kappa:.4f}")
    
    # Confidence intervals (95%)
    logging.info("\n📈 95% Confidence Intervals:")
    ci_mult = 1.96  # for 95% CI
    logging.info(f"  Accuracy: [{avg_accuracy - ci_mult*std_accuracy:.4f}, {avg_accuracy + ci_mult*std_accuracy:.4f}]")
    logging.info(f"  Macro F1: [{avg_f1 - ci_mult*std_f1:.4f}, {avg_f1 + ci_mult*std_f1:.4f}]")
    logging.info(f"  Kappa: [{avg_kappa - ci_mult*std_kappa:.4f}, {avg_kappa + ci_mult*std_kappa:.4f}]")
    
    # Check against targets
    logging.info("\n🎯 Target Achievement (Based on Average):")
    if avg_accuracy >= 0.87:
        logging.info(f"  ✅ Accuracy: {avg_accuracy:.4f} ≥ 0.87")
    else:
        logging.info(f"  ❌ Accuracy: {avg_accuracy:.4f} < 0.87 (gap: {0.87-avg_accuracy:.4f})")
    
    if avg_f1 >= 0.80:
        logging.info(f"  ✅ Macro F1: {avg_f1:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Macro F1: {avg_f1:.4f} < 0.80 (gap: {0.80-avg_f1:.4f})")
    
    if avg_kappa >= 0.80:
        logging.info(f"  ✅ Kappa: {avg_kappa:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Kappa: {avg_kappa:.4f} < 0.80 (gap: {0.80-avg_kappa:.4f})")
    
    logging.info(f"\n⏱️ Total training time: {total_time/3600:.1f} hours")
    
    # Save final results
    final_results = {
        'timestamp': timestamp,
        'config': config,
        'fold_results': all_fold_results,
        'average_metrics': {
            'accuracy': {'mean': float(avg_accuracy), 'std': float(std_accuracy)},
            'macro_f1': {'mean': float(avg_f1), 'std': float(std_f1)},
            'kappa': {'mean': float(avg_kappa), 'std': float(std_kappa)}
        },
        'confidence_intervals_95': {
            'accuracy': {
                'lower': float(avg_accuracy - ci_mult*std_accuracy),
                'upper': float(avg_accuracy + ci_mult*std_accuracy)
            },
            'macro_f1': {
                'lower': float(avg_f1 - ci_mult*std_f1),
                'upper': float(avg_f1 + ci_mult*std_f1)
            },
            'kappa': {
                'lower': float(avg_kappa - ci_mult*std_kappa),
                'upper': float(avg_kappa + ci_mult*std_kappa)
            }
        },
        'targets_achieved': {
            'accuracy': avg_accuracy >= 0.87,
            'macro_f1': avg_f1 >= 0.80,
            'kappa': avg_kappa >= 0.80
        },
        'total_training_time_hours': total_time/3600
    }
    
    results_file = os.path.join(log_dir, 'final_cv_results.json')
    with open(results_file, 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    # Create summary visualization
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Plot 1: Metrics across folds
    metrics = ['Accuracy', 'Macro F1', 'Kappa']
    fold_metrics = [
        [r['test_accuracy'] for r in all_fold_results],
        [r['test_macro_f1'] for r in all_fold_results],
        [r['test_kappa'] for r in all_fold_results]
    ]
    targets = [0.87, 0.80, 0.80]
    
    for ax, metric_name, metric_values, target in zip(axes[0], metrics, fold_metrics, targets):
        x = range(1, 6)
        bars = ax.bar(x, metric_values, color='skyblue', edgecolor='navy', alpha=0.7)
        
        # Color bars based on target achievement
        for i, (bar, val) in enumerate(zip(bars, metric_values)):
            if val >= target:
                bar.set_color('lightgreen')
            else:
                bar.set_color('lightcoral')
        
        ax.axhline(y=np.mean(metric_values), color='red', linestyle='--', 
                  label=f'Mean: {np.mean(metric_values):.4f}', linewidth=2)
        ax.axhline(y=target, color='green', linestyle=':', 
                  label=f'Target: {target}', linewidth=2)
        
        # Add value labels on bars
        for i, v in enumerate(metric_values):
            ax.text(i + 1, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        ax.set_xlabel('Fold')
        ax.set_ylabel(metric_name)
        ax.set_title(f'{metric_name} Across Folds')
        ax.set_xticks(x)
        ax.set_ylim([min(metric_values) - 0.05, max(max(metric_values), target) + 0.05])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # Plot 2: Training history for each fold
    for i, fold_result in enumerate(all_fold_results):
        ax = axes[1, i] if i < 3 else None
        if ax and 'training_history' in fold_result:
            history = fold_result['training_history']
            epochs = [h['epoch'] for h in history]
            val_f1 = [h['val_f1'] for h in history]
            
            ax.plot(epochs, val_f1, marker='o', label=f'Fold {i+1}')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Validation F1')
            ax.set_title(f'Fold {i+1} Training')
            ax.grid(True, alpha=0.3)
            ax.legend()
    
    plt.suptitle('TRUE 5-Fold Cross-Validation Results', fontsize=16, y=1.02)
    plt.tight_layout()
    plot_file = os.path.join(log_dir, 'cv_results.png')
    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    logging.info(f"📊 Plot saved to {plot_file}")
    logging.info("\n✅ TRUE 5-Fold Cross-Validation Complete!")
    logging.info("🎯 This is academically rigorous with NO data leakage")
    
    return final_results


if __name__ == "__main__":
    main()