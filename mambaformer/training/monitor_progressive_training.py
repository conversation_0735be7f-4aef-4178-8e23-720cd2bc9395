#!/usr/bin/env python3
"""监控渐进式训练进度"""

import os
import time
import subprocess
from datetime import datetime

def check_training_status():
    """检查训练进程状态"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        processes = result.stdout
        
        training_processes = []
        for line in processes.split('\n'):
            if 'train_v14_progressive' in line and 'python' in line:
                parts = line.split()
                if len(parts) > 10:
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    cmd = ' '.join(parts[10:])
                    if 'step1' in cmd:
                        step = 'Step 1 (Baseline)'
                    elif 'step2' in cmd:
                        step = 'Step 2 (Progressive)'
                    elif 'step3' in cmd:
                        step = 'Step 3 (Calibration)'
                    else:
                        step = 'Unknown'
                    
                    training_processes.append({
                        'pid': pid,
                        'cpu': cpu,
                        'mem': mem,
                        'step': step
                    })
        
        return training_processes
    except Exception as e:
        print(f"Error checking processes: {e}")
        return []

def get_latest_log_info(log_file):
    """获取最新的日志信息"""
    if not os.path.exists(log_file):
        return None
    
    try:
        # 获取最后50行
        result = subprocess.run(['tail', '-n', '50', log_file], 
                              capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')
        
        # 查找关键信息
        latest_info = {}
        for line in reversed(lines):
            if 'Epoch' in line and 'Test' in line and 'Acc=' in line:
                # 提取准确率
                if 'Acc=' in line:
                    acc_str = line.split('Acc=')[1].split(',')[0].strip()
                    try:
                        latest_info['test_acc'] = float(acc_str)
                    except:
                        pass
                
                # 提取epoch
                if 'Epoch' in line:
                    epoch_str = line.split('Epoch')[1].split('/')[0].strip()
                    try:
                        latest_info['epoch'] = int(epoch_str)
                    except:
                        pass
                
                if latest_info:
                    return latest_info
        
        # 查找训练进度条
        for line in reversed(lines):
            if 'Training:' in line and '%' in line:
                # 提取进度
                try:
                    percent_str = line.split('%')[0].split()[-1]
                    latest_info['progress'] = int(percent_str)
                    return latest_info
                except:
                    pass
        
        return None
    except Exception as e:
        print(f"Error reading log: {e}")
        return None

def main():
    """主监控循环"""
    print("="*60)
    print("渐进式训练监控器")
    print("="*60)
    
    log_files = {
        'Step 1': 'v14_step1_training.log',
        'Step 2': 'v14_step2_training.log',
        'Step 3': 'v14_step3_training.log'
    }
    
    while True:
        os.system('clear')
        
        print(f"\n{'='*60}")
        print(f"V14 Progressive Training Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
        # 检查运行的进程
        processes = check_training_status()
        
        if processes:
            print("🔄 运行中的训练进程:")
            print("-"*50)
            for proc in processes:
                print(f"  {proc['step']}")
                print(f"    PID: {proc['pid']}, CPU: {proc['cpu']}%, MEM: {proc['mem']}%")
        else:
            print("⚠️  没有检测到训练进程")
        
        print("\n" + "="*60)
        print("📊 训练进度:")
        print("-"*50)
        
        # 检查各个步骤的日志
        for step_name, log_file in log_files.items():
            info = get_latest_log_info(log_file)
            
            if info:
                if 'test_acc' in info:
                    acc_percent = info['test_acc'] * 100
                    status = "✅" if acc_percent >= 87 else "🔄"
                    print(f"\n{step_name}: {status}")
                    print(f"  Epoch: {info.get('epoch', '?')}")
                    print(f"  Test Acc: {acc_percent:.2f}%")
                    
                    if acc_percent >= 87:
                        print(f"  🎉 已达到87%目标!")
                    else:
                        gap = 87 - acc_percent
                        print(f"  Gap to 87%: {gap:.2f}%")
                elif 'progress' in info:
                    print(f"\n{step_name}: 🔄 训练中")
                    print(f"  Progress: {info['progress']}%")
            else:
                if os.path.exists(log_file):
                    print(f"\n{step_name}: ⏸️  等待中")
                else:
                    print(f"\n{step_name}: ❌ 未开始")
        
        print("\n" + "="*60)
        print("提示: 按 Ctrl+C 退出监控")
        
        # 等待30秒后刷新
        time.sleep(30)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n监控已停止")