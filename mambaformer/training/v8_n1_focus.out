2025-08-12 02:18:31,256 - INFO - ================================================================================
2025-08-12 02:18:31,256 - INFO - 🎯 V8 N1-Focus - Optimizing N1 Classification
2025-08-12 02:18:31,257 - INFO - ================================================================================
2025-08-12 02:18:31,257 - INFO - Configuration: {
  "d_model": 224,
  "n_heads": 14,
  "n_layers": 6,
  "dropout": 0.12,
  "seq_len": 5,
  "batch_size": 32,
  "learning_rate": 0.00015,
  "weight_decay": 3e-05,
  "num_epochs": 60,
  "patience": 15,
  "n1_oversample": 2.0
}
2025-08-12 02:18:31,425 - INFO - Device: cuda
2025-08-12 02:18:31,426 - INFO - Loading datasets...
2025-08-12 02:18:32,912 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 02:18:32,913 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 02:18:34,063 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 02:18:34,063 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 02:18:34,736 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 02:18:34,737 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 02:18:34,737 - INFO - Dataset sizes: Train=27003 (augmented), Val=4931, Test=11955
2025-08-12 02:18:35,095 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=3,860,591, d_model=224, n_heads=14, n_layers=6
2025-08-12 02:18:35,302 - INFO - Model parameters: 3,938,838
2025-08-12 02:18:36,672 - INFO - Starting V8 N1-Focus training...

Training:   0%|          | 0/844 [00:00<?, ?it/s]
Training:   0%|          | 0/844 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_n1_focus.py", line 585, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_n1_focus.py", line 502, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_n1_focus.py", line 303, in train_epoch
    final_logits, n1_detection, n1_refined = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_n1_focus.py", line 266, in forward
    main_logits = self.main_classifier(features)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/container.py", line 240, in forward
    input = module(input)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x5 and 224x112)
