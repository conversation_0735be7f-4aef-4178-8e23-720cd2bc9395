"""
MAMBAFORMER训练脚本
与原始Cross-Modal Transformer对比实验
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
import os
import json
from pathlib import Path
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import time
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet
from models.epoch_cmt import Epoch_Cross_Transformer_Network
from datasets.sleep_edf import SleepEDF_MultiChan_Dataset, get_dataset

def parse_args():
    parser = argparse.ArgumentParser('MAMBAFORMER Sleep Stage Classification')
    
    # 数据相关
    parser.add_argument('--data_path', type=str, default='./processed_data_test',
                       help='预处理数据路径')
    parser.add_argument('--train_data_list', type=str, default='1',
                       help='训练数据列表')
    parser.add_argument('--val_data_list', type=str, default='2', 
                       help='验证数据列表')
    parser.add_argument('--batch_size', type=int, default=64, 
                       help='批大小')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载器进程数')
    
    # 模型相关
    parser.add_argument('--model_type', type=str, default='Epoch',
                       help='模型类型: Epoch/Seq')
    parser.add_argument('--d_model', type=int, default=64,
                       help='模型维度')
    parser.add_argument('--num_layers', type=int, default=4, 
                       help='MambaFormer层数')
    parser.add_argument('--nhead', type=int, default=8,
                       help='注意力头数')
    parser.add_argument('--window_size', type=int, default=50,
                       help='CNN窗口大小')
    
    # 训练相关
    parser.add_argument('--epochs', type=int, default=50,
                       help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                       help='权重衰减')
    parser.add_argument('--patience', type=int, default=10,
                       help='早停耐心值')
    
    # 实验相关
    parser.add_argument('--run_baseline', action='store_true',
                       help='运行原始Cross-Modal Transformer基准')
    parser.add_argument('--use_progressive', action='store_true', default=True,
                       help='使用渐进式分类器')
    parser.add_argument('--use_wica', action='store_true',
                       help='使用wICA预处理')
    parser.add_argument('--save_dir', type=str, default='./results',
                       help='结果保存目录')
    
    # GPU设置
    parser.add_argument('--device', type=str, default='auto',
                       help='设备选择: auto/cpu/cuda:0/cuda:1')
    
    return parser.parse_args()

def setup_device(device_str):
    """设置计算设备"""
    if device_str == 'auto':
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
            print(f"✓ 使用GPU: {torch.cuda.get_device_name(0)}")
        else:
            device = torch.device('cpu')
            print("✓ 使用CPU")
    else:
        device = torch.device(device_str)
        if device.type == 'cuda':
            print(f"✓ 使用指定GPU: {torch.cuda.get_device_name(device)}")
        else:
            print("✓ 使用CPU")
    
    return device

def create_data_loaders(args, device):
    """创建数据加载器"""
    print("📊 加载数据...")
    
    # 转换数据列表格式
    args.train_data_list = [args.train_data_list]  # 包装为列表
    args.val_data_list = [args.val_data_list]
    
    # 使用原始的数据加载函数
    train_loader, val_loader, test_loader = get_dataset(device, args, only_val=False)
    
    print(f"✓ 训练集: {len(train_loader.dataset)} 样本")
    print(f"✓ 验证集: {len(val_loader.dataset)} 样本") 
    print(f"✓ 测试集: {len(test_loader.dataset)} 样本")
    
    return train_loader, val_loader, test_loader

def create_mambaformer_model(args, device):
    """创建MAMBAFORMER模型"""
    model = MambaFormerSleepNet(
        d_model=args.d_model,
        num_mambaformer_layers=args.num_layers,
        nhead=args.nhead,
        window_size=args.window_size,
        use_wica_online=False,  # 数据已预处理
        use_progressive=args.use_progressive,
        use_adaptive_mambaformer=True,
        dropout=0.1
    )
    
    model = model.to(device)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"✓ MAMBAFORMER模型参数: {total_params:,} (可训练: {trainable_params:,})")
    
    return model

def create_baseline_model(args, device):
    """创建原始Cross-Modal Transformer模型"""
    model = Epoch_Cross_Transformer_Network(
        d_model=args.d_model,
        dim_feedforward=args.d_model * 4,
        window_size=args.window_size
    )
    
    model = model.to(device)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"✓ Cross-Modal Transformer参数: {total_params:,} (可训练: {trainable_params:,})")
    
    return model

def train_epoch_mambaformer(model, train_loader, optimizer, device, epoch):
    """MAMBAFORMER训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} [Train]", leave=False)
    
    for batch_idx, (eeg_data, eog_data, labels, _, _, _, _) in enumerate(pbar):
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device) 
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(eeg_data, eog_data, stage="both")
        
        # 计算损失
        losses = model.compute_loss(outputs, labels, stage="both")
        loss = losses['total_loss']
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        
        # 获取预测结果
        if 'fine_probs' in outputs:
            pred = outputs['fine_probs'].argmax(dim=1)
        elif 'logits' in outputs:
            pred = outputs['logits'].argmax(dim=1)
        else:
            pred = outputs['fine_logits'].argmax(dim=1)
            
        correct += (pred == labels).sum().item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    return total_loss / len(train_loader), correct / total

def train_epoch_baseline(model, train_loader, criterion, optimizer, device, epoch):
    """原始模型训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} [Train]", leave=False)
    
    for batch_idx, (eeg_data, eog_data, labels, _, _, _, _) in enumerate(pbar):
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs, _, _ = model(eeg_data, eog_data, finetune=True)
        
        # 计算损失
        loss = criterion(outputs, labels)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        pred = outputs.argmax(dim=1)
        correct += (pred == labels).sum().item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    return total_loss / len(train_loader), correct / total

def validate_mambaformer(model, val_loader, device):
    """MAMBAFORMER验证"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg_data, eog_data, labels, _, _, _, _ in val_loader:
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(eeg_data, eog_data, stage="fine")
            
            # 计算损失
            losses = model.compute_loss(outputs, labels, stage="fine")
            total_loss += losses['total_loss'].item()
            
            # 获取预测
            if 'fine_probs' in outputs:
                pred = outputs['fine_probs'].argmax(dim=1)
            elif 'logits' in outputs:
                pred = outputs['logits'].argmax(dim=1)
            else:
                pred = outputs['fine_logits'].argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def validate_baseline(model, val_loader, criterion, device):
    """原始模型验证"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg_data, eog_data, labels, _, _, _, _ in val_loader:
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs, _, _ = model(eeg_data, eog_data, finetune=True)
            
            # 计算损失
            loss = criterion(outputs, labels)
            total_loss += loss.item()
            
            # 获取预测
            pred = outputs.argmax(dim=1)
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def train_model(model, train_loader, val_loader, args, device, model_name):
    """训练模型"""
    print(f"\n🚀 开始训练 {model_name}")
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
    
    # 损失函数（仅用于基准模型）
    if 'Cross-Modal' in model_name:
        criterion = nn.CrossEntropyLoss()
    else:
        criterion = None
    
    # 训练历史
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': [],
        'best_val_acc': 0, 'best_epoch': 0
    }
    
    # 早停
    patience_counter = 0
    best_val_loss = float('inf')
    
    start_time = time.time()
    
    for epoch in range(args.epochs):
        # 训练
        if 'MAMBAFORMER' in model_name:
            train_loss, train_acc = train_epoch_mambaformer(model, train_loader, optimizer, device, epoch)
        else:
            train_loss, train_acc = train_epoch_baseline(model, train_loader, criterion, optimizer, device, epoch)
        
        # 验证
        if 'MAMBAFORMER' in model_name:
            val_loss, val_acc, val_preds, val_labels = validate_mambaformer(model, val_loader, device)
        else:
            val_loss, val_acc, val_preds, val_labels = validate_baseline(model, val_loader, criterion, device)
        
        # 更新历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 打印进度
        print(f"Epoch {epoch+1:3d}/{args.epochs}: "
              f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
              f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 保存最佳模型
        if val_acc > history['best_val_acc']:
            history['best_val_acc'] = val_acc
            history['best_epoch'] = epoch
            patience_counter = 0
            
            # 保存模型
            model_path = os.path.join(args.save_dir, f"{model_name.lower().replace(' ', '_')}_best.pth")
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'val_acc': val_acc,
                'val_loss': val_loss,
                'history': history
            }, model_path)
            
        else:
            patience_counter += 1
            if patience_counter >= args.patience:
                print(f"🛑 早停在epoch {epoch+1}, 最佳验证准确率: {history['best_val_acc']:.4f}")
                break
    
    training_time = time.time() - start_time
    print(f"✓ {model_name} 训练完成，用时: {training_time:.2f}s")
    print(f"✓ 最佳验证准确率: {history['best_val_acc']:.4f} (Epoch {history['best_epoch']+1})")
    
    return history

def evaluate_model(model, test_loader, device, model_name):
    """评估模型"""
    print(f"\n📊 评估 {model_name}")
    
    if 'MAMBAFORMER' in model_name:
        test_loss, test_acc, test_preds, test_labels = validate_mambaformer(model, test_loader, device)
    else:
        criterion = nn.CrossEntropyLoss()
        test_loss, test_acc, test_preds, test_labels = validate_baseline(model, test_loader, criterion, device)
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, target_names=class_names, digits=4)
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    
    print(f"✓ 测试准确率: {test_acc:.4f}")
    print(f"✓ 测试损失: {test_loss:.4f}")
    print("\n分类报告:")
    print(report)
    print("\n混淆矩阵:")
    print(cm)
    
    return {
        'test_acc': test_acc,
        'test_loss': test_loss,
        'classification_report': report,
        'confusion_matrix': cm.tolist(),
        'predictions': test_preds,
        'labels': test_labels
    }

def main():
    args = parse_args()
    
    print("🧪 MAMBAFORMER vs Cross-Modal Transformer 对比实验")
    print("=" * 60)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(args, device)
    
    results = {}
    
    # 1. 训练MAMBAFORMER
    print("\n" + "="*60)
    print("1️⃣ MAMBAFORMER 实验")
    print("="*60)
    
    mambaformer_model = create_mambaformer_model(args, device)
    mambaformer_history = train_model(
        mambaformer_model, train_loader, val_loader, args, device, "MAMBAFORMER"
    )
    mambaformer_results = evaluate_model(mambaformer_model, test_loader, device, "MAMBAFORMER")
    results['mambaformer'] = {
        'history': mambaformer_history,
        'results': mambaformer_results
    }
    
    # 2. 训练原始Cross-Modal Transformer（如果指定）
    if args.run_baseline:
        print("\n" + "="*60)
        print("2️⃣ Cross-Modal Transformer 基准实验")
        print("="*60)
        
        baseline_model = create_baseline_model(args, device)
        baseline_history = train_model(
            baseline_model, train_loader, val_loader, args, device, "Cross-Modal Transformer"
        )
        baseline_results = evaluate_model(baseline_model, test_loader, device, "Cross-Modal Transformer")
        results['baseline'] = {
            'history': baseline_history,
            'results': baseline_results
        }
    
    # 3. 对比结果
    print("\n" + "="*60)
    print("📊 对比结果总结")
    print("="*60)
    
    print(f"MAMBAFORMER:")
    print(f"  - 最佳验证准确率: {mambaformer_history['best_val_acc']:.4f}")
    print(f"  - 测试准确率: {mambaformer_results['test_acc']:.4f}")
    print(f"  - 参数量: {sum(p.numel() for p in mambaformer_model.parameters()):,}")
    
    if args.run_baseline:
        print(f"\nCross-Modal Transformer:")
        print(f"  - 最佳验证准确率: {baseline_history['best_val_acc']:.4f}")
        print(f"  - 测试准确率: {baseline_results['test_acc']:.4f}")
        print(f"  - 参数量: {sum(p.numel() for p in baseline_model.parameters()):,}")
        
        print(f"\n🏆 性能提升:")
        val_improvement = mambaformer_history['best_val_acc'] - baseline_history['best_val_acc']
        test_improvement = mambaformer_results['test_acc'] - baseline_results['test_acc']
        print(f"  - 验证集: {val_improvement:+.4f} ({val_improvement*100:+.2f}%)")
        print(f"  - 测试集: {test_improvement:+.4f} ({test_improvement*100:+.2f}%)")
    
    # 保存结果
    results_file = os.path.join(args.save_dir, 'comparison_results.json')
    with open(results_file, 'w') as f:
        # 转换numpy数组为列表以便JSON序列化
        json_results = {}
        for model_name, model_results in results.items():
            json_results[model_name] = {
                'history': model_results['history'],
                'results': {
                    k: v.tolist() if isinstance(v, np.ndarray) else v 
                    for k, v in model_results['results'].items()
                }
            }
        json.dump(json_results, f, indent=2)
    
    print(f"\n✅ 实验完成！结果保存到: {results_file}")

if __name__ == '__main__':
    main()