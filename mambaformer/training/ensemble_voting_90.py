#!/usr/bin/env python3
"""
Ensemble Voting System - 组合最佳模型冲击90%
使用多个已训练模型的投票机制
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/ensemble_voting_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "ensemble.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Ensemble Voting System - Target 90%")
    logging.info("="*80)
    
    return log_dir

class ModelWrapper:
    """包装单个模型用于预测"""
    def __init__(self, model_path, config, device):
        self.device = device
        self.config = config
        
        # 加载模型
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 创建模型架构
        self.model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config.get('dropout', 0.1),
            seq_len=config['seq_len']
        ).to(device)
        
        # 加载权重
        try:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        except:
            # 尝试部分加载
            model_dict = self.model.state_dict()
            pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() if k in model_dict}
            model_dict.update(pretrained_dict)
            self.model.load_state_dict(model_dict)
        
        self.model.eval()
        
        # 获取测试指标
        self.test_metrics = checkpoint.get('test_metrics', {})
        self.accuracy = self.test_metrics.get('accuracy', 0)
        
    def predict(self, x):
        """预测"""
        with torch.no_grad():
            output, _ = self.model(x)
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            return output

class EnsembleVoting:
    """集成投票系统"""
    def __init__(self, model_configs, device):
        self.device = device
        self.models = []
        self.weights = []
        
        # 加载所有模型
        for config in model_configs:
            try:
                model = ModelWrapper(config['path'], config['params'], device)
                self.models.append(model)
                # 使用准确率作为权重
                weight = model.accuracy ** 2  # 平方以增强高准确率模型的影响
                self.weights.append(weight)
                logging.info(f"Loaded model: {config['name']} (Acc: {model.accuracy:.4f}, Weight: {weight:.4f})")
            except Exception as e:
                logging.warning(f"Failed to load {config['name']}: {e}")
        
        # 归一化权重
        total_weight = sum(self.weights)
        self.weights = [w/total_weight for w in self.weights]
        logging.info(f"Normalized weights: {self.weights}")
        
    def predict_soft_voting(self, x):
        """软投票（概率平均）"""
        all_probs = []
        
        for model, weight in zip(self.models, self.weights):
            logits = model.predict(x)
            probs = F.softmax(logits, dim=-1)
            all_probs.append(probs * weight)
        
        # 加权平均
        avg_probs = torch.stack(all_probs).sum(dim=0)
        return avg_probs.argmax(dim=-1)
    
    def predict_hard_voting(self, x):
        """硬投票（多数投票）"""
        all_preds = []
        
        for model in self.models:
            logits = model.predict(x)
            preds = logits.argmax(dim=-1)
            all_preds.append(preds)
        
        # 投票
        stacked = torch.stack(all_preds)
        # 使用mode找出最常见的预测
        mode_result = torch.mode(stacked, dim=0)
        return mode_result.values
    
    def predict_weighted_voting(self, x):
        """加权投票"""
        vote_counts = torch.zeros(x.size(0), 5).to(self.device)
        
        for model, weight in zip(self.models, self.weights):
            logits = model.predict(x)
            preds = logits.argmax(dim=-1)
            # 为每个预测添加权重
            for i in range(x.size(0)):
                vote_counts[i, preds[i]] += weight
        
        return vote_counts.argmax(dim=-1)
    
    def predict_confidence_weighted(self, x):
        """基于置信度的加权投票"""
        all_logits = []
        all_confidences = []
        
        for model in self.models:
            logits = model.predict(x)
            probs = F.softmax(logits, dim=-1)
            # 使用最大概率作为置信度
            confidence, preds = probs.max(dim=-1)
            all_logits.append(logits)
            all_confidences.append(confidence)
        
        # 使用置信度作为权重
        weighted_logits = torch.zeros_like(all_logits[0])
        total_confidence = torch.zeros(x.size(0)).to(self.device)
        
        for logits, confidence in zip(all_logits, all_confidences):
            weighted_logits += logits * confidence.unsqueeze(1)
            total_confidence += confidence
        
        weighted_logits /= total_confidence.unsqueeze(1)
        return weighted_logits.argmax(dim=-1)

def evaluate_ensemble(ensemble, data_loader, device, method='soft'):
    """评估集成模型"""
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc=f'Evaluating ({method})'):
            data = data.to(device)
            
            if method == 'soft':
                preds = ensemble.predict_soft_voting(data)
            elif method == 'hard':
                preds = ensemble.predict_hard_voting(data)
            elif method == 'weighted':
                preds = ensemble.predict_weighted_voting(data)
            elif method == 'confidence':
                preds = ensemble.predict_confidence_weighted(data)
            else:
                raise ValueError(f"Unknown method: {method}")
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    log_dir = setup_logging()
    
    # 模型配置
    model_configs = [
        {
            'name': 'V8 Improved',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_improved_20250812_013748/best_model.pth',
            'params': {
                'd_model': 192,
                'n_heads': 12,
                'n_layers': 5,
                'dropout': 0.1,
                'seq_len': 5
            }
        },
        {
            'name': 'V14 Robust',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_robust_20250812_011519/best_model.pth',
            'params': {
                'd_model': 280,
                'n_heads': 14,
                'n_layers': 8,
                'dropout': 0.12,
                'seq_len': 5
            }
        },
        {
            'name': 'V8 Fixed',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_fixed_20250812_022342/best_model.pth',
            'params': {
                'd_model': 256,
                'n_heads': 16,
                'n_layers': 6,
                'dropout': 0.12,
                'seq_len': 5
            }
        }
    ]
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading test dataset...")
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=5,
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=5,
        max_samples_per_file=None
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=64, 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=64,
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建集成模型
    logging.info("Creating ensemble...")
    ensemble = EnsembleVoting(model_configs, device)
    
    if len(ensemble.models) == 0:
        logging.error("No models loaded successfully!")
        return
    
    # 评估不同投票策略
    methods = ['soft', 'hard', 'weighted', 'confidence']
    best_method = None
    best_acc = 0
    best_metrics = {}
    
    logging.info("\n" + "="*80)
    logging.info("Evaluating different voting strategies on validation set...")
    logging.info("="*80)
    
    for method in methods:
        logging.info(f"\n{method.upper()} VOTING:")
        
        # 验证集评估
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_ensemble(
            ensemble, val_loader, device, method
        )
        
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f}")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        if val_acc > best_acc:
            best_acc = val_acc
            best_method = method
    
    logging.info(f"\nBest method on validation: {best_method.upper()} (Acc: {best_acc:.4f})")
    
    # 使用最佳方法在测试集上评估
    logging.info("\n" + "="*80)
    logging.info(f"Final evaluation on TEST SET using {best_method.upper()} voting...")
    logging.info("="*80)
    
    test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate_ensemble(
        ensemble, test_loader, device, best_method
    )
    
    logging.info(f"\nTEST RESULTS:")
    logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"  F1 Score: {test_f1:.4f}")
    logging.info(f"  Kappa: {test_kappa:.4f}")
    logging.info(f"  Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # 打印混淆矩阵
    logging.info("\nConfusion Matrix:")
    logging.info(test_cm)
    
    # 保存结果
    results = {
        'method': best_method,
        'test_accuracy': test_acc,
        'test_f1': test_f1,
        'test_kappa': test_kappa,
        'test_class_f1': test_class_f1.tolist(),
        'confusion_matrix': test_cm.tolist(),
        'ensemble_models': [cfg['name'] for cfg in model_configs],
        'model_weights': ensemble.weights
    }
    
    with open(os.path.join(log_dir, 'ensemble_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # 检查是否达到目标
    logging.info("\n" + "="*80)
    if test_acc >= 0.90:
        logging.info("🎉 SUCCESS! ACHIEVED 90% TARGET!")
        logging.info("="*80)
        # Git commit
        os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ ENSEMBLE ACHIEVED 90%: {test_acc:.4f}'")
    else:
        gap = 0.90 - test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
        logging.info("="*80)
        
        # 分析弱点
        logging.info("\nWeak classes:")
        for i, (class_name, f1) in enumerate(zip(['W', 'N1', 'N2', 'N3', 'REM'], test_class_f1)):
            if f1 < 0.7:
                logging.info(f"  {class_name}: {f1:.3f}")

if __name__ == "__main__":
    main()