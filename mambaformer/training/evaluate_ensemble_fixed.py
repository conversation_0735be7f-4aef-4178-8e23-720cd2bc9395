#!/usr/bin/env python3
"""
Evaluate ensemble of V13 and V14 fixed models
"""

import os
import sys
import json
import numpy as np
import torch
import logging
from datetime import datetime
from tqdm import tqdm
from scipy.optimize import minimize
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from torch.utils.data import DataLoader


def get_model_predictions(model_path, config, data_loader, device):
    """Get predictions from a single model"""
    
    # Load model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(model_path, map_location=device)
    if isinstance(checkpoint, dict):
        model.load_state_dict(checkpoint.get('model_state_dict', checkpoint))
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    
    # Collect predictions
    all_probs = []
    all_labels = []
    
    with torch.no_grad():
        for data, labels in tqdm(data_loader, desc='Getting predictions'):
            data = data.to(device)
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            # Flatten batch and sequence dimensions
            probs = probs.view(-1, 5)  # (batch*seq_len, n_classes)
            labels = labels.view(-1)    # (batch*seq_len,)
            
            all_probs.append(probs.cpu().numpy())
            all_labels.append(labels.cpu().numpy())
    
    # Concatenate all predictions
    all_probs = np.concatenate(all_probs, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    
    return all_probs, all_labels


def evaluate_ensemble_weights(weights, model_probs, labels):
    """Evaluate ensemble with given weights"""
    # Weighted average of probabilities
    ensemble_probs = np.zeros_like(model_probs[0])
    for i, prob in enumerate(model_probs):
        ensemble_probs += weights[i] * prob
    
    # Get predictions
    predictions = np.argmax(ensemble_probs, axis=1)
    
    # Calculate metrics
    acc = accuracy_score(labels, predictions)
    f1 = f1_score(labels, predictions, average='macro')
    kappa = cohen_kappa_score(labels, predictions)
    
    return acc, f1, kappa


def optimize_ensemble_weights(model_probs, labels, metric='f1'):
    """Optimize ensemble weights for a specific metric"""
    n_models = len(model_probs)
    
    def objective(weights):
        # Normalize weights
        weights = weights / weights.sum()
        acc, f1, kappa = evaluate_ensemble_weights(weights, model_probs, labels)
        
        if metric == 'f1':
            return -f1
        elif metric == 'accuracy':
            return -acc
        elif metric == 'kappa':
            return -kappa
        elif metric == 'combined':
            # Combined metric targeting all three thresholds
            score = 0
            if acc >= 0.87:
                score += 1
            else:
                score += acc / 0.87
            if f1 >= 0.80:
                score += 1
            else:
                score += f1 / 0.80
            if kappa >= 0.80:
                score += 1
            else:
                score += kappa / 0.80
            return -score
    
    # Initial weights (equal)
    x0 = np.ones(n_models) / n_models
    
    # Constraints: weights sum to 1 and are non-negative
    constraints = {'type': 'eq', 'fun': lambda x: x.sum() - 1}
    bounds = [(0, 1) for _ in range(n_models)]
    
    # Optimize
    result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
    
    optimal_weights = result.x / result.x.sum()
    return optimal_weights


def main():
    # Setup logging
    log_file = f"../logs/ensemble_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER Ensemble Evaluation (V13 + V14 Fixed)")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🔧 Device: {device}")
    
    # Configurations
    v13_config = {
        'd_model': 256,
        'n_heads': 8,
        'n_layers': 6,
        'dropout': 0.1,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4
    }
    
    v14_config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4
    }
    
    # Load data splits
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Prepare validation data for weight optimization
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    val_dataset = SequenceSleepDataset(
        val_files, 
        seq_len=5, 
        use_channels=3,
        max_samples_per_file=None
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=32,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    logging.info(f"📊 Validation set: {val_dataset.total_epochs} epochs")
    
    # Get validation predictions from both models
    logging.info("\n🔍 Getting validation predictions from V13...")
    v13_val_probs, val_labels = get_model_predictions(
        '../../checkpoints/v13_fixed.pth',
        v13_config,
        val_loader,
        device
    )
    
    logging.info("\n🔍 Getting validation predictions from V14...")
    v14_val_probs, _ = get_model_predictions(
        '../../checkpoints/v14_fixed.pth',
        v14_config,
        val_loader,
        device
    )
    
    model_val_probs = [v13_val_probs, v14_val_probs]
    
    # Optimize weights on validation set
    logging.info("\n⚖️ Optimizing ensemble weights on validation set...")
    
    # Try different optimization targets
    strategies = ['combined', 'f1', 'accuracy', 'kappa']
    best_weights = {}
    
    for strategy in strategies:
        logging.info(f"\n  Strategy: {strategy}")
        weights = optimize_ensemble_weights(model_val_probs, val_labels, metric=strategy)
        acc, f1, kappa = evaluate_ensemble_weights(weights, model_val_probs, val_labels)
        
        logging.info(f"    Weights: V13={weights[0]:.3f}, V14={weights[1]:.3f}")
        logging.info(f"    Val Acc: {acc:.4f}, F1: {f1:.4f}, Kappa: {kappa:.4f}")
        
        best_weights[strategy] = weights
    
    # Prepare test data
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    test_dataset = SequenceSleepDataset(
        test_files, 
        seq_len=5, 
        use_channels=3,
        max_samples_per_file=None
    )
    test_loader = DataLoader(
        test_dataset,
        batch_size=32,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    logging.info(f"\n📊 Test set: {test_dataset.total_epochs} epochs")
    
    # Get test predictions
    logging.info("\n🔍 Getting test predictions from V13...")
    v13_test_probs, test_labels = get_model_predictions(
        '../../checkpoints/v13_fixed.pth',
        v13_config,
        test_loader,
        device
    )
    
    logging.info("\n🔍 Getting test predictions from V14...")
    v14_test_probs, _ = get_model_predictions(
        '../../checkpoints/v14_fixed.pth',
        v14_config,
        test_loader,
        device
    )
    
    model_test_probs = [v13_test_probs, v14_test_probs]
    
    # Evaluate each weight strategy on test set
    logging.info("\n" + "="*80)
    logging.info("📊 TEST SET RESULTS")
    logging.info("="*80)
    
    # Also test equal weights
    best_weights['equal'] = np.array([0.5, 0.5])
    
    results = {}
    
    for strategy, weights in best_weights.items():
        acc, f1, kappa = evaluate_ensemble_weights(weights, model_test_probs, test_labels)
        
        logging.info(f"\n📊 Strategy: {strategy}")
        logging.info(f"   Weights: V13={weights[0]:.3f}, V14={weights[1]:.3f}")
        logging.info(f"   Test Accuracy: {acc:.4f} ({acc*100:.2f}%)")
        logging.info(f"   Test Macro F1: {f1:.4f} ({f1*100:.2f}%)")
        logging.info(f"   Test Kappa: {kappa:.4f}")
        
        # Check targets
        targets_met = []
        if acc >= 0.87:
            targets_met.append("✅ Acc ≥ 87%")
        if f1 >= 0.80:
            targets_met.append("✅ F1 ≥ 80%")
        if kappa >= 0.80:
            targets_met.append("✅ Kappa ≥ 0.80")
        
        if len(targets_met) == 3:
            logging.info(f"   🎉 ALL TARGETS MET! {' '.join(targets_met)}")
        elif targets_met:
            logging.info(f"   {' '.join(targets_met)}")
        else:
            logging.info(f"   ⚠️ Below all targets")
        
        results[strategy] = {
            'weights': {'v13': float(weights[0]), 'v14': float(weights[1])},
            'test_accuracy': float(acc),
            'test_macro_f1': float(f1),
            'test_kappa': float(kappa),
            'targets_met': len(targets_met) == 3
        }
    
    # Save results
    results_file = '../../configs/ensemble_fixed_results.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n📊 Results saved to {results_file}")
    
    # Find best strategy
    best_strategy = None
    best_score = -1
    
    for strategy, res in results.items():
        score = 0
        if res['test_accuracy'] >= 0.87:
            score += 1
        if res['test_macro_f1'] >= 0.80:
            score += 1
        if res['test_kappa'] >= 0.80:
            score += 1
        
        # Tie breaker: sum of normalized metrics
        score += res['test_accuracy'] / 0.87 + res['test_macro_f1'] / 0.80 + res['test_kappa'] / 0.80
        
        if score > best_score:
            best_score = score
            best_strategy = strategy
    
    logging.info("\n" + "="*80)
    logging.info(f"🏆 BEST STRATEGY: {best_strategy}")
    logging.info("="*80)
    
    best_res = results[best_strategy]
    logging.info(f"Weights: V13={best_res['weights']['v13']:.3f}, V14={best_res['weights']['v14']:.3f}")
    logging.info(f"Test Accuracy: {best_res['test_accuracy']:.4f} ({best_res['test_accuracy']*100:.2f}%)")
    logging.info(f"Test Macro F1: {best_res['test_macro_f1']:.4f} ({best_res['test_macro_f1']*100:.2f}%)")
    logging.info(f"Test Kappa: {best_res['test_kappa']:.4f}")
    
    if best_res['targets_met']:
        logging.info("\n🎉🎉🎉 ALL TARGETS ACHIEVED! 🎉🎉🎉")
    
    logging.info("\n✅ Ensemble evaluation complete!")
    
    return results


if __name__ == "__main__":
    main()