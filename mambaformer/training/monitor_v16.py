#!/usr/bin/env python3
"""
Monitor V16 training progress
"""
import os
import time
import json
from datetime import datetime

def monitor_training():
    log_dir = "../logs/"
    v16_dirs = [d for d in os.listdir(log_dir) if d.startswith("v16_final_push")]
    
    if not v16_dirs:
        print("No V16 training found")
        return
    
    latest_dir = sorted(v16_dirs)[-1]
    log_file = os.path.join(log_dir, latest_dir, "training.log")
    results_file = os.path.join(log_dir, latest_dir, "final_results.json")
    
    print(f"Monitoring: {log_file}")
    print("="*80)
    
    last_epoch = 0
    best_acc = 0
    best_f1 = 0
    best_kappa = 0
    
    while True:
        if os.path.exists(results_file):
            with open(results_file, 'r') as f:
                results = json.load(f)
            print("\n" + "="*80)
            print("🎉 TRAINING COMPLETED!")
            print(f"Final Test Accuracy: {results['test_acc']:.4f}")
            print(f"Final Test F1: {results['test_f1']:.4f}")
            print(f"Final Test Kappa: {results['test_kappa']:.4f}")
            
            if results.get('targets_achieved'):
                print("✅ ALL TARGETS ACHIEVED!")
            else:
                print("❌ Some targets not met")
            break
        
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                if "Epoch" in line and "/" in line:
                    try:
                        epoch_str = line.split("Epoch")[1].split(":")[0].strip()
                        current_epoch = int(epoch_str.split("/")[0])
                        total_epochs = int(epoch_str.split("/")[1])
                        
                        if current_epoch > last_epoch:
                            last_epoch = current_epoch
                            print(f"\n📊 Epoch {current_epoch}/{total_epochs}")
                    except:
                        pass
                
                if "Val Acc:" in line:
                    try:
                        acc = float(line.split("Val Acc:")[1].split("(")[0].strip())
                        if acc > best_acc:
                            best_acc = acc
                        print(f"  Accuracy: {acc:.4f} (best: {best_acc:.4f})")
                    except:
                        pass
                
                if "Val F1:" in line:
                    try:
                        f1 = float(line.split("Val F1:")[1].split("(")[0].strip())
                        if f1 > best_f1:
                            best_f1 = f1
                        print(f"  Macro F1: {f1:.4f} (best: {best_f1:.4f})")
                    except:
                        pass
                
                if "Val Kappa:" in line:
                    try:
                        kappa = float(line.split("Val Kappa:")[1].strip())
                        if kappa > best_kappa:
                            best_kappa = kappa
                        print(f"  Kappa: {kappa:.4f} (best: {best_kappa:.4f})")
                    except:
                        pass
                
                if "Targets met:" in line:
                    targets = line.split("Targets met:")[1].strip()
                    print(f"  🎯 Targets met: {targets}")
                
                if "SUCCESS" in line:
                    print("\n🎉🎉🎉 SUCCESS! All targets achieved!")
                
                if "Early stopping" in line:
                    print("\n⏹️ Early stopping triggered")
        
        print(f"\nCurrent Best - Acc: {best_acc:.4f}, F1: {best_f1:.4f}, Kappa: {best_kappa:.4f}")
        print(f"Target Goals - Acc: ≥0.87, F1: ≥0.80, Kappa: ≥0.80")
        
        gaps = []
        if best_acc < 0.87:
            gaps.append(f"Acc gap: {0.87-best_acc:.4f}")
        if best_f1 < 0.80:
            gaps.append(f"F1 gap: {0.80-best_f1:.4f}")
        if best_kappa < 0.80:
            gaps.append(f"Kappa gap: {0.80-best_kappa:.4f}")
        
        if gaps:
            print(f"Gaps: {', '.join(gaps)}")
        else:
            print("✅ All validation targets met!")
        
        time.sleep(30)
        os.system('clear')

if __name__ == "__main__":
    monitor_training()