#!/usr/bin/env python3
"""
🔥 EXTREME REM SPECIALIST - AGGRESSIVE APPROACH
Target: Force model to learn REM patterns at all costs
Current REM F1: 26% → Target: >50%
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import OneCycleLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class ExtremeREMLoss(nn.Module):
    """Extreme loss for forcing REM detection"""
    def __init__(self, device='cuda'):
        super().__init__()
        # EXTREME weights - make REM errors 100x more costly
        weights = torch.tensor([1.0, 2.0, 1.0, 1.0, 100.0]).to(device)
        self.ce_loss = nn.CrossEntropyLoss(weight=weights)
        self.device = device
        
    def forward(self, outputs, targets):
        # Handle sequential output
        if outputs.dim() == 3:
            batch_size, seq_len, n_classes = outputs.shape
            outputs = outputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        # Base loss
        ce_loss = self.ce_loss(outputs, targets)
        
        # Additional REM-specific penalties
        probs = F.softmax(outputs, dim=1)
        
        # Penalty for low REM confidence when target is REM
        rem_mask = (targets == 4).float()
        rem_probs = probs[:, 4]  # REM class probabilities
        
        # Force high confidence on REM
        rem_confidence_loss = rem_mask * (1 - rem_probs) ** 2
        
        # Penalty for not predicting REM enough (encourage more REM predictions)
        rem_prediction_penalty = -torch.log(rem_probs.mean() + 1e-7) * 0.1
        
        total_loss = ce_loss + rem_confidence_loss.mean() * 10.0 + rem_prediction_penalty
        
        return total_loss


class REMOnlyDataset(Dataset):
    """Dataset that oversamples REM heavily"""
    def __init__(self, original_dataset, rem_ratio=0.5):
        self.original_dataset = original_dataset
        self.rem_indices = []
        self.other_indices = []
        
        # Identify REM sequences
        for idx in range(len(original_dataset)):
            _, label = original_dataset[idx]
            if label.dim() > 0:
                rem_count = (label == 4).sum().item()
                if rem_count >= 4:  # Strong REM sequence
                    self.rem_indices.append(idx)
                else:
                    self.other_indices.append(idx)
            else:
                if label == 4:
                    self.rem_indices.append(idx)
                else:
                    self.other_indices.append(idx)
        
        # Create balanced dataset
        n_rem = len(self.rem_indices)
        n_other = int(n_rem * (1 - rem_ratio) / rem_ratio) if rem_ratio > 0 else len(self.other_indices)
        
        # Sample other indices
        if n_other < len(self.other_indices):
            sampled_other = np.random.choice(self.other_indices, n_other, replace=False)
        else:
            sampled_other = self.other_indices
        
        # Combine with heavy REM repetition
        self.indices = []
        for _ in range(10):  # Repeat REM samples 10 times
            self.indices.extend(self.rem_indices)
        self.indices.extend(sampled_other)
        
        np.random.shuffle(self.indices)
        
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        return self.original_dataset[self.indices[idx]]


def train_epoch(model, train_loader, criterion, optimizer, scheduler, device):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for data, target in pbar:
        data, target = data.to(device), target.to(device)
        
        # Handle sequence labels
        if target.dim() > 1:
            target_eval = target[:, target.shape[1]//2]
        else:
            target_eval = target
        
        optimizer.zero_grad()
        output, _ = model(data)
        
        loss = criterion(output, target)
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        scheduler.step()
        
        total_loss += loss.item()
        
        # Track predictions
        if output.dim() == 3:
            pred = output[:, output.shape[1]//2, :].argmax(dim=1)
        else:
            pred = output.argmax(dim=1)
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target_eval.cpu().numpy())
        
        # Calculate REM metrics
        rem_predicted = (pred == 4).sum().item()
        rem_actual = (target_eval == 4).sum().item()
        
        pbar.set_postfix({
            'loss': loss.item(),
            'rem_pred': rem_predicted,
            'rem_actual': rem_actual,
            'lr': optimizer.param_groups[0]['lr']
        })
    
    # Calculate epoch metrics
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)
    
    rem_pred_count = (all_preds == 4).sum()
    rem_actual_count = (all_targets == 4).sum()
    
    if rem_actual_count > 0:
        rem_recall = ((all_preds == 4) & (all_targets == 4)).sum() / rem_actual_count
    else:
        rem_recall = 0
    
    return total_loss / len(train_loader), rem_pred_count, rem_actual_count, rem_recall


def evaluate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            output, _ = model(data)
            
            # Force more REM predictions during evaluation
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            
            # Bias toward REM
            output[:, 4] += 0.5  # Add bias to REM class
            
            pred = output.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)
    
    # Calculate metrics
    cm = confusion_matrix(all_targets, all_preds, labels=[0, 1, 2, 3, 4])
    
    # Calculate REM-specific metrics
    rem_pred_count = (all_preds == 4).sum()
    rem_actual_count = (all_targets == 4).sum()
    
    if rem_actual_count > 0:
        rem_tp = ((all_preds == 4) & (all_targets == 4)).sum()
        rem_recall = rem_tp / rem_actual_count
    else:
        rem_recall = 0
    
    if rem_pred_count > 0:
        rem_tp = ((all_preds == 4) & (all_targets == 4)).sum()
        rem_precision = rem_tp / rem_pred_count
    else:
        rem_precision = 0
    
    if rem_precision + rem_recall > 0:
        rem_f1 = 2 * rem_precision * rem_recall / (rem_precision + rem_recall)
    else:
        rem_f1 = 0
    
    accuracy = (all_preds == all_targets).mean()
    
    return accuracy, rem_f1, rem_precision, rem_recall, rem_pred_count, rem_actual_count, cm


def main():
    # Configuration
    config = {
        'd_model': 320,  # Use n1_specialist dimensions
        'n_heads': 20,
        'n_layers': 8,
        'dropout': 0.2,
        'seq_len': 7,
        'batch_size': 32,
        'learning_rate': 5e-4,  # Higher learning rate
        'num_epochs': 50,
        'patience': 15,
        'gradient_clip': 1.0,
        'weight_decay': 0.01
    }
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'../logs/rem_extreme_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🔥 EXTREME REM SPECIALIST MODEL TRAINING")
    logging.info("Current REM F1: ~26% → Target: >50%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # Prepare file paths
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        is_training=True
    )
    
    # Create REM-heavy dataset
    rem_train_dataset = REMOnlyDataset(train_dataset, rem_ratio=0.7)
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        is_training=False
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        is_training=False
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        rem_train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Load n1_specialist weights as starting point
    n1_path = '../logs/n1_specialist_20250811_171630/best_n1_model.pth'
    if os.path.exists(n1_path):
        checkpoint = torch.load(n1_path, map_location=device, weights_only=False)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            logging.info(f"✅ Loaded n1_specialist weights")
    
    logging.info(f"\nModel Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Loss and optimizer
    criterion = ExtremeREMLoss(device=device)
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    total_steps = len(train_loader) * config['num_epochs']
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'],
        total_steps=total_steps,
        pct_start=0.3
    )
    
    # Training loop
    logging.info("\n🔥 Starting EXTREME REM Training...")
    logging.info("="*80)
    
    best_rem_f1 = 0
    patience_counter = 0
    
    for epoch in range(config['num_epochs']):
        # Train
        train_loss, rem_pred, rem_actual, train_rem_recall = train_epoch(
            model, train_loader, criterion, optimizer, scheduler, device
        )
        
        # Validate
        val_acc, val_rem_f1, val_rem_prec, val_rem_rec, val_rem_pred, val_rem_actual, val_cm = evaluate(
            model, val_loader, device
        )
        
        logging.info(f"\nEpoch {epoch+1}:")
        logging.info(f"  Loss: {train_loss:.4f}")
        logging.info(f"  Train REM: pred={rem_pred}, actual={rem_actual}, recall={train_rem_recall:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f}")
        logging.info(f"  Val REM: F1={val_rem_f1:.4f}, Prec={val_rem_prec:.4f}, Rec={val_rem_rec:.4f}")
        logging.info(f"  Val REM counts: pred={val_rem_pred}, actual={val_rem_actual}")
        
        if val_rem_f1 > best_rem_f1:
            best_rem_f1 = val_rem_f1
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'rem_f1': val_rem_f1,
                'val_acc': val_acc,
                'config': config
            }, os.path.join(log_dir, 'best_rem_extreme.pth'))
            
            logging.info(f"  💾 Saved best REM model (F1: {val_rem_f1:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⛔ Early stopping at epoch {epoch+1}")
                break
    
    # Test evaluation
    logging.info("\n" + "="*80)
    logging.info("📊 TEST EVALUATION")
    logging.info("="*80)
    
    # Load best model
    checkpoint = torch.load(os.path.join(log_dir, 'best_rem_extreme.pth'),
                          map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    test_acc, test_rem_f1, test_rem_prec, test_rem_rec, test_rem_pred, test_rem_actual, test_cm = evaluate(
        model, test_loader, device
    )
    
    logging.info("\n🎯 TEST RESULTS:")
    logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"  REM F1: {test_rem_f1:.4f}")
    logging.info(f"  REM Precision: {test_rem_prec:.4f}")
    logging.info(f"  REM Recall: {test_rem_rec:.4f}")
    logging.info(f"  REM Predictions: {test_rem_pred}/{test_rem_actual}")
    
    # Save results
    results = {
        'test_accuracy': test_acc,
        'rem_f1': test_rem_f1,
        'rem_precision': test_rem_prec,
        'rem_recall': test_rem_rec,
        'rem_predictions': int(test_rem_pred),
        'rem_actual': int(test_rem_actual),
        'confusion_matrix': test_cm.tolist(),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {log_dir}/results.json")
    
if __name__ == '__main__':
    main()