"""
MAMBAFORMER训练脚本 - 修复版本
修复了数据标准化和类别不平衡问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import os
import glob
import json
import logging
from pathlib import Path
from datetime import datetime
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight
from tqdm import tqdm
import time
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet
from models.epoch_cmt import Epoch_Cross_Transformer_Network

class SleepEDFDataset(Dataset):
    """Sleep-EDF数据集加载器 - 修复版本"""
    
    def __init__(self, data_path, subject_list, sub_wise_norm=True):
        self.data_path = data_path
        self.subject_list = subject_list
        self.sub_wise_norm = sub_wise_norm
        
        # 加载所有数据
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        self.mean_eeg = []
        self.std_eeg = []
        self.mean_eog = []
        self.std_eog = []
        
        for subject_id in subject_list:
            # 加载EEG数据
            eeg_file = os.path.join(data_path, f'x{int(subject_id):02d}.h5')
            if os.path.exists(eeg_file):
                with h5py.File(eeg_file, 'r') as f:
                    eeg = f['data'][:]
                    self.eeg_data.append(eeg)
                    
                # 加载EOG数据
                eog_file = os.path.join(data_path, f'eog{int(subject_id):02d}.h5')
                with h5py.File(eog_file, 'r') as f:
                    eog = f['data'][:]
                    self.eog_data.append(eog)
                    
                # 加载标签
                label_file = os.path.join(data_path, f'y{int(subject_id):02d}.h5')
                with h5py.File(label_file, 'r') as f:
                    labels = f['data'][:]
                    self.labels.append(labels)
                
                if self.sub_wise_norm:
                    # 加载均值和标准差
                    mean_eeg_file = os.path.join(data_path, f'mean{int(subject_id):02d}.h5')
                    with h5py.File(mean_eeg_file, 'r') as f:
                        mean_eeg = f['data'][:]
                        self.mean_eeg.append(mean_eeg)
                    
                    std_eeg_file = os.path.join(data_path, f'std{int(subject_id):02d}.h5')
                    with h5py.File(std_eeg_file, 'r') as f:
                        std_eeg = f['data'][:]
                        self.std_eeg.append(std_eeg)
                    
                    mean_eog_file = os.path.join(data_path, f'eog_m{int(subject_id):02d}.h5')
                    with h5py.File(mean_eog_file, 'r') as f:
                        mean_eog = f['data'][:]
                        self.mean_eog.append(mean_eog)
                    
                    std_eog_file = os.path.join(data_path, f'eog_s{int(subject_id):02d}.h5')
                    with h5py.File(std_eog_file, 'r') as f:
                        std_eog = f['data'][:]
                        self.std_eog.append(std_eog)
        
        # 合并所有数据
        if len(self.eeg_data) > 0:
            self.eeg_data = np.concatenate(self.eeg_data, axis=0)
            self.eog_data = np.concatenate(self.eog_data, axis=0)
            self.labels = np.concatenate(self.labels, axis=0)
            
            if self.sub_wise_norm:
                self.mean_eeg = np.concatenate(self.mean_eeg, axis=0)
                self.std_eeg = np.concatenate(self.std_eeg, axis=0)
                self.mean_eog = np.concatenate(self.mean_eog, axis=0)
                self.std_eog = np.concatenate(self.std_eog, axis=0)
            
            # 确保标签是整数类型
            self.labels = self.labels.astype(np.int64)
            
            print(f"加载数据集: {len(subject_list)} subjects, {len(self.labels)} epochs")
            print(f"EEG形状: {self.eeg_data.shape}, EOG形状: {self.eog_data.shape}")
            print(f"标签分布: {np.bincount(self.labels)}")
            
            # 计算类别权重
            self.class_weights = compute_class_weight(
                'balanced',
                classes=np.unique(self.labels),
                y=self.labels
            )
            print(f"类别权重: {self.class_weights}")
        else:
            raise ValueError("没有找到数据文件")
    
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        eeg = self.eeg_data[idx]
        eog = self.eog_data[idx]
        label = self.labels[idx]
        
        # Subject-wise normalization
        if self.sub_wise_norm:
            eeg = (eeg - self.mean_eeg[idx]) / (self.std_eeg[idx] + 1e-8)
            eog = (eog - self.mean_eog[idx]) / (self.std_eog[idx] + 1e-8)
        
        # 转换为张量并添加通道维度
        eeg = torch.tensor(eeg, dtype=torch.float32).unsqueeze(0)  # [1, seq_len]
        eog = torch.tensor(eog, dtype=torch.float32).unsqueeze(0)  # [1, seq_len]
        label = torch.tensor(label, dtype=torch.long)
        
        return eeg, eog, label

def setup_logging(log_dir, experiment_name):
    """设置日志系统"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"{experiment_name}_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    
    return logger

def train_epoch(model, train_loader, optimizer, criterion, device, epoch, logger):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} [Train]")
    
    for batch_idx, (eeg_data, eog_data, labels) in enumerate(pbar):
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(eeg_data, eog_data, stage="both")
        
        # 使用加权交叉熵损失
        if 'fine_probs' in outputs:
            logits = outputs['fine_logits']
        elif 'logits' in outputs:
            logits = outputs['logits']
        else:
            logits = outputs['fine_logits']
        
        loss = criterion(logits, labels)
        
        # 反向传播
        loss.backward()
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        pred = logits.argmax(dim=1)
        correct += (pred == labels).sum().item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    avg_loss = total_loss / len(train_loader)
    avg_acc = correct / total
    
    return avg_loss, avg_acc

def validate(model, val_loader, criterion, device, logger):
    """验证模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg_data, eog_data, labels in tqdm(val_loader, desc="Validating"):
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(eeg_data, eog_data, stage="fine")
            
            # 获取logits
            if 'fine_probs' in outputs:
                logits = outputs['fine_logits']
            elif 'logits' in outputs:
                logits = outputs['logits']
            else:
                logits = outputs['fine_logits']
            
            loss = criterion(logits, labels)
            total_loss += loss.item()
            
            # 获取预测
            pred = logits.argmax(dim=1)
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def main():
    # 配置
    config = {
        'data_path': './processed_data_small',
        'train_subjects': ['1', '2', '3'],
        'val_subjects': ['4'],
        'test_subjects': ['5'],
        'batch_size': 64,
        'epochs': 30,
        'lr': 5e-4,
        'patience': 10,
        'experiment_name': 'mambaformer_fixed',
        'log_dir': './log',
        'save_dir': './checkpoints',
        'sub_wise_norm': True  # 使用subject-wise normalization
    }
    
    # 创建目录
    os.makedirs(config['log_dir'], exist_ok=True)
    os.makedirs(config['save_dir'], exist_ok=True)
    
    # 设置日志
    logger = setup_logging(config['log_dir'], config['experiment_name'])
    logger.info("🧪 MAMBAFORMER Sleep Stage Classification - Fixed Version")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据集
    train_dataset = SleepEDFDataset(config['data_path'], config['train_subjects'], 
                                    sub_wise_norm=config['sub_wise_norm'])
    val_dataset = SleepEDFDataset(config['data_path'], config['val_subjects'],
                                  sub_wise_norm=config['sub_wise_norm'])
    test_dataset = SleepEDFDataset(config['data_path'], config['test_subjects'],
                                   sub_wise_norm=config['sub_wise_norm'])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], 
                              shuffle=True, num_workers=2, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], 
                            shuffle=False, num_workers=2, pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], 
                             shuffle=False, num_workers=2, pin_memory=True)
    
    logger.info(f"训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    logger.info(f"验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    logger.info(f"测试集: {len(test_dataset)} 样本, {len(test_loader)} 批次")
    
    # 创建模型
    model = MambaFormerSleepNet(
        d_model=64,
        num_mambaformer_layers=4,
        nhead=8,
        window_size=50,
        use_progressive=True,
        dropout=0.1
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"模型参数量: {total_params:,}")
    
    # 损失函数（使用加权交叉熵）
    class_weights = torch.tensor(train_dataset.class_weights, dtype=torch.float32).to(device)
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)
    
    # 训练
    best_val_acc = 0
    patience_counter = 0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, 
                                            device, epoch, logger)
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, criterion, device, logger)
        
        # 更新历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        logger.info(f"Epoch {epoch+1}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} | "
                   f"LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'val_acc': val_acc,
                'config': config
            }, os.path.join(config['save_dir'], f"{config['experiment_name']}_best.pth"))
            logger.info(f"✓ 新的最佳验证准确率: {best_val_acc:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logger.info("早停")
                break
    
    training_time = time.time() - start_time
    logger.info(f"训练完成，用时: {training_time/60:.2f}分钟")
    
    # 加载最佳模型
    checkpoint = torch.load(os.path.join(config['save_dir'], f"{config['experiment_name']}_best.pth"))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试
    logger.info("\n📊 测试集评估")
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, criterion, device, logger)
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, target_names=class_names, digits=4)
    
    logger.info(f"测试准确率: {test_acc:.4f}")
    logger.info(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    # 保存结果
    results = {
        'config': config,
        'best_val_acc': float(best_val_acc),
        'test_acc': float(test_acc),
        'history': history,
        'classification_report': report,
        'confusion_matrix': cm.tolist()
    }
    
    results_file = os.path.join(config['log_dir'], f"{config['experiment_name']}_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ 实验完成！结果保存在: {results_file}")

if __name__ == '__main__':
    main()