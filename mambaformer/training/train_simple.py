"""
简化的MAMBAFORMER训练脚本
直接加载h5数据，避免复杂的原始数据加载器
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import os
import glob
from pathlib import Path
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import time
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet
from models.epoch_cmt import Epoch_Cross_Transformer_Network

class SimpleH5Dataset(Dataset):
    """简化的H5数据集加载器"""
    
    def __init__(self, eeg_file, eog_file, label_file, device):
        self.device = device
        
        # 加载数据
        with h5py.File(eeg_file, 'r') as f:
            self.eeg_data = torch.tensor(f['data'][:], dtype=torch.float32)
            
        with h5py.File(eog_file, 'r') as f:
            self.eog_data = torch.tensor(f['data'][:], dtype=torch.float32) 
            
        with h5py.File(label_file, 'r') as f:
            self.labels = torch.tensor(f['data'][:], dtype=torch.long)
            
        print(f"Loaded dataset: EEG {self.eeg_data.shape}, EOG {self.eog_data.shape}, Labels {self.labels.shape}")
        print(f"Label distribution: {torch.bincount(self.labels)}")
        
        # 标准化 (每个样本独立标准化)
        self.eeg_data = (self.eeg_data - self.eeg_data.mean(dim=1, keepdim=True)) / (self.eeg_data.std(dim=1, keepdim=True) + 1e-8)
        self.eog_data = (self.eog_data - self.eog_data.mean(dim=1, keepdim=True)) / (self.eog_data.std(dim=1, keepdim=True) + 1e-8)
    
    def __len__(self):
        return len(self.eeg_data)
    
    def __getitem__(self, idx):
        return (
            self.eeg_data[idx].unsqueeze(0),  # 添加通道维度
            self.eog_data[idx].unsqueeze(0),
            self.labels[idx]
        )

def create_datasets(data_path):
    """创建数据集"""
    print("📊 加载数据...")
    
    # 查找数据文件
    eeg_files = sorted(glob.glob(os.path.join(data_path, 'x*.h5')))
    eog_files = sorted(glob.glob(os.path.join(data_path, 'eog*.h5')))
    label_files = sorted(glob.glob(os.path.join(data_path, 'y*.h5')))
    
    print(f"找到数据文件: {len(eeg_files)}个EEG, {len(eog_files)}个EOG, {len(label_files)}个标签")
    
    # 创建训练和验证数据集
    train_dataset = SimpleH5Dataset(eeg_files[0], eog_files[0], label_files[0], 'cpu')
    val_dataset = SimpleH5Dataset(eeg_files[1], eog_files[1], label_files[1], 'cpu')
    
    return train_dataset, val_dataset

def train_epoch(model, train_loader, optimizer, criterion, device, model_type='mambaformer'):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc="Training", leave=False)
    
    for eeg_data, eog_data, labels in pbar:
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        if model_type == 'mambaformer':
            # MAMBAFORMER前向传播
            outputs = model(eeg_data, eog_data, stage="fine")
            
            if 'fine_probs' in outputs:
                logits = outputs['fine_logits']
                pred = outputs['fine_probs'].argmax(dim=1)
            else:
                logits = outputs['logits']
                pred = logits.argmax(dim=1)
                
            loss = criterion(logits, labels)
        else:
            # 原始模型前向传播
            logits, _, _ = model(eeg_data, eog_data, finetune=True)
            loss = criterion(logits, labels)
            pred = logits.argmax(dim=1)
        
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        correct += (pred == labels).sum().item()
        total += labels.size(0)
        
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    return total_loss / len(train_loader), correct / total

def validate(model, val_loader, criterion, device, model_type='mambaformer'):
    """验证"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg_data, eog_data, labels in val_loader:
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            if model_type == 'mambaformer':
                outputs = model(eeg_data, eog_data, stage="fine")
                
                if 'fine_probs' in outputs:
                    logits = outputs['fine_logits']
                    pred = outputs['fine_probs'].argmax(dim=1)
                else:
                    logits = outputs['logits']
                    pred = logits.argmax(dim=1)
            else:
                logits, _, _ = model(eeg_data, eog_data, finetune=True)
                pred = logits.argmax(dim=1)
            
            loss = criterion(logits, labels)
            total_loss += loss.item()
            
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def train_model(model, train_loader, val_loader, epochs, device, model_name):
    """训练模型"""
    print(f"🚀 开始训练 {model_name}")
    
    model_type = 'mambaformer' if 'MAMBAFORMER' in model_name else 'baseline'
    
    # 设置优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()
    
    best_val_acc = 0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device, model_type)
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, criterion, device, model_type)
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        print(f"Epoch {epoch+1:2d}/{epochs}: "
              f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
              f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            print(f"  ✓ 新的最佳验证准确率: {best_val_acc:.4f}")
    
    training_time = time.time() - start_time
    print(f"✅ {model_name} 训练完成，用时: {training_time:.2f}s")
    print(f"🏆 最佳验证准确率: {best_val_acc:.4f}")
    
    return history, best_val_acc

def main():
    print("🧪 MAMBAFORMER vs Cross-Modal Transformer 简化对比实验")
    print("=" * 70)
    
    # 设置设备
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    print(f"✓ 使用设备: {device}")
    
    # 创建数据集
    data_path = './processed_data_test'
    train_dataset, val_dataset = create_datasets(data_path)
    
    # 创建数据加载器
    batch_size = 64
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    print(f"✓ 训练集: {len(train_dataset)} 样本, {len(train_loader)} 批")
    print(f"✓ 验证集: {len(val_dataset)} 样本, {len(val_loader)} 批")
    
    results = {}
    
    # 1. 训练MAMBAFORMER
    print("\\n" + "="*70)
    print("1️⃣ MAMBAFORMER 实验")
    print("="*70)
    
    mambaformer_model = MambaFormerSleepNet(
        d_model=64,
        num_mambaformer_layers=4,
        window_size=50,
        use_progressive=True,
        dropout=0.1
    ).to(device)
    
    total_params = sum(p.numel() for p in mambaformer_model.parameters())
    print(f"✓ MAMBAFORMER参数量: {total_params:,}")
    
    mambaformer_history, mambaformer_best = train_model(
        mambaformer_model, train_loader, val_loader, 10, device, "MAMBAFORMER"
    )
    results['mambaformer'] = {'best_val_acc': mambaformer_best, 'params': total_params}
    
    # 2. 训练原始Cross-Modal Transformer
    print("\\n" + "="*70)
    print("2️⃣ Cross-Modal Transformer 基准实验")
    print("="*70)
    
    baseline_model = Epoch_Cross_Transformer_Network(
        d_model=64,
        window_size=50
    ).to(device)
    
    total_params_baseline = sum(p.numel() for p in baseline_model.parameters())
    print(f"✓ Cross-Modal Transformer参数量: {total_params_baseline:,}")
    
    baseline_history, baseline_best = train_model(
        baseline_model, train_loader, val_loader, 10, device, "Cross-Modal Transformer"
    )
    results['baseline'] = {'best_val_acc': baseline_best, 'params': total_params_baseline}
    
    # 3. 对比结果
    print("\\n" + "="*70)
    print("📊 实验结果对比")
    print("="*70)
    
    print(f"MAMBAFORMER:")
    print(f"  - 最佳验证准确率: {results['mambaformer']['best_val_acc']:.4f}")
    print(f"  - 参数量: {results['mambaformer']['params']:,}")
    
    print(f"\\nCross-Modal Transformer:")
    print(f"  - 最佳验证准确率: {results['baseline']['best_val_acc']:.4f}")
    print(f"  - 参数量: {results['baseline']['params']:,}")
    
    # 计算提升
    acc_improvement = results['mambaformer']['best_val_acc'] - results['baseline']['best_val_acc']
    param_ratio = results['mambaformer']['params'] / results['baseline']['params']
    
    print(f"\\n🏆 性能对比:")
    print(f"  - 准确率提升: {acc_improvement:+.4f} ({acc_improvement*100:+.2f}%)")
    print(f"  - 参数量比例: {param_ratio:.2f}x")
    
    if acc_improvement > 0:
        print(f"🎉 MAMBAFORMER在小批量数据上表现更好！")
    else:
        print(f"🤔 需要进一步优化MAMBAFORMER架构")
    
    # 保存结果
    os.makedirs('./results', exist_ok=True)
    import json
    with open('./results/simple_comparison.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\\n✅ 实验完成！结果已保存。")

if __name__ == '__main__':
    main()