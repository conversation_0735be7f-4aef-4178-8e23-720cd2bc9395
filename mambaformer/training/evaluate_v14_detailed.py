#!/usr/bin/env python3
"""
Detailed evaluation of V14 fixed model with comprehensive metrics
Similar to V11's evaluation output format
"""

import os
import sys
import json
import numpy as np
import torch
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    accuracy_score, f1_score, cohen_kappa_score, 
    confusion_matrix, classification_report,
    precision_recall_fscore_support
)
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from torch.utils.data import DataLoader


def plot_confusion_matrix(cm, class_names, save_path):
    """Plot and save confusion matrix"""
    plt.figure(figsize=(10, 8))
    
    # Normalize for display
    cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    
    # Create heatmap
    sns.heatmap(cm_normalized, annot=True, fmt='.2%', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                cbar_kws={'label': 'Percentage'})
    
    # Add raw counts as text
    for i in range(len(class_names)):
        for j in range(len(class_names)):
            plt.text(j + 0.5, i + 0.7, f'({cm[i,j]})', 
                    ha='center', va='center', fontsize=8, color='gray')
    
    plt.title('V14 Fixed Model - Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    logging.info(f"📊 Confusion matrix plot saved to {save_path}")


def detailed_evaluation(model_path, config, device):
    """Perform detailed evaluation with comprehensive metrics"""
    
    logging.info("="*80)
    logging.info("🔍 V14 FIXED MODEL - DETAILED EVALUATION")
    logging.info("="*80)
    
    # Load model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(model_path, map_location=device)
    if isinstance(checkpoint, dict):
        model.load_state_dict(checkpoint.get('model_state_dict', checkpoint))
    else:
        model.load_state_dict(checkpoint)
    
    logging.info(f"✅ Model loaded from {model_path}")
    
    # Model architecture summary
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logging.info(f"📊 Model Parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Load data splits
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Prepare test data
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    test_subjects = splits['splits']['test']['subjects']
    
    logging.info("\n📁 Test Set Information:")
    logging.info(f"  Subjects: {test_subjects}")
    logging.info(f"  Files: {len(test_files)}")
    
    test_dataset = SequenceSleepDataset(
        test_files, 
        seq_len=config['seq_len'], 
        use_channels=3,
        max_samples_per_file=None  # CRITICAL: Use all data!
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config['num_workers'],
        pin_memory=True
    )
    
    logging.info(f"  Total epochs: {test_dataset.total_epochs}")
    logging.info(f"  Total sequences: {len(test_dataset)}")
    
    # Evaluate
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    evaluator.total_epochs = test_dataset.total_epochs
    
    # Collect all predictions for detailed analysis
    all_sequence_preds = []
    all_sequence_labels = []
    all_epoch_preds = []
    all_epoch_labels = []
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for data, labels in tqdm(test_loader, desc='Evaluating'):
            data = data.to(device)
            labels_gpu = labels.to(device)
            
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            # Store sequence-level predictions
            seq_preds = torch.argmax(outputs, dim=-1)
            all_sequence_preds.extend(seq_preds.view(-1).cpu().numpy())
            all_sequence_labels.extend(labels.view(-1).cpu().numpy())
            
            # Process for epoch-level evaluation
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    # Get epoch-level metrics
    epoch_metrics = evaluator.evaluate()
    
    # Get epoch-level predictions for confusion matrix
    # Use the evaluator's method to get final predictions
    final_preds, final_labels, _ = evaluator.get_final_predictions()
    all_epoch_preds = final_preds
    all_epoch_labels = final_labels
    
    all_epoch_preds = np.array(all_epoch_preds, dtype=np.int32)
    all_epoch_labels = np.array(all_epoch_labels, dtype=np.int32)
    
    # Class names
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    class_mapping = {0: 'Wake', 1: 'N1', 2: 'N2', 3: 'N3', 4: 'REM'}
    
    logging.info("\n" + "="*80)
    logging.info("📊 EVALUATION RESULTS")
    logging.info("="*80)
    
    # Overall metrics
    logging.info("\n🎯 Overall Performance:")
    logging.info(f"  Accuracy: {epoch_metrics['accuracy']:.4f} ({epoch_metrics['accuracy']*100:.2f}%)")
    logging.info(f"  Macro F1: {epoch_metrics['macro_f1']:.4f} ({epoch_metrics['macro_f1']*100:.2f}%)")
    logging.info(f"  Cohen's Kappa: {epoch_metrics['kappa']:.4f}")
    
    # Target achievement status
    logging.info("\n🎯 Target Achievement:")
    targets_met = []
    if epoch_metrics['accuracy'] >= 0.87:
        logging.info(f"  ✅ Accuracy: {epoch_metrics['accuracy']*100:.2f}% ≥ 87%")
        targets_met.append("Accuracy")
    else:
        gap = 0.87 - epoch_metrics['accuracy']
        logging.info(f"  ❌ Accuracy: {epoch_metrics['accuracy']*100:.2f}% < 87% (gap: {gap*100:.2f}%)")
    
    if epoch_metrics['macro_f1'] >= 0.80:
        logging.info(f"  ✅ Macro F1: {epoch_metrics['macro_f1']*100:.2f}% ≥ 80%")
        targets_met.append("F1")
    else:
        gap = 0.80 - epoch_metrics['macro_f1']
        logging.info(f"  ❌ Macro F1: {epoch_metrics['macro_f1']*100:.2f}% < 80% (gap: {gap*100:.2f}%)")
    
    if epoch_metrics['kappa'] >= 0.80:
        logging.info(f"  ✅ Kappa: {epoch_metrics['kappa']:.4f} ≥ 0.80")
        targets_met.append("Kappa")
    else:
        gap = 0.80 - epoch_metrics['kappa']
        logging.info(f"  ❌ Kappa: {epoch_metrics['kappa']:.4f} < 0.80 (gap: {gap:.4f})")
    
    if len(targets_met) == 3:
        logging.info("\n🎉🎉🎉 ALL TARGETS ACHIEVED! 🎉🎉🎉")
    else:
        logging.info(f"\n📈 Progress: {len(targets_met)}/3 targets met ({targets_met})")
    
    # Confusion Matrix
    logging.info("\n📊 Confusion Matrix (Epoch-Level):")
    cm = confusion_matrix(all_epoch_labels, all_epoch_preds)
    
    # Print confusion matrix in readable format
    logging.info("     Predicted →")
    logging.info("     " + "  ".join([f"{name:>6}" for name in class_names]))
    logging.info("  " + "-"*50)
    for i, true_class in enumerate(class_names):
        row = cm[i]
        row_str = " ".join([f"{count:6d}" for count in row])
        total = row.sum()
        accuracy = row[i] / total * 100 if total > 0 else 0
        logging.info(f"{true_class:>5} | {row_str} | {total:5d} ({accuracy:5.1f}%)")
    
    # Per-class metrics
    logging.info("\n📊 Per-Class Metrics:")
    precision, recall, f1, support = precision_recall_fscore_support(
        all_epoch_labels, all_epoch_preds, average=None
    )
    
    # Create detailed table
    logging.info(f"{'Class':<10} {'Precision':>10} {'Recall':>10} {'F1-Score':>10} {'Support':>10} {'Accuracy':>10}")
    logging.info("-" * 62)
    
    for i, class_name in enumerate(class_names):
        class_acc = cm[i,i] / cm[i].sum() if cm[i].sum() > 0 else 0
        logging.info(f"{class_name:<10} {precision[i]:>10.4f} {recall[i]:>10.4f} "
                    f"{f1[i]:>10.4f} {int(support[i]):>10d} {class_acc:>10.4f}")
    
    # Weighted averages
    weighted_avg_precision = np.average(precision, weights=support)
    weighted_avg_recall = np.average(recall, weights=support)
    weighted_avg_f1 = np.average(f1, weights=support)
    
    logging.info("-" * 62)
    logging.info(f"{'Macro Avg':<10} {np.mean(precision):>10.4f} {np.mean(recall):>10.4f} "
                f"{np.mean(f1):>10.4f} {int(np.sum(support)):>10d}")
    logging.info(f"{'Weighted':<10} {weighted_avg_precision:>10.4f} {weighted_avg_recall:>10.4f} "
                f"{weighted_avg_f1:>10.4f} {int(np.sum(support)):>10d}")
    
    # Class distribution analysis
    logging.info("\n📊 Class Distribution in Test Set:")
    unique, counts = np.unique(all_epoch_labels, return_counts=True)
    total_samples = len(all_epoch_labels)
    for cls_idx, count in zip(unique, counts):
        percentage = count / total_samples * 100
        logging.info(f"  {class_mapping[cls_idx]:>5}: {count:5d} samples ({percentage:5.2f}%)")
    
    # Performance analysis by class
    logging.info("\n🔍 Performance Analysis:")
    
    # Best and worst performing classes
    best_f1_idx = np.argmax(f1)
    worst_f1_idx = np.argmin(f1)
    
    logging.info(f"  Best F1: {class_names[best_f1_idx]} ({f1[best_f1_idx]:.4f})")
    logging.info(f"  Worst F1: {class_names[worst_f1_idx]} ({f1[worst_f1_idx]:.4f})")
    
    # Common misclassifications
    logging.info("\n🔄 Top Misclassifications:")
    misclass = []
    for i in range(5):
        for j in range(5):
            if i != j and cm[i,j] > 0:
                misclass.append((class_names[i], class_names[j], cm[i,j]))
    
    misclass.sort(key=lambda x: x[2], reverse=True)
    for true_cls, pred_cls, count in misclass[:5]:
        percentage = count / cm.sum() * 100
        logging.info(f"  {true_cls} → {pred_cls}: {count} ({percentage:.2f}%)")
    
    # Academic benchmark comparison
    logging.info("\n📚 Academic Benchmark Comparison:")
    wake_acc = cm[0,0] / cm[0].sum() if cm[0].sum() > 0 else 0
    n1_acc = cm[1,1] / cm[1].sum() if cm[1].sum() > 0 else 0
    
    logging.info(f"  Wake Accuracy: {wake_acc*100:.2f}% (typical: >90%)")
    if wake_acc >= 0.90:
        logging.info("    ✅ Meets academic standard")
    else:
        logging.info(f"    ⚠️ Below typical ({(0.90-wake_acc)*100:.2f}% gap)")
    
    logging.info(f"  N1 Accuracy: {n1_acc*100:.2f}% (typical: 55-60%)")
    if 0.55 <= n1_acc <= 0.65:
        logging.info("    ✅ Within typical range")
    elif n1_acc < 0.55:
        logging.info(f"    ⚠️ Below typical range")
    else:
        logging.info(f"    ℹ️ Above typical range")
    
    # Save detailed results
    results = {
        'model': 'V14_FIXED',
        'timestamp': datetime.now().isoformat(),
        'configuration': config,
        'overall_metrics': {
            'accuracy': float(epoch_metrics['accuracy']),
            'macro_f1': float(epoch_metrics['macro_f1']),
            'weighted_f1': float(weighted_avg_f1),
            'kappa': float(epoch_metrics['kappa'])
        },
        'targets_achieved': {
            'accuracy': epoch_metrics['accuracy'] >= 0.87,
            'macro_f1': epoch_metrics['macro_f1'] >= 0.80,
            'kappa': epoch_metrics['kappa'] >= 0.80,
            'all_met': len(targets_met) == 3
        },
        'confusion_matrix': cm.tolist(),
        'per_class_metrics': {},
        'class_distribution': {},
        'misclassifications': []
    }
    
    for i, class_name in enumerate(class_names):
        results['per_class_metrics'][class_name] = {
            'precision': float(precision[i]),
            'recall': float(recall[i]),
            'f1_score': float(f1[i]),
            'support': int(support[i]),
            'accuracy': float(cm[i,i] / cm[i].sum() if cm[i].sum() > 0 else 0)
        }
        results['class_distribution'][class_name] = int(counts[i]) if i < len(counts) else 0
    
    for true_cls, pred_cls, count in misclass[:10]:
        results['misclassifications'].append({
            'true': true_cls,
            'predicted': pred_cls,
            'count': int(count),
            'percentage': float(count / cm.sum() * 100)
        })
    
    # Convert numpy types to Python types for JSON serialization
    def convert_to_json_serializable(obj):
        if isinstance(obj, (np.bool_, np.integer)):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_to_json_serializable(item) for item in obj]
        else:
            return obj
    
    results = convert_to_json_serializable(results)
    
    # Save results
    results_file = '../../configs/v14_detailed_evaluation.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Detailed results saved to {results_file}")
    
    # Plot confusion matrix
    plot_path = '../../configs/v14_confusion_matrix.png'
    plot_confusion_matrix(cm, class_names, plot_path)
    
    # Data integrity check
    logging.info("\n🔒 Data Integrity Verification:")
    logging.info(f"  Total epochs evaluated: {len(all_epoch_preds)}")
    logging.info(f"  Expected epochs: {test_dataset.total_epochs}")
    logging.info(f"  Coverage: {len(all_epoch_preds)/test_dataset.total_epochs*100:.2f}%")
    
    if len(all_epoch_preds) == test_dataset.total_epochs:
        logging.info("  ✅ Full test set evaluated (no data truncation)")
    else:
        logging.info(f"  ⚠️ Missing {test_dataset.total_epochs - len(all_epoch_preds)} epochs")
    
    logging.info("\n" + "="*80)
    logging.info("✅ DETAILED EVALUATION COMPLETE")
    logging.info("="*80)
    
    return results


def main():
    # Setup logging
    log_file = f"../logs/v14_detailed_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    os.makedirs("../logs", exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V14 FIXED - DETAILED EVALUATION")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🔧 Device: {device}")
    
    # V14 configuration
    v14_config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4
    }
    
    # Model architecture details
    logging.info("\n📐 Model Architecture:")
    logging.info(f"  Model: Sequential MAMBAFORMER V2")
    logging.info(f"  d_model: {v14_config['d_model']}")
    logging.info(f"  n_heads: {v14_config['n_heads']}")
    logging.info(f"  n_layers: {v14_config['n_layers']}")
    logging.info(f"  dropout: {v14_config['dropout']}")
    logging.info(f"  seq_len: {v14_config['seq_len']}")
    logging.info(f"  Loss: REMFocusedLoss (REM weight=3.0, Wake weight=5.0)")
    
    # Check if model exists
    model_path = '../../checkpoints/v14_fixed.pth'
    if not os.path.exists(model_path):
        logging.error(f"❌ Model not found at {model_path}")
        return None
    
    # Run detailed evaluation
    results = detailed_evaluation(model_path, v14_config, device)
    
    logging.info(f"\n📄 Full log saved to {log_file}")
    
    return results


if __name__ == "__main__":
    main()