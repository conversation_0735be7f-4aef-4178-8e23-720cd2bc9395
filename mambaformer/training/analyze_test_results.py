#!/usr/bin/env python3
"""Analyze test results from all trained models."""

import json
import os
from pathlib import Path

log_dir = Path("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs")

# Find all final_results.json files
results = []

for json_file in log_dir.rglob("final_results.json"):
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
            model_name = json_file.parent.name
            
            # Extract key metrics
            test_acc = data.get('test_acc', -1)
            test_f1 = data.get('test_f1', -1)
            test_kappa = data.get('test_kappa', -1)
            val_acc = data.get('best_val_acc', -1)
            val_f1 = data.get('best_val_f1', -1)
            val_kappa = data.get('best_val_kappa', -1)
            
            if test_acc > 0:  # Only include models with test results
                val_test_gap_acc = val_acc - test_acc if val_acc > 0 else -1
                val_test_gap_f1 = val_f1 - test_f1 if val_f1 > 0 else -1
                
                results.append({
                    'model': model_name,
                    'test_acc': test_acc,
                    'test_f1': test_f1,
                    'test_kappa': test_kappa,
                    'val_acc': val_acc,
                    'val_f1': val_f1,
                    'val_kappa': val_kappa,
                    'val_test_gap_acc': val_test_gap_acc,
                    'val_test_gap_f1': val_test_gap_f1
                })
    except Exception as e:
        print(f"Error reading {json_file}: {e}")

# Check for ensemble results
ensemble_files = list(log_dir.glob("ensemble_*_results*.json"))
for json_file in ensemble_files:
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
            model_name = json_file.stem
            
            # Extract ensemble test metrics (stored differently)
            test_acc = data.get('accuracy', -1)
            test_f1 = data.get('macro_f1', -1) 
            test_kappa = data.get('kappa', -1)
            
            if test_acc > 0:
                results.append({
                    'model': model_name,
                    'test_acc': test_acc,
                    'test_f1': test_f1, 
                    'test_kappa': test_kappa,
                    'val_acc': -1,  # No val metrics for ensemble
                    'val_f1': -1,
                    'val_kappa': -1,
                    'val_test_gap_acc': -1,
                    'val_test_gap_f1': -1
                })
    except Exception as e:
        print(f"Error reading {json_file}: {e}")

# Sort by test accuracy
results.sort(key=lambda x: x['test_acc'], reverse=True)

# Print results
print("\n" + "="*120)
print("TEST SET PERFORMANCE SUMMARY (All Models)")
print("="*120)
print(f"{'Model':<35} {'Test Acc':<12} {'Test F1':<12} {'Test Kappa':<12} {'Val-Test Gap (Acc)':<20} {'Val-Test Gap (F1)'}")
print("-"*120)

for r in results:
    gap_acc_str = f"{r['val_test_gap_acc']:.4f}" if r['val_test_gap_acc'] >= 0 else "N/A"
    gap_f1_str = f"{r['val_test_gap_f1']:.4f}" if r['val_test_gap_f1'] >= 0 else "N/A"
    
    print(f"{r['model']:<35} {r['test_acc']:.4f} ({r['test_acc']*100:.2f}%) "
          f"{r['test_f1']:.4f} ({r['test_f1']*100:.2f}%) "
          f"{r['test_kappa']:.4f} "
          f"{gap_acc_str:<20} {gap_f1_str}")

# Find best model
if results:
    print("\n" + "="*120)
    print("BEST PERFORMING MODELS ON TEST SET")
    print("="*120)
    
    best_acc = max(results, key=lambda x: x['test_acc'])
    best_f1 = max(results, key=lambda x: x['test_f1'])
    best_kappa = max(results, key=lambda x: x['test_kappa'])
    
    print(f"\n🏆 Best Test Accuracy: {best_acc['model']}")
    print(f"   - Test Acc: {best_acc['test_acc']:.4f} ({best_acc['test_acc']*100:.2f}%)")
    print(f"   - Test F1:  {best_acc['test_f1']:.4f} ({best_acc['test_f1']*100:.2f}%)")
    print(f"   - Test Kappa: {best_acc['test_kappa']:.4f}")
    
    print(f"\n🏆 Best Test F1: {best_f1['model']}")
    print(f"   - Test Acc: {best_f1['test_acc']:.4f} ({best_f1['test_acc']*100:.2f}%)")
    print(f"   - Test F1:  {best_f1['test_f1']:.4f} ({best_f1['test_f1']*100:.2f}%)")
    print(f"   - Test Kappa: {best_f1['test_kappa']:.4f}")
    
    print(f"\n🏆 Best Test Kappa: {best_kappa['model']}")
    print(f"   - Test Acc: {best_kappa['test_acc']:.4f} ({best_kappa['test_acc']*100:.2f}%)")
    print(f"   - Test F1:  {best_kappa['test_f1']:.4f} ({best_kappa['test_f1']*100:.2f}%)")
    print(f"   - Test Kappa: {best_kappa['test_kappa']:.4f}")
    
    # Analysis of validation-test gaps
    valid_gaps = [r for r in results if r['val_test_gap_acc'] >= 0]
    if valid_gaps:
        avg_gap_acc = sum(r['val_test_gap_acc'] for r in valid_gaps) / len(valid_gaps)
        avg_gap_f1 = sum(r['val_test_gap_f1'] for r in valid_gaps) / len(valid_gaps)
        max_gap_acc = max(r['val_test_gap_acc'] for r in valid_gaps)
        max_gap_f1 = max(r['val_test_gap_f1'] for r in valid_gaps)
        
        print("\n" + "="*120)
        print("VALIDATION-TEST GAP ANALYSIS")
        print("="*120)
        print(f"Average Gap (Accuracy): {avg_gap_acc:.4f} ({avg_gap_acc*100:.2f}%)")
        print(f"Average Gap (F1):       {avg_gap_f1:.4f} ({avg_gap_f1*100:.2f}%)")
        print(f"Maximum Gap (Accuracy): {max_gap_acc:.4f} ({max_gap_acc*100:.2f}%)")
        print(f"Maximum Gap (F1):       {max_gap_f1:.4f} ({max_gap_f1*100:.2f}%)")
        
        # Show models with smallest gaps
        valid_gaps.sort(key=lambda x: x['val_test_gap_acc'])
        print("\nModels with smallest validation-test gap (Accuracy):")
        for r in valid_gaps[:3]:
            print(f"  - {r['model']}: Gap = {r['val_test_gap_acc']:.4f} ({r['val_test_gap_acc']*100:.2f}%)")

print("\n" + "="*120)
print("NOTES:")
print("- V21 pseudo-labeling appears to still be training (no final_results.json yet)")
print("- Ensemble V20 achieved 87.23% test accuracy with 81.84% F1 score")
print("- Individual models show significant validation-test gaps (4-7%)")
print("="*120)