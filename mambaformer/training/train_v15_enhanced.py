#!/usr/bin/env python3
"""
V15 MAMBAFORMER - Targeted Enhancement for 87% Accuracy
Target: 87% Accuracy, 80% Macro F1, 0.80 Kappa
Focus: N1 improvement and stability
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2, SequentialFocalLoss, TemporalConsistencyLoss
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class N1FocusedLoss(nn.Module):
    """Specialized loss focusing on N1 classification improvement"""
    def __init__(self, n1_weight=4.0, focal_gamma=2.5):
        super().__init__()
        self.focal = SequentialFocalLoss(gamma=focal_gamma)
        self.n1_weight = n1_weight
        self.ce_loss = nn.CrossEntropyLoss(reduction='none')
        
    def forward(self, predictions, targets):
        batch_size, seq_len = targets.shape
        
        # Base focal loss
        focal_loss = self.focal(predictions, targets)
        
        # Extra penalty for N1 misclassification
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        
        ce_losses = self.ce_loss(pred_flat, target_flat)
        ce_losses = ce_losses.reshape(batch_size, seq_len)
        
        # Apply extra weight to N1 errors
        n1_mask = (targets == 1).float()
        n1_penalty = (ce_losses * n1_mask).mean() * self.n1_weight
        
        # Penalize false N1 predictions
        pred_classes = torch.argmax(predictions, dim=-1)
        false_n1 = ((pred_classes == 1) & (targets != 1)).float()
        false_n1_penalty = (ce_losses * false_n1).mean() * 1.5
        
        return focal_loss + n1_penalty + false_n1_penalty


class BalancedLoss(nn.Module):
    """Balanced loss with moderate class weighting"""
    def __init__(self, class_weights=None):
        super().__init__()
        self.n1_loss = N1FocusedLoss(n1_weight=4.0, focal_gamma=2.5)
        self.temporal_loss = TemporalConsistencyLoss(weight=0.3)
        
        if class_weights is None:
            # Wake, N1, N2, N3, REM - moderate weights
            self.class_weights = torch.tensor([2.0, 4.0, 1.0, 1.5, 2.0])
        else:
            self.class_weights = class_weights
            
    def forward(self, predictions, targets):
        # N1-enhanced focal loss
        n1_focal = self.n1_loss(predictions, targets)
        
        # Temporal consistency
        temporal = self.temporal_loss(predictions)
        
        # Class-weighted cross entropy
        ce_loss = nn.CrossEntropyLoss(weight=self.class_weights.to(predictions.device))
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        ce = ce_loss(pred_flat, target_flat) * 0.2
        
        return n1_focal + temporal + ce


def create_v15_model(config, device):
    """Create V15 model with moderate scaling"""
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logging.info(f"Created V15 Model:")
    logging.info(f"  Total parameters: {total_params:,}")
    logging.info(f"  Trainable parameters: {trainable_params:,}")
    logging.info(f"  Model size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    return model


def analyze_performance(cm, per_class_metrics, target_acc=0.87, target_f1=0.80, target_kappa=0.80):
    """Analyze model performance with focus on weak classes"""
    
    logging.info("\n" + "="*60)
    logging.info("📊 DETAILED PERFORMANCE ANALYSIS")
    logging.info("="*60)
    
    # Overall metrics vs targets
    overall_acc = np.trace(cm) / np.sum(cm)
    
    # Per-class analysis
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("\nPer-Class Performance:")
    
    weakest_class = None
    weakest_f1 = 1.0
    
    for class_name in class_names:
        metrics = per_class_metrics.get(class_name, {})
        f1 = metrics.get('f1', 0)
        
        logging.info(f"\n{class_name}:")
        logging.info(f"  Precision: {metrics.get('precision', 0)*100:.2f}%")
        logging.info(f"  Recall: {metrics.get('recall', 0)*100:.2f}%")
        logging.info(f"  F1-Score: {f1*100:.2f}%")
        
        if f1 < weakest_f1:
            weakest_f1 = f1
            weakest_class = class_name
    
    logging.info(f"\n⚠️ Weakest class: {weakest_class} (F1: {weakest_f1*100:.2f}%)")
    
    # Confusion patterns
    logging.info("\nMajor Confusion Patterns:")
    for i, true_class in enumerate(class_names):
        for j, pred_class in enumerate(class_names):
            if i != j and cm[i, j] > 50:  # Significant confusion
                logging.info(f"  {true_class} → {pred_class}: {cm[i, j]} samples")
    
    return weakest_class, weakest_f1


def train_v15():
    """Main training function for V15"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v15_targeted_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V15 - TARGETED ENHANCEMENT")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥87%, Macro F1 ≥80%, Kappa ≥0.80")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V15 Configuration - moderate scaling
    config = {
        'd_model': 320,      # +25% from V14 (256)
        'n_heads': 20,       # +25% from V14 (16)
        'n_layers': 7,       # +17% from V14 (6)
        'dropout': 0.18,     # +20% from V14 (0.15)
        'seq_len': 6,        # +20% from V14 (5)
        'batch_size': 28,    # Slightly smaller
        'learning_rate': 5e-5,
        'num_epochs': 30,
        'patience': 8,
        'gradient_clip': 0.7,
        'weight_decay': 0.03,
        'warmup_epochs': 2
    }
    
    logging.info("\n📋 V15 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Use V14's data split
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    
    # Create datasets
    logging.info(f"\n📊 Loading datasets with seq_len={config['seq_len']}...")
    
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {train_dataset.total_epochs} epochs")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = create_v15_model(config, device)
    
    # Loss function
    criterion = BalancedLoss()
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # Scheduler
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=8,
        T_mult=2,
        eta_min=1e-6
    )
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        # Warmup
        if epoch < config['warmup_epochs']:
            warmup_lr = config['learning_rate'] * (epoch + 1) / config['warmup_epochs']
            for param_group in optimizer.param_groups:
                param_group['lr'] = warmup_lr
            logging.info(f"Warmup LR: {warmup_lr:.2e}")
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs, _ = model(data)
            
            loss = criterion(outputs, labels)
            
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Analyze performance
        weakest_class, weakest_f1 = analyze_performance(cm, val_metrics['per_class_metrics'])
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  Weakest: {weakest_class} (F1: {weakest_f1*100:.2f}%)")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.87:
            targets_met.append("ACC")
        if val_f1 >= 0.80:
            targets_met.append("F1")
        if val_kappa >= 0.80:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'weakest_class': weakest_class,
            'weakest_f1': weakest_f1,
            'time': epoch_time
        })
        
        # Scheduler step
        if epoch >= config['warmup_epochs']:
            scheduler.step()
        
        # Save best model
        if val_acc > best_val_acc or (val_acc == best_val_acc and val_f1 > best_val_f1):
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                # Early stop on success
                if epoch >= 10:  # At least 10 epochs
                    logging.info("  ✅ Stopping early - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (87% / 80% / 0.80):")
        success = True
        
        if test_acc >= 0.87:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.87")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.87 (gap: {0.87-test_acc:.4f})")
            success = False
        
        if test_f1 >= 0.80:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.80")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.80 (gap: {0.80-test_f1:.4f})")
            success = False
        
        if test_kappa >= 0.80:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.80")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.80 (gap: {0.80-test_kappa:.4f})")
            success = False
        
        # Per-class metrics
        logging.info("\n📊 Per-Class Performance:")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for class_name in class_names:
            metrics = test_metrics['per_class_metrics'][class_name]
            logging.info(f"\n{class_name}:")
            logging.info(f"  Precision: {metrics['precision']*100:.2f}%")
            logging.info(f"  Recall: {metrics['recall']*100:.2f}%")
            logging.info(f"  F1-Score: {metrics['f1']*100:.2f}%")
            logging.info(f"  Support: {metrics['support']}")
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': {
                'accuracy': test_acc >= 0.87,
                'macro_f1': test_f1 >= 0.80,
                'kappa': test_kappa >= 0.80,
                'all_targets': success
            }
        }
        
        results_file = os.path.join(log_dir, 'final_results.json')
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if success:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("Model V15 is ready for ICASSP 2026!")
            logging.info("="*80)
        else:
            logging.info("\n📈 Progress made. Continuing optimization...")
    
    return final_results


if __name__ == "__main__":
    results = train_v15()