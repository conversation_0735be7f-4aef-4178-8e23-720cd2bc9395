"""
序列MAMBAFORMER V4训练脚本 - 加权损失版本
目标：参考原版CMT，使用类别权重解决REM检测失败问题
策略：基于V3架构，添加加权损失
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v3 import (
    SequentialMAMBAFORMER_V3,
    TemporalConsistencyLoss,
    MildDataAugmentation
)
from utils.sequence_dataset import create_sequence_dataloaders
from utils.enhanced_metrics import get_comprehensive_metrics, log_detailed_metrics


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"sequential_v4_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


def create_auxiliary_labels(labels):
    """创建辅助任务标签"""
    aux_labels = ((labels == 4) | (labels == 3)).long()
    return aux_labels


def train_epoch_v4(model, train_loader, criterion, temp_loss_fn, aux_criterion,
                   optimizer, device, epoch, config, data_aug):
    """V4训练函数 - 使用加权损失"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device)
        labels = labels.to(device)
        
        # 数据增强
        data, labels = data_aug(data, labels)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_output, aux_output = model(data)
        
        # 损失计算
        main_loss = criterion(main_output, labels)
        
        # 辅助损失
        aux_labels = create_auxiliary_labels(labels)
        aux_loss = aux_criterion(
            aux_output.view(-1, 2), 
            aux_labels.view(-1)
        )
        
        # 时序一致性损失
        temp_loss = temp_loss_fn(main_output)
        
        # 总损失
        loss = main_loss + 0.2 * aux_loss + temp_loss
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1


def evaluate_v4(model, data_loader, criterion, aux_criterion, device, phase='Val'):
    """V4评估函数"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    seq_preds = []
    seq_labels = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc=f'{phase}')
        
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            main_output, aux_output = model(data)
            
            # 损失计算
            main_loss = criterion(main_output, labels)
            aux_labels = create_auxiliary_labels(labels)
            aux_loss = aux_criterion(
                aux_output.view(-1, 2),
                aux_labels.view(-1)
            )
            loss = main_loss + 0.2 * aux_loss
            
            total_loss += loss.item()
            
            # 收集预测
            preds = torch.argmax(main_output, dim=-1)
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            seq_preds.append(preds.cpu().numpy())
            seq_labels.append(labels.cpu().numpy())
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    # 位置准确率
    seq_preds = np.concatenate(seq_preds, axis=0)
    seq_labels = np.concatenate(seq_labels, axis=0)
    
    position_acc = []
    for pos in range(seq_preds.shape[1]):
        pos_acc = accuracy_score(seq_labels[:, pos], seq_preds[:, pos])
        position_acc.append(pos_acc)
    
    avg_loss = total_loss / len(data_loader)
    
    # 计算综合指标
    metrics = get_comprehensive_metrics(
        np.array(all_labels), 
        np.array(all_preds), 
        n_classes=5
    )
    
    # 详细分析
    if phase == 'Test':
        report = classification_report(all_labels, all_preds, 
                                     target_names=['Wake', 'N1', 'N2', 'N3', 'REM'],
                                     output_dict=True)
        cm = confusion_matrix(all_labels, all_preds)
        return avg_loss, acc, f1, position_acc, report, cm, metrics
    
    return avg_loss, acc, f1, position_acc, metrics


def train_fold_v4(fold_id, fold_info, config, device):
    """V4 fold训练 - 加权损失版本"""
    logging.info(f"\n{'='*60}")
    logging.info(f"🚀 开始训练 Sequential MAMBAFORMER V4 - Fold {fold_id + 1}")
    logging.info(f"📋 参考原版CMT，使用加权损失解决REM检测问题")
    logging.info(f"训练文件数: {len(fold_info['train_files'])}")
    logging.info(f"测试文件数: {len(fold_info['test_files'])}")
    
    # 创建数据加载器 - 使用4通道数据
    train_loader, val_loader, test_loader = create_sequence_dataloaders(
        fold_info,
        batch_size=config['batch_size'],
        seq_len=config['seq_len'],
        use_channels=4,  # 多模态
        max_samples_per_file=config['max_samples_per_file']
    )
    
    # 验证集划分
    train_dataset = train_loader.dataset
    total_size = len(train_dataset)
    val_size = int(0.25 * total_size)
    train_size = total_size - val_size
    
    train_subset, val_subset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # 重新创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_subset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_subset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    logging.info(f"📊 数据分布:")
    logging.info(f"  训练批次数: {len(train_loader)} (样本数: {train_size})")
    logging.info(f"  验证批次数: {len(val_loader)} (样本数: {val_size})")
    logging.info(f"  测试批次数: {len(test_loader)}")
    
    # 创建V4模型 - 基于V3架构
    model = SequentialMAMBAFORMER_V3(
        input_channels=4,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'], 
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 损失函数 - 使用原版CMT的权重
    weights = torch.tensor(config['class_weights']).to(device)
    criterion = nn.CrossEntropyLoss(weight=weights)
    aux_criterion = nn.CrossEntropyLoss()
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    logging.info(f"📊 使用类别权重: {config['class_weights']} (对应 [Wake, N1, N2, N3, REM])")
    
    # 数据增强
    data_aug = MildDataAugmentation(p=0.5)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 3,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos'
    )
    
    # 训练循环
    best_val_f1 = 0
    patience_counter = 0
    best_model_state = None
    train_losses = []
    val_losses = []
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch_v4(
            model, train_loader, criterion, temp_loss_fn, aux_criterion,
            optimizer, device, epoch, config, data_aug
        )
        
        # 验证
        val_loss, val_acc, val_f1, val_pos_acc, val_metrics = evaluate_v4(
            model, val_loader, criterion, aux_criterion, device, 'Val'
        )
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step()
        
        # 记录 - 包含Kappa和Macro-F1
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, F1: {train_f1:.4f}")
        logging.info(f"Val   - Loss: {val_loss:.4f}, Acc: {val_acc:.4f}, F1: {val_f1:.4f}, Kappa: {val_metrics['kappa']:.4f}, Macro-F1: {val_metrics['macro_f1']:.4f}")
        logging.info(f"Val Position Acc: {[f'{acc:.4f}' for acc in val_pos_acc]}")
        logging.info(f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # 记录每类验证指标
        if epoch % 5 == 0:
            logging.info("Val Per-Class F1:")
            for class_name, metrics in val_metrics['per_class_metrics'].items():
                logging.info(f"  {class_name}: F1={metrics['f1']:.3f}")
        
        # 过拟合监控
        if len(train_losses) > 3:
            recent_train_loss = np.mean(train_losses[-3:])
            recent_val_loss = np.mean(val_losses[-3:])
            gap = recent_val_loss - recent_train_loss
            if gap > 0.15:
                logging.warning(f"⚠️  过拟合警告: Gap = {gap:.4f}")
        
        # 保存最佳模型
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳Val F1: {best_val_f1:.4f}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: 验证F1已{config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
        logging.info("✅ 已加载最佳模型进行测试")
    
    test_loss, test_acc, test_f1, test_pos_acc, report, cm, test_metrics = evaluate_v4(
        model, test_loader, criterion, aux_criterion, device, 'Test'
    )
    
    # 详细测试结果输出
    logging.info(f"\n🎯 Sequential V4 - Fold {fold_id + 1} 完整测试结果:")
    logging.info(f"="*60)
    logging.info(f"测试准确率: {test_acc:.4f}")
    logging.info(f"测试F1分数: {test_f1:.4f}")
    logging.info(f"测试Kappa系数: {test_metrics['kappa']:.4f}")
    logging.info(f"测试Macro-F1: {test_metrics['macro_f1']:.4f}")
    logging.info(f"测试G-Mean: {test_metrics['g_mean']:.4f}")
    logging.info(f"最佳验证F1: {best_val_f1:.4f}")
    logging.info(f"验证-测试F1差距: {best_val_f1 - test_f1:.4f}")
    logging.info(f"各位置准确率: {[f'{acc:.4f}' for acc in test_pos_acc]}")
    
    # 记录详细的每类指标
    log_detailed_metrics(test_metrics, phase='Test', logger=logging)
    
    # 特别关注REM类别
    rem_metrics = test_metrics['per_class_metrics']['REM']
    logging.info(f"\n🔍 REM类别特别分析:")
    logging.info(f"  REM F1: {rem_metrics['f1']:.4f}")
    logging.info(f"  REM Precision: {rem_metrics['precision']:.4f}")
    logging.info(f"  REM Recall: {rem_metrics['sensitivity']:.4f}")
    logging.info(f"  REM Support: {rem_metrics['support']}")
    
    if rem_metrics['f1'] > 0.3:
        logging.info("✅ 成功：REM检测得到改善！")
    else:
        logging.info("⚠️  REM检测仍需优化")
    
    # 版本对比
    logging.info(f"\n📈 版本对比:")
    logging.info(f"  V2: REM F1=0%, 无过拟合")
    logging.info(f"  V3: REM F1=0%, 轻微过拟合")
    logging.info(f"  V4: REM F1={rem_metrics['f1']:.1%}, 加权损失")
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), f'../../checkpoints/sequential_v4_fold_{fold_id}.pth')
    
    return {
        'fold_id': fold_id,
        'test_acc': test_acc,
        'test_f1': test_f1,
        'test_kappa': test_metrics['kappa'],
        'test_macro_f1': test_metrics['macro_f1'],
        'rem_f1': rem_metrics['f1'],
        'position_acc': test_pos_acc,
        'val_f1': best_val_f1,
        'val_test_gap': best_val_f1 - test_f1,
        'confusion_matrix': cm.tolist(),
        'classification_report': report
    }


def main():
    # 配置 - 基于V3，添加类别权重
    config = {
        'batch_size': 32,
        'seq_len': 5,
        'learning_rate': 2e-5,
        'weight_decay': 1e-4,
        'num_epochs': 50,
        'patience': 8,
        'max_samples_per_file': 150,
        'd_model': 128,
        'n_heads': 8,
        'n_layers': 4,
        'dropout': 0.15,
        'temp_loss_weight': 0.1,
        'class_weights': [1.0, 2.0, 1.0, 2.0, 2.0]  # 原版CMT的权重
    }
    
    log_file = setup_logging()
    
    logging.info("📈 序列MAMBAFORMER V4训练 - 加权损失版本")
    logging.info("=" * 60)
    logging.info("🎯 V4策略:")
    logging.info("  • 基于V3的多模态架构")
    logging.info("  • 参考原版CMT使用类别权重")
    logging.info("  • 重点解决REM检测失败问题")
    logging.info("  • 权重: [1.0, 2.0, 1.0, 2.0, 2.0] for [Wake, N1, N2, N3, REM]")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    with open('../../configs/subject_aware_folds.json', 'r') as f:
        fold_data = json.load(f)
    
    all_results = []
    
    for fold_id, fold_info in enumerate(fold_data['folds'].values()):
        if fold_id >= 1:  # 先测试一个fold
            break
        
        result = train_fold_v4(fold_id, fold_info, config, device)
        all_results.append(result)
    
    # 汇总结果
    avg_acc = np.mean([r['test_acc'] for r in all_results])
    avg_f1 = np.mean([r['test_f1'] for r in all_results])
    avg_kappa = np.mean([r['test_kappa'] for r in all_results])
    avg_macro_f1 = np.mean([r['test_macro_f1'] for r in all_results])
    avg_rem_f1 = np.mean([r['rem_f1'] for r in all_results])
    avg_gap = np.mean([r['val_test_gap'] for r in all_results])
    
    logging.info("\n" + "="*60)
    logging.info("🏆 序列MAMBAFORMER V4训练完成")
    logging.info(f"📈 平均测试准确率: {avg_acc:.4f}")
    logging.info(f"📈 平均测试F1分数: {avg_f1:.4f}")
    logging.info(f"📈 平均测试Kappa: {avg_kappa:.4f}")
    logging.info(f"📈 平均测试Macro-F1: {avg_macro_f1:.4f}")
    logging.info(f"📈 平均REM F1: {avg_rem_f1:.4f}")
    logging.info(f"📊 平均验证-测试F1差距: {avg_gap:.4f}")
    
    results = {
        'config': config,
        'fold_results': all_results,
        'summary': {
            'mean_accuracy': avg_acc,
            'mean_f1_score': avg_f1,
            'mean_kappa': avg_kappa,
            'mean_macro_f1': avg_macro_f1,
            'mean_rem_f1': avg_rem_f1,
            'mean_val_test_gap': avg_gap,
            'log_file': log_file
        }
    }
    
    with open('../../configs/sequential_v4_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"💾 结果已保存至: ../../configs/sequential_v4_results.json")


if __name__ == "__main__":
    main()