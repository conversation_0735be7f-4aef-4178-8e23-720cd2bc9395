#!/usr/bin/env python3
"""
V17 MAMBAFORMER - Stable improvement from V14
Conservative approach to reach 87% accuracy
Target: 87% Accuracy, 80% Macro F1, 0.80 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import ReduceLROnPlateau
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class REMFocusedLoss(nn.Module):
    """Loss function with REM and N1 focus - proven in V14"""
    def __init__(self, rem_weight=3.0, wake_weight=5.0, n1_extra=2.0):
        super().__init__()
        self.rem_weight = rem_weight
        self.wake_weight = wake_weight
        self.n1_extra = n1_extra
        
    def forward(self, predictions, targets):
        batch_size, seq_len, n_classes = predictions.shape
        
        # Base cross entropy
        ce_loss = nn.CrossEntropyLoss(reduction='none')
        pred_flat = predictions.reshape(-1, n_classes)
        target_flat = targets.reshape(-1)
        
        losses = ce_loss(pred_flat, target_flat)
        losses = losses.reshape(batch_size, seq_len)
        
        # Apply class-specific weights
        weights = torch.ones_like(targets, dtype=torch.float32)
        weights[targets == 0] = self.wake_weight  # Wake
        weights[targets == 1] = self.n1_extra     # N1
        weights[targets == 4] = self.rem_weight   # REM
        
        weighted_loss = (losses * weights).mean()
        
        return weighted_loss


class TemporalConsistencyLoss(nn.Module):
    """Temporal consistency loss - proven effective"""
    def __init__(self, weight=0.2):
        super().__init__()
        self.weight = weight
        
    def forward(self, predictions):
        if predictions.shape[1] <= 1:
            return 0
        
        # Compute difference between consecutive predictions
        pred_probs = torch.softmax(predictions, dim=-1)
        temporal_diff = torch.abs(pred_probs[:, 1:] - pred_probs[:, :-1])
        
        # Penalize large changes
        temporal_loss = temporal_diff.mean() * self.weight
        
        return temporal_loss


class CombinedLoss(nn.Module):
    """Combined loss - stable version"""
    def __init__(self):
        super().__init__()
        self.rem_loss = REMFocusedLoss(rem_weight=3.0, wake_weight=5.0, n1_extra=2.0)
        self.temporal_loss = TemporalConsistencyLoss(weight=0.2)
        
    def forward(self, predictions, targets):
        rem_focused = self.rem_loss(predictions, targets)
        temporal = self.temporal_loss(predictions)
        
        return rem_focused + temporal


def train_v17():
    """Main training function for V17"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v17_stable_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V17 - STABLE IMPROVEMENT")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥87%, Macro F1 ≥80%, Kappa ≥0.80")
    logging.info("Strategy: Conservative scaling from V14's 86.35% baseline")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V17 Configuration - conservative scaling from V14
    config = {
        'd_model': 288,      # +12.5% from V14 (256)
        'n_heads': 18,       # +12.5% from V14 (16)
        'n_layers': 6,       # Same as V14 (stable)
        'dropout': 0.15,     # Same as V14 (proven)
        'seq_len': 5,        # Same as V14 (proven)
        'batch_size': 32,    # Same as V14
        'learning_rate': 1e-4,  # Same as V14
        'num_epochs': 50,    # More epochs
        'patience': 15,      # More patience
        'gradient_clip': 1.0,
        'weight_decay': 0.01,
        'min_lr': 1e-6
    }
    
    logging.info("\n📋 V17 Configuration (Conservative Scaling):")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Use V14's exact data split
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Data Split (Same as V14):")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    
    # Create datasets
    logging.info("\n📊 Loading datasets...")
    
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {train_dataset.total_epochs} epochs")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Try to load V14 weights as initialization
    v14_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/checkpoints/mambaformer_v14_20250809_184458_best.pth'
    if os.path.exists(v14_path):
        try:
            checkpoint = torch.load(v14_path)
            # Load what we can
            model_dict = model.state_dict()
            pretrained_dict = {}
            
            for k, v in checkpoint['model_state_dict'].items():
                if k in model_dict:
                    if model_dict[k].shape == v.shape:
                        pretrained_dict[k] = v
                    else:
                        logging.info(f"  Shape mismatch for {k}: {v.shape} vs {model_dict[k].shape}")
            
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)
            logging.info(f"✅ Loaded {len(pretrained_dict)}/{len(model_dict)} weights from V14")
        except Exception as e:
            logging.info(f"⚠️ Could not load V14 weights: {e}")
    
    # Loss and optimizer
    criterion = CombinedLoss()
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # Scheduler
    scheduler = ReduceLROnPlateau(
        optimizer,
        mode='max',
        factor=0.5,
        patience=5,
        min_lr=config['min_lr']
    )
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs, _ = model(data)
            
            loss = criterion(outputs, labels)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Get per-class performance
        n1_f1 = val_metrics['per_class_metrics']['N1']['f1']
        rem_f1 = val_metrics['per_class_metrics']['REM']['f1']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} | REM F1: {rem_f1:.4f}")
        logging.info(f"  LR: {optimizer.param_groups[0]['lr']:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.87:
            targets_met.append("ACC")
        if val_f1 >= 0.80:
            targets_met.append("F1")
        if val_kappa >= 0.80:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'n1_f1': n1_f1,
            'rem_f1': rem_f1,
            'lr': optimizer.param_groups[0]['lr'],
            'time': epoch_time
        })
        
        # Scheduler step
        scheduler.step(val_acc)
        
        # Save best model
        improved = False
        if val_acc > best_val_acc:
            improved = True
        elif val_acc == best_val_acc and val_f1 > best_val_f1:
            improved = True
        
        if improved:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                if epoch >= 10:  # At least 10 epochs
                    logging.info("  ✅ Stopping - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (87% / 80% / 0.80):")
        all_achieved = True
        
        if test_acc >= 0.87:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.87")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.87 (gap: {0.87-test_acc:.4f})")
            all_achieved = False
        
        if test_f1 >= 0.80:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.80")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.80 (gap: {0.80-test_f1:.4f})")
            all_achieved = False
        
        if test_kappa >= 0.80:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.80")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.80 (gap: {0.80-test_kappa:.4f})")
            all_achieved = False
        
        # Per-class performance
        logging.info("\n📊 Per-Class Performance:")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for class_name in class_names:
            metrics = test_metrics['per_class_metrics'][class_name]
            logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1']*100:.1f}%")
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': all_achieved
        }
        
        results_file = os.path.join(log_dir, 'final_results.json')
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if all_achieved:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V17 Model Ready for ICASSP 2026!")
            logging.info("="*80)
        else:
            logging.info("\n📈 Training complete. Continuing optimization...")
    
    return final_results


if __name__ == "__main__":
    results = train_v17()