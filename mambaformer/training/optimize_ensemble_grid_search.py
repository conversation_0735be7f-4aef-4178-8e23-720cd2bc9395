#!/usr/bin/env python3
"""
Grid Search Optimization for Ensemble
Current best: 87.23% (V20 ensemble)
Target: 90% test accuracy through optimal weight search
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
from itertools import product
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator

def load_model(checkpoint_path, device, config):
    """Load a trained model from checkpoint"""
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=0.0,
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model

def enhanced_tta(model, data, device):
    """Enhanced Test-Time Augmentation with optimal settings"""
    predictions = []
    
    with torch.no_grad():
        # Original - highest weight
        outputs, _ = model(data)
        predictions.append(torch.softmax(outputs, dim=-1) * 1.5)
        
        # Small noise augmentations
        for noise_level in [0.005, 0.01, 0.015]:
            noise = torch.randn_like(data) * noise_level
            outputs, _ = model(data + noise)
            predictions.append(torch.softmax(outputs, dim=-1))
        
        # Tiny scaling
        for scale in [0.995, 1.005]:
            outputs, _ = model(data * scale)
            predictions.append(torch.softmax(outputs, dim=-1))
    
    return torch.stack(predictions).mean(dim=0)

def smart_post_processing(predictions, confidence_threshold=0.85):
    """Smart post-processing with high-confidence preservation"""
    processed = predictions.copy()
    n_epochs = len(predictions)
    
    # Only smooth low-confidence predictions
    for i in range(1, n_epochs - 1):
        max_conf = np.max(predictions[i])
        
        if max_conf < confidence_threshold:
            # Use small window for smoothing
            window_start = max(0, i - 1)
            window_end = min(n_epochs, i + 2)
            window = predictions[window_start:window_end]
            
            # Distance-based weights
            if len(window) == 3:
                weights = np.array([0.3, 0.4, 0.3])
            elif len(window) == 2:
                weights = np.array([0.5, 0.5])
            else:
                weights = np.array([1.0])
            
            weights = weights[:len(window)]
            processed[i] = np.average(window, axis=0, weights=weights)
    
    return processed

def grid_search_weights(all_model_probs, labels, model_names):
    """Grid search for optimal ensemble weights"""
    n_models = len(all_model_probs)
    
    # Define weight grid (must sum to 1)
    if n_models == 2:
        weight_options = [
            [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55],
            [0.5, 0.5], [0.55, 0.45], [0.6, 0.4], [0.65, 0.35], [0.7, 0.3]
        ]
    elif n_models == 3:
        weight_options = []
        for w1 in np.arange(0.2, 0.6, 0.05):
            for w2 in np.arange(0.2, 0.6, 0.05):
                w3 = 1.0 - w1 - w2
                if w3 >= 0.2 and w3 <= 0.6:
                    weight_options.append([w1, w2, w3])
    else:
        # For 4+ models, use fewer options
        weight_options = []
        step = 0.1
        for weights in product(*[np.arange(0.1, 0.5, step) for _ in range(n_models-1)]):
            weights = list(weights)
            last_weight = 1.0 - sum(weights)
            if last_weight > 0 and last_weight < 0.6:
                weight_options.append(weights + [last_weight])
    
    best_acc = 0
    best_weights = None
    best_f1 = 0
    best_kappa = 0
    
    print(f"Testing {len(weight_options)} weight combinations...")
    
    for weights in weight_options:
        # Ensure weights sum to 1
        weights = np.array(weights)
        weights = weights / weights.sum()
        
        # Compute weighted ensemble
        ensemble_probs = sum(prob * w for prob, w in zip(all_model_probs, weights))
        ensemble_preds = np.argmax(ensemble_probs, axis=-1)
        
        # Calculate metrics
        acc = accuracy_score(labels, ensemble_preds)
        
        if acc > best_acc:
            best_acc = acc
            best_weights = weights
            best_f1 = f1_score(labels, ensemble_preds, average='macro')
            best_kappa = cohen_kappa_score(labels, ensemble_preds)
    
    return best_weights, best_acc, best_f1, best_kappa

def optimize_ensemble():
    """Main optimization function"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/optimized_ensemble_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'optimization.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🔍 GRID SEARCH ENSEMBLE OPTIMIZATION")
    logging.info("📊 Current Best: 87.23% (V20 Ensemble)")
    logging.info("🎯 Target: 90% Test Accuracy")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Best performing models (excluding V14 since checkpoint not found)
    models_config = [
        {
            'name': 'V17_Stable',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth',
            'd_model': 288,
            'n_heads': 18,
            'n_layers': 6,
            'seq_len': 5
        },
        {
            'name': 'V18_Fixed',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v18_fixed_20250811_014911/best_model.pth',
            'd_model': 384,
            'n_heads': 24,
            'n_layers': 7,
            'seq_len': 6
        },
        {
            'name': 'V22_Deep',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v22_deep_20250811_030913/best_model.pth',
            'd_model': 448,
            'n_heads': 28,
            'n_layers': 10,
            'seq_len': 9
        }
    ]
    
    # Test data
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Load models and get predictions
    all_model_probs = []
    all_labels = None
    model_names = []
    
    for config in models_config:
        logging.info(f"\nLoading {config['name']}...")
        
        try:
            model = load_model(config['path'], device, config)
            model_names.append(config['name'])
            
            # Create dataset
            test_dataset = SequenceSleepDataset(
                test_files,
                seq_len=config['seq_len'],
                use_channels=3,
                max_samples_per_file=None
            )
            
            test_loader = DataLoader(
                test_dataset,
                batch_size=32,
                shuffle=False,
                num_workers=4,
                pin_memory=True
            )
            
            # Get predictions with TTA
            evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
            evaluator.total_epochs = test_dataset.total_epochs
            
            with torch.no_grad():
                batch_start_idx = 0
                for data, labels in tqdm(test_loader, desc=f'Processing {config["name"]}'):
                    data = data.to(device)
                    
                    # Apply enhanced TTA
                    probs = enhanced_tta(model, data, device)
                    
                    batch_size = data.shape[0]
                    start_indices = []
                    
                    for i in range(batch_size):
                        seq_idx = batch_start_idx + i
                        if seq_idx < len(test_dataset):
                            seq_info = test_dataset.get_sequence_info(seq_idx)
                            if seq_info:
                                start_indices.append(seq_info['start_epoch_idx'])
                    
                    if start_indices:
                        valid_batch_size = len(start_indices)
                        evaluator.add_batch_predictions(
                            probs[:valid_batch_size].cpu().numpy(),
                            labels[:valid_batch_size].cpu().numpy(),
                            start_indices
                        )
                    
                    batch_start_idx += batch_size
            
            # Get final predictions
            _, final_labels, final_probs = evaluator.get_final_predictions()
            all_model_probs.append(final_probs)
            
            if all_labels is None:
                all_labels = final_labels
            
            # Individual performance
            metrics = evaluator.evaluate()
            logging.info(f"  {config['name']}: Acc={metrics['accuracy']:.4f}, F1={metrics['macro_f1']:.4f}")
            
        except Exception as e:
            logging.error(f"Error loading {config['name']}: {e}")
    
    if len(all_model_probs) < 2:
        logging.error("Need at least 2 models for ensemble!")
        return None
    
    logging.info("\n" + "="*80)
    logging.info("OPTIMIZATION STRATEGIES:")
    logging.info("="*80)
    
    # Strategy 1: Grid Search for optimal weights
    logging.info("\n1. Grid Search Optimization...")
    best_weights, best_acc, best_f1, best_kappa = grid_search_weights(
        all_model_probs, all_labels, model_names
    )
    
    logging.info(f"\n   Best Weights Found:")
    for name, weight in zip(model_names, best_weights):
        logging.info(f"     {name}: {weight:.3f}")
    logging.info(f"   Accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")
    logging.info(f"   Macro F1: {best_f1:.4f}")
    logging.info(f"   Kappa: {best_kappa:.4f}")
    
    # Strategy 2: Grid Search + Post-Processing
    logging.info("\n2. Grid Search + Smart Post-Processing...")
    
    # Apply best weights
    ensemble_probs = sum(prob * w for prob, w in zip(all_model_probs, best_weights))
    
    # Apply smart post-processing
    processed_probs = np.array([
        smart_post_processing(ensemble_probs[i])
        for i in range(len(ensemble_probs))
    ])
    processed_preds = np.argmax(processed_probs, axis=-1)
    
    pp_acc = accuracy_score(all_labels, processed_preds)
    pp_f1 = f1_score(all_labels, processed_preds, average='macro')
    pp_kappa = cohen_kappa_score(all_labels, processed_preds)
    
    logging.info(f"   Accuracy: {pp_acc:.4f} ({pp_acc*100:.2f}%)")
    logging.info(f"   Macro F1: {pp_f1:.4f}")
    logging.info(f"   Kappa: {pp_kappa:.4f}")
    
    # Strategy 3: Stacking with meta-learner simulation
    logging.info("\n3. Pseudo-Stacking (confidence-weighted)...")
    
    # Simulate a meta-learner by using confidence scores
    stacked_preds = []
    for i in range(len(all_labels)):
        # Get each model's prediction and confidence
        votes = {}
        for j, probs in enumerate(all_model_probs):
            pred = np.argmax(probs[i])
            conf = np.max(probs[i])
            
            # Weight by both grid search weight and confidence
            weight = best_weights[j] * (0.5 + 0.5 * conf)  # Confidence bonus
            
            if pred not in votes:
                votes[pred] = 0
            votes[pred] += weight
        
        # Select highest weighted vote
        stacked_preds.append(max(votes.keys(), key=lambda k: votes[k]))
    
    stacked_preds = np.array(stacked_preds)
    
    stack_acc = accuracy_score(all_labels, stacked_preds)
    stack_f1 = f1_score(all_labels, stacked_preds, average='macro')
    stack_kappa = cohen_kappa_score(all_labels, stacked_preds)
    
    logging.info(f"   Accuracy: {stack_acc:.4f} ({stack_acc*100:.2f}%)")
    logging.info(f"   Macro F1: {stack_f1:.4f}")
    logging.info(f"   Kappa: {stack_kappa:.4f}")
    
    # Find best overall strategy
    all_accs = [best_acc, pp_acc, stack_acc]
    all_names = ["Grid Search", "Grid + Post-Processing", "Pseudo-Stacking"]
    
    final_best_acc = max(all_accs)
    final_best_name = all_names[all_accs.index(final_best_acc)]
    
    logging.info("\n" + "="*80)
    logging.info(f"🏆 BEST STRATEGY: {final_best_name}")
    logging.info(f"   Test Accuracy: {final_best_acc:.4f} ({final_best_acc*100:.2f}%)")
    
    # Compare with previous best
    improvement = final_best_acc - 0.8723
    logging.info(f"\n📊 Comparison with V20 Ensemble (87.23%):")
    if improvement > 0:
        logging.info(f"   ✅ Improvement: +{improvement*100:.2f}%")
    else:
        logging.info(f"   ❌ No improvement: {improvement*100:.2f}%")
    
    # Check if we reached target
    if final_best_acc >= 0.90:
        logging.info("\n🎉🎉🎉 SUCCESS! 90% TARGET ACHIEVED! 🎉🎉🎉")
        logging.info("Ready for ICASSP 2026!")
    else:
        gap = 0.90 - final_best_acc
        logging.info(f"\n📈 Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
        
        # Suggest next steps
        logging.info("\n🔮 Suggested Next Steps:")
        logging.info("   1. Train more diverse models (different architectures)")
        logging.info("   2. Use validation set for pseudo-labeling")
        logging.info("   3. Implement true stacking with a meta-learner")
        logging.info("   4. Fine-tune on difficult classes (N1)")
        logging.info("   5. Collect more training data")
    
    logging.info("="*80)
    
    # Save results
    results = {
        'timestamp': timestamp,
        'models': model_names,
        'grid_search': {
            'best_weights': best_weights.tolist(),
            'accuracy': float(best_acc),
            'f1': float(best_f1),
            'kappa': float(best_kappa)
        },
        'with_post_processing': {
            'accuracy': float(pp_acc),
            'f1': float(pp_f1),
            'kappa': float(pp_kappa)
        },
        'pseudo_stacking': {
            'accuracy': float(stack_acc),
            'f1': float(stack_f1),
            'kappa': float(stack_kappa)
        },
        'best_strategy': final_best_name,
        'best_accuracy': float(final_best_acc),
        'improvement_over_v20': float(improvement),
        'target_achieved': final_best_acc >= 0.90
    }
    
    results_file = os.path.join(log_dir, 'optimization_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    return results


if __name__ == "__main__":
    results = optimize_ensemble()