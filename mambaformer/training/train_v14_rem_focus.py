"""
MAMBAFORMER V14 - REM-Focused Training
Target: ACC=87%, Kappa=0.8, MF1=80%

Key Strategy: Fix REM classification which is the main bottleneck
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import (
    SequentialMAMBAFORMER_V2,
    TemporalConsistencyLoss
)
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"v14_rem_focus_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


class REMFocusedLoss(nn.Module):
    """专注于改善REM分类的损失函数"""
    def __init__(self, alpha=None, gamma=2.0, rem_weight=3.0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.rem_weight = rem_weight  # REM类的额外权重
        self.ce_loss = nn.CrossEntropyLoss(reduction='none')
        
    def forward(self, input, target):
        # 基础CE损失
        ce_loss = self.ce_loss(input, target)
        
        # Focal损失
        p = torch.exp(-ce_loss)
        focal_loss = (1 - p) ** self.gamma * ce_loss
        
        # 类别权重
        if self.alpha is not None:
            if self.alpha.dim() > 1:
                alpha = self.alpha.view(-1)
            else:
                alpha = self.alpha
            alpha_t = alpha.gather(0, target)
            focal_loss = alpha_t * focal_loss
        
        # REM类额外加权 (class 0)
        rem_mask = (target == 0).float()
        focal_loss = focal_loss * (1 + rem_mask * (self.rem_weight - 1))
        
        return focal_loss.mean()


class REMDataAugmentation:
    """专门针对REM类的数据增强"""
    def __init__(self):
        self.noise_level = 0.03
        self.shift_range = 30
        
    def __call__(self, data, labels):
        # 找出REM样本
        rem_mask = (labels == 0)
        
        if rem_mask.any():
            # 对REM样本添加轻微噪声
            noise = torch.randn_like(data) * self.noise_level
            data = data + noise * rem_mask.unsqueeze(-1).unsqueeze(-1).float()
            
            # 时间轴微调
            if np.random.random() < 0.3:
                shift = np.random.randint(-self.shift_range, self.shift_range)
                data[rem_mask] = torch.roll(data[rem_mask], shifts=shift, dims=-1)
        
        return data, labels


def train_epoch_v14(model, train_loader, criterion, temp_loss_fn, optimizer, 
                   device, epoch, config, data_aug):
    """V14训练函数 - REM专注"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    # REM类专门统计
    rem_correct = 0
    rem_total = 0
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device)
        labels = labels.to(device)
        
        # 数据增强
        if data_aug and epoch > 3:  # 前3个epoch不增强
            data, labels = data_aug(data, labels)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs, _ = model(data)
        
        # 确保维度正确
        if outputs.dim() == 3:
            batch_size, seq_len, n_classes = outputs.shape
            outputs = outputs.view(-1, n_classes)
        
        labels_flat = labels.view(-1)
        
        # 计算损失
        main_loss = criterion(outputs, labels_flat)
        temp_loss = temp_loss_fn(outputs) * config['temp_loss_weight']
        
        # REM类正则化损失 - 鼓励REM预测更自信
        rem_mask = (labels_flat == 0)
        if rem_mask.any():
            rem_outputs = outputs[rem_mask]
            rem_probs = torch.softmax(rem_outputs, dim=-1)
            # 鼓励REM类概率更高
            rem_confidence_loss = -torch.log(rem_probs[:, 0] + 1e-8).mean()
            loss = main_loss + temp_loss + 0.1 * rem_confidence_loss
        else:
            loss = main_loss + temp_loss
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(outputs, dim=-1)
        all_preds.extend(preds.cpu().numpy())
        all_labels.extend(labels_flat.cpu().numpy())
        
        # REM类统计
        rem_preds = preds[rem_mask]
        rem_correct += (rem_preds == 0).sum().item()
        rem_total += rem_mask.sum().item()
        
        # 更新进度条
        rem_acc = rem_correct / rem_total if rem_total > 0 else 0
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'REM_acc': f'{rem_acc:.3f}'
        })
    
    # 计算整体指标
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    kappa = cohen_kappa_score(all_labels, all_preds)
    
    # 计算每类F1
    per_class_f1 = f1_score(all_labels, all_preds, average=None)
    
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1, kappa, per_class_f1


def evaluate_epoch_level_v14(model, test_dataset, test_loader, device, seq_len=5):
    """V14评估函数"""
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=seq_len, n_classes=5)
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (data, labels) in enumerate(tqdm(test_loader, desc="Evaluating")):
            data = data.to(device)
            labels = labels.to(device)
            
            # 获取模型输出
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取序列信息
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        start_indices.append(seq_idx)
                else:
                    break
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    return evaluator.evaluate()


def calculate_class_weights(train_loader, device, rem_boost=2.0):
    """计算类别权重，特别增强REM类"""
    all_labels = []
    for _, labels in train_loader:
        all_labels.extend(labels.numpy().flatten())
    
    class_counts = np.bincount(all_labels, minlength=5)
    total_samples = len(all_labels)
    
    # 计算基础权重
    class_weights = total_samples / (5 * class_counts + 1)
    
    # 特别增强REM类权重
    class_weights[0] *= rem_boost
    
    # 归一化
    class_weights = class_weights / class_weights.mean()
    
    logging.info(f"类别分布: {class_counts}")
    logging.info(f"类别权重 (REM增强): {class_weights}")
    
    return torch.tensor(class_weights, dtype=torch.float32).to(device)


def train_v14(config, device):
    """V14训练主函数 - REM专注"""
    logging.info("\n" + "="*80)
    logging.info("🚀 开始训练 MAMBAFORMER V14 - REM专注策略")
    logging.info(f"🎯 目标: ACC=87%, Kappa=0.8, MF1=80%")
    logging.info(f"💡 核心策略: 修复REM分类问题 (当前F1仅19-25%)")
    
    # 加载数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 创建数据集
    train_dataset = SequenceSleepDataset(train_files, seq_len=config['seq_len'], use_channels=3)
    val_dataset = SequenceSleepDataset(val_files, seq_len=config['seq_len'], use_channels=3)
    test_dataset = SequenceSleepDataset(test_files, seq_len=config['seq_len'], use_channels=3)
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], shuffle=True,
        num_workers=config['num_workers'], pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], shuffle=False,
        num_workers=config['num_workers'], pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], shuffle=False,
        num_workers=config['num_workers'], pin_memory=True
    )
    
    logging.info(f"📊 数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 计算类别权重 - 特别增强REM
    class_weights = calculate_class_weights(train_loader, device, rem_boost=2.5)
    
    # 创建模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"模型参数量: {total_params:,}")
    
    # 损失函数 - REM专注
    criterion = REMFocusedLoss(alpha=class_weights, gamma=2.5, rem_weight=3.0)
    temp_loss_fn = TemporalConsistencyLoss(weight=1.0)
    
    # 优化器 - 使用不同的学习率
    optimizer = optim.AdamW([
        {'params': model.parameters(), 'lr': config['learning_rate']},
    ], weight_decay=config['weight_decay'], betas=(0.9, 0.999))
    
    # 学习率调度器
    scheduler = CosineAnnealingWarmRestarts(
        optimizer, T_0=20, T_mult=1, eta_min=1e-6
    )
    
    # 数据增强 - REM专注
    data_aug = REMDataAugmentation()
    
    # 规则后处理器
    rule_processor = RuleBasedPostProcessor()
    
    logging.info("📋 V14核心策略:")
    logging.info("  • REM类3倍权重增强")
    logging.info("  • REM专门的数据增强")
    logging.info("  • REM置信度正则化")
    logging.info("  • 增大模型容量")
    
    # 训练循环
    best_val_metrics = {'macro_f1': 0, 'rem_f1': 0}
    best_model_state = None
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1, train_kappa, per_class_f1 = train_epoch_v14(
            model, train_loader, criterion, temp_loss_fn, optimizer,
            device, epoch, config, data_aug
        )
        
        # 验证
        val_metrics = evaluate_epoch_level_v14(
            model, val_dataset, val_loader, device, config['seq_len']
        )
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        current_lr = optimizer.param_groups[0]['lr']
        rem_f1 = per_class_f1[0]  # REM是第0类
        
        logging.info(f"\nEpoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, "
                    f"F1: {train_f1:.4f}, Kappa: {train_kappa:.4f}")
        logging.info(f"Train REM F1: {rem_f1:.4f}")
        logging.info(f"Val   - Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}, "
                    f"Kappa: {val_metrics['kappa']:.4f}")
        logging.info(f"Val REM F1: {val_metrics['per_class_metrics']['REM']['f1']:.4f}")
        logging.info(f"LR: {current_lr:.2e}")
        
        # 保存最佳模型 - 优先考虑REM F1
        val_rem_f1 = val_metrics['per_class_metrics']['REM']['f1']
        val_score = val_metrics['macro_f1'] + 0.3 * val_rem_f1  # 更重视REM
        best_score = best_val_metrics['macro_f1'] + 0.3 * best_val_metrics['rem_f1']
        
        if val_score > best_score:
            best_val_metrics['macro_f1'] = val_metrics['macro_f1']
            best_val_metrics['rem_f1'] = val_rem_f1
            best_val_metrics['full'] = val_metrics.copy()
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳模型: F1={val_metrics['macro_f1']:.4f}, REM F1={val_rem_f1:.4f}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"⏹️ 早停: {config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
    
    test_metrics = evaluate_epoch_level_v14(
        model, test_dataset, test_loader, device, config['seq_len']
    )
    
    # 详细结果
    logging.info("\n" + "="*80)
    logging.info("📊 V14最终测试结果")
    logging.info("="*80)
    logging.info(f"Accuracy: {test_metrics['accuracy']:.4f}")
    logging.info(f"Macro F1: {test_metrics['macro_f1']:.4f}")
    logging.info(f"Kappa: {test_metrics['kappa']:.4f}")
    logging.info("\n每类F1分数:")
    for class_name, class_metrics in test_metrics['per_class_metrics'].items():
        logging.info(f"  {class_name}: {class_metrics['f1']:.4f}")
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), '../../checkpoints/v14_rem_focus.pth')
    
    return test_metrics


def main():
    # V14配置
    config = {
        'batch_size': 32,
        'seq_len': 5,
        'learning_rate': 2e-5,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 25,
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'temp_loss_weight': 0.1,
        'num_workers': 4
    }
    
    log_file = setup_logging()
    
    logging.info("🚀 MAMBAFORMER V14 - REM专注训练")
    logging.info("="*80)
    logging.info("🎯 目标:")
    logging.info("  - 提升REM F1从20%到60%+")
    logging.info("  - 达到总体F1 80%")
    logging.info("  - 保持其他类别性能")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️ 使用设备: {device}")
    
    # 训练
    test_metrics = train_v14(config, device)
    
    # 目标评估
    target_acc = 0.87
    target_kappa = 0.8
    target_f1 = 0.8
    
    logging.info(f"\n最终结果 vs 目标:")
    logging.info(f"Accuracy: {test_metrics['accuracy']:.4f} (目标: {target_acc})")
    logging.info(f"Kappa: {test_metrics['kappa']:.4f} (目标: {target_kappa})")
    logging.info(f"Macro F1: {test_metrics['macro_f1']:.4f} (目标: {target_f1})")
    
    if (test_metrics['accuracy'] >= target_acc and 
        test_metrics['kappa'] >= target_kappa and 
        test_metrics['macro_f1'] >= target_f1):
        logging.info("🎉 恭喜！已达到所有目标！")
    else:
        logging.info("🔥 继续优化...")


if __name__ == "__main__":
    main()