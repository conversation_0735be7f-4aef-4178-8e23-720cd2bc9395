#!/usr/bin/env python3
"""
V18 MAMBAFORMER - Aggressive push to 90% accuracy
Major architecture upgrade and N1-focused training
Target: 90% Accuracy, 82% Macro F1, 0.82 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import OneCycleLR
from torch.cuda.amp import autocast, GradScaler
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class N1SpecializedAttention(nn.Module):
    """Special attention module for N1 detection"""
    def __init__(self, d_model):
        super().__init__()
        self.n1_detector = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 5)  # 5 classes
        )
        
    def forward(self, x):
        # x: [batch, seq_len, d_model]
        n1_logits = self.n1_detector(x)
        return n1_logits


class EnhancedMAMBAFORMER(nn.Module):
    """Enhanced MAMBAFORMER with N1 specialization"""
    def __init__(self, input_channels=3, n_classes=5, d_model=384, n_heads=24, 
                 n_layers=8, dropout=0.2, seq_len=7):
        super().__init__()
        
        # Base model
        self.base_model = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # N1 specialized branch
        self.n1_branch = N1SpecializedAttention(d_model)
        
        # Fusion layer
        self.fusion = nn.Linear(n_classes * 2, n_classes)
        
    def forward(self, x):
        # Base model output
        base_out, features = self.base_model(x)
        
        # N1 specialized output
        n1_out = self.n1_branch(features)
        
        # Fuse outputs
        combined = torch.cat([base_out, n1_out], dim=-1)
        final_out = self.fusion(combined)
        
        return final_out, features


class FocalLossWithN1(nn.Module):
    """Focal loss with extreme N1 focus"""
    def __init__(self, gamma=3.0, n1_multiplier=8.0):
        super().__init__()
        self.gamma = gamma
        self.n1_multiplier = n1_multiplier
        
    def forward(self, inputs, targets):
        batch_size, seq_len = targets.shape
        n_classes = inputs.shape[-1]
        
        # Flatten
        inputs_flat = inputs.reshape(-1, n_classes)
        targets_flat = targets.reshape(-1)
        
        # Calculate focal loss
        ce_loss = F.cross_entropy(inputs_flat, targets_flat, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # Apply extreme weight to N1
        weights = torch.ones_like(targets_flat, dtype=torch.float32)
        weights[targets_flat == 1] = self.n1_multiplier  # N1 class
        weights[targets_flat == 0] = 3.0  # Wake
        weights[targets_flat == 4] = 2.5  # REM
        
        weighted_loss = focal_loss * weights
        
        return weighted_loss.mean()


class TemporalSmoothing(nn.Module):
    """Advanced temporal consistency with N1 transition detection"""
    def __init__(self, weight=0.3):
        super().__init__()
        self.weight = weight
        
    def forward(self, predictions, targets=None):
        if predictions.shape[1] <= 1:
            return 0
        
        pred_probs = torch.softmax(predictions, dim=-1)
        
        # Basic temporal consistency
        temporal_diff = torch.abs(pred_probs[:, 1:] - pred_probs[:, :-1])
        base_loss = temporal_diff.mean() * self.weight
        
        # Extra penalty for N1 transitions (N1 often occurs at sleep transitions)
        if targets is not None:
            # Find N1 positions
            n1_mask = (targets == 1).float()
            
            # Penalize inconsistent N1 predictions more
            n1_transitions = n1_mask[:, 1:] - n1_mask[:, :-1]
            transition_penalty = torch.abs(n1_transitions).mean() * 0.5
            
            return base_loss + transition_penalty
        
        return base_loss


class CombinedLossV18(nn.Module):
    """Combined loss for V18"""
    def __init__(self):
        super().__init__()
        self.focal = FocalLossWithN1(gamma=3.0, n1_multiplier=8.0)
        self.temporal = TemporalSmoothing(weight=0.3)
        self.ce = nn.CrossEntropyLoss()
        
    def forward(self, predictions, targets):
        focal_loss = self.focal(predictions, targets)
        temporal_loss = self.temporal(predictions, targets)
        
        # Additional CE loss for stability
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        ce_loss = self.ce(pred_flat, target_flat) * 0.1
        
        return focal_loss + temporal_loss + ce_loss


class AugmentedDataset(Dataset):
    """Dataset with aggressive augmentation"""
    def __init__(self, base_dataset, augment=True, n1_oversample=2.0):
        self.base_dataset = base_dataset
        self.augment = augment
        self.n1_oversample = n1_oversample
        
        # Find N1 samples for oversampling
        self.n1_indices = []
        for i in range(len(base_dataset)):
            _, label = base_dataset[i]
            if 1 in label:  # N1 present
                self.n1_indices.append(i)
        
        # Create oversampled indices
        self.indices = list(range(len(base_dataset)))
        if n1_oversample > 1:
            n1_extra = int(len(self.n1_indices) * (n1_oversample - 1))
            extra_indices = np.random.choice(self.n1_indices, n1_extra, replace=True)
            self.indices.extend(extra_indices.tolist())
        
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        data, label = self.base_dataset[real_idx]
        
        if self.augment and np.random.random() > 0.3:
            # Gaussian noise
            noise_level = np.random.uniform(0.01, 0.08)
            noise = torch.randn_like(data) * noise_level
            data = data + noise
            
            # Random scaling
            scale = np.random.uniform(0.9, 1.1)
            data = data * scale
            
            # Random channel dropout
            if np.random.random() > 0.8:
                channel_idx = np.random.randint(0, data.shape[0])
                data[channel_idx] = data[channel_idx] * 0.5
        
        return data, label


def train_v18():
    """Main training function for V18"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v18_90percent_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V18 - AGGRESSIVE PUSH TO 90%")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V18 Configuration - major upgrade
    config = {
        'd_model': 384,      # 50% larger than V17
        'n_heads': 24,       
        'n_layers': 8,       # Deeper
        'dropout': 0.2,      
        'seq_len': 7,        # Longer context
        'batch_size': 20,    # Smaller for larger model
        'learning_rate': 8e-5,
        'num_epochs': 60,    # Long training
        'patience': 20,      # More patience
        'gradient_clip': 0.5,
        'weight_decay': 0.02,
        'n1_oversample': 2.0,  # Oversample N1
        'warmup_epochs': 3,
        'mixed_precision': True
    }
    
    logging.info("\n📋 V18 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # V14's data split
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    
    # Create datasets
    logging.info("\n📊 Loading datasets with augmentation...")
    
    base_train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Augmented training set with N1 oversampling
    train_dataset = AugmentedDataset(
        base_train_dataset, 
        augment=True,
        n1_oversample=config['n1_oversample']
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {len(train_dataset)} (with oversampling)")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create enhanced model
    model = EnhancedMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Loss and optimizer
    criterion = CombinedLossV18()
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # OneCycle scheduler
    total_steps = len(train_loader) * config['num_epochs']
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 10,
        total_steps=total_steps,
        pct_start=0.2,
        anneal_strategy='cos'
    )
    
    # Mixed precision training
    scaler = GradScaler() if config['mixed_precision'] else None
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            if config['mixed_precision'] and scaler is not None:
                with autocast():
                    outputs, _ = model(data)
                    loss = criterion(outputs, labels)
                
                scaler.scale(loss).backward()
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
                scaler.step(optimizer)
                scaler.update()
            else:
                outputs, _ = model(data)
                loss = criterion(outputs, labels)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
                optimizer.step()
            
            scheduler.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            current_lr = scheduler.get_last_lr()[0]
            pbar.set_postfix({'loss': f'{loss.item():.4f}', 'lr': f'{current_lr:.2e}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                
                if config['mixed_precision']:
                    with autocast():
                        outputs, _ = model(data)
                else:
                    outputs, _ = model(data)
                
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Get N1 performance
        n1_f1 = val_metrics['per_class_metrics']['N1']['f1']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} ({n1_f1*100:.2f}%)")
        logging.info(f"  LR: {current_lr:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.90:
            targets_met.append("ACC")
        if val_f1 >= 0.82:
            targets_met.append("F1")
        if val_kappa >= 0.82:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'n1_f1': n1_f1,
            'lr': current_lr,
            'time': epoch_time
        })
        
        # Save best model
        improved = False
        if val_acc > best_val_acc:
            improved = True
        elif val_acc == best_val_acc and val_f1 > best_val_f1:
            improved = True
        
        if improved:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                if epoch >= 15:  # At least 15 epochs
                    logging.info("  ✅ Stopping - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                
                if config['mixed_precision']:
                    with autocast():
                        outputs, _ = model(data)
                else:
                    outputs, _ = model(data)
                
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (90% / 82% / 0.82):")
        all_achieved = True
        
        if test_acc >= 0.90:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.90")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.90 (gap: {0.90-test_acc:.4f})")
            all_achieved = False
        
        if test_f1 >= 0.82:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.82 (gap: {0.82-test_f1:.4f})")
            all_achieved = False
        
        if test_kappa >= 0.82:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.82 (gap: {0.82-test_kappa:.4f})")
            all_achieved = False
        
        # Per-class metrics
        logging.info("\n📊 Per-Class Performance:")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for class_name in class_names:
            metrics = test_metrics['per_class_metrics'][class_name]
            logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1']*100:.1f}%")
        
        # Confusion Matrix
        logging.info("\n📊 Confusion Matrix:")
        logging.info("     Wake   N1    N2    N3   REM")
        for i, row in enumerate(cm):
            row_str = f"{class_names[i]:4} "
            for val in row:
                row_str += f"{val:5d} "
            logging.info(row_str)
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': all_achieved
        }
        
        results_file = os.path.join(log_dir, 'final_results.json')
        
        # Convert numpy types for JSON serialization
        def convert_to_python_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_to_python_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_python_types(item) for item in obj]
            return obj
        
        final_results = convert_to_python_types(final_results)
        
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if all_achieved:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V18 Model Ready for ICASSP 2026!")
            logging.info("="*80)
        else:
            logging.info("\n📈 Training complete. Continuing optimization...")
    
    return final_results


if __name__ == "__main__":
    results = train_v18()