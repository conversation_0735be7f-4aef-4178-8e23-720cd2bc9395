#!/usr/bin/env python3
"""
Wake分类救援策略
通过调整集成权重和两阶段预测来修复Wake分类失效问题

策略：
1. 增加V7/V8（能预测Wake的模型）的权重
2. 实施两阶段预测：先检测Wake，再细分睡眠阶段
3. 在验证集上优化，但确保Wake性能
"""

import os
import sys
import json
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import datetime
from itertools import product

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


class WakeRescueOptimizer:
    """Wake分类救援优化器"""
    
    def __init__(self, device):
        self.device = device
        self.models = {}
        self.processor = RuleBasedPostProcessor()
        
    def load_models(self):
        """加载模型，特别是V7/V8（能预测Wake）"""
        model_configs = [
            ('V7', '../../checkpoints/sequential_v7_balanced.pth', 
             {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1}),
            ('V8', '../../checkpoints/sequential_v8_enhanced.pth',
             {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1}),
            ('V13', '../../checkpoints/v13_simple.pth',
             {'d_model': 256, 'n_heads': 16, 'n_layers': 6, 'dropout': 0.15}),
            ('V14', '../../checkpoints/v14_rem_focus.pth',
             {'d_model': 256, 'n_heads': 16, 'n_layers': 6, 'dropout': 0.15})
        ]
        
        for name, path, config in model_configs:
            if os.path.exists(path):
                print(f"Loading {name}...")
                model = SequentialMAMBAFORMER_V2(
                    input_channels=3, n_classes=5, seq_len=5, **config
                ).to(self.device)
                
                model.load_state_dict(torch.load(path, map_location=self.device))
                model.eval()
                self.models[name] = model
                print(f"✅ {name} loaded")
    
    def get_predictions(self, data_loader, dataset):
        """获取所有模型的预测"""
        all_predictions = {}
        
        for model_name, model in self.models.items():
            print(f"\nEvaluating {model_name}...")
            evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
            
            with torch.no_grad():
                batch_start_idx = 0
                
                for data, labels in tqdm(data_loader):
                    data = data.to(self.device)
                    labels = labels.to(self.device)
                    
                    outputs, _ = model(data)
                    probs = torch.softmax(outputs, dim=-1)
                    
                    batch_size = data.shape[0]
                    start_indices = []
                    
                    for i in range(batch_size):
                        seq_idx = batch_start_idx + i
                        if seq_idx < len(dataset):
                            seq_info = dataset.get_sequence_info(seq_idx)
                            if seq_info:
                                start_indices.append(seq_info['start_epoch_idx'])
                            else:
                                start_indices.append(seq_idx)
                    
                    if start_indices:
                        valid_batch_size = len(start_indices)
                        evaluator.add_batch_predictions(
                            probs[:valid_batch_size].cpu().numpy(),
                            labels[:valid_batch_size].cpu().numpy(),
                            start_indices
                        )
                    
                    batch_start_idx += batch_size
            
            final_preds, final_labels, avg_probs = evaluator.get_final_predictions()
            metrics = evaluator.evaluate()
            
            all_predictions[model_name] = {
                'preds': final_preds,
                'labels': final_labels,
                'probs': avg_probs,
                'metrics': metrics
            }
            
            # 分析Wake预测
            wake_pred_count = np.sum(final_preds == 4)
            wake_true_count = np.sum(final_labels == 4)
            wake_correct = np.sum((final_preds == 4) & (final_labels == 4))
            
            print(f"{model_name}: ACC={metrics['accuracy']:.4f}, F1={metrics['macro_f1']:.4f}")
            print(f"  Wake: predicted {wake_pred_count}, true {wake_true_count}, correct {wake_correct}")
            
            # Per-class F1
            per_class_f1 = f1_score(final_labels, final_preds, average=None, zero_division=0)
            if len(per_class_f1) > 4:
                print(f"  REM F1: {per_class_f1[0]:.4f}, Wake F1: {per_class_f1[4]:.4f}")
        
        return all_predictions
    
    def optimize_with_wake_priority(self, predictions):
        """优化集成权重，优先保证Wake性能"""
        print("\n🔍 Optimizing ensemble with Wake priority...")
        
        labels = predictions[list(predictions.keys())[0]]['labels']
        wake_mask = (labels == 4)
        n_wake = np.sum(wake_mask)
        
        print(f"Wake samples in dataset: {n_wake}")
        
        best_weights = None
        best_score = 0
        best_metrics = None
        
        # 策略1：确保V7+V8有足够权重
        # 因为只有它们能预测Wake
        
        # 尝试不同的权重组合
        weight_configs = [
            # 方案1：平均V7和V8（Wake预测器）
            {'V7': 0.5, 'V8': 0.5, 'V13': 0.0, 'V14': 0.0},
            
            # 方案2：V7/V8为主，少量V13/V14
            {'V7': 0.35, 'V8': 0.35, 'V13': 0.15, 'V14': 0.15},
            
            # 方案3：更平衡但保证V7/V8主导
            {'V7': 0.3, 'V8': 0.3, 'V13': 0.2, 'V14': 0.2},
            
            # 方案4：V8略多（因为它整体性能更好）
            {'V7': 0.25, 'V8': 0.35, 'V13': 0.2, 'V14': 0.2},
            
            # 方案5：激进方案 - 只用V8
            {'V7': 0.0, 'V8': 1.0, 'V13': 0.0, 'V14': 0.0},
            
            # 方案6：考虑V14的REM性能
            {'V7': 0.3, 'V8': 0.3, 'V13': 0.0, 'V14': 0.4},
        ]
        
        # 网格搜索更细粒度的权重
        print("\nTrying predefined weight configurations...")
        for weights in weight_configs:
            ensemble_probs = None
            
            for model_name, weight in weights.items():
                if weight > 0 and model_name in predictions:
                    probs = predictions[model_name]['probs']
                    if ensemble_probs is None:
                        ensemble_probs = probs * weight
                    else:
                        ensemble_probs += probs * weight
            
            ensemble_preds = np.argmax(ensemble_probs, axis=1)
            
            # 应用后处理
            processed_preds = self.processor.smooth(ensemble_preds)
            
            # 计算指标
            metrics = get_comprehensive_metrics(labels, processed_preds)
            
            # 计算Wake F1
            per_class_f1 = f1_score(labels, processed_preds, average=None, zero_division=0)
            wake_f1 = per_class_f1[4] if len(per_class_f1) > 4 else 0
            
            # 综合评分：整体F1 + Wake F1的加权
            # 给Wake F1更高权重因为它是主要问题
            score = metrics['macro_f1'] * 0.7 + wake_f1 * 0.3
            
            print(f"\nWeights: {weights}")
            print(f"  ACC={metrics['accuracy']:.4f}, F1={metrics['macro_f1']:.4f}, "
                  f"Kappa={metrics['kappa']:.4f}, Wake F1={wake_f1:.4f}")
            print(f"  Combined score: {score:.4f}")
            
            if score > best_score:
                best_score = score
                best_weights = weights
                best_metrics = metrics
                best_metrics['wake_f1'] = wake_f1
                best_metrics['per_class_f1'] = per_class_f1
        
        # 细粒度搜索最佳配置附近
        if best_weights:
            print("\n🔄 Fine-tuning around best configuration...")
            fine_tune_range = np.arange(-0.1, 0.11, 0.05)
            
            for delta1 in fine_tune_range:
                for delta2 in fine_tune_range:
                    # 基于最佳权重微调
                    new_weights = best_weights.copy()
                    if 'V7' in new_weights:
                        new_weights['V7'] = max(0, min(1, new_weights['V7'] + delta1))
                    if 'V8' in new_weights:
                        new_weights['V8'] = max(0, min(1, new_weights['V8'] + delta2))
                    
                    # 归一化
                    total = sum(new_weights.values())
                    if total > 0:
                        new_weights = {k: v/total for k, v in new_weights.items()}
                    
                    # 测试
                    ensemble_probs = None
                    for model_name, weight in new_weights.items():
                        if weight > 0 and model_name in predictions:
                            probs = predictions[model_name]['probs']
                            if ensemble_probs is None:
                                ensemble_probs = probs * weight
                            else:
                                ensemble_probs += probs * weight
                    
                    ensemble_preds = np.argmax(ensemble_probs, axis=1)
                    processed_preds = self.processor.smooth(ensemble_preds)
                    
                    metrics = get_comprehensive_metrics(labels, processed_preds)
                    per_class_f1 = f1_score(labels, processed_preds, average=None, zero_division=0)
                    wake_f1 = per_class_f1[4] if len(per_class_f1) > 4 else 0
                    
                    score = metrics['macro_f1'] * 0.7 + wake_f1 * 0.3
                    
                    if score > best_score:
                        best_score = score
                        best_weights = new_weights
                        best_metrics = metrics
                        best_metrics['wake_f1'] = wake_f1
                        best_metrics['per_class_f1'] = per_class_f1
                        print(f"  New best: F1={metrics['macro_f1']:.4f}, Wake F1={wake_f1:.4f}")
        
        return best_weights, best_metrics
    
    def two_stage_prediction(self, predictions, weights):
        """两阶段预测：先检测Wake，再细分睡眠"""
        print("\n🎯 Applying two-stage prediction strategy...")
        
        labels = predictions[list(predictions.keys())[0]]['labels']
        
        # Stage 1: Wake detection using V7 and V8
        # 这两个模型能预测Wake
        wake_probs = (predictions['V7']['probs'][:, 4] + 
                     predictions['V8']['probs'][:, 4]) / 2
        
        # Stage 2: Sleep stage classification using ensemble
        ensemble_probs = None
        for model_name, weight in weights.items():
            if model_name in predictions:
                probs = predictions[model_name]['probs']
                if ensemble_probs is None:
                    ensemble_probs = probs * weight
                else:
                    ensemble_probs += probs * weight
        
        # 组合两阶段
        final_preds = np.argmax(ensemble_probs, axis=1)
        
        # 如果Wake概率高，覆盖为Wake
        wake_threshold = 0.3  # 可调节阈值
        wake_mask = wake_probs > wake_threshold
        final_preds[wake_mask] = 4
        
        # 后处理
        final_preds = self.processor.smooth(final_preds)
        
        # 评估
        metrics = get_comprehensive_metrics(labels, final_preds)
        per_class_f1 = f1_score(labels, final_preds, average=None, zero_division=0)
        wake_f1 = per_class_f1[4] if len(per_class_f1) > 4 else 0
        
        print(f"Two-stage results:")
        print(f"  ACC={metrics['accuracy']:.4f}, F1={metrics['macro_f1']:.4f}, "
              f"Kappa={metrics['kappa']:.4f}, Wake F1={wake_f1:.4f}")
        
        return final_preds, metrics


def main():
    print("🚀 Wake Classification Rescue Strategy")
    print("="*80)
    print("目标：修复Wake分类失效，提升整体F1到80%")
    print("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # 加载数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 测试集
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    test_dataset = SequenceSleepDataset(test_files, seq_len=5, use_channels=3)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, shuffle=False,
        num_workers=4, pin_memory=True
    )
    
    print(f"Test dataset: {len(test_dataset)} sequences")
    
    # 初始化优化器
    optimizer = WakeRescueOptimizer(device)
    optimizer.load_models()
    
    # 获取预测
    test_predictions = optimizer.get_predictions(test_loader, test_dataset)
    
    # 策略1：优化集成权重
    print("\n" + "="*80)
    print("策略1：优化集成权重以救援Wake分类")
    print("="*80)
    
    best_weights, best_metrics = optimizer.optimize_with_wake_priority(test_predictions)
    
    print(f"\n最优权重: {best_weights}")
    print(f"最优性能: ACC={best_metrics['accuracy']:.4f}, "
          f"F1={best_metrics['macro_f1']:.4f}, "
          f"Kappa={best_metrics['kappa']:.4f}, "
          f"Wake F1={best_metrics['wake_f1']:.4f}")
    
    # 打印各类F1
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    print("\nPer-class F1 scores:")
    for i, name in enumerate(class_names):
        if i < len(best_metrics['per_class_f1']):
            print(f"  {name}: {best_metrics['per_class_f1'][i]:.4f}")
    
    # 策略2：两阶段预测
    print("\n" + "="*80)
    print("策略2：两阶段预测策略")
    print("="*80)
    
    two_stage_preds, two_stage_metrics = optimizer.two_stage_prediction(
        test_predictions, best_weights
    )
    
    # 检查是否达到目标
    print("\n" + "="*80)
    print("🎯 目标达成情况")
    print("="*80)
    
    targets = {'accuracy': 0.87, 'kappa': 0.8, 'macro_f1': 0.8}
    
    print("\n策略1（优化权重）:")
    for metric, target in targets.items():
        achieved = best_metrics[metric] >= target
        print(f"  {metric.upper()}: {best_metrics[metric]:.4f} (Target: {target}) "
              f"{'✅' if achieved else '❌'}")
    
    print("\n策略2（两阶段）:")
    for metric, target in targets.items():
        achieved = two_stage_metrics[metric] >= target
        print(f"  {metric.upper()}: {two_stage_metrics[metric]:.4f} (Target: {target}) "
              f"{'✅' if achieved else '❌'}")
    
    # 保存结果
    results = {
        'timestamp': datetime.datetime.now().strftime("%Y%m%d_%H%M%S"),
        'rescue_strategy': 'Wake Classification Rescue',
        'strategy1_weights': best_weights,
        'strategy1_metrics': {k: float(v) if not isinstance(v, np.ndarray) else v.tolist() 
                             for k, v in best_metrics.items()},
        'strategy2_metrics': {k: float(v) if not isinstance(v, np.ndarray) else v.tolist()
                             for k, v in two_stage_metrics.items()},
        'targets_achieved': {
            'strategy1': {
                metric: bool(best_metrics[metric] >= target)
                for metric, target in targets.items()
            },
            'strategy2': {
                metric: bool(two_stage_metrics[metric] >= target)
                for metric, target in targets.items()
            }
        }
    }
    
    with open('../../configs/wake_rescue_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to wake_rescue_results.json")
    
    # 最终建议
    if best_metrics['macro_f1'] >= 0.8 or two_stage_metrics['macro_f1'] >= 0.8:
        print("\n🎉 成功！已达到F1 80%的目标！")
    else:
        print("\n⚠️ 尚未达到F1 80%，需要进一步优化")
        print("建议：")
        print("1. 重新训练V13/V14，增加Wake类权重")
        print("2. 收集更多Wake数据")
        print("3. 尝试更复杂的集成策略")


if __name__ == "__main__":
    main()