2025-08-12 02:30:36,746 - INFO - ================================================================================
2025-08-12 02:30:36,747 - INFO - 🚀 V8 ULTIMATE - FINAL PUSH TO 90%
2025-08-12 02:30:36,747 - INFO - ================================================================================
2025-08-12 02:30:36,747 - INFO - Configuration: {
  "d_model": 320,
  "n_heads": 20,
  "n_layers": 7,
  "dropout": 0.12,
  "seq_len": 5,
  "batch_size": 24,
  "learning_rate": 0.00015,
  "weight_decay": 3e-05,
  "num_epochs": 100,
  "patience": 20
}
2025-08-12 02:30:36,924 - INFO - Device: cuda
2025-08-12 02:30:36,924 - INFO - Loading datasets...
2025-08-12 02:30:38,365 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 02:30:38,365 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 02:30:38,755 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 02:30:38,755 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 02:30:39,457 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 02:30:39,458 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 02:30:39,458 - INFO - Dataset sizes: Train=25266, Val=4931, Test=11955
2025-08-12 02:30:39,868 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=8,950,519, d_model=320, n_heads=20, n_layers=7
2025-08-12 02:30:40,100 - INFO - Model parameters: 9,684,086
2025-08-12 02:30:41,528 - INFO - Starting V8 ULTIMATE training...
2025-08-12 02:30:41,529 - INFO - Target: 90% accuracy

Training Epoch 1:   0%|          | 0/1053 [00:00<?, ?it/s]
Training Epoch 1:   0%|          | 0/1053 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate.py", line 630, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate.py", line 536, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate.py", line 357, in train_epoch
    final_output, expert_outputs, coarse_logits, fine_logits = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate.py", line 150, in forward
    enhanced = self.attention_enhancer(backbone_output)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate.py", line 58, in forward
    attn_out, _ = self.multihead(x, x, x)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/activation.py", line 1373, in forward
    attn_output, attn_output_weights = F.multi_head_attention_forward(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/functional.py", line 6202, in multi_head_attention_forward
    assert (
AssertionError: was expecting embedding dimension of 320, but got 5
