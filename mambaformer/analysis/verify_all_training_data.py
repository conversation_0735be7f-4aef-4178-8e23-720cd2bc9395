#!/usr/bin/env python3
"""
全面验证所有训练脚本的数据加载设置
确保训练使用了完整数据
"""

import os
import sys
import glob
import re

def check_training_scripts():
    """检查所有训练脚本的数据加载设置"""
    
    training_dir = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training'
    
    # 关键训练脚本
    key_scripts = [
        'train_sequential_v7_balanced.py',
        'train_sequential_v8_enhanced.py', 
        'train_v13_simple.py',
        'train_v14_rem_focus.py'
    ]
    
    # 所有训练脚本
    all_scripts = glob.glob(os.path.join(training_dir, 'train_*.py'))
    
    print("🔍 检查所有训练脚本的数据加载设置")
    print("="*80)
    
    results = {
        'correct': [],  # max_samples_per_file=None
        'limited': [],  # max_samples_per_file=150或其他限制
        'no_explicit': []  # 没有显式设置
    }
    
    for script_path in all_scripts:
        script_name = os.path.basename(script_path)
        
        with open(script_path, 'r') as f:
            content = f.read()
        
        # 查找max_samples_per_file设置
        pattern = r'max_samples_per_file\s*=\s*([^,\)]+)'
        matches = re.findall(pattern, content)
        
        # 查找SequenceSleepDataset调用
        dataset_pattern = r'SequenceSleepDataset\([^)]+\)'
        dataset_calls = re.findall(dataset_pattern, content)
        
        if matches:
            # 分析所有匹配
            has_none = any('None' in m for m in matches)
            has_limit = any(m.strip().isdigit() for m in matches)
            
            if has_none and not has_limit:
                results['correct'].append({
                    'script': script_name,
                    'setting': 'None (正确！使用所有数据)',
                    'matches': matches
                })
            elif has_limit:
                limit_values = [m.strip() for m in matches if m.strip().isdigit()]
                results['limited'].append({
                    'script': script_name,
                    'setting': f'限制为 {", ".join(limit_values)}',
                    'matches': matches
                })
            else:
                results['no_explicit'].append({
                    'script': script_name,
                    'note': '配置不明确',
                    'matches': matches
                })
        else:
            # 检查是否有SequenceSleepDataset但没有max_samples_per_file
            if dataset_calls:
                results['no_explicit'].append({
                    'script': script_name,
                    'note': '使用SequenceSleepDataset但未设置max_samples_per_file',
                    'calls': len(dataset_calls)
                })
    
    # 输出结果
    print("\n✅ 正确配置（使用所有数据）:")
    print("-"*60)
    for item in results['correct']:
        print(f"  {item['script']}: {item['setting']}")
    
    print(f"\n❌ 有数据限制的脚本 ({len(results['limited'])}个):")
    print("-"*60)
    for item in results['limited']:
        print(f"  {item['script']}: {item['setting']}")
        if item['script'] in key_scripts:
            print(f"    ⚠️ 这是关键模型！需要修复！")
    
    print(f"\n❓ 未明确设置的脚本 ({len(results['no_explicit'])}个):")
    print("-"*60)
    for item in results['no_explicit']:
        print(f"  {item['script']}: {item['note']}")
        if item['script'] in key_scripts:
            print(f"    ⚠️ 这是关键模型！需要检查！")
    
    # 特别检查关键模型
    print("\n" + "="*80)
    print("🎯 关键模型状态检查:")
    print("-"*60)
    
    for script_name in key_scripts:
        found = False
        for category, items in results.items():
            for item in items:
                if item['script'] == script_name:
                    if category == 'correct':
                        print(f"✅ {script_name}: 正确 - 使用所有数据")
                    elif category == 'limited':
                        print(f"❌ {script_name}: 错误 - {item['setting']}")
                    else:
                        print(f"❓ {script_name}: 不明确 - 需要检查")
                    found = True
                    break
            if found:
                break
        if not found:
            print(f"⚠️ {script_name}: 未找到文件")
    
    return results


def check_v13_v14_specifically():
    """特别检查V13和V14的训练脚本"""
    print("\n" + "="*80)
    print("🔬 详细检查V13和V14训练脚本")
    print("="*80)
    
    scripts = {
        'V13': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py',
        'V14': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_rem_focus.py'
    }
    
    for model_name, script_path in scripts.items():
        print(f"\n检查 {model_name}:")
        print("-"*40)
        
        if not os.path.exists(script_path):
            print(f"  ❌ 文件不存在: {script_path}")
            continue
        
        with open(script_path, 'r') as f:
            lines = f.readlines()
        
        # 查找SequenceSleepDataset的调用
        for i, line in enumerate(lines):
            if 'SequenceSleepDataset' in line:
                # 打印上下文
                start = max(0, i-2)
                end = min(len(lines), i+3)
                print(f"  第{i+1}行附近:")
                for j in range(start, end):
                    prefix = ">>> " if j == i else "    "
                    print(f"{prefix}{lines[j].rstrip()}")
                
                # 检查是否有max_samples_per_file
                context = ''.join(lines[i:min(i+10, len(lines))])
                if 'max_samples_per_file' in context:
                    match = re.search(r'max_samples_per_file\s*=\s*([^,\)]+)', context)
                    if match:
                        value = match.group(1).strip()
                        if 'None' in value:
                            print(f"  ✅ 设置为None - 使用所有数据")
                        else:
                            print(f"  ❌ 设置为{value} - 数据被限制！")
                else:
                    print(f"  ⚠️ 未找到max_samples_per_file参数 - 使用默认值")
                
                print()


if __name__ == "__main__":
    # 检查所有训练脚本
    results = check_training_scripts()
    
    # 特别检查V13和V14
    check_v13_v14_specifically()
    
    # 总结
    print("\n" + "="*80)
    print("📊 总结")
    print("="*80)
    
    total_scripts = sum(len(v) for v in results.values())
    correct_count = len(results['correct'])
    limited_count = len(results['limited'])
    
    print(f"总共检查了 {total_scripts} 个训练脚本")
    print(f"  ✅ 正确配置: {correct_count} 个")
    print(f"  ❌ 有数据限制: {limited_count} 个")
    print(f"  ❓ 未明确设置: {len(results['no_explicit'])} 个")
    
    if limited_count > 0:
        print("\n⚠️ 建议: 需要修复有数据限制的训练脚本！")
        print("将所有 max_samples_per_file=150 改为 max_samples_per_file=None")