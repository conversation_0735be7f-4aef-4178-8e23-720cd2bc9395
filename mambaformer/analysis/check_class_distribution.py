"""
检查正确数据划分后的类别分布
"""
import json
import numpy as np
import os

# 加载数据划分配置
with open('../../configs/subject_aware_splits.json', 'r') as f:
    splits = json.load(f)

data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'

def check_class_distribution(file_list, split_name):
    """检查某个数据集的类别分布"""
    class_counts = np.zeros(5)
    total_epochs = 0
    
    for file in file_list:
        file_path = os.path.join(data_dir, file)
        if os.path.exists(file_path):
            data = np.load(file_path)
            labels = data['y']
            
            # 统计各类别
            for i in range(5):
                class_counts[i] += np.sum(labels == i)
            total_epochs += len(labels)
    
    # 打印结果
    print(f"\n{split_name}集 类别分布:")
    print(f"总epochs数: {total_epochs}")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    for i, name in enumerate(class_names):
        count = int(class_counts[i])
        percentage = (count / total_epochs * 100) if total_epochs > 0 else 0
        print(f"{name}: {count} ({percentage:.1f}%)")
    
    return class_counts, total_epochs

# 检查各个数据集
train_counts, train_total = check_class_distribution(splits['splits']['train']['files'], "训练")
val_counts, val_total = check_class_distribution(splits['splits']['val']['files'], "验证")
test_counts, test_total = check_class_distribution(splits['splits']['test']['files'], "测试")

# 创建序列后的分布（滑动窗口会改变分布）
print("\n创建序列后的近似分布（序列长度=5）:")
print(f"训练序列数: ~{(train_total-4)*28//28}")
print(f"验证序列数: ~{(val_total-4)*4//4}")
print(f"测试序列数: ~{(test_total-4)*8//8}")

# 检查REM类别的具体情况
print(f"\nREM类别分析:")
print(f"训练集REM: {int(train_counts[4])} epochs")
print(f"验证集REM: {int(val_counts[4])} epochs") 
print(f"测试集REM: {int(test_counts[4])} epochs")

# 计算类别不平衡比例
print(f"\n类别不平衡分析:")
class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
for i in range(5):
    total_class = train_counts[i] + val_counts[i] + test_counts[i]
    train_ratio = train_counts[i] / total_class if total_class > 0 else 0
    val_ratio = val_counts[i] / total_class if total_class > 0 else 0
    test_ratio = test_counts[i] / total_class if total_class > 0 else 0
    print(f"{class_names[i]}: 训练{train_ratio:.1%}, 验证{val_ratio:.1%}, 测试{test_ratio:.1%}")

# 序列化后的REM样本估算
print(f"\n序列化后的REM样本估算（考虑滑动窗口）:")
print(f"训练集REM序列: ~{int(train_counts[4]) * 5} (每个序列5个epoch)")
print(f"验证集REM序列: ~{int(val_counts[4]) * 5}")
print(f"测试集REM序列: ~{int(test_counts[4]) * 5}")
print(f"\n⚠️  测试结果显示只有30个REM样本，远低于预期的~8000个！")