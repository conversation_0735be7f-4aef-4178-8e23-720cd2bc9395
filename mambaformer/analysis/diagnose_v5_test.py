"""
诊断V5测试过程中的REM样本丢失问题
"""
import sys
import os
import torch
import numpy as np
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset

# 测试文件列表
test_files = [
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4001E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4002E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4011E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4012E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4151E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4152E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4171E0.npz",
    "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4172E0.npz"
]

# 1. 检查原始数据中的REM分布
print("=== 1. 原始数据中的REM分布 ===")
total_rem = 0
for file in test_files:
    if os.path.exists(file):
        data = np.load(file)
        labels = data['y']
        rem_count = np.sum(labels == 4)
        total_rem += rem_count
        print(f"{os.path.basename(file)}: {rem_count} REM epochs")
print(f"总REM epochs: {total_rem}")

# 2. 创建数据集并检查序列中的REM分布
print("\n=== 2. 序列数据集中的REM分布 ===")
test_dataset = SequenceSleepDataset(
    test_files,
    max_samples_per_file=150,
    seq_len=5,
    use_channels=3
)

print(f"数据集大小: {len(test_dataset)}")

# 检查前100个序列
rem_sequences = 0
rem_epochs_in_sequences = 0
for i in range(min(100, len(test_dataset))):
    data, labels = test_dataset[i]
    # labels shape: (seq_len,) = (5,)
    rem_in_seq = np.sum(labels.numpy() == 4)
    if rem_in_seq > 0:
        rem_sequences += 1
        rem_epochs_in_sequences += rem_in_seq

print(f"前100个序列中包含REM的序列数: {rem_sequences}")
print(f"前100个序列中的REM epochs数: {rem_epochs_in_sequences}")

# 3. 创建数据加载器并检查批次中的REM分布
print("\n=== 3. DataLoader批次中的REM分布 ===")
test_loader = torch.utils.data.DataLoader(
    test_dataset,
    batch_size=32,
    shuffle=False,
    num_workers=4,
    pin_memory=True
)

# 检查所有批次
total_samples = 0
total_rem_samples = 0
batch_rem_counts = []

for batch_idx, (data, labels) in enumerate(tqdm(test_loader, desc="检查批次")):
    # labels shape: (batch_size, seq_len)
    batch_labels = labels.numpy()
    batch_rem = np.sum(batch_labels == 4)
    batch_rem_counts.append(batch_rem)
    total_rem_samples += batch_rem
    total_samples += batch_labels.size
    
    if batch_idx < 5:  # 打印前5个批次的详情
        print(f"批次 {batch_idx}: 标签shape={labels.shape}, REM数量={batch_rem}")
        unique_labels = np.unique(batch_labels)
        print(f"  唯一标签: {unique_labels}")

print(f"\n总样本数: {total_samples}")
print(f"总REM样本数: {total_rem_samples}")
print(f"REM比例: {total_rem_samples/total_samples*100:.2f}%")

# 4. 检查序列标签展平后的分布
print("\n=== 4. 展平后的标签分布 ===")
all_labels = []
for data, labels in tqdm(test_loader, desc="收集所有标签"):
    all_labels.extend(labels.numpy().flatten())

all_labels = np.array(all_labels)
print(f"总标签数: {len(all_labels)}")
for i in range(5):
    count = np.sum(all_labels == i)
    print(f"类别 {i}: {count} ({count/len(all_labels)*100:.2f}%)")

# 5. 诊断结论
print("\n=== 诊断结论 ===")
if total_rem_samples < 100:
    print("⚠️  严重问题：DataLoader中几乎没有REM样本！")
    print("可能原因：")
    print("1. max_samples_per_file=150 限制导致REM样本被截断")
    print("2. REM通常出现在睡眠后期，可能被跳过")
    print("3. 测试集中某些文件的REM分布不均")
else:
    print("✅ DataLoader中有足够的REM样本")
    print(f"但测试结果只显示30个，可能是评估代码的问题")