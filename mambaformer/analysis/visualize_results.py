"""
可视化实验结果对比
"""
import matplotlib.pyplot as plt
import numpy as np

# 实验结果数据
experiments = {
    'V1-V4\n(数据泄露)': {
        'accuracy': 0.73,
        'f1': 0.57,
        'kappa': 0.63,
        'rem_f1': 0.0,
        'note': '不可信'
    },
    'V5\n(正确划分\n+截断)': {
        'accuracy': 0.861,
        'f1': 0.647,
        'kappa': 0.799,
        'rem_f1': 0.0,
        'note': 'REM样本少'
    },
    'V6\n(完整数据\n+权重)': {
        'accuracy': 0.14,
        'f1': 0.05,
        'kappa': -0.005,
        'rem_f1': 0.0,
        'note': '训练崩溃'
    },
    'V7\n(完整数据\n+Focal)': {
        'accuracy': 0.712,
        'f1': 0.571,
        'kappa': 0.578,
        'rem_f1': 0.677,
        'note': '平衡最佳'
    }
}

# 创建图表
fig, axes = plt.subplots(2, 2, figsize=(12, 10))
fig.suptitle('序列MAMBAFORMER实验结果对比', fontsize=16)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

versions = list(experiments.keys())
colors = ['red', 'blue', 'orange', 'green']

# 1. 准确率对比
ax1 = axes[0, 0]
accuracies = [experiments[v]['accuracy'] for v in versions]
bars1 = ax1.bar(versions, accuracies, color=colors, alpha=0.7)
ax1.set_ylabel('Accuracy')
ax1.set_title('Test Accuracy')
ax1.set_ylim(0, 1)
for i, (bar, acc) in enumerate(zip(bars1, accuracies)):
    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{acc:.2%}', ha='center', va='bottom')

# 2. F1分数对比
ax2 = axes[0, 1]
f1_scores = [experiments[v]['f1'] for v in versions]
bars2 = ax2.bar(versions, f1_scores, color=colors, alpha=0.7)
ax2.set_ylabel('F1 Score')
ax2.set_title('Macro F1 Score')
ax2.set_ylim(0, 1)
for i, (bar, f1) in enumerate(zip(bars2, f1_scores)):
    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{f1:.2%}', ha='center', va='bottom')

# 3. Kappa系数对比
ax3 = axes[1, 0]
kappa_scores = [experiments[v]['kappa'] for v in versions]
bars3 = ax3.bar(versions, kappa_scores, color=colors, alpha=0.7)
ax3.set_ylabel('Kappa')
ax3.set_title('Cohen\'s Kappa')
ax3.set_ylim(-0.1, 1)
ax3.axhline(y=0, color='black', linestyle='--', alpha=0.3)
for i, (bar, kappa) in enumerate(zip(bars3, kappa_scores)):
    y_pos = bar.get_height() + 0.01 if bar.get_height() > 0 else -0.05
    ax3.text(bar.get_x() + bar.get_width()/2, y_pos, 
             f'{kappa:.3f}', ha='center', va='bottom' if bar.get_height() > 0 else 'top')

# 4. REM F1对比
ax4 = axes[1, 1]
rem_f1_scores = [experiments[v]['rem_f1'] for v in versions]
bars4 = ax4.bar(versions, rem_f1_scores, color=colors, alpha=0.7)
ax4.set_ylabel('REM F1 Score')
ax4.set_title('REM Detection Performance')
ax4.set_ylim(0, 1)
for i, (bar, rem) in enumerate(zip(bars4, rem_f1_scores)):
    ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{rem:.2%}', ha='center', va='bottom')

# 添加注释
for i, v in enumerate(versions):
    note = experiments[v]['note']
    ax4.text(i, -0.15, note, ha='center', va='top', fontsize=8, style='italic')

plt.tight_layout()
plt.savefig('../../results/experiment_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# 打印详细对比
print("\n实验结果详细对比:")
print("="*60)
print(f"{'版本':<15} {'准确率':<10} {'F1':<10} {'Kappa':<10} {'REM F1':<10}")
print("-"*60)
for v in versions:
    exp = experiments[v]
    v_clean = v.replace('\n', ' ')
    print(f"{v_clean:<15} {exp['accuracy']:<10.3f} {exp['f1']:<10.3f} {exp['kappa']:<10.3f} {exp['rem_f1']:<10.3f}")

# 关键结论
print("\n关键结论:")
print("1. V5虽然整体指标高，但因数据截断导致REM检测失败")
print("2. V6使用类别权重导致训练不稳定")
print("3. V7使用Focal Loss成功平衡了整体性能和REM检测")
print("4. Focal Loss (γ=1.5) 是处理类别不平衡的有效方法")