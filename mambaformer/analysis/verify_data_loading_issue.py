#!/usr/bin/env python3
"""
验证数据加载问题
检查max_samples_per_file参数导致的数据截断
"""

import os
import sys
import numpy as np

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset


def analyze_dataset_loading():
    """分析数据集加载问题"""
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_file_paths = [os.path.join(data_dir, f) for f in test_files]
    
    print("🔍 分析数据加载问题")
    print("="*80)
    
    # 1. 真实数据统计
    print("\n1. 真实数据统计（直接从NPZ文件）:")
    print("-"*60)
    
    total_real_epochs = 0
    total_real_wake = 0
    
    for file in test_files:
        data = np.load(os.path.join(data_dir, file))
        labels = data['y']
        wake_count = np.sum(labels == 4)
        total_real_epochs += len(labels)
        total_real_wake += wake_count
        print(f"  {file}: {len(labels)} epochs, {wake_count} Wake")
    
    print(f"\n  总计: {total_real_epochs} epochs, {total_real_wake} Wake "
          f"({total_real_wake/total_real_epochs*100:.2f}%)")
    
    # 2. 使用默认参数加载（max_samples_per_file=150）
    print("\n2. 使用默认参数加载 (max_samples_per_file=150):")
    print("-"*60)
    
    dataset_limited = SequenceSleepDataset(
        test_file_paths, 
        seq_len=5, 
        use_channels=3,
        max_samples_per_file=150  # 默认限制
    )
    
    # 统计加载的数据
    all_labels = []
    for i in range(len(dataset_limited)):
        _, labels = dataset_limited[i]
        all_labels.extend(labels.numpy())
    
    unique_labels, counts = np.unique(all_labels, return_counts=True)
    wake_count_limited = counts[unique_labels == 4][0] if 4 in unique_labels else 0
    
    print(f"  加载的序列数: {len(dataset_limited)}")
    print(f"  总epochs数: {len(all_labels)}")
    print(f"  唯一epochs数: {dataset_limited.total_epochs}")
    print(f"  Wake epochs: {wake_count_limited} ({wake_count_limited/len(all_labels)*100:.2f}%)")
    
    # 分析每个类的分布
    print("\n  类别分布:")
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    for i in range(5):
        count = counts[unique_labels == i][0] if i in unique_labels else 0
        print(f"    {class_names[i]}: {count} ({count/len(all_labels)*100:.2f}%)")
    
    # 3. 不限制加载（max_samples_per_file=None）
    print("\n3. 不限制加载 (max_samples_per_file=None):")
    print("-"*60)
    
    dataset_full = SequenceSleepDataset(
        test_file_paths, 
        seq_len=5, 
        use_channels=3,
        max_samples_per_file=None  # 不限制
    )
    
    # 统计加载的数据
    all_labels_full = []
    for i in range(len(dataset_full)):
        _, labels = dataset_full[i]
        all_labels_full.extend(labels.numpy())
    
    unique_labels_full, counts_full = np.unique(all_labels_full, return_counts=True)
    wake_count_full = counts_full[unique_labels_full == 4][0] if 4 in unique_labels_full else 0
    
    print(f"  加载的序列数: {len(dataset_full)}")
    print(f"  总epochs数: {len(all_labels_full)}")
    print(f"  唯一epochs数: {dataset_full.total_epochs}")
    print(f"  Wake epochs: {wake_count_full} ({wake_count_full/len(all_labels_full)*100:.2f}%)")
    
    # 分析每个类的分布
    print("\n  类别分布:")
    for i in range(5):
        count = counts_full[unique_labels_full == i][0] if i in unique_labels_full else 0
        print(f"    {class_names[i]}: {count} ({count/len(all_labels_full)*100:.2f}%)")
    
    # 4. 对比分析
    print("\n4. 对比分析:")
    print("="*80)
    
    print(f"  数据丢失情况:")
    print(f"    总epochs: {total_real_epochs} -> {dataset_limited.total_epochs} "
          f"(丢失 {total_real_epochs - dataset_limited.total_epochs}, "
          f"{(total_real_epochs - dataset_limited.total_epochs)/total_real_epochs*100:.1f}%)")
    
    print(f"    Wake epochs: {total_real_wake} -> {wake_count_limited//5} "
          f"(丢失 {total_real_wake - wake_count_limited//5}, "
          f"{(total_real_wake - wake_count_limited//5)/total_real_wake*100:.1f}%)")
    
    print("\n⚠️ 结论:")
    print("  max_samples_per_file=150 导致严重的数据截断！")
    print(f"  - 丢失了 {(total_real_epochs - dataset_limited.total_epochs)/total_real_epochs*100:.1f}% 的数据")
    print(f"  - 丢失了 {(total_real_wake - wake_count_limited//5)/total_real_wake*100:.1f}% 的Wake数据")
    print("  - 这就是为什么模型看不到足够的Wake样本！")
    
    # 5. 检查前150个epochs的Wake分布
    print("\n5. 前150个epochs的Wake分布:")
    print("-"*60)
    
    for file in test_files:
        data = np.load(os.path.join(data_dir, file))
        labels = data['y'][:150]  # 只看前150个
        wake_count = np.sum(labels == 4)
        print(f"  {file}前150个: {wake_count} Wake ({wake_count/150*100:.1f}%)")


if __name__ == "__main__":
    analyze_dataset_loading()