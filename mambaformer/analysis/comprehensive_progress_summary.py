"""
序列MAMBAFORMER全面进展总结
分析从V1到V8的所有改进和发现
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

def load_all_results():
    """加载所有版本的结果"""
    results = {}
    
    # V7正确评估结果
    v7_correct = {
        'version': 'V7_correct_evaluation',
        'accuracy': 0.8564,
        'macro_f1': 0.7890,
        'kappa': 0.8051,
        'rem_f1': 0.8152,
        'total_epochs': 9746,
        'method': 'Focal Loss + 正确epoch级别评估',
        'key_improvements': [
            '正确的被试级别数据划分',
            '使用完整数据（无截断）',
            'Focal Loss处理类别不平衡',
            '正确的epoch级别评估和平均概率策略'
        ]
    }
    
    # 历史版本对比数据
    historical_versions = {
        'V1-V4': {
            'version': 'V1-V4_data_leakage',
            'accuracy': 0.73,
            'macro_f1': 0.57,
            'kappa': 0.63,
            'rem_f1': 0.0,
            'total_epochs': 'N/A',
            'method': '随机划分（数据泄露）',
            'key_improvements': ['基础架构探索'],
            'issues': ['数据泄露', 'REM检测失败']
        },
        'V5': {
            'version': 'V5_truncated_data',
            'accuracy': 0.861,
            'macro_f1': 0.647,
            'kappa': 0.799,
            'rem_f1': 0.0,
            'total_epochs': 'N/A',
            'method': '正确划分 + 数据截断',
            'key_improvements': ['正确的被试级别划分'],
            'issues': ['数据截断导致REM丢失']
        },
        'V6': {
            'version': 'V6_class_weights',
            'accuracy': 0.14,
            'macro_f1': 0.05,
            'kappa': -0.005,
            'rem_f1': 0.0,
            'total_epochs': 'N/A',
            'method': '完整数据 + 类别权重',
            'key_improvements': ['使用完整数据'],
            'issues': ['类别权重过强，训练不稳定']
        },
        'V7': {
            'version': 'V7_focal_loss',
            'accuracy': 0.8564,  # 正确评估后的结果
            'macro_f1': 0.7890,
            'kappa': 0.8051,
            'rem_f1': 0.8152,
            'total_epochs': 9746,
            'method': '完整数据 + Focal Loss',
            'key_improvements': ['Focal Loss', 'REM检测成功']
        }
    }
    
    results.update(historical_versions)
    results['V7_correct'] = v7_correct
    
    return results


def create_progress_visualization(results):
    """创建进展可视化"""
    versions = ['V1-V4', 'V5', 'V6', 'V7']
    metrics = ['accuracy', 'macro_f1', 'kappa', 'rem_f1']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('序列MAMBAFORMER版本进展分析', fontsize=16, fontweight='bold')
    
    colors = ['#ff7f7f', '#7fbfff', '#ff7fff', '#7fff7f']  # 红、蓝、紫、绿
    
    for i, metric in enumerate(metrics):
        ax = axes[i//2, i%2]
        values = []
        labels = []
        colors_used = []
        
        for j, version in enumerate(versions):
            if version in results:
                val = results[version][metric]
                if val is not None and val >= 0:  # 排除无效值
                    values.append(val)
                    labels.append(version)
                    colors_used.append(colors[j])
        
        bars = ax.bar(labels, values, color=colors_used, alpha=0.7)
        ax.set_ylabel(metric.replace('_', ' ').title())
        ax.set_title(f'{metric.replace("_", " ").title()} 进展')
        ax.set_ylim(0, 1 if metric != 'kappa' else 1)
        
        # 添加数值标签
        for bar, val in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{val:.3f}' if val >= 0 else 'N/A',
                   ha='center', va='bottom', fontweight='bold')
        
        # 添加零线（对于kappa）
        if metric == 'kappa':
            ax.axhline(y=0, color='black', linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('../../results/comprehensive_progress.png', dpi=300, bbox_inches='tight')
    plt.show()


def analyze_key_discoveries(results):
    """分析关键发现"""
    print("🔍 关键发现和解决方案分析")
    print("=" * 80)
    
    discoveries = [
        {
            'issue': '数据泄露问题',
            'versions_affected': 'V1-V4',
            'problem': '使用random_split导致同一被试数据出现在训练和验证集',
            'solution': 'V5开始使用被试级别划分',
            'impact': '验证指标从虚高变为可信',
            'lesson': '时序医疗数据必须按被试划分，不能随机划分'
        },
        {
            'issue': 'REM样本截断问题',
            'versions_affected': 'V1-V5',
            'problem': 'max_samples_per_file=150导致REM样本大量丢失',
            'solution': 'V6/V7移除限制，使用完整数据',
            'impact': 'REM F1从0%提升到81.5%',
            'lesson': 'REM通常在睡眠后期，不能截断数据'
        },
        {
            'issue': '类别不平衡处理',
            'versions_affected': 'V6',
            'problem': '类别权重[1,1.3,1,1.3,1.5]过强，导致训练崩溃',
            'solution': 'V7使用Focal Loss(γ=1.5)替代',
            'impact': '训练稳定，各类别性能平衡',
            'lesson': 'Focal Loss比类别权重更适合序列到序列任务'
        },
        {
            'issue': '评估方法错误',
            'versions_affected': 'V7原始评估',
            'problem': '统计序列位置预测而不是epoch最终预测',
            'solution': '实现正确的epoch级别评估和平均概率策略',
            'impact': '所有指标小幅提升（准确率+1.05%）',
            'lesson': '序列到序列模型需要正确的epoch级别聚合评估'
        },
        {
            'issue': 'Kappa和Macro-F1指标缺失',
            'versions_affected': 'V1',
            'problem': '缺少重要的评估指标',
            'solution': 'V2开始加入Kappa和Macro-F1',
            'impact': '更全面的模型评估',
            'lesson': '睡眠分期需要多个指标综合评估'
        }
    ]
    
    for i, discovery in enumerate(discoveries, 1):
        print(f"\n{i}. {discovery['issue']}")
        print(f"   影响版本: {discovery['versions_affected']}")
        print(f"   问题描述: {discovery['problem']}")
        print(f"   解决方案: {discovery['solution']}")
        print(f"   实际影响: {discovery['impact']}")
        print(f"   经验教训: {discovery['lesson']}")


def analyze_performance_metrics(results):
    """分析性能指标"""
    print("\n📊 性能指标详细分析")
    print("=" * 80)
    
    v7_metrics = results['V7_correct']
    
    print("V7最终性能（正确评估）:")
    print(f"  测试准确率: {v7_metrics['accuracy']:.1%} (85.6%)")
    print(f"  Macro F1:   {v7_metrics['macro_f1']:.3f} (0.789)")
    print(f"  Cohen Kappa: {v7_metrics['kappa']:.3f} (0.805, 很强一致性)")
    print(f"  REM F1:     {v7_metrics['rem_f1']:.1%} (81.5%)")
    print(f"  测试epochs: {v7_metrics['total_epochs']}")
    
    print("\n各睡眠阶段性能（基于V7正确评估）:")
    stage_performance = {
        'Wake': {'f1': 0.913, 'precision': 0.989, 'recall': 0.848},
        'N1': {'f1': 0.467, 'precision': 0.550, 'recall': 0.406},
        'N2': {'f1': 0.876, 'precision': 0.855, 'recall': 0.899},
        'N3': {'f1': 0.874, 'precision': 0.897, 'recall': 0.852},
        'REM': {'f1': 0.815, 'precision': 0.740, 'recall': 0.908}
    }
    
    for stage, metrics in stage_performance.items():
        print(f"  {stage:>4}: F1={metrics['f1']:.3f}, "
              f"Prec={metrics['precision']:.3f}, "
              f"Recall={metrics['recall']:.3f}")
    
    print("\n🎯 性能亮点:")
    print("  ✅ Wake检测精确率98.9%（误报很少）")
    print("  ✅ N2/N3检测F1均达87%+（主要睡眠阶段）")
    print("  ✅ REM检测召回率90.8%（漏检很少）")
    print("  ⚠️  N1检测F1仅46.7%（过渡阶段，本来就难）")


def analyze_technical_innovations(results):
    """分析技术创新"""
    print("\n🚀 技术创新和改进")
    print("=" * 80)
    
    innovations = [
        {
            'innovation': '序列到序列架构',
            'description': '输入5个连续epochs，输出5个预测，利用时序上下文',
            'benefit': '比单epoch预测提供更好的时序一致性'
        },
        {
            'innovation': '滑动窗口策略',
            'description': 'stride=1的滑动窗口，同一epoch被多个序列包含',
            'benefit': '增加数据量，每个epoch得到多次预测'
        },
        {
            'innovation': '平均概率策略',
            'description': '对同一epoch的多个预测概率取平均，然后选择最高概率类别',
            'benefit': '减少预测噪声，提高单epoch预测准确性'
        },
        {
            'innovation': 'Focal Loss',
            'description': 'γ=1.5的Focal Loss，关注难分类样本',
            'benefit': '有效处理类别不平衡，避免训练崩溃'
        },
        {
            'innovation': '多任务学习',
            'description': '主任务（5类分类）+ 辅助任务（深睡眠检测）',
            'benefit': '辅助任务帮助学习更好的特征表示'
        },
        {
            'innovation': '时序一致性损失',
            'description': '相邻预测的平滑度约束',
            'benefit': '减少时序上的抖动，提高预测平滑度'
        },
        {
            'innovation': '被试级别数据划分',
            'description': '确保同一被试数据不会同时出现在训练和测试集',
            'benefit': '避免数据泄露，确保泛化性能评估准确性'
        }
    ]
    
    for i, innovation in enumerate(innovations, 1):
        print(f"{i}. {innovation['innovation']}")
        print(f"   描述: {innovation['description']}")
        print(f"   优势: {innovation['benefit']}")


def future_improvements():
    """未来改进方向"""
    print("\n🔮 未来改进方向")
    print("=" * 80)
    
    improvements = [
        {
            'direction': 'V8增强版本（进行中）',
            'features': [
                '标签平滑损失（减少过拟合）',
                '学习率预热+余弦退火（更好的收敛）',
                '混合精度训练（加速训练）',
                '增强数据增强（提高泛化）'
            ],
            'expected_gain': '预期准确率提升1-2%'
        },
        {
            'direction': '多模态融合',
            'features': [
                '逐步添加EOG和EMG信号',
                'Cross-Modal Transformer注意力机制',
                '模态级别的dropout和噪声注入'
            ],
            'expected_gain': '预期整体性能提升2-3%'
        },
        {
            'direction': '架构优化',
            'features': [
                '更深的网络（6-8层Transformer）',
                'MAMBAFORMER的状态空间建模',
                '自适应序列长度'
            ],
            'expected_gain': '预期在复杂模式上有更好表现'
        },
        {
            'direction': '后处理优化',
            'features': [
                'HMM后处理平滑',
                '基于规则的约束（如最小REM持续时间）',
                '集成多个模型的预测'
            ],
            'expected_gain': '预期Kappa提升0.02-0.03'
        }
    ]
    
    for improvement in improvements:
        print(f"📈 {improvement['direction']}")
        for feature in improvement['features']:
            print(f"   • {feature}")
        print(f"   预期收益: {improvement['expected_gain']}")
        print()


def main():
    """主函数"""
    print("🎯 序列MAMBAFORMER全面进展总结")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"目标: 睡眠分期分类论文，投稿ICASSP 2026")
    print()
    
    # 加载所有结果
    results = load_all_results()
    
    # 创建可视化
    create_progress_visualization(results)
    
    # 分析关键发现
    analyze_key_discoveries(results)
    
    # 分析性能指标
    analyze_performance_metrics(results)
    
    # 分析技术创新
    analyze_technical_innovations(results)
    
    # 未来改进方向
    future_improvements()
    
    # 总结
    print("\n🏆 总结")
    print("=" * 80)
    print("✅ 已解决的关键问题:")
    print("   • 数据泄露 (V5+)")
    print("   • REM检测失败 (V6+)")
    print("   • 类别不平衡 (V7+)")
    print("   • 评估方法错误 (V7正确评估)")
    print("   • 缺少关键指标 (V2+)")
    print()
    print("📊 当前最佳性能 (V7正确评估):")
    print("   • 准确率: 85.6%")
    print("   • Kappa: 0.805 (很强一致性)")
    print("   • REM F1: 81.5%")
    print("   • 测试epochs: 9,746")
    print()
    print("🚀 下一步:")
    print("   • V8训练中（增强技巧）")
    print("   • 准备多模态融合")
    print("   • 考虑后处理优化")
    print("   • 文档和论文准备")


if __name__ == "__main__":
    main()