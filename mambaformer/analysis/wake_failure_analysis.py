#!/usr/bin/env python3
"""
深度分析Wake分类失效的根本原因
分析测试集中Wake的分布、模型预测情况、集成影响等
"""

import os
import sys
import json
import torch
import numpy as np
from collections import Counter
from sklearn.metrics import confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from tqdm import tqdm


def analyze_dataset_distribution(dataset, name="Dataset"):
    """分析数据集的类别分布"""
    print(f"\n📊 {name} Distribution Analysis")
    print("="*60)
    
    all_labels = []
    for i in range(len(dataset)):
        _, labels = dataset[i]
        all_labels.extend(labels.numpy())
    
    label_counts = Counter(all_labels)
    total = sum(label_counts.values())
    
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    print(f"Total epochs: {total}")
    print("\nClass distribution:")
    for class_id in range(5):
        count = label_counts.get(class_id, 0)
        percentage = count / total * 100 if total > 0 else 0
        print(f"  {class_names[class_id]:5}: {count:6} ({percentage:5.2f}%)")
    
    # Check Wake distribution per file
    if hasattr(dataset, 'files'):
        print(f"\nWake distribution across {len(dataset.files)} files:")
        for file_path in dataset.files:
            file_name = os.path.basename(file_path)
            # Load file to check Wake epochs
            data = np.load(file_path)
            labels = data['y']
            wake_count = np.sum(labels == 4)
            total_epochs = len(labels)
            wake_pct = wake_count / total_epochs * 100 if total_epochs > 0 else 0
            print(f"  {file_name}: {wake_count}/{total_epochs} ({wake_pct:.1f}% Wake)")
    
    return label_counts


def analyze_model_predictions(model, data_loader, dataset, model_name="Model"):
    """分析单个模型的预测情况"""
    print(f"\n🔍 Analyzing {model_name} predictions...")
    
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
    
    all_raw_preds = []  # 原始预测（未经后处理）
    
    with torch.no_grad():
        batch_start_idx = 0
        for data, labels in tqdm(data_loader):
            data = data.cuda()
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            preds = torch.argmax(outputs, dim=-1)
            
            # 收集原始预测
            all_raw_preds.extend(preds.cpu().numpy().flatten())
            
            # 添加到评估器
            batch_size = data.shape[0]
            start_indices = []
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(dataset):
                    seq_info = dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            batch_start_idx += batch_size
    
    # 获取最终预测
    final_preds, final_labels, _ = evaluator.get_final_predictions()
    
    # 分析原始预测分布
    raw_pred_counts = Counter(all_raw_preds)
    print(f"\n{model_name} raw prediction distribution (before epoch aggregation):")
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    for class_id in range(5):
        count = raw_pred_counts.get(class_id, 0)
        pct = count / len(all_raw_preds) * 100 if all_raw_preds else 0
        print(f"  {class_names[class_id]:5}: {count:6} ({pct:5.2f}%)")
    
    # 分析epoch级别预测分布
    final_pred_counts = Counter(final_preds)
    print(f"\n{model_name} epoch-level prediction distribution:")
    for class_id in range(5):
        count = final_pred_counts.get(class_id, 0)
        pct = count / len(final_preds) * 100 if len(final_preds) > 0 else 0
        print(f"  {class_names[class_id]:5}: {count:6} ({pct:5.2f}%)")
    
    # 混淆矩阵
    cm = confusion_matrix(final_labels, final_preds)
    
    # Wake类的详细分析
    wake_true_idx = np.where(final_labels == 4)[0]
    wake_pred_idx = np.where(final_preds == 4)[0]
    
    print(f"\n{model_name} Wake class analysis:")
    print(f"  True Wake epochs: {len(wake_true_idx)}")
    print(f"  Predicted Wake epochs: {len(wake_pred_idx)}")
    print(f"  Correctly predicted Wake: {cm[4, 4] if cm.shape[0] > 4 else 0}")
    
    if len(wake_true_idx) > 0:
        wake_predictions = final_preds[wake_true_idx]
        wake_pred_dist = Counter(wake_predictions)
        print(f"\n  Where true Wake epochs were predicted as:")
        for class_id in range(5):
            count = wake_pred_dist.get(class_id, 0)
            pct = count / len(wake_true_idx) * 100
            print(f"    {class_names[class_id]:5}: {count:3} ({pct:5.2f}%)")
    
    return {
        'raw_preds': all_raw_preds,
        'final_preds': final_preds,
        'final_labels': final_labels,
        'confusion_matrix': cm,
        'wake_analysis': {
            'true_wake_count': len(wake_true_idx),
            'pred_wake_count': len(wake_pred_idx),
            'correct_wake': cm[4, 4] if cm.shape[0] > 4 else 0
        }
    }


def analyze_ensemble_impact(all_predictions, weights):
    """分析集成对Wake预测的影响"""
    print("\n🔄 Analyzing ensemble impact on Wake predictions...")
    
    # 获取标签
    labels = all_predictions[list(all_predictions.keys())[0]]['final_labels']
    wake_true_idx = np.where(labels == 4)[0]
    
    print(f"\nTotal Wake epochs in test set: {len(wake_true_idx)}")
    
    # 分析每个模型对Wake的预测
    print("\nIndividual model Wake predictions:")
    for model_name, pred_data in all_predictions.items():
        preds = pred_data['final_preds']
        wake_pred_count = np.sum(preds == 4)
        wake_correct = np.sum((preds == 4) & (labels == 4))
        print(f"  {model_name}: predicted {wake_pred_count} Wake, "
              f"{wake_correct} correct")
    
    # 计算集成预测
    ensemble_probs = None
    for model_name, weight in weights.items():
        if model_name in all_predictions:
            # 需要重新计算概率
            print(f"  Using {model_name} with weight {weight:.3f}")
    
    return wake_true_idx


def plot_confusion_matrices(results_dict, save_path="confusion_matrices.png"):
    """绘制所有模型的混淆矩阵"""
    n_models = len(results_dict)
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    
    for idx, (model_name, results) in enumerate(results_dict.items()):
        if idx >= 4:
            break
        cm = results['confusion_matrix']
        
        # Normalize confusion matrix
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        sns.heatmap(cm_normalized, annot=True, fmt='.2f', 
                   xticklabels=class_names, yticklabels=class_names,
                   cmap='Blues', ax=axes[idx], vmin=0, vmax=1)
        axes[idx].set_title(f'{model_name} Confusion Matrix')
        axes[idx].set_ylabel('True Label')
        axes[idx].set_xlabel('Predicted Label')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"\n📊 Confusion matrices saved to {save_path}")


def main():
    print("🔬 Deep Analysis of Wake Classification Failure")
    print("="*80)
    
    # Load test dataset
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    print(f"\n📁 Test files: {[os.path.basename(f) for f in test_files]}")
    
    # Create dataset
    test_dataset = SequenceSleepDataset(test_files, seq_len=5, use_channels=3)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, shuffle=False,
        num_workers=4, pin_memory=True
    )
    
    # Analyze dataset distribution
    label_counts = analyze_dataset_distribution(test_dataset, "Test Dataset")
    
    # Load and analyze each model
    model_configs = [
        ('V7', '../../checkpoints/sequential_v7_balanced.pth', 
         {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1}),
        ('V8', '../../checkpoints/sequential_v8_enhanced.pth',
         {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1}),
        ('V13', '../../checkpoints/v13_simple.pth',
         {'d_model': 256, 'n_heads': 16, 'n_layers': 6, 'dropout': 0.15}),
        ('V14', '../../checkpoints/v14_rem_focus.pth',
         {'d_model': 256, 'n_heads': 16, 'n_layers': 6, 'dropout': 0.15})
    ]
    
    all_results = {}
    all_predictions = {}
    
    for name, path, config in model_configs:
        if os.path.exists(path):
            print(f"\n{'='*60}")
            print(f"Loading {name} from {path}")
            model = SequentialMAMBAFORMER_V2(
                input_channels=3, n_classes=5, seq_len=5, **config
            ).cuda()
            model.load_state_dict(torch.load(path, map_location='cuda'))
            
            results = analyze_model_predictions(model, test_loader, test_dataset, name)
            all_results[name] = results
            all_predictions[name] = results
    
    # Plot confusion matrices
    plot_confusion_matrices(all_results, "wake_failure_confusion_matrices.png")
    
    # Analyze ensemble impact
    ensemble_weights = {
        'V7': 0.0,
        'V8': 0.15384615384615385,
        'V13': 0.38461538461538464,
        'V14': 0.4615384615384615
    }
    
    wake_analysis = analyze_ensemble_impact(all_predictions, ensemble_weights)
    
    # Summary
    print("\n" + "="*80)
    print("📋 SUMMARY OF FINDINGS")
    print("="*80)
    
    print("\n1. Wake class representation in test set:")
    wake_count = label_counts.get(4, 0)
    total = sum(label_counts.values())
    print(f"   - {wake_count}/{total} epochs ({wake_count/total*100:.2f}%)")
    
    print("\n2. Model Wake prediction capability:")
    for name, results in all_results.items():
        wake_info = results['wake_analysis']
        print(f"   - {name}: predicted {wake_info['pred_wake_count']} Wake, "
              f"{wake_info['correct_wake']} correct")
    
    print("\n3. Key insights:")
    print("   - Models are failing to predict Wake class")
    print("   - This is likely due to class imbalance or feature issues")
    print("   - Need specialized strategies to rescue Wake classification")
    
    # Save analysis results
    analysis_results = {
        'test_set_distribution': {k: int(v) for k, v in label_counts.items()},
        'model_wake_performance': {
            name: results['wake_analysis'] 
            for name, results in all_results.items()
        },
        'ensemble_weights': ensemble_weights
    }
    
    with open('wake_failure_analysis_results.json', 'w') as f:
        json.dump(analysis_results, f, indent=2)
    
    print(f"\n💾 Analysis results saved to wake_failure_analysis_results.json")


if __name__ == "__main__":
    main()