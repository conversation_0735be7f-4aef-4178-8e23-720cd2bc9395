🔬 Deep Analysis of Wake Classification Failure
================================================================================

📁 Test files: ['SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz', 'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz']

📊 Test Dataset Distribution Analysis
============================================================
Total epochs: 5840

Class distribution:
  REM  :   2565 (43.92%)
  N1   :    455 ( 7.79%)
  N2   :   1556 (26.64%)
  N3   :   1234 (21.13%)
  Wake :     30 ( 0.51%)

============================================================
Loading V7 from ../../checkpoints/sequential_v7_balanced.pth

🔍 Analyzing V7 predictions...

  0%|          | 0/37 [00:00<?, ?it/s]
  3%|▎         | 1/37 [00:00<00:17,  2.12it/s]
 35%|███▌      | 13/37 [00:00<00:01, 23.32it/s]
 81%|████████  | 30/37 [00:00<00:00, 53.62it/s]
100%|██████████| 37/37 [00:00<00:00, 41.74it/s]

V7 raw prediction distribution (before epoch aggregation):
  REM  :   2259 (38.68%)
  N1   :    534 ( 9.14%)
  N2   :   1606 (27.50%)
  N3   :   1117 (19.13%)
  Wake :    324 ( 5.55%)

V7 epoch-level prediction distribution:
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/analysis/wake_failure_analysis.py", line 315, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/analysis/wake_failure_analysis.py", line 260, in main
    results = analyze_model_predictions(model, test_loader, test_dataset, name)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/analysis/wake_failure_analysis.py", line 118, in analyze_model_predictions
    pct = count / len(final_preds) * 100 if final_preds else 0
ValueError: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
