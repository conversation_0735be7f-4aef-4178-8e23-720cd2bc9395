"""
实时进展监控器
监控所有版本的训练进展：V8, V9, V10
分析性能对比和技术演进

基于ultrathink原则，持续监控所有并行训练
"""

import os
import re
import json
import time
import datetime
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from collections import defaultdict
import logging


class RealTimeProgressMonitor:
    """实时训练进展监控器"""
    
    def __init__(self):
        self.logs_dir = "../logs"
        self.results_dir = "../../results"
        self.log_patterns = {
            'V8': r'sequential_v8_enhanced_\d+_\d+\.log',
            'V9': r'multimodal_v9_eeg_only_\d+_\d+\.log',
            'V10': r'multimodal_v10_eeg_eog_\d+_\d+\.log'
        }
        
        # 性能基线
        self.baselines = {
            'V7_correct': {'accuracy': 0.8564, 'macro_f1': 0.7890, 'kappa': 0.8051, 'rem_f1': 0.8152},
            'target_icassp': {'accuracy': 0.90, 'macro_f1': 0.85, 'kappa': 0.87, 'rem_f1': 0.85}
        }
        
    def find_latest_logs(self):
        """找到最新的日志文件"""
        latest_logs = {}
        
        for version, pattern in self.log_patterns.items():
            log_files = []
            if os.path.exists(self.logs_dir):
                for filename in os.listdir(self.logs_dir):
                    if re.match(pattern, filename):
                        filepath = os.path.join(self.logs_dir, filename)
                        log_files.append((filepath, os.path.getctime(filepath)))
            
            if log_files:
                # 按创建时间排序，取最新的
                latest_log = max(log_files, key=lambda x: x[1])[0]
                latest_logs[version] = latest_log
                
        return latest_logs
    
    def parse_training_progress(self, log_file, version):
        """解析训练进展"""
        if not os.path.exists(log_file):
            return None
            
        progress = {
            'version': version,
            'epochs': [],
            'current_epoch': 0,
            'best_metrics': {},
            'status': 'unknown',
            'architecture': '',
            'features': []
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                
                # 架构信息
                if '创建' in line and 'MAMBAFORMER' in line:
                    if 'MultiModalMAMBAFORMER' in line:
                        progress['architecture'] = 'MultiModal MAMBAFORMER'
                    else:
                        progress['architecture'] = 'Sequential MAMBAFORMER'
                
                # 特性信息
                if '新特性:' in line or 'V9新特性:' in line or 'V10新特性:' in line:
                    progress['features'] = self._extract_features_from_log(lines, line)
                
                # 训练进展
                epoch_match = re.search(r'Epoch (\d+)/(\d+)', line)
                if epoch_match:
                    current_epoch = int(epoch_match.group(1))
                    total_epochs = int(epoch_match.group(2))
                    progress['current_epoch'] = current_epoch
                    progress['total_epochs'] = total_epochs
                
                # 性能指标
                if 'Val   -' in line and 'Acc:' in line:
                    metrics = self._parse_metrics_line(line)
                    if metrics:
                        metrics['epoch'] = progress['current_epoch']
                        progress['epochs'].append(metrics)
                
                # 最佳模型
                if '💾 新的最佳模型:' in line:
                    best_metrics = self._parse_best_metrics_line(line)
                    if best_metrics:
                        progress['best_metrics'] = best_metrics
                
                # 训练状态
                if '早停:' in line:
                    progress['status'] = 'early_stopped'
                elif '训练完成' in line:
                    progress['status'] = 'completed'
                elif 'Epoch' in line and 'Train' in line:
                    progress['status'] = 'training'
        
        except Exception as e:
            print(f"解析日志文件 {log_file} 失败: {e}")
            return None
        
        return progress
    
    def _extract_features_from_log(self, lines, start_line):
        """从日志中提取特性列表"""
        features = []
        start_idx = lines.index(start_line + '\n') if start_line + '\n' in lines else -1
        
        if start_idx >= 0:
            for i in range(start_idx + 1, min(start_idx + 10, len(lines))):
                line = lines[i].strip()
                if line.startswith('  •'):
                    feature = line.replace('  •', '').strip()
                    features.append(feature)
                elif line.startswith('📋') or line.startswith('🖥️'):
                    break
        
        return features
    
    def _parse_metrics_line(self, line):
        """解析指标行"""
        try:
            # Val   - Acc: 0.8393, F1: 0.7711, Kappa: 0.7784
            metrics = {}
            
            acc_match = re.search(r'Acc: ([\d.]+)', line)
            f1_match = re.search(r'F1: ([\d.]+)', line)  
            kappa_match = re.search(r'Kappa: ([\d.]+)', line)
            
            if acc_match:
                metrics['accuracy'] = float(acc_match.group(1))
            if f1_match:
                metrics['macro_f1'] = float(f1_match.group(1))
            if kappa_match:
                metrics['kappa'] = float(kappa_match.group(1))
                
            return metrics if metrics else None
        except:
            return None
    
    def _parse_best_metrics_line(self, line):
        """解析最佳模型行"""
        try:
            # 💾 新的最佳模型: F1=0.7711, Kappa=0.7784
            metrics = {}
            
            f1_match = re.search(r'F1=([\d.]+)', line)
            kappa_match = re.search(r'Kappa=([\d.]+)', line)
            
            if f1_match:
                metrics['macro_f1'] = float(f1_match.group(1))
            if kappa_match:
                metrics['kappa'] = float(kappa_match.group(1))
                
            return metrics if metrics else None
        except:
            return None
    
    def generate_progress_report(self):
        """生成进展报告"""
        latest_logs = self.find_latest_logs()
        
        if not latest_logs:
            print("没有找到训练日志文件")
            return
        
        print("🚀 MAMBAFORMER训练进展实时监控")
        print("=" * 80)
        print(f"监控时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        all_progress = {}
        for version, log_file in latest_logs.items():
            progress = self.parse_training_progress(log_file, version)
            if progress:
                all_progress[version] = progress
        
        # 版本对比表
        self._print_version_comparison(all_progress)
        
        # 详细进展
        for version, progress in all_progress.items():
            self._print_detailed_progress(version, progress)
        
        # 技术分析
        self._print_technical_analysis(all_progress)
        
        # 展望
        self._print_outlook(all_progress)
        
        return all_progress
    
    def _print_version_comparison(self, all_progress):
        """打印版本对比表"""
        print("📊 版本对比表")
        print("-" * 80)
        
        print(f"{'版本':<8} {'架构':<20} {'状态':<12} {'进度':<12} {'最佳F1':<10} {'最佳Kappa':<12}")
        print("-" * 80)
        
        for version, progress in all_progress.items():
            status_map = {
                'training': '🔄 训练中',
                'early_stopped': '⏹️ 早停',
                'completed': '✅ 完成',
                'unknown': '❓ 未知'
            }
            
            arch_short = progress['architecture'].replace('MAMBAFORMER', 'MF')
            status_str = status_map.get(progress['status'], progress['status'])
            
            if progress.get('total_epochs'):
                progress_str = f"{progress['current_epoch']}/{progress['total_epochs']}"
            else:
                progress_str = f"{progress['current_epoch']}/?"
            
            best_f1 = progress['best_metrics'].get('macro_f1', 0)
            best_kappa = progress['best_metrics'].get('kappa', 0)
            
            print(f"{version:<8} {arch_short:<20} {status_str:<12} {progress_str:<12} {best_f1:<10.4f} {best_kappa:<12.4f}")
        
        print()
    
    def _print_detailed_progress(self, version, progress):
        """打印详细进展"""
        print(f"🔍 {version} 详细进展")
        print("-" * 40)
        
        print(f"架构: {progress['architecture']}")
        print(f"状态: {progress['status']}")
        print(f"当前epoch: {progress['current_epoch']}")
        
        if progress['features']:
            print("特性:")
            for feature in progress['features'][:5]:  # 显示前5个特性
                print(f"  • {feature}")
        
        if progress['best_metrics']:
            best = progress['best_metrics']
            print(f"最佳指标: F1={best.get('macro_f1', 0):.4f}, Kappa={best.get('kappa', 0):.4f}")
        
        # 最近几轮性能
        if progress['epochs']:
            recent_epochs = progress['epochs'][-3:]  # 最近3轮
            print("最近性能:")
            for epoch_data in recent_epochs:
                epoch_num = epoch_data.get('epoch', '?')
                acc = epoch_data.get('accuracy', 0)
                f1 = epoch_data.get('macro_f1', 0)
                kappa = epoch_data.get('kappa', 0)
                print(f"  Epoch {epoch_num}: Acc={acc:.4f}, F1={f1:.4f}, Kappa={kappa:.4f}")
        
        print()
    
    def _print_technical_analysis(self, all_progress):
        """打印技术分析"""
        print("🔬 技术分析")
        print("-" * 40)
        
        # V7基线对比
        v7_baseline = self.baselines['V7_correct']
        print(f"V7基线: Acc={v7_baseline['accuracy']:.4f}, F1={v7_baseline['macro_f1']:.4f}, Kappa={v7_baseline['kappa']:.4f}")
        print()
        
        # 各版本相对V7的改进
        for version, progress in all_progress.items():
            if progress['best_metrics']:
                best = progress['best_metrics']
                current_f1 = best.get('macro_f1', 0)
                current_kappa = best.get('kappa', 0)
                
                f1_improvement = current_f1 - v7_baseline['macro_f1']
                kappa_improvement = current_kappa - v7_baseline['kappa']
                
                print(f"{version} vs V7:")
                print(f"  F1改进: {f1_improvement:+.4f} ({'✅' if f1_improvement > 0 else '❌'})")
                print(f"  Kappa改进: {kappa_improvement:+.4f} ({'✅' if kappa_improvement > 0 else '❌'})")
        
        print()
    
    def _print_outlook(self, all_progress):
        """打印展望"""
        print("🎯 研究展望")
        print("-" * 40)
        
        # ICASSP 2026目标
        target = self.baselines['target_icassp']
        print(f"ICASSP 2026目标: Acc={target['accuracy']:.3f}, F1={target['macro_f1']:.3f}, Kappa={target['kappa']:.3f}")
        print()
        
        # 差距分析
        best_current_f1 = 0
        best_version = None
        for version, progress in all_progress.items():
            if progress['best_metrics']:
                f1 = progress['best_metrics'].get('macro_f1', 0)
                if f1 > best_current_f1:
                    best_current_f1 = f1
                    best_version = version
        
        if best_current_f1 > 0:
            f1_gap = target['macro_f1'] - best_current_f1
            print(f"当前最佳: {best_version} F1={best_current_f1:.4f}")
            print(f"距离目标还需: F1提升{f1_gap:.4f}")
            
            if f1_gap > 0:
                print("\n下一步策略:")
                if 'V10' not in all_progress or all_progress['V10']['status'] != 'completed':
                    print("  • 完成V10 EEG+EOG多模态融合验证")
                print("  • 实施V11完整多模态（EEG+EOG+EMG）")
                print("  • 尝试更深的网络架构")
                print("  • 实施HMM后处理平滑")
                print("  • 集成多模型预测")
        
        print()
        print("🌟 研究进展很好！继续保持ultrathink节奏！")


def main():
    """主函数"""
    monitor = RealTimeProgressMonitor()
    
    try:
        progress_data = monitor.generate_progress_report()
        
        # 保存进展数据
        if progress_data:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"../../results/progress_monitor_{timestamp}.json"
            
            os.makedirs("../../results", exist_ok=True)
            with open(output_file, 'w') as f:
                json.dump(progress_data, f, indent=2, default=str)
            
            print(f"📁 进展数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"监控过程出错: {e}")


if __name__ == "__main__":
    main()