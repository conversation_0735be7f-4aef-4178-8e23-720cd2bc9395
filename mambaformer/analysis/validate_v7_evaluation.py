"""
验证V7的测试评估是否正确
分析：是否进行了正确的epoch级别聚合
"""

import sys
import os
import json
import numpy as np
import torch
from tqdm import tqdm

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


def analyze_v7_evaluation():
    """分析V7的评估方法"""
    print("=" * 80)
    print("🔍 分析V7测试评估的正确性")
    print("=" * 80)
    
    # 1. 读取V7结果
    with open('../../configs/sequential_v7_balanced_results.json', 'r') as f:
        v7_results = json.load(f)
    
    cm = np.array(v7_results['result']['confusion_matrix'])
    total_predictions = cm.sum()
    
    print(f"📊 V7结果分析：")
    print(f"  混淆矩阵总预测数: {total_predictions}")
    print(f"  测试准确率: {v7_results['result']['test_acc']:.4f}")
    print(f"  测试F1: {v7_results['result']['test_f1']:.4f}")
    print(f"  测试Kappa: {v7_results['result']['test_kappa']:.4f}")
    print(f"  REM F1: {v7_results['result']['rem_f1']:.4f}")
    
    # 2. 分析数据集构成
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    test_files = [os.path.join('/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/', f) 
                  for f in splits['splits']['test']['files']]
    
    # 创建测试数据集（模拟V7的设置）
    test_dataset = SequenceSleepDataset(
        test_files,
        max_samples_per_file=None,  # V7使用完整数据
        seq_len=5,
        use_channels=3
    )
    
    print(f"\n📊 测试数据集分析：")
    print(f"  测试文件数: {len(test_files)}")
    print(f"  测试序列数: {len(test_dataset)}")
    print(f"  总epochs: {test_dataset.get_total_epochs()}")
    print(f"  序列预测数: {len(test_dataset) * 5}")
    
    # 3. 判断评估方法
    print(f"\n🎯 评估方法判断：")
    if total_predictions == test_dataset.get_total_epochs():
        print("✅ V7使用了正确的epoch级别评估！")
        print("   - 混淆矩阵统计的是每个epoch的最终预测")
        print("   - 实现了正确的平均概率策略")
        evaluation_correct = True
    elif total_predictions == len(test_dataset) * 5:
        print("❌ V7使用了序列级别评估（错误）！")
        print("   - 混淆矩阵统计的是序列中每个位置的预测")
        print("   - 没有进行epoch级别的聚合")
        print("   - 同一个epoch被重复统计了多次")
        evaluation_correct = False
    else:
        print(f"⚠️  无法确定评估方法，预测数 {total_predictions} 不匹配任何预期值")
        evaluation_correct = None
    
    # 4. 分析重复统计问题
    if not evaluation_correct:
        print(f"\n🔍 重复统计分析：")
        
        # 计算每个epoch被预测的次数
        epoch_prediction_counts = {}
        for seq_idx in range(len(test_dataset)):
            seq_info = test_dataset.get_sequence_info(seq_idx)
            if seq_info:
                for epoch_idx in seq_info['epoch_indices']:
                    epoch_prediction_counts[epoch_idx] = epoch_prediction_counts.get(epoch_idx, 0) + 1
        
        unique_epochs = len(epoch_prediction_counts)
        total_epoch_predictions = sum(epoch_prediction_counts.values())
        avg_predictions_per_epoch = total_epoch_predictions / unique_epochs if unique_epochs > 0 else 0
        
        print(f"  独特epochs数: {unique_epochs}")
        print(f"  总epoch预测数: {total_epoch_predictions}")
        print(f"  平均每epoch预测次数: {avg_predictions_per_epoch:.2f}")
        
        # 分析边界epochs
        min_predictions = min(epoch_prediction_counts.values()) if epoch_prediction_counts else 0
        max_predictions = max(epoch_prediction_counts.values()) if epoch_prediction_counts else 0
        print(f"  最少预测次数: {min_predictions} (边界epochs)")
        print(f"  最多预测次数: {max_predictions} (中心epochs)")
    
    # 5. 给出建议
    print(f"\n💡 建议：")
    if evaluation_correct:
        print("✅ V7的评估方法是正确的，结果可信。")
        print("📈 可以继续基于这些结果进行后续改进。")
    else:
        print("❌ 需要重新使用正确的epoch级别评估方法。")
        print("🔧 使用epoch_level_evaluation.py中的EpochLevelEvaluator。")
        print("📊 正确的评估可能会显示不同的性能指标。")
    
    return evaluation_correct, {
        'v7_total_predictions': total_predictions,
        'dataset_total_epochs': test_dataset.get_total_epochs(),
        'dataset_sequences': len(test_dataset),
        'sequence_predictions': len(test_dataset) * 5
    }


def load_and_analyze_test_data():
    """加载并分析测试数据的实际分布"""
    print("\n" + "=" * 80)
    print("📋 测试数据详细分析")
    print("=" * 80)
    
    # 加载数据划分
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    test_files = [os.path.join('/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/', f) 
                  for f in splits['splits']['test']['files']]
    
    print("测试文件详情：")
    total_epochs = 0
    file_details = []
    
    for file_path in test_files:
        if os.path.exists(file_path):
            data = np.load(file_path)
            n_epochs = len(data['y'])
            n_sequences = max(0, n_epochs - 4)  # 序列不跨文件
            
            # 统计类别分布
            labels = data['y']
            class_counts = [(labels == i).sum() for i in range(5)]
            
            file_details.append({
                'file': os.path.basename(file_path),
                'epochs': n_epochs,
                'sequences': n_sequences,
                'class_counts': class_counts
            })
            total_epochs += n_epochs
            
            print(f"  {os.path.basename(file_path)}: {n_epochs} epochs → {n_sequences} sequences")
            print(f"    类别分布: W={class_counts[0]}, N1={class_counts[1]}, N2={class_counts[2]}, N3={class_counts[3]}, REM={class_counts[4]}")
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    total_sequences = sum(detail['sequences'] for detail in file_details)
    total_class_counts = [sum(detail['class_counts'][i] for detail in file_details) for i in range(5)]
    
    print(f"\n📊 总计：")
    print(f"  测试epochs: {total_epochs}")
    print(f"  测试sequences: {total_sequences}")
    print(f"  总类别分布: W={total_class_counts[0]}, N1={total_class_counts[1]}, N2={total_class_counts[2]}, N3={total_class_counts[3]}, REM={total_class_counts[4]}")
    
    return total_epochs, total_sequences, total_class_counts


if __name__ == "__main__":
    # 分析V7评估
    is_correct, stats = analyze_v7_evaluation()
    
    # 分析测试数据
    actual_epochs, actual_sequences, class_distribution = load_and_analyze_test_data()
    
    # 最终总结
    print("\n" + "=" * 80)
    print("🎯 最终总结")
    print("=" * 80)
    
    print(f"V7混淆矩阵总数: {stats['v7_total_predictions']}")
    print(f"实际测试epochs: {actual_epochs}")
    print(f"实际测试sequences: {actual_sequences}")
    print(f"序列预测总数: {actual_sequences * 5}")
    
    if stats['v7_total_predictions'] == actual_epochs:
        print("\n✅ 结论：V7使用了正确的epoch级别评估")
        print("📈 指标可信，可以继续改进模型")
    elif stats['v7_total_predictions'] == actual_sequences * 5:
        print("\n❌ 结论：V7使用了错误的序列级别评估")
        print("🔧 需要重新评估以获得正确的epoch级别指标")
    else:
        print(f"\n⚠️  结论：评估方法不确定，需要进一步调查")