# MAMBAFORMER Sleep Stage Classification

基于简化MAMBAFORMER架构的睡眠分期分类系统，面向ICASSP 2026论文投稿。

## 🎯 项目特点

- **无数据泄露**: 严格的受试者级别K折交叉验证
- **真实性能**: 81.75% ± 2.12% 准确率，75.76% ± 2.86% F1分数
- **多任务学习**: 主任务（5类睡眠分期）+ 辅助任务（REM/SWS检测）
- **简化架构**: 去除过度复杂的组件，保持有效性

## 🚀 快速开始

```bash
# 激活环境
conda activate sl

# 运行完整实验
python run_training.py
```

## 📁 目录结构

```
mambaformer/
├── models/                       # 模型定义
│   ├── simple_attn_mambaformer.py    # ⭐ 主模型实现
│   ├── mambaformer_model.py          # 完整MAMBAFORMER（未使用）
│   ├── crossmodal_attention.py       # 跨模态注意力模块
│   └── mamba_ssm.py                  # MAMBA状态空间模型
├── training/                     # 训练脚本
│   ├── train_subject_aware_mambaformer.py  # ⭐ 主训练脚本
│   └── train_*.py                          # 其他实验版本
├── evaluation/                   # 评估工具
│   ├── subject_aware_kfold.py        # ⭐ K折分割生成
│   ├── data_leakage_audit.py         # ⭐ 数据泄露审计
│   └── test_*.py                     # 测试脚本
├── preprocessing/                # 数据预处理
├── utils/                        # 工具函数
└── run_training.py               # ⭐ 一键运行脚本
```

## 📊 实验结果

| Fold | 准确率 | F1分数 | 测试受试者 |
|------|--------|--------|------------|
| 1 | 82.16% | 75.20% | 00, 17, 15, 01 |
| 2 | 78.84% | 71.32% | 08, 05, 11, 03 |
| 3 | 80.52% | 76.22% | 18, 16, 13, 02 |
| 4 | 81.99% | 75.77% | 09, 19, 04, 12 |
| 5 | 85.27% | 80.29% | 07, 10, 14, 06 |
| **平均** | **81.75% ± 2.12%** | **75.76% ± 2.86%** | - |

## 🔧 配置修改

编辑 `training/train_subject_aware_mambaformer.py`:
```python
config = {
    'batch_size': 32,
    'learning_rate': 5e-5,
    'weight_decay': 1e-4,
    'num_epochs': 30,
    'patience': 8,
}
```

## 📈 监控训练

```bash
# 实时查看日志
tail -f ../logs/subject_aware_mambaformer_*.log

# 查看GPU使用
watch -n 1 nvidia-smi
```

## 🎯 核心文件说明

1. **simple_attn_mambaformer.py**: 简化的MAMBAFORMER模型，使用CNN特征提取+Transformer编码器
2. **train_subject_aware_mambaformer.py**: 实现无数据泄露的K折交叉验证训练
3. **subject_aware_kfold.py**: 生成受试者级别的K折分割，确保同一受试者数据不跨训练/测试集
4. **data_leakage_audit.py**: 严格验证数据分割的正确性，适合论文发表要求