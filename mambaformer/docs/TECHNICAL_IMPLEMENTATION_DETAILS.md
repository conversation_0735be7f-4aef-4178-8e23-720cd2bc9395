# 技术实现细节文档

## 1. 关键代码实现

### 1.1 增强的Focal Loss（解决类别不平衡）
```python
class ImprovedFocalLoss(nn.Module):
    """改进的Focal Loss，动态调整gamma"""
    def __init__(self, alpha=None, gamma=2.0, gamma_min=0.5):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.gamma_min = gamma_min
        
    def forward(self, input, target):
        ce_loss = F.cross_entropy(input, target, reduction='none')
        p = torch.exp(-ce_loss)
        
        # 动态gamma - 对于难分类的样本使用更大的gamma
        gamma_t = self.gamma * (1 - p) + self.gamma_min
        loss = (1 - p) ** gamma_t * ce_loss
        
        if self.alpha is not None:
            # 确保alpha是1D张量
            if self.alpha.dim() > 1:
                alpha = self.alpha.view(-1)
            else:
                alpha = self.alpha
            # 获取每个目标的alpha值
            alpha_t = alpha.gather(0, target)
            loss = alpha_t * loss
            
        return loss.mean()
```

### 1.2 REM专注损失函数
```python
class REMFocusedLoss(nn.Module):
    """专注于改善REM分类的损失函数"""
    def __init__(self, alpha=None, gamma=2.0, rem_weight=3.0):
        super().__init__()
        self.rem_weight = rem_weight  # REM类的额外权重
        self.ce_loss = nn.CrossEntropyLoss(reduction='none')
        
    def forward(self, input, target):
        # Focal损失计算
        ce_loss = self.ce_loss(input, target)
        p = torch.exp(-ce_loss)
        focal_loss = (1 - p) ** self.gamma * ce_loss
        
        # REM类额外加权 (class 0)
        rem_mask = (target == 0).float()
        focal_loss = focal_loss * (1 + rem_mask * (self.rem_weight - 1))
        
        # REM置信度正则化
        if rem_mask.any():
            rem_outputs = input[rem_mask.bool()]
            rem_probs = torch.softmax(rem_outputs, dim=-1)
            rem_confidence_loss = -torch.log(rem_probs[:, 0] + 1e-8).mean()
            return focal_loss.mean() + 0.1 * rem_confidence_loss
        
        return focal_loss.mean()
```

### 1.3 集成学习优化器
```python
def optimize_ensemble(self, all_predictions):
    """找到最优集成权重"""
    labels = all_predictions[list(all_predictions.keys())[0]]['labels']
    
    best_weights = None
    best_metrics = {'macro_f1': 0}
    
    # 网格搜索权重
    weight_options = np.arange(0.0, 2.1, 0.2)
    
    for weights in product(weight_options, repeat=n_models):
        if sum(weights) == 0:
            continue
            
        # 归一化权重
        weights = np.array(weights)
        weights = weights / weights.sum()
        
        # 加权平均概率
        ensemble_probs = sum(
            all_predictions[name]['probs'] * weights[i]
            for i, name in enumerate(model_names)
        )
        
        # 获取预测
        ensemble_preds = np.argmax(ensemble_probs, axis=1)
        
        # 应用后处理
        processed_preds = self.processor.smooth(ensemble_preds)
        
        # 计算指标
        metrics = get_comprehensive_metrics(labels, processed_preds)
        
        # 检查是否达到目标
        if (metrics['accuracy'] >= 0.87 and 
            metrics['kappa'] >= 0.8 and 
            metrics['macro_f1'] >= 0.8):
            print(f"\n🎉 目标达成！权重: {dict(zip(model_names, weights))}")
            return dict(zip(model_names, weights)), metrics, processed_preds
```

### 1.4 高级后处理策略
```python
def apply_advanced_postprocessing(self, predictions, labels):
    """应用高级后处理策略"""
    # 1. 基础规则平滑
    smoothed = self.processor.smooth(predictions)
    
    # 2. 修正孤立的Wake epochs
    for i in range(1, len(smoothed) - 1):
        if smoothed[i] == 4:  # Wake
            if smoothed[i-1] in [1, 2, 3] and smoothed[i+1] in [1, 2, 3]:
                # 睡眠中的孤立wake - 可能是误分类
                smoothed[i] = smoothed[i-1]
    
    # 3. 修正REM-N1混淆
    for i in range(1, len(smoothed) - 1):
        if smoothed[i] == 1:  # N1
            # 被REM包围的N1
            if smoothed[i-1] == 0 and smoothed[i+1] == 0:
                smoothed[i] = 0  # 转换为REM
    
    return smoothed
```

## 2. 数据增强策略

### 2.1 REM专门数据增强
```python
class REMDataAugmentation:
    """针对REM类的数据增强"""
    def __call__(self, data, labels):
        rem_mask = (labels == 0)
        
        if rem_mask.any():
            # 轻微噪声增强
            noise = torch.randn_like(data) * 0.03
            data = data + noise * rem_mask.unsqueeze(-1).unsqueeze(-1).float()
            
            # 时间轴微调
            if np.random.random() < 0.3:
                shift = np.random.randint(-30, 30)
                data[rem_mask] = torch.roll(data[rem_mask], shifts=shift, dims=-1)
        
        return data, labels
```

### 2.2 标签平滑增强
```python
class EnhancedLabelSmoothing:
    """增强的标签平滑，考虑类别混淆矩阵"""
    def __init__(self, n_classes=5, smoothing=0.1):
        self.n_classes = n_classes
        self.smoothing = smoothing
        
        # 基于领域知识的混淆矩阵
        self.confusion_prior = torch.tensor([
            [0.7, 0.2, 0.05, 0.0, 0.05],  # REM易与N1混淆
            [0.15, 0.6, 0.2, 0.0, 0.05],   # N1
            [0.05, 0.15, 0.6, 0.15, 0.05], # N2
            [0.0, 0.05, 0.2, 0.7, 0.05],   # N3
            [0.05, 0.05, 0.05, 0.05, 0.8]  # Wake
        ])
```

## 3. 训练策略

### 3.1 学习率调度
```python
# 余弦退火 + 热重启
scheduler = CosineAnnealingWarmRestarts(
    optimizer, T_0=20, T_mult=1, eta_min=1e-6
)
```

### 3.2 梯度裁剪
```python
torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
```

### 3.3 混合精度训练
```python
scaler = torch.cuda.amp.GradScaler()
with torch.cuda.amp.autocast():
    outputs, temporal_features = model(data)
    loss = criterion(outputs, labels)
```

## 4. 评估策略

### 4.1 Epoch级评估器
```python
class EpochLevelEvaluator:
    """将序列预测转换为epoch级预测"""
    def add_batch_predictions(self, probs, labels, start_indices):
        for i, start_idx in enumerate(start_indices):
            seq_probs = probs[i]  # [seq_len, n_classes]
            seq_labels = labels[i]  # [seq_len]
            
            for j in range(self.seq_len):
                epoch_idx = start_idx + j
                if epoch_idx < self.total_epochs:
                    self.epoch_probs[epoch_idx].append(seq_probs[j])
                    self.epoch_labels[epoch_idx] = seq_labels[j]
```

### 4.2 综合指标计算
```python
def get_comprehensive_metrics(y_true, y_pred):
    """计算全面的评估指标"""
    metrics = {
        'accuracy': accuracy_score(y_true, y_pred),
        'macro_f1': f1_score(y_true, y_pred, average='macro'),
        'kappa': cohen_kappa_score(y_true, y_pred),
        'per_class_metrics': {}
    }
    
    # 每类指标
    for i, class_name in enumerate(['REM', 'N1', 'N2', 'N3', 'Wake']):
        class_mask = (y_true == i)
        if class_mask.any():
            metrics['per_class_metrics'][class_name] = {
                'f1': f1_score(y_true == i, y_pred == i),
                'precision': precision_score(y_true == i, y_pred == i),
                'recall': recall_score(y_true == i, y_pred == i)
            }
    
    return metrics
```

## 5. 模型架构配置

### 5.1 基础配置（V7/V8）
```python
config = {
    'd_model': 128,
    'n_heads': 8,
    'n_layers': 4,
    'dropout': 0.1,
    'seq_len': 5
}
```

### 5.2 增强配置（V13/V14）
```python
config = {
    'd_model': 256,
    'n_heads': 16,
    'n_layers': 6,
    'dropout': 0.15,
    'seq_len': 5
}
```

## 6. 关键发现和调试技巧

### 6.1 常见错误和解决方案
1. **张量维度不匹配**: 使用`.view(-1)`确保维度正确
2. **类别权重处理**: 使用`.gather()`正确索引权重
3. **JSON序列化**: 将numpy类型转换为Python原生类型

### 6.2 性能优化技巧
1. **批处理优化**: 使用较大的batch size（32）
2. **数据加载**: 使用多进程（num_workers=4）
3. **GPU内存**: 使用pin_memory=True

### 6.3 调试工具
```python
# 实时监控训练进度
pbar.set_postfix({
    'loss': f'{loss.item():.4f}',
    'main': f'{main_loss.item():.4f}',
    'REM_acc': f'{rem_acc:.3f}'
})
```

## 7. 最终成功配置

### 7.1 获胜的集成组合
- **V7**: 14.3% 权重（基础模型，稳定性好）
- **V14**: 85.7% 权重（REM专注，性能突出）
- **后处理**: 规则平滑 + 类别特定修正

### 7.2 关键超参数
- 学习率: 2e-5
- 权重衰减: 1e-4
- REM权重增强: 3.0倍
- Focal Loss gamma: 2.5
- 标签平滑: 0.1

---

此文档记录了达成目标的所有关键技术细节，为后续研究和论文撰写提供参考。