# 🚨 关键发现报告：训练数据也被截断！

## 重大发现

### 1. V13和V14训练时也使用了截断的数据！

```python
# V13和V14的训练脚本中：
train_dataset = SequenceSleepDataset(train_files, seq_len=5, use_channels=3)
# 没有设置max_samples_per_file参数！
```

**问题**：
- `SequenceSleepDataset`的默认值是`max_samples_per_file=150`
- 这意味着V13和V14训练时也只用了每个文件的前150个epochs
- 训练集28个文件，应该有约28,000+ epochs，但只用了4,200个epochs（85%数据丢失）

### 2. 哪些模型受影响？

| 模型 | 数据加载设置 | 状态 | 影响 |
|------|-------------|------|------|
| **V7** | `max_samples_per_file=None` | ✅ 正确 | 使用了所有数据 |
| **V8** | `max_samples_per_file=None` | ✅ 正确 | 使用了所有数据 |
| **V13** | 未设置（默认150） | ❌ 错误 | 85%训练数据丢失 |
| **V14** | 未设置（默认150） | ❌ 错误 | 85%训练数据丢失 |

### 3. 为什么V7/V8性能更好？

- V7/V8使用了**完整数据**训练
- V13/V14只用了**15%的数据**训练
- 这解释了为什么V13/V14不能识别Wake（训练时几乎没见过Wake样本）

## 影响分析

### 训练数据丢失情况

假设训练集有28个文件，每个文件约1000个epochs：
- **应该使用**: ~28,000 epochs
- **实际使用**: 28 × 150 = 4,200 epochs
- **数据丢失**: 85%

### Wake类影响

训练集中Wake通常出现在后半部分（人们早上醒来）：
- 前150个epochs很少有Wake
- V13/V14训练时几乎没见过Wake
- 导致模型完全无法识别Wake

## 验证脚本输出

```
🔍 检查所有训练脚本的数据加载设置
================================================================================
✅ 正确配置（使用所有数据）:
  - train_sequential_v7_balanced.py ✅
  - train_sequential_v8_enhanced.py ✅

❓ 未明确设置的脚本:
  - train_v13_simple.py ⚠️ 关键模型！
  - train_v14_rem_focus.py ⚠️ 关键模型！

检查V13详情:
  第278行: train_dataset = SequenceSleepDataset(train_files, seq_len=5, use_channels=3)
  ⚠️ 未找到max_samples_per_file参数 - 使用默认值150
```

## 解决方案

### 立即行动

1. **重新训练V13和V14**（使用完整数据）
2. **验证所有其他模型**的训练脚本
3. **更新默认值**：将`SequenceSleepDataset`的默认值改为`None`

### 已创建的修复脚本

- `train_v13_FIXED.py` - 显式设置`max_samples_per_file=None`
- `train_v14_FIXED.py` - 显式设置`max_samples_per_file=None`

## 经验教训

1. **默认值的危险性**：默认限制150个样本导致85%数据丢失
2. **全面验证的重要性**：不仅要检查评估，还要检查训练
3. **显式优于隐式**：总是显式设置关键参数

## 预期改进

重新训练V13/V14后，预期：
- Wake F1: 0% → 80%+
- 整体F1: 可能突破85%
- 模型性能大幅提升

## 结论

这个发现解释了为什么：
1. V13/V14无法识别Wake
2. V7/V8性能相对更好
3. 集成时V13/V14权重很高但贡献有限

**这是一个关键的bug，修复后性能应该会有巨大提升！**