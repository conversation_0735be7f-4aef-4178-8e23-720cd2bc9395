"""
保守的数据预处理管道
调整wICA参数，避免过度移除信号成分
"""

import os
import numpy as np
import torch
import h5py
from scipy import signal
from tqdm import tqdm
from sklearn.decomposition import FastICA
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class ConservativeSleepPreprocessor:
    """
    保守的睡眠EEG预处理器
    更加谨慎地处理伪迹移除
    """
    
    def __init__(self, 
                 sampling_rate=100,
                 target_length=3000,
                 use_advanced_filtering=True,
                 use_conservative_artifact_removal=True,
                 verbose=True):
        self.sampling_rate = sampling_rate
        self.target_length = target_length
        self.use_advanced_filtering = use_advanced_filtering
        self.use_conservative_artifact_removal = use_conservative_artifact_removal
        self.verbose = verbose
        
    def apply_advanced_bandpass_filter(self, data):
        """
        高级带通滤波器
        针对不同睡眠频带优化
        """
        filtered_data = np.zeros_like(data)
        
        for ch in range(data.shape[0]):
            # 基础带通滤波 (0.3-35 Hz)
            nyquist = self.sampling_rate / 2
            low = 0.3 / nyquist
            high = 35.0 / nyquist
            b, a = signal.butter(4, [low, high], btype='band')
            filtered_data[ch] = signal.filtfilt(b, a, data[ch])
            
        return filtered_data
    
    def conservative_artifact_detection(self, data):
        """
        保守的伪迹检测
        只移除最明显的伪迹
        """
        bad_epochs = []
        
        for epoch_idx in range(data.shape[0]):
            epoch_data = data[epoch_idx]
            
            # 1. 极端幅度检测（更宽松的阈值）
            max_amplitude = np.max(np.abs(epoch_data))
            if max_amplitude > 2000:  # 2000μV阈值（之前是动态的）
                bad_epochs.append(epoch_idx)
                continue
                
            # 2. 平坦信号检测（传感器脱落）
            epoch_std = np.std(epoch_data, axis=1)
            if np.any(epoch_std < 0.5):  # 非常平坦的信号
                bad_epochs.append(epoch_idx)
                continue
                
            # 3. 异常高频噪声检测
            for ch in range(epoch_data.shape[0]):
                # 计算高频功率比例
                freqs, psd = signal.welch(epoch_data[ch], fs=self.sampling_rate, nperseg=256)
                high_freq_power = np.sum(psd[freqs > 50])
                total_power = np.sum(psd)
                
                if high_freq_power / (total_power + 1e-10) > 0.8:  # 80%以上是高频噪声
                    bad_epochs.append(epoch_idx)
                    break
        
        return bad_epochs
    
    def conservative_ica_cleaning(self, epochs_data):
        """
        保守的ICA清理
        只移除最明显的伪迹成分
        """
        if epochs_data.shape[0] < 10:  # 数据太少，跳过ICA
            return epochs_data, {'n_artifacts': 0, 'n_components': 0}
            
        # 将epochs合并进行ICA
        original_shape = epochs_data.shape
        data_concat = epochs_data.reshape(epochs_data.shape[0], -1).T  # (n_samples_total, n_channels)
        
        # 标准化
        scaler = StandardScaler()
        data_scaled = scaler.fit_transform(data_concat)
        
        # ICA分解（限制成分数量）
        n_components = min(data_scaled.shape[1], 8)  # 最多8个成分
        
        try:
            ica = FastICA(
                n_components=n_components,
                random_state=42,
                max_iter=200,  # 减少迭代次数
                tol=1e-3       # 放宽收敛条件
            )
            
            components = ica.fit_transform(data_scaled)
            mixing_matrix = ica.mixing_
            
            # 保守的伪迹检测
            artifact_components = []
            
            for i, component in enumerate(components.T):
                is_artifact = False
                
                # 1. 极端眼电伪迹（非常明显的低频大幅度）
                freqs, psd = signal.welch(component, fs=self.sampling_rate, nperseg=256)
                low_freq_power = np.sum(psd[freqs <= 1])  # 1Hz以下
                total_power = np.sum(psd)
                
                if low_freq_power / (total_power + 1e-10) > 0.9:  # 90%以上是极低频
                    amplitude_ratio = np.std(component) / np.mean(np.abs(component))
                    if amplitude_ratio > 10:  # 并且幅度变化极大
                        is_artifact = True
                        
                # 2. 明显的50Hz线路噪声
                noise_50hz_power = np.sum(psd[(freqs >= 49) & (freqs <= 51)])
                if noise_50hz_power / (total_power + 1e-10) > 0.7:  # 70%以上是50Hz
                    is_artifact = True
                    
                # 3. 极端高频肌电伪迹
                high_freq_power = np.sum(psd[freqs >= 30])
                if high_freq_power / (total_power + 1e-10) > 0.9:  # 90%以上是高频
                    if np.std(component) > 5 * np.median(np.std(components, axis=0)):
                        is_artifact = True
                
                if is_artifact:
                    artifact_components.append(i)
            
            # 限制移除的成分数量（最多移除一半）
            max_remove = max(1, n_components // 2)
            if len(artifact_components) > max_remove:
                # 只保留最明显的伪迹成分
                component_scores = []
                for idx in artifact_components:
                    component = components[:, idx]
                    # 计算伪迹严重程度
                    freqs, psd = signal.welch(component, fs=self.sampling_rate, nperseg=256)
                    low_freq_ratio = np.sum(psd[freqs <= 1]) / np.sum(psd)
                    high_freq_ratio = np.sum(psd[freqs >= 30]) / np.sum(psd)
                    amplitude_std = np.std(component)
                    
                    score = max(low_freq_ratio, high_freq_ratio) * amplitude_std
                    component_scores.append((idx, score))
                
                # 按严重程度排序，只移除最严重的
                component_scores.sort(key=lambda x: x[1], reverse=True)
                artifact_components = [idx for idx, _ in component_scores[:max_remove]]
            
            # 移除伪迹成分
            cleaned_components = components.copy()
            cleaned_components[:, artifact_components] = 0
            
            # 重构信号
            cleaned_data_scaled = ica.inverse_transform(cleaned_components)
            cleaned_data = scaler.inverse_transform(cleaned_data_scaled).T
            
            # 恢复原始形状
            cleaned_data = cleaned_data.reshape(original_shape[1], -1)
            cleaned_data = cleaned_data.reshape(original_shape[1], original_shape[0], original_shape[2])
            cleaned_data = cleaned_data.transpose(1, 0, 2)
            
            info = {
                'n_artifacts': len(artifact_components),
                'n_components': n_components,
                'artifact_components': artifact_components
            }
            
            return cleaned_data, info
            
        except Exception as e:
            if self.verbose:
                print(f"    ICA处理失败: {e}，使用原始数据")
            return epochs_data, {'n_artifacts': 0, 'n_components': 0}
    
    def robust_normalize(self, data):
        """
        鲁棒的信号标准化
        """
        normalized_data = np.zeros_like(data)
        
        for epoch in range(data.shape[0]):
            for ch in range(data.shape[1]):
                signal_data = data[epoch, ch]
                
                # 使用中位数和四分位距进行鲁棒标准化
                median = np.median(signal_data)
                q75, q25 = np.percentile(signal_data, [75, 25])
                iqr = q75 - q25
                
                if iqr > 0:
                    normalized_data[epoch, ch] = (signal_data - median) / iqr
                else:
                    # 如果IQR为0，使用标准差
                    std = np.std(signal_data)
                    if std > 0:
                        normalized_data[epoch, ch] = (signal_data - median) / std
                    else:
                        normalized_data[epoch, ch] = signal_data - median
        
        return normalized_data
    
    def preprocess_subject_data(self, epochs_data, subject_id=None):
        """
        保守预处理单个受试者的数据
        """
        if self.verbose:
            print(f"开始预处理受试者 {subject_id} 的数据...")
            print(f"  输入数据形状: {epochs_data.shape}")
            
        processing_info = {
            'subject_id': subject_id,
            'original_shape': epochs_data.shape,
            'bad_epochs_removed': 0,
            'ica_artifacts': 0
        }
        
        # 1. 高级滤波
        if self.use_advanced_filtering:
            filtered_data = np.zeros_like(epochs_data)
            for epoch in range(epochs_data.shape[0]):
                filtered_data[epoch] = self.apply_advanced_bandpass_filter(epochs_data[epoch])
        else:
            filtered_data = epochs_data.copy()
        
        # 2. 保守的异常epoch检测
        bad_epochs = self.conservative_artifact_detection(filtered_data)
        if bad_epochs:
            good_epochs = [i for i in range(filtered_data.shape[0]) if i not in bad_epochs]
            filtered_data = filtered_data[good_epochs]
            processing_info['bad_epochs_removed'] = len(bad_epochs)
            
            if self.verbose:
                print(f"  移除了 {len(bad_epochs)} 个明显异常的epochs")
        
        # 3. 保守的ICA伪迹去除
        if self.use_conservative_artifact_removal and filtered_data.shape[0] >= 5:
            cleaned_data, ica_info = self.conservative_ica_cleaning(filtered_data)
            processing_info['ica_artifacts'] = ica_info['n_artifacts']
            
            if self.verbose:
                print(f"  ICA分析: {ica_info['n_components']}个成分，移除{ica_info['n_artifacts']}个明显伪迹")
        else:
            cleaned_data = filtered_data
            if self.verbose:
                print(f"  跳过ICA处理（数据量不足或已禁用）")
        
        # 4. 鲁棒标准化
        normalized_data = self.robust_normalize(cleaned_data)
        
        processing_info['final_shape'] = normalized_data.shape
        
        if self.verbose:
            print(f"  预处理完成: {normalized_data.shape}")
            retention_rate = normalized_data.shape[0] / epochs_data.shape[0] * 100
            print(f"  数据保留率: {retention_rate:.1f}%")
            
        return normalized_data, processing_info
    
    def process_dataset(self, input_file, output_file):
        """
        处理完整数据集
        """
        if self.verbose:
            print(f"开始保守预处理数据集: {input_file}")
            print(f"输出文件: {output_file}")
            
        with h5py.File(input_file, 'r') as input_hf:
            subjects = [key for key in input_hf.keys() if key.startswith('subject_')]
            subjects.sort()
            
            if self.verbose:
                print(f"找到 {len(subjects)} 个受试者")
            
            with h5py.File(output_file, 'w') as output_hf:
                # 复制基础元数据
                for attr_name, attr_value in input_hf.attrs.items():
                    output_hf.attrs[attr_name] = attr_value
                
                output_hf.attrs['conservative_preprocessing'] = True
                
                valid_subjects = []
                all_processing_info = []
                
                for subject_key in tqdm(subjects, desc="保守预处理"):
                    try:
                        subject_group = input_hf[subject_key]
                        epochs_data = subject_group['data'][:]
                        labels = subject_group['labels'][:]
                        
                        subject_id = subject_key.split('_')[1]
                        
                        # 预处理
                        cleaned_data, proc_info = self.preprocess_subject_data(
                            epochs_data, subject_id
                        )
                        
                        # 调整标签
                        if proc_info['bad_epochs_removed'] > 0:
                            bad_epochs = self.conservative_artifact_detection(epochs_data)
                            good_epochs = [i for i in range(len(labels)) if i not in bad_epochs]
                            labels = labels[good_epochs]
                        
                        # 检查数据完整性
                        if len(cleaned_data) != len(labels) or len(cleaned_data) < 5:
                            if self.verbose:
                                print(f"受试者 {subject_id} 数据不足或不匹配，跳过")
                            continue
                            
                        # 保存数据
                        output_subject_group = output_hf.create_group(subject_key)
                        
                        output_subject_group.create_dataset(
                            'data', data=cleaned_data,
                            compression='gzip', compression_opts=4
                        )
                        output_subject_group.create_dataset(
                            'labels', data=labels,
                            compression='gzip', compression_opts=4
                        )
                        
                        # 复制和更新属性
                        for attr_name, attr_value in subject_group.attrs.items():
                            output_subject_group.attrs[attr_name] = attr_value
                        
                        output_subject_group.attrs['conservative_preprocessing'] = True
                        output_subject_group.attrs['bad_epochs_removed'] = proc_info['bad_epochs_removed']
                        output_subject_group.attrs['ica_artifacts_removed'] = proc_info['ica_artifacts']
                        
                        valid_subjects.append(subject_key)
                        all_processing_info.append(proc_info)
                        
                    except Exception as e:
                        print(f"处理受试者 {subject_key} 时出错: {e}")
                        continue
                
                # 更新总体统计
                output_hf.attrs['num_subjects'] = len(valid_subjects)
                
                if all_processing_info:
                    total_original = sum(info['original_shape'][0] for info in all_processing_info)
                    total_final = sum(info['final_shape'][0] for info in all_processing_info)
                    total_bad_epochs = sum(info['bad_epochs_removed'] for info in all_processing_info)
                    total_ica_artifacts = sum(info['ica_artifacts'] for info in all_processing_info)
                    
                    output_hf.attrs['total_original_epochs'] = total_original
                    output_hf.attrs['total_final_epochs'] = total_final
                    output_hf.attrs['total_bad_epochs_removed'] = total_bad_epochs
                    output_hf.attrs['total_ica_artifacts_removed'] = total_ica_artifacts
                    output_hf.attrs['overall_retention_rate'] = total_final / total_original * 100
                    
                    if self.verbose:
                        print(f"\n保守预处理完成:")
                        print(f"  有效受试者: {len(valid_subjects)}")
                        print(f"  原始epochs: {total_original}")
                        print(f"  最终epochs: {total_final}")
                        print(f"  移除异常epochs: {total_bad_epochs}")
                        print(f"  移除ICA伪迹: {total_ica_artifacts}")
                        print(f"  总体保留率: {total_final/total_original*100:.1f}%")

def main():
    """
    主函数：保守预处理Sleep-EDF-20数据集
    """
    input_file = "sleep_edf_20_correct.h5"
    output_file = "sleep_edf_20_conservative_preprocessed.h5"
    
    # 创建保守预处理器
    preprocessor = ConservativeSleepPreprocessor(
        sampling_rate=100,
        use_advanced_filtering=True,
        use_conservative_artifact_removal=True,
        verbose=True
    )
    
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        return
    
    try:
        preprocessor.process_dataset(input_file, output_file)
        print(f"保守预处理完成，保存至: {output_file}")
        
        # 验证结果
        with h5py.File(output_file, 'r') as hf:
            print(f"\n验证预处理结果:")
            print(f"  受试者数量: {hf.attrs.get('num_subjects', 'N/A')}")
            print(f"  总epochs数: {hf.attrs.get('total_final_epochs', 'N/A')}")
            print(f"  数据保留率: {hf.attrs.get('overall_retention_rate', 'N/A'):.1f}%")
            
    except Exception as e:
        print(f"预处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()