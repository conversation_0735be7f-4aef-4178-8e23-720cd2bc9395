"""
Sleep-EDF-20数据集正确预处理脚本
确保同一受试者的所有数据保持在一起，避免数据泄露
"""

import os
import numpy as np
import mne
from scipy import signal
import h5py
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Sleep-EDF-20数据集参数
SAMPLING_RATE = 100  # Hz
EPOCH_DURATION = 30  # seconds
SEQUENCE_LENGTH = EPOCH_DURATION * SAMPLING_RATE  # 3000 samples

# 睡眠阶段映射
stage_mapping = {
    'Sleep stage W': 0,
    'Sleep stage 1': 1,
    'Sleep stage 2': 2,
    'Sleep stage 3': 3,
    'Sleep stage 4': 3,  # N3和N4合并为N3
    'Sleep stage R': 4,
    'Sleep stage ?': -1  # 未知阶段将被排除
}

def extract_subject_id(filename):
    """从文件名正确提取受试者ID"""
    # SC4001E0-PSG.edf -> 受试者00
    # SC4002E0-PSG.edf -> 受试者00（同一个人的第二晚）
    # SC4011E0-PSG.edf -> 受试者01
    # SC4012E0-PSG.edf -> 受试者01（同一个人的第二晚）
    name = os.path.basename(filename)
    # 提取中间两位数字作为受试者ID
    record_id = name[2:6]  # 例如：4001, 4002, 4011等
    
    # 正确的受试者ID计算
    # 40X1和40X2属于受试者0X
    # 41X1和41X2属于受试者1X
    first_two = int(record_id[:2])  # 40或41
    last_two = int(record_id[2:])   # 01, 02, 11, 12等
    
    if first_two == 40:
        # 4001,4002 -> 受试者00
        # 4011,4012 -> 受试者01
        # ...
        # 4091,4092 -> 受试者09
        subject_id = (last_two - 1) // 10
    else:  # first_two == 41
        # 4101,4102 -> 受试者10
        # 4111,4112 -> 受试者11（注意：4112可能不存在）
        # ...
        # 4191,4192 -> 受试者19
        subject_id = 10 + (last_two - 1) // 10
    
    # 判断是第几晚
    night = 1 if last_two % 10 == 1 else 2
    
    return subject_id, night

def preprocess_psg_file(edf_file):
    """预处理单个PSG文件"""
    try:
        # 读取EDF文件
        raw = mne.io.read_raw_edf(edf_file, preload=True, verbose=False)
        
        # 选择EEG和EOG通道
        channels = ['EEG Fpz-Cz', 'EEG Pz-Oz', 'EOG horizontal']
        raw.pick_channels(channels, ordered=True)
        
        # 获取数据（单位：V）
        data = raw.get_data()
        
        # 转换为微伏
        data = data * 1e6  # V to μV
        
        # 应用带通滤波器 (0.3-35 Hz)
        nyquist = SAMPLING_RATE / 2
        low = 0.3 / nyquist
        high = 35.0 / nyquist
        b, a = signal.butter(4, [low, high], btype='band')
        
        for i in range(data.shape[0]):
            data[i] = signal.filtfilt(b, a, data[i])
        
        # 获取睡眠阶段标注
        annotations = mne.read_annotations(edf_file)
        
        # 提取每个30秒epoch的标签
        epochs_data = []
        labels = []
        
        for i, (onset, duration, description) in enumerate(zip(annotations.onset, 
                                                              annotations.duration, 
                                                              annotations.description)):
            if description in stage_mapping:
                label = stage_mapping[description]
                if label == -1:  # 跳过未知阶段
                    continue
                
                # 提取对应的数据段
                start_sample = int(onset * SAMPLING_RATE)
                end_sample = start_sample + SEQUENCE_LENGTH
                
                if end_sample <= data.shape[1]:
                    epoch_data = data[:, start_sample:end_sample]
                    epochs_data.append(epoch_data)
                    labels.append(label)
        
        return np.array(epochs_data), np.array(labels)
    
    except Exception as e:
        print(f"处理文件 {edf_file} 时出错: {e}")
        return None, None

def main():
    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-edf/sleep_edf_20"
    output_file = "sleep_edf_20_correct.h5"
    
    # 获取所有PSG文件
    psg_files = [f for f in os.listdir(data_dir) if f.endswith('-PSG.edf')]
    psg_files.sort()
    
    print(f"找到 {len(psg_files)} 个PSG文件")
    
    # 组织数据：按受试者分组
    subject_data = {}
    
    # 处理每个文件
    for psg_file in tqdm(psg_files, desc="处理PSG文件"):
        file_path = os.path.join(data_dir, psg_file)
        subject_id, night = extract_subject_id(psg_file)
        
        print(f"\n处理: {psg_file} -> 受试者{subject_id:02d}, 第{night}晚")
        
        epochs_data, labels = preprocess_psg_file(file_path)
        
        if epochs_data is not None:
            if subject_id not in subject_data:
                subject_data[subject_id] = {}
            
            subject_data[subject_id][f'night{night}'] = {
                'data': epochs_data,
                'labels': labels,
                'filename': psg_file
            }
            
            print(f"  提取了 {len(labels)} 个epochs")
            if len(labels) > 0:
                print(f"  标签分布: {np.bincount(labels.astype(int))}")
    
    # 显示受试者统计
    print("\n受试者数据统计:")
    print("=" * 50)
    for subj_id in sorted(subject_data.keys()):
        nights = list(subject_data[subj_id].keys())
        total_epochs = sum(len(subject_data[subj_id][night]['labels']) 
                          for night in nights)
        print(f"受试者{subj_id:02d}: {len(nights)}晚数据, 共{total_epochs}个epochs")
    
    # 保存数据（保持受试者完整性）
    print(f"\n保存数据到 {output_file}")
    
    with h5py.File(output_file, 'w') as hf:
        # 保存元数据
        hf.attrs['sampling_rate'] = SAMPLING_RATE
        hf.attrs['sequence_length'] = SEQUENCE_LENGTH
        hf.attrs['num_subjects'] = len(subject_data)
        
        # 为每个受试者创建组
        for subj_id in sorted(subject_data.keys()):
            subj_group = hf.create_group(f'subject_{subj_id:02d}')
            
            # 合并该受试者的所有数据
            all_data = []
            all_labels = []
            
            for night in sorted(subject_data[subj_id].keys()):
                night_data = subject_data[subj_id][night]
                all_data.append(night_data['data'])
                all_labels.append(night_data['labels'])
                
                # 记录每晚的文件名
                subj_group.attrs[f'{night}_file'] = night_data['filename']
            
            # 连接所有数据
            if all_data:
                combined_data = np.concatenate(all_data, axis=0)
                combined_labels = np.concatenate(all_labels, axis=0)
                
                # 保存数据
                subj_group.create_dataset('data', data=combined_data, 
                                        compression='gzip', compression_opts=4)
                subj_group.create_dataset('labels', data=combined_labels, 
                                        compression='gzip', compression_opts=4)
                
                # 保存统计信息
                subj_group.attrs['num_epochs'] = len(combined_labels)
                subj_group.attrs['num_nights'] = len(all_data)
                
                # 计算并保存标签分布
                label_counts = np.bincount(combined_labels, minlength=5)
                for i, count in enumerate(label_counts):
                    subj_group.attrs[f'class_{i}_count'] = count
    
    print("预处理完成！")
    
    # 验证数据完整性
    print("\n验证数据完整性...")
    with h5py.File(output_file, 'r') as hf:
        print(f"总受试者数: {hf.attrs['num_subjects']}")
        
        total_epochs = 0
        for subj_id in range(20):  # Sleep-EDF-20有20个受试者
            key = f'subject_{subj_id:02d}'
            if key in hf:
                subj_group = hf[key]
                num_epochs = subj_group.attrs['num_epochs']
                num_nights = subj_group.attrs['num_nights']
                total_epochs += num_epochs
                print(f"  受试者{subj_id:02d}: {num_nights}晚, {num_epochs}个epochs")
        
        print(f"\n总epochs数: {total_epochs}")

if __name__ == "__main__":
    main()