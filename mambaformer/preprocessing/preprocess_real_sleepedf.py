#!/usr/bin/env python3
"""
预处理真实的Sleep-EDF-20数据集
确保每个受试者的数据都是独立和真实的
"""

import os
import numpy as np
import mne
import h5py
from scipy import signal
from pathlib import Path
import logging
from tqdm import tqdm

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_subject_id(filename):
    """从文件名提取受试者ID"""
    # SC4001E0-PSG.edf -> 4001
    name = os.path.basename(filename)
    subject_id = name[2:6]  # 提取4001部分
    return int(subject_id)

def process_single_subject(psg_file, hyp_file, output_dir, subject_idx):
    """处理单个受试者的数据"""
    try:
        # 读取PSG数据
        logger.info(f"处理受试者 {subject_idx}: {os.path.basename(psg_file)}")
        raw = mne.io.read_raw_edf(psg_file, preload=True, verbose=False)
        
        # 选择EEG和EOG通道
        eeg_channels = ['EEG Fpz-Cz', 'EEG Pz-Oz']
        eog_channel = ['EOG horizontal']
        
        # 确保通道存在
        available_channels = raw.ch_names
        eeg_picks = [ch for ch in eeg_channels if ch in available_channels]
        eog_picks = [ch for ch in eog_channel if ch in available_channels]
        
        if not eeg_picks:
            logger.warning(f"未找到EEG通道，跳过受试者 {subject_idx}")
            return False
            
        if not eog_picks:
            logger.warning(f"未找到EOG通道，使用第二个EEG通道作为替代")
            if len(eeg_picks) > 1:
                eog_picks = [eeg_picks[1]]
                eeg_picks = [eeg_picks[0]]
            else:
                logger.warning(f"通道不足，跳过受试者 {subject_idx}")
                return False
        
        # 提取数据
        eeg_data = raw.get_data(picks=eeg_picks[0])
        eog_data = raw.get_data(picks=eog_picks[0])
        sfreq = raw.info['sfreq']
        
        logger.info(f"  采样率: {sfreq} Hz")
        logger.info(f"  EEG数据形状: {eeg_data.shape}")
        
        # 检查数据单位并转换为微伏
        eeg_sample = np.abs(eeg_data[:, :1000]).mean()
        if eeg_sample < 1:  # 可能是伏特单位
            logger.info(f"  检测到数据可能是伏特单位（均值={eeg_sample:.6f}），转换为微伏")
            eeg_data = eeg_data * 1e6
            eog_data = eog_data * 1e6
        
        # 重采样到100Hz
        if sfreq != 100:
            logger.info(f"  重采样从 {sfreq}Hz 到 100Hz")
            resample_factor = 100 / sfreq
            new_length = int(len(eeg_data[0]) * resample_factor)
            eeg_data = signal.resample(eeg_data, new_length, axis=1)
            eog_data = signal.resample(eog_data, new_length, axis=1)
            sfreq = 100
        
        # 带通滤波 0.3-35Hz
        logger.info("  应用0.3-35Hz带通滤波")
        b, a = signal.butter(4, [0.3, 35], btype='band', fs=sfreq)
        eeg_data = signal.filtfilt(b, a, eeg_data, axis=1)
        eog_data = signal.filtfilt(b, a, eog_data, axis=1)
        
        # 读取标签
        annotations = mne.read_annotations(hyp_file)
        
        # 将标注转换为30秒的epoch标签
        epoch_duration = 30  # 秒
        n_samples_per_epoch = int(epoch_duration * sfreq)
        n_epochs = len(eeg_data[0]) // n_samples_per_epoch
        
        labels = np.zeros(n_epochs, dtype=int)
        
        # Sleep-EDF标签映射
        label_mapping = {
            'Sleep stage W': 0,
            'Sleep stage 1': 1,
            'Sleep stage 2': 2,
            'Sleep stage 3': 3,
            'Sleep stage 4': 3,  # N3和N4合并
            'Sleep stage R': 4,
        }
        
        # 为每个epoch分配标签
        for ann in annotations:
            if ann['description'] in label_mapping:
                label = label_mapping[ann['description']]
                start_sample = int(ann['onset'] * sfreq)
                end_sample = int((ann['onset'] + ann['duration']) * sfreq)
                
                start_epoch = start_sample // n_samples_per_epoch
                end_epoch = min(end_sample // n_samples_per_epoch, n_epochs)
                
                for epoch_idx in range(start_epoch, end_epoch):
                    if epoch_idx < n_epochs:
                        labels[epoch_idx] = label
        
        # 提取30秒的epochs
        eeg_epochs = []
        eog_epochs = []
        valid_labels = []
        
        for i in range(n_epochs):
            start = i * n_samples_per_epoch
            end = (i + 1) * n_samples_per_epoch
            
            if end <= len(eeg_data[0]):
                eeg_epoch = eeg_data[0, start:end]
                eog_epoch = eog_data[0, start:end]
                
                # 检查数据质量
                if not (np.isnan(eeg_epoch).any() or np.isnan(eog_epoch).any()):
                    eeg_epochs.append(eeg_epoch)
                    eog_epochs.append(eog_epoch)
                    valid_labels.append(labels[i])
        
        eeg_epochs = np.array(eeg_epochs)
        eog_epochs = np.array(eog_epochs)
        valid_labels = np.array(valid_labels)
        
        logger.info(f"  有效epochs: {len(valid_labels)}")
        logger.info(f"  标签分布: {np.bincount(valid_labels)}")
        
        # 计算归一化参数（每个epoch独立归一化）
        eeg_means = np.mean(eeg_epochs, axis=1, keepdims=True)
        eeg_stds = np.std(eeg_epochs, axis=1, keepdims=True)
        eog_means = np.mean(eog_epochs, axis=1, keepdims=True)
        eog_stds = np.std(eog_epochs, axis=1, keepdims=True)
        
        # 归一化
        eeg_epochs_norm = (eeg_epochs - eeg_means) / (eeg_stds + 1e-8)
        eog_epochs_norm = (eog_epochs - eog_means) / (eog_stds + 1e-8)
        
        # 保存数据
        output_prefix = f'{output_dir}/subj{subject_idx:02d}'
        
        # EEG数据
        with h5py.File(f'{output_prefix}_eeg.h5', 'w') as f:
            f.create_dataset('data', data=eeg_epochs_norm, compression='gzip')
            f.create_dataset('mean', data=eeg_means.squeeze(), compression='gzip')
            f.create_dataset('std', data=eeg_stds.squeeze(), compression='gzip')
        
        # EOG数据
        with h5py.File(f'{output_prefix}_eog.h5', 'w') as f:
            f.create_dataset('data', data=eog_epochs_norm, compression='gzip')
            f.create_dataset('mean', data=eog_means.squeeze(), compression='gzip')
            f.create_dataset('std', data=eog_stds.squeeze(), compression='gzip')
        
        # 标签
        with h5py.File(f'{output_prefix}_labels.h5', 'w') as f:
            f.create_dataset('data', data=valid_labels, compression='gzip')
        
        logger.info(f"  ✓ 保存完成")
        return True
        
    except Exception as e:
        logger.error(f"处理受试者 {subject_idx} 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 路径设置
    data_dir = Path('../data-edf/sleep_edf_20')
    output_dir = Path('./real_sleepedf_processed')
    output_dir.mkdir(exist_ok=True)
    
    # 获取所有PSG和Hypnogram文件对
    psg_files = sorted(list(data_dir.glob('*PSG.edf')))
    
    logger.info(f"找到 {len(psg_files)} 个PSG文件")
    
    # 处理每个受试者
    successful = 0
    failed = []
    
    for idx, psg_file in enumerate(tqdm(psg_files, desc="处理受试者")):
        # 找到对应的Hypnogram文件
        subject_id = extract_subject_id(psg_file)
        
        # 尝试不同的Hypnogram文件名模式
        hyp_patterns = [
            f'SC{subject_id}EC-Hypnogram.edf',
            f'SC{subject_id}EH-Hypnogram.edf',
        ]
        
        hyp_file = None
        for pattern in hyp_patterns:
            potential_hyp = data_dir / pattern
            if potential_hyp.exists():
                hyp_file = potential_hyp
                break
        
        if hyp_file is None:
            logger.warning(f"未找到受试者 {subject_id} 的Hypnogram文件")
            failed.append(subject_id)
            continue
        
        # 处理数据
        if process_single_subject(psg_file, hyp_file, output_dir, idx + 1):
            successful += 1
        else:
            failed.append(subject_id)
    
    # 总结
    logger.info(f"\n处理完成！")
    logger.info(f"成功: {successful}/{len(psg_files)}")
    if failed:
        logger.info(f"失败的受试者ID: {failed}")
    
    # 创建数据集划分文件
    if successful >= 20:
        n_subjects = successful
        n_train = int(n_subjects * 0.7)
        n_val = int(n_subjects * 0.15)
        n_test = n_subjects - n_train - n_val
        
        split_info = {
            'train': list(range(1, n_train + 1)),
            'val': list(range(n_train + 1, n_train + n_val + 1)),
            'test': list(range(n_train + n_val + 1, n_subjects + 1)),
            'total': n_subjects
        }
        
        import json
        with open(output_dir / 'data_split.json', 'w') as f:
            json.dump(split_info, f, indent=2)
        
        logger.info(f"\n数据集划分:")
        logger.info(f"训练集: {n_train} 个受试者")
        logger.info(f"验证集: {n_val} 个受试者")
        logger.info(f"测试集: {n_test} 个受试者")

if __name__ == "__main__":
    main()