"""
ICLabel自动伪迹分类器
基于深度学习的ICA成分自动分类系统
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy import signal
from scipy.stats import skew, kurtosis
import warnings
warnings.filterwarnings('ignore')

class ICLabelNet(nn.Module):
    """
    ICLabel神经网络分类器
    自动识别ICA成分的类别：脑电/眼电/肌电/心电/线路噪声/通道噪声/其他
    """
    
    def __init__(self, 
                 n_classes=7, 
                 dropout=0.3,
                 time_features_dim=32,
                 freq_features_dim=32,
                 spatial_features_dim=16):
        super().__init__()
        
        self.n_classes = n_classes
        self.class_names = [
            'Brain', 'Muscle', 'Eye', 'Heart', 
            'Line_Noise', 'Channel_Noise', 'Other'
        ]
        
        # 时域特征网络
        self.time_net = nn.Sequential(
            nn.Linear(10, 64),  # 10个时域统计特征
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, time_features_dim),
            nn.ReLU()
        )
        
        # 频域特征网络
        self.freq_net = nn.Sequential(
            nn.Conv1d(1, 16, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(freq_features_dim),
            nn.Flatten()
        )
        
        # 空间模式网络
        self.spatial_net = nn.Sequential(
            nn.Linear(3, 32),  # 3通道空间权重
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(32, spatial_features_dim),
            nn.ReLU()
        )
        
        # 特征融合和分类
        total_features = time_features_dim + freq_features_dim + spatial_features_dim
        self.classifier = nn.Sequential(
            nn.Linear(total_features, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, n_classes)
        )
        
    def forward(self, time_features, freq_features, spatial_features):
        """
        前向传播
        
        Args:
            time_features: 时域特征 (batch, 10)
            freq_features: 频域特征 (batch, 1, freq_bins) 
            spatial_features: 空间特征 (batch, 3)
        """
        # 提取各维度特征
        time_emb = self.time_net(time_features)
        freq_emb = self.freq_net(freq_features)  
        spatial_emb = self.spatial_net(spatial_features)
        
        # 特征融合
        combined = torch.cat([time_emb, freq_emb, spatial_emb], dim=1)
        
        # 分类
        output = self.classifier(combined)
        
        return output

class ICLabelClassifier:
    """
    ICLabel分类器主类
    整合特征提取和神经网络分类
    """
    
    def __init__(self, 
                 sampling_rate=100,
                 confidence_threshold=0.7,
                 device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.sampling_rate = sampling_rate
        self.confidence_threshold = confidence_threshold
        self.device = device
        
        # 创建预训练模型
        self.model = ICLabelNet().to(device)
        self._load_pretrained_weights()
        self.model.eval()
        
    def _load_pretrained_weights(self):
        """
        加载预训练权重
        注：实际应用中应从训练好的模型加载
        这里使用随机初始化的权重作为占位符
        """
        # 在实际应用中，这里应该加载真正的ICLabel预训练权重
        # torch.load('iclabel_weights.pth')
        print("注意：使用随机初始化权重，实际应用需要加载预训练的ICLabel模型")
        
    def extract_time_features(self, component):
        """
        提取时域统计特征
        """
        features = []
        
        # 基本统计量
        features.append(np.mean(component))           # 均值
        features.append(np.std(component))            # 标准差
        features.append(np.var(component))            # 方差
        features.append(skew(component))              # 偏度
        features.append(kurtosis(component))          # 峰度
        
        # 幅度特征
        features.append(np.max(component))            # 最大值
        features.append(np.min(component))            # 最小值
        features.append(np.ptp(component))            # 峰峰值
        features.append(np.median(component))         # 中位数
        
        # 能量特征
        features.append(np.sum(component ** 2))       # 总能量
        
        return np.array(features, dtype=np.float32)
        
    def extract_freq_features(self, component):
        """
        提取频域特征
        """
        # 计算功率谱密度
        freqs, psd = signal.welch(
            component, 
            fs=self.sampling_rate,
            nperseg=min(256, len(component)//4),
            window='hann'
        )
        
        # 限制频率范围到100Hz
        max_freq_idx = np.where(freqs <= 50)[0][-1] if len(np.where(freqs <= 50)[0]) > 0 else len(freqs)-1
        freqs = freqs[:max_freq_idx+1]
        psd = psd[:max_freq_idx+1]
        
        # 对数变换并标准化
        psd_log = np.log10(psd + 1e-10)
        psd_norm = (psd_log - np.mean(psd_log)) / (np.std(psd_log) + 1e-10)
        
        return psd_norm
        
    def extract_spatial_features(self, mixing_weights):
        """
        提取空间模式特征
        """
        # 空间权重的统计特征
        features = []
        features.append(np.max(np.abs(mixing_weights)))     # 最大绝对权重
        features.append(np.argmax(np.abs(mixing_weights)))  # 最大权重通道索引
        features.append(np.sum(mixing_weights ** 2))        # 权重能量
        
        return np.array(features, dtype=np.float32)
        
    def classify_component(self, component, mixing_weights):
        """
        分类单个ICA成分
        
        Args:
            component: ICA成分时间序列
            mixing_weights: 该成分的空间混合权重
            
        Returns:
            class_probs: 各类别概率
            predicted_class: 预测类别
            is_artifact: 是否为伪迹
        """
        # 提取特征
        time_feats = self.extract_time_features(component)
        freq_feats = self.extract_freq_features(component)  
        spatial_feats = self.extract_spatial_features(mixing_weights)
        
        # 转换为张量
        time_tensor = torch.FloatTensor(time_feats).unsqueeze(0).to(self.device)
        freq_tensor = torch.FloatTensor(freq_feats).unsqueeze(0).unsqueeze(0).to(self.device)
        spatial_tensor = torch.FloatTensor(spatial_feats).unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            logits = self.model(time_tensor, freq_tensor, spatial_tensor)
            probs = F.softmax(logits, dim=1)
            
        class_probs = probs.cpu().numpy()[0]
        predicted_class = np.argmax(class_probs)
        max_confidence = class_probs[predicted_class]
        
        # 判断是否为伪迹（非脑电成分）
        is_artifact = (predicted_class != 0) and (max_confidence > self.confidence_threshold)
        
        return class_probs, predicted_class, is_artifact
        
    def classify_components(self, components, mixing_matrix, verbose=True):
        """
        批量分类ICA成分
        
        Args:
            components: ICA成分矩阵 (n_components, n_samples)
            mixing_matrix: 混合矩阵 (n_channels, n_components)
            
        Returns:
            classifications: 分类结果列表
            artifact_indices: 伪迹成分索引
        """
        classifications = []
        artifact_indices = []
        
        if verbose:
            print(f"使用ICLabel分类 {components.shape[0]} 个ICA成分...")
            
        for i, component in enumerate(components):
            mixing_weights = mixing_matrix[:, i]
            
            class_probs, predicted_class, is_artifact = self.classify_component(
                component, mixing_weights
            )
            
            classification = {
                'component_idx': i,
                'class_probs': class_probs,
                'predicted_class': predicted_class,
                'class_name': self.model.class_names[predicted_class],
                'confidence': class_probs[predicted_class],
                'is_artifact': is_artifact
            }
            
            classifications.append(classification)
            
            if is_artifact:
                artifact_indices.append(i)
                
            if verbose:
                class_name = self.model.class_names[predicted_class]
                confidence = class_probs[predicted_class]
                status = "伪迹" if is_artifact else "保留"
                print(f"  成分 {i:2d}: {class_name:12s} ({confidence:.3f}) -> {status}")
                
        if verbose:
            print(f"识别出 {len(artifact_indices)} 个伪迹成分: {artifact_indices}")
            
        return classifications, artifact_indices
        
    def get_artifact_summary(self, classifications):
        """
        获取伪迹检测摘要
        """
        artifact_types = {}
        total_components = len(classifications)
        artifact_components = 0
        
        for cls in classifications:
            if cls['is_artifact']:
                artifact_components += 1
                class_name = cls['class_name']
                if class_name not in artifact_types:
                    artifact_types[class_name] = 0
                artifact_types[class_name] += 1
                
        summary = {
            'total_components': total_components,
            'artifact_components': artifact_components,
            'brain_components': total_components - artifact_components,
            'artifact_ratio': artifact_components / total_components,
            'artifact_types': artifact_types
        }
        
        return summary

def apply_iclabel_classification(components, mixing_matrix, sampling_rate=100, verbose=True):
    """
    应用ICLabel分类到ICA成分
    
    Args:
        components: ICA成分 (n_components, n_samples)
        mixing_matrix: 混合矩阵 (n_channels, n_components)
        sampling_rate: 采样率
        verbose: 是否输出详细信息
        
    Returns:
        artifact_indices: 伪迹成分索引
        classifications: 详细分类结果
        summary: 分类摘要
    """
    # 创建分类器
    classifier = ICLabelClassifier(
        sampling_rate=sampling_rate,
        confidence_threshold=0.7
    )
    
    # 执行分类
    classifications, artifact_indices = classifier.classify_components(
        components, mixing_matrix, verbose=verbose
    )
    
    # 生成摘要
    summary = classifier.get_artifact_summary(classifications)
    
    if verbose:
        print(f"\nICLabel分类摘要:")
        print(f"  总成分数: {summary['total_components']}")
        print(f"  脑电成分: {summary['brain_components']}")
        print(f"  伪迹成分: {summary['artifact_components']}")
        print(f"  伪迹比例: {summary['artifact_ratio']:.1%}")
        
        if summary['artifact_types']:
            print("  伪迹类型分布:")
            for artifact_type, count in summary['artifact_types'].items():
                print(f"    {artifact_type}: {count}")
                
    return artifact_indices, classifications, summary

# 使用示例和测试
if __name__ == "__main__":
    # 创建模拟数据
    np.random.seed(42)
    n_components = 5
    n_samples = 3000
    n_channels = 3
    
    # 模拟ICA成分
    components = np.random.randn(n_components, n_samples) * 50
    
    # 添加特定类型的模拟伪迹
    # 成分0：脑电（正常）
    components[0] = signal.filtfilt(*signal.butter(4, [1, 30], btype='band', fs=100), components[0])
    
    # 成分1：眼电（低频大幅度）
    components[1] = np.sin(2 * np.pi * 1 * np.linspace(0, 30, n_samples)) * 200 + components[1] * 20
    
    # 成分2：肌电（高频）
    emg = signal.filtfilt(*signal.butter(4, [20, 40], btype='band', fs=100), components[2])
    components[2] = emg * 3
    
    # 成分3：心电（1-2Hz）
    components[3] = np.sin(2 * np.pi * 1.2 * np.linspace(0, 30, n_samples)) * 100 + components[3] * 10
    
    # 成分4：线路噪声（50Hz）
    components[4] = np.sin(2 * np.pi * 50 * np.linspace(0, 30, n_samples)) * 80 + components[4] * 5
    
    # 模拟混合矩阵
    mixing_matrix = np.random.randn(n_channels, n_components)
    mixing_matrix[0, 1] = 2.0  # 眼电主要在第一通道
    mixing_matrix[1, 2] = 1.5  # 肌电主要在第二通道
    
    print("测试ICLabel分类...")
    artifact_indices, classifications, summary = apply_iclabel_classification(
        components, mixing_matrix, sampling_rate=100, verbose=True
    )
    
    print(f"\n检测结果: 找到 {len(artifact_indices)} 个伪迹成分")
    print(f"伪迹索引: {artifact_indices}")