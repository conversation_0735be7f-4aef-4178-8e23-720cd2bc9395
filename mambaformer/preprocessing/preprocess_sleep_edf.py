"""
Sleep-EDF数据预处理脚本
将原始.edf文件转换为Cross-Modal Transformer所需的h5格式
整合wICA预处理
"""

import os
import h5py
import numpy as np
import mne
from pathlib import Path
import argparse
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入我们的wICA预处理
from preprocessing import wICAProcessor

def load_sleep_edf_file(psg_file, hypno_file):
    """
    加载Sleep-EDF文件
    
    Args:
        psg_file: PSG数据文件路径
        hypno_file: Hypnogram标注文件路径
        
    Returns:
        eeg_data: EEG数据
        eog_data: EOG数据
        labels: 睡眠分期标签
        sfreq: 采样频率
    """
    # 加载PSG数据
    raw = mne.io.read_raw_edf(psg_file, preload=True, verbose=False)
    
    # 获取EEG和EOG通道
    eeg_channels = [ch for ch in raw.ch_names if 'EEG' in ch or 'Fpz' in ch or 'Cz' in ch or 'Pz' in ch or 'Oz' in ch]
    eog_channels = [ch for ch in raw.ch_names if 'EOG' in ch]
    
    if not eeg_channels:
        # 如果没有明确的EEG标记，使用常见的电极名称
        possible_eeg = ['Fpz-Cz', 'Pz-Oz', 'C3-A2', 'C4-A1']
        eeg_channels = [ch for ch in possible_eeg if ch in raw.ch_names]
    
    if not eog_channels:
        # 如果没有明确的EOG标记，使用常见的电极名称
        possible_eog = ['EOG horizontal', 'ROC-A1', 'LOC-A2']
        eog_channels = [ch for ch in possible_eog if ch in raw.ch_names]
    
    print(f"Available channels: {raw.ch_names}")
    print(f"Selected EEG channels: {eeg_channels}")
    print(f"Selected EOG channels: {eog_channels}")
    
    if not eeg_channels or not eog_channels:
        # 如果仍然没有找到，使用前几个通道作为替代
        print("Warning: Using first two channels as EEG and EOG")
        if len(raw.ch_names) >= 2:
            eeg_channels = [raw.ch_names[0]]
            eog_channels = [raw.ch_names[1]]
        else:
            raise ValueError("Insufficient channels in the data")
    
    # 应用带通滤波 (与原始代码一致: 0.2-40Hz)
    raw = raw.filter(l_freq=0.2, h_freq=40.0, verbose=False)
    
    # 提取数据
    eeg_data = raw.get_data(picks=eeg_channels)[0]  # 使用第一个EEG通道
    eog_data = raw.get_data(picks=eog_channels)[0]  # 使用第一个EOG通道
    sfreq = raw.info['sfreq']
    
    # 加载睡眠分期标注 - hypnogram是纯标注文件
    annotations = mne.read_annotations(hypno_file)
    
    # 转换标注为数值标签 (5类现代标准)
    # W=0, N1=1, N2=2, N3=3, REM=4 (S4归并到S3)
    label_mapping = {
        'Sleep stage W': 0,
        'Sleep stage 1': 1, 
        'Sleep stage 2': 2,
        'Sleep stage 3': 3,
        'Sleep stage 4': 3,  # S4归并到S3/N3
        'Sleep stage R': 4,  # REM=4
        'Sleep stage ?': -1,  # 未知阶段，稍后过滤
        'Movement time': -1,  # 运动时间，稍后过滤
    }
    
    # 创建30秒epoch的标签序列
    epoch_duration = 30  # 秒
    n_epochs = int(len(eeg_data) // (epoch_duration * sfreq))
    labels = []
    
    for i in range(n_epochs):
        epoch_start = i * epoch_duration
        epoch_end = (i + 1) * epoch_duration
        
        # 找到对应时间段的标注
        epoch_labels = []
        for annot in annotations:
            if annot['onset'] < epoch_end and (annot['onset'] + annot['duration']) > epoch_start:
                if annot['description'] in label_mapping:
                    epoch_labels.append(label_mapping[annot['description']])
        
        if epoch_labels:
            # 使用该epoch最常见的标签
            label = max(set(epoch_labels), key=epoch_labels.count)
            if label >= 0:  # 有效标签
                labels.append(label)
            else:
                labels.append(-1)  # 无效标签
        else:
            labels.append(-1)  # 无标注
    
    # 截取对应长度的数据
    data_length = n_epochs * int(epoch_duration * sfreq)
    eeg_data = eeg_data[:data_length]
    eog_data = eog_data[:data_length]
    
    return eeg_data, eog_data, np.array(labels), sfreq

def apply_wica_preprocessing(data, sfreq, use_wica=True):
    """
    应用wICA预处理
    
    Args:
        data: 输入数据 [n_samples]
        sfreq: 采样频率
        use_wica: 是否使用wICA预处理
        
    Returns:
        clean_data: 预处理后的数据
    """
    if not use_wica:
        return data
        
    try:
        # 重塑为[n_channels, n_samples]格式
        data_2d = data.reshape(1, -1)
        
        # 初始化wICA处理器
        wica_processor = wICAProcessor(n_components=5, random_state=42)
        
        # 应用预处理
        clean_data, _, _ = wica_processor.fit_transform(data_2d)
        
        return clean_data[0]  # 返回1D数据
        
    except Exception as e:
        print(f"Warning: wICA preprocessing failed: {e}")
        return data  # 返回原始数据

def segment_into_epochs(data, labels, epoch_length_samples, overlap=0):
    """
    将连续数据分割为epochs
    
    Args:
        data: 连续数据
        labels: 对应标签
        epoch_length_samples: epoch长度（采样点数）
        overlap: 重叠比例
        
    Returns:
        epochs: [n_epochs, epoch_length_samples]
        epoch_labels: [n_epochs]
    """
    epochs = []
    epoch_labels = []
    
    step_size = int(epoch_length_samples * (1 - overlap))
    n_label_epochs = len(labels)
    
    for i in range(n_label_epochs):
        # 每个30秒标签对应一个epoch
        start_idx = i * epoch_length_samples
        end_idx = start_idx + epoch_length_samples
        
        if end_idx <= len(data) and labels[i] >= 0:  # 有效标签
            epoch_data = data[start_idx:end_idx]
            epochs.append(epoch_data)
            epoch_labels.append(labels[i])
    
    return np.array(epochs), np.array(epoch_labels)

def save_subject_data(subject_idx, eeg_epochs, eog_epochs, labels, 
                     eeg_stats, eog_stats, save_dir):
    """
    保存单个受试者的数据为h5格式
    
    Args:
        subject_idx: 受试者编号
        eeg_epochs: EEG epochs [n_epochs, epoch_length]
        eog_epochs: EOG epochs [n_epochs, epoch_length]  
        labels: 标签 [n_epochs]
        eeg_stats: EEG统计信息 (mean, std)
        eog_stats: EOG统计信息 (mean, std)
        save_dir: 保存目录
    """
    # 保存EEG数据
    with h5py.File(os.path.join(save_dir, f'x{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=eeg_epochs, dtype=np.float32)
    
    # 保存EOG数据
    with h5py.File(os.path.join(save_dir, f'eog{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=eog_epochs, dtype=np.float32)
    
    # 保存标签
    with h5py.File(os.path.join(save_dir, f'y{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=labels, dtype=np.int32)
    
    # 保存EEG统计信息
    with h5py.File(os.path.join(save_dir, f'mean{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=np.array([eeg_stats[0]]), dtype=np.float32)
    
    with h5py.File(os.path.join(save_dir, f'std{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=np.array([eeg_stats[1]]), dtype=np.float32)
    
    # 保存EOG统计信息  
    with h5py.File(os.path.join(save_dir, f'eog_m{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=np.array([eog_stats[0]]), dtype=np.float32)
    
    with h5py.File(os.path.join(save_dir, f'eog_s{subject_idx}.h5'), 'w') as f:
        f.create_dataset('data', data=np.array([eog_stats[1]]), dtype=np.float32)

def process_sleep_edf_dataset(data_dir, save_dir, use_wica=True, max_subjects=None):
    """
    处理完整的Sleep-EDF数据集
    
    Args:
        data_dir: 原始数据目录
        save_dir: 保存目录
        use_wica: 是否使用wICA预处理
        max_subjects: 最大受试者数量（用于测试）
    """
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 查找所有PSG文件
    psg_files = list(Path(data_dir).glob('*PSG.edf'))
    
    if max_subjects:
        psg_files = psg_files[:max_subjects*2]  # 每个受试者有2个记录
    
    print(f"Found {len(psg_files)} PSG files")
    
    subject_idx = 1
    successful_subjects = 0
    
    for psg_file in tqdm(psg_files, desc="Processing files"):
        try:
            # 找到对应的hypnogram文件 - Sleep-EDF命名模式：SC4XXXE0 对应 SC4XXXEXX
            psg_basename = psg_file.stem.replace('-PSG', '')  # SC4001E0-PSG -> SC4001E0
            subject_base = psg_basename[:6]  # SC4001E0 -> SC4001
            
            # 查找对应的hypnogram文件
            hypno_files = list(Path(data_dir).glob(f'{subject_base}*Hypnogram.edf'))
            
            if not hypno_files:
                print(f"Warning: No hypnogram file found for {psg_file}")
                continue
                
            hypno_file = hypno_files[0]
            
            print(f"\\nProcessing subject {subject_idx}: {psg_file.name}")
            
            # 加载数据
            eeg_data, eog_data, labels, sfreq = load_sleep_edf_file(str(psg_file), str(hypno_file))
            
            print(f"Raw data shape: EEG {eeg_data.shape}, EOG {eog_data.shape}")
            print(f"Labels: {len(labels)}, unique: {np.unique(labels[labels>=0])}")
            
            # 过滤无效标签
            valid_mask = labels >= 0
            if np.sum(valid_mask) < 10:  # 至少需要10个有效epoch
                print(f"Warning: Too few valid epochs ({np.sum(valid_mask)}), skipping")
                continue
            
            # 应用wICA预处理
            if use_wica:
                print("Applying wICA preprocessing...")
                eeg_clean = apply_wica_preprocessing(eeg_data, sfreq, use_wica=True)
                eog_clean = apply_wica_preprocessing(eog_data, sfreq, use_wica=True)
            else:
                eeg_clean = eeg_data
                eog_clean = eog_data
            
            # 分割为epochs (30秒 = 3000采样点 @ 100Hz)
            epoch_length = int(30 * sfreq)  
            eeg_epochs, eeg_labels = segment_into_epochs(eeg_clean, labels, epoch_length)
            eog_epochs, eog_labels = segment_into_epochs(eog_clean, labels, epoch_length)
            
            # 确保EEG和EOG epoch数量一致
            min_epochs = min(len(eeg_epochs), len(eog_epochs))
            eeg_epochs = eeg_epochs[:min_epochs]
            eog_epochs = eog_epochs[:min_epochs]
            final_labels = eeg_labels[:min_epochs]
            
            if len(eeg_epochs) < 10:
                print(f"Warning: Too few epochs after segmentation ({len(eeg_epochs)}), skipping")
                continue
            
            print(f"Final epochs: {len(eeg_epochs)}")
            print(f"Label distribution: {np.bincount(final_labels)}")
            
            # 计算统计信息
            eeg_mean = np.mean(eeg_epochs)
            eeg_std = np.std(eeg_epochs)
            eog_mean = np.mean(eog_epochs)
            eog_std = np.std(eog_epochs)
            
            # 标准化
            eeg_epochs = (eeg_epochs - eeg_mean) / (eeg_std + 1e-8)
            eog_epochs = (eog_epochs - eog_mean) / (eog_std + 1e-8)
            
            # 保存数据
            save_subject_data(
                subject_idx, eeg_epochs, eog_epochs, final_labels,
                (eeg_mean, eeg_std), (eog_mean, eog_std), save_dir
            )
            
            print(f"✓ Successfully processed subject {subject_idx}")
            subject_idx += 1
            successful_subjects += 1
            
        except Exception as e:
            print(f"✗ Error processing {psg_file}: {e}")
            continue
    
    print(f"\\n📊 Processing Summary:")
    print(f"Total files processed: {len(psg_files)}")  
    print(f"Successful subjects: {successful_subjects}")
    print(f"Data saved to: {save_dir}")

def main():
    parser = argparse.ArgumentParser(description='Preprocess Sleep-EDF dataset')
    parser.add_argument('--data_dir', type=str, 
                       default='../data-edf/sleep_edf_20',
                       help='Path to Sleep-EDF data directory')
    parser.add_argument('--save_dir', type=str,
                       default='./processed_data',
                       help='Path to save processed data')
    parser.add_argument('--use_wica', action='store_true',
                       help='Apply wICA preprocessing')
    parser.add_argument('--max_subjects', type=int, default=None,
                       help='Maximum number of subjects to process (for testing)')
    
    args = parser.parse_args()
    
    print("🚀 Sleep-EDF Data Preprocessing")
    print(f"Data directory: {args.data_dir}")
    print(f"Save directory: {args.save_dir}")
    print(f"Use wICA: {args.use_wica}")
    
    process_sleep_edf_dataset(
        args.data_dir, args.save_dir, 
        use_wica=args.use_wica,
        max_subjects=args.max_subjects
    )
    
    print("✅ Preprocessing completed!")

if __name__ == '__main__':
    main()