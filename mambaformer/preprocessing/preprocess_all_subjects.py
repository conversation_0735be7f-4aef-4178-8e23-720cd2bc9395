#!/usr/bin/env python3
"""
预处理Sleep-EDF-20完整数据集
"""

import os
import sys
import json
import argparse
from pathlib import Path
import logging
from datetime import datetime

# 导入预处理函数
from preprocess_full_dataset import process_subject

def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f'preprocess_full_{timestamp}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def get_all_subjects():
    """获取所有39个subjects的列表"""
    # Sleep-EDF-20数据集包含39个subjects (SC subjects)
    # 编号从SC4001到SC4102 (有一些缺失的编号)
    all_subjects = []
    
    # 实际存在的subjects (根据数据集文档)
    for i in range(1, 20):  # SC4001-SC4019
        all_subjects.append(f"SC400{i:01d}")
    for i in range(21, 40):  # SC4021-SC4039
        all_subjects.append(f"SC40{i:02d}")
    for i in range(41, 60):  # SC4041-SC4059
        all_subjects.append(f"SC40{i:02d}")
    for i in range(61, 80):  # SC4061-SC4079
        all_subjects.append(f"SC40{i:02d}")
    for i in range(81, 103):  # SC4081-SC4102
        all_subjects.append(f"SC40{i:02d}")
    
    return all_subjects

def find_subject_files(data_dir, subject_id):
    """查找subject的PSG和Hypnogram文件"""
    psg_pattern = f"*{subject_id}*PSG.edf"
    hyp_pattern = f"*{subject_id}*Hypnogram.edf"
    
    psg_files = list(Path(data_dir).glob(psg_pattern))
    hyp_files = list(Path(data_dir).glob(hyp_pattern))
    
    return psg_files, hyp_files

def main():
    parser = argparse.ArgumentParser(description='预处理Sleep-EDF-20完整数据集')
    parser.add_argument('--data_dir', type=str, 
                        default='/media/main/ypf/eeg/data-edf/sleep_edf_20',
                        help='原始数据目录')
    parser.add_argument('--output_dir', type=str, 
                        default='./processed_data_full',
                        help='输出目录')
    parser.add_argument('--log_dir', type=str, 
                        default='./log',
                        help='日志目录')
    parser.add_argument('--subject_list', type=str, default=None,
                        help='Subject列表文件（可选）')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.log_dir)
    logger.info("🚀 开始预处理Sleep-EDF-20完整数据集")
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"输出目录: {args.output_dir}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取所有可用的subjects
    all_subjects = get_all_subjects()
    logger.info(f"理论上应有 {len(all_subjects)} 个subjects")
    
    # 实际处理的subjects
    processed_subjects = []
    failed_subjects = []
    subject_mapping = {}  # 原始ID到数字ID的映射
    
    subject_idx = 1
    for subject_id in all_subjects:
        try:
            # 查找文件
            psg_files, hyp_files = find_subject_files(args.data_dir, subject_id)
            
            if not psg_files:
                logger.warning(f"⚠️  未找到 {subject_id} 的PSG文件")
                continue
                
            if not hyp_files:
                logger.warning(f"⚠️  未找到 {subject_id} 的Hypnogram文件")
                continue
            
            # 处理每个session (有些subjects可能有多个sessions)
            for psg_file, hyp_file in zip(sorted(psg_files), sorted(hyp_files)):
                logger.info(f"\n处理 {subject_id} (Subject {subject_idx}):")
                logger.info(f"  PSG: {psg_file.name}")
                logger.info(f"  Hypnogram: {hyp_file.name}")
                
                try:
                    # 处理数据
                    process_subject(
                        str(psg_file), 
                        str(hyp_file), 
                        args.output_dir, 
                        subject_id=subject_idx
                    )
                    
                    processed_subjects.append(subject_idx)
                    subject_mapping[subject_id] = subject_idx
                    logger.info(f"✅ Subject {subject_idx} ({subject_id}) 处理成功")
                    subject_idx += 1
                    
                except Exception as e:
                    logger.error(f"❌ 处理 {subject_id} 时出错: {str(e)}")
                    failed_subjects.append(subject_id)
                    
        except Exception as e:
            logger.error(f"❌ 查找 {subject_id} 文件时出错: {str(e)}")
            failed_subjects.append(subject_id)
    
    # 保存处理信息
    process_info = {
        'total_subjects': len(processed_subjects),
        'processed_subjects': processed_subjects,
        'failed_subjects': failed_subjects,
        'subject_mapping': subject_mapping,
        'data_dir': args.data_dir,
        'output_dir': args.output_dir,
        'process_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    info_file = os.path.join(args.output_dir, 'process_info.json')
    with open(info_file, 'w') as f:
        json.dump(process_info, f, indent=2)
    
    # 生成训练/验证/测试分割建议
    if len(processed_subjects) >= 10:
        # 标准分割: 60% train, 20% val, 20% test
        n_subjects = len(processed_subjects)
        n_train = int(n_subjects * 0.6)
        n_val = int(n_subjects * 0.2)
        
        train_subjects = processed_subjects[:n_train]
        val_subjects = processed_subjects[n_train:n_train+n_val]
        test_subjects = processed_subjects[n_train+n_val:]
        
        split_info = {
            'train_subjects': ','.join(map(str, train_subjects)),
            'val_subjects': ','.join(map(str, val_subjects)),
            'test_subjects': ','.join(map(str, test_subjects)),
            'n_train': len(train_subjects),
            'n_val': len(val_subjects),
            'n_test': len(test_subjects)
        }
        
        split_file = os.path.join(args.output_dir, 'data_split.json')
        with open(split_file, 'w') as f:
            json.dump(split_info, f, indent=2)
        
        logger.info(f"\n📊 数据分割建议:")
        logger.info(f"  训练集: {len(train_subjects)} subjects")
        logger.info(f"  验证集: {len(val_subjects)} subjects")
        logger.info(f"  测试集: {len(test_subjects)} subjects")
    
    # 总结
    logger.info(f"\n✅ 预处理完成!")
    logger.info(f"  成功处理: {len(processed_subjects)} subjects")
    logger.info(f"  失败: {len(failed_subjects)} subjects")
    logger.info(f"  处理信息保存在: {info_file}")
    
    if split_file:
        logger.info(f"  数据分割保存在: {split_file}")

if __name__ == "__main__":
    main()