"""
高级数据预处理管道
集成wICA-ICLabel的完整预处理流程
"""

import os
import numpy as np
import torch
import h5py
from scipy import signal
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入我们的预处理模块
from wica_preprocessing import wICAProcessor, apply_wica_to_epochs
from iclabel_classifier import ICLabelClassifier, apply_iclabel_classification

class AdvancedSleepPreprocessor:
    """
    高级睡眠EEG预处理器
    整合wICA盲源分离和ICLabel自动伪迹分类
    """
    
    def __init__(self, 
                 sampling_rate=100,
                 target_length=3000,
                 use_wica=True,
                 use_iclabel=True,
                 wica_config=None,
                 iclabel_config=None,
                 verbose=True):
        """
        初始化高级预处理器
        
        Args:
            sampling_rate: 采样率
            target_length: 目标序列长度
            use_wica: 是否使用wICA预处理
            use_iclabel: 是否使用ICLabel分类
            wica_config: wICA配置参数
            iclabel_config: ICLabel配置参数
            verbose: 是否输出详细信息
        """
        self.sampling_rate = sampling_rate
        self.target_length = target_length
        self.use_wica = use_wica
        self.use_iclabel = use_iclabel
        self.verbose = verbose
        
        # wICA配置
        self.wica_config = wica_config or {
            'n_components': None,  # 自动选择
            'frequency_weights': True,
            'max_iter': 500,
            'random_state': 42
        }
        
        # ICLabel配置
        self.iclabel_config = iclabel_config or {
            'confidence_threshold': 0.7
        }
        
        # 统计信息
        self.processing_stats = {
            'total_epochs': 0,
            'artifacts_removed': 0,
            'wica_components_removed': 0,
            'iclabel_components_removed': 0,
            'signal_quality_improvement': 0.0
        }
        
    def apply_bandpass_filter(self, data, low_freq=0.3, high_freq=35):
        """
        应用带通滤波器
        针对睡眠EEG特点优化的滤波参数
        """
        nyquist = self.sampling_rate / 2
        low = low_freq / nyquist
        high = high_freq / nyquist
        
        # 设计4阶Butterworth带通滤波器
        b, a = signal.butter(4, [low, high], btype='band')
        
        filtered_data = np.zeros_like(data)
        for ch in range(data.shape[0]):
            filtered_data[ch] = signal.filtfilt(b, a, data[ch])
            
        return filtered_data
    
    def normalize_signals(self, data, method='robust'):
        """
        信号标准化
        
        Args:
            data: 输入数据 (n_epochs, n_channels, n_samples)
            method: 标准化方法 ('robust', 'zscore', 'minmax')
        """
        if method == 'robust':
            # 使用中位数和MAD进行鲁棒标准化
            for epoch in range(data.shape[0]):
                for ch in range(data.shape[1]):
                    median = np.median(data[epoch, ch])
                    mad = np.median(np.abs(data[epoch, ch] - median))
                    if mad > 0:
                        data[epoch, ch] = (data[epoch, ch] - median) / (1.4826 * mad)
                        
        elif method == 'zscore':
            # Z-score标准化
            for epoch in range(data.shape[0]):
                for ch in range(data.shape[1]):
                    mean = np.mean(data[epoch, ch])
                    std = np.std(data[epoch, ch])
                    if std > 0:
                        data[epoch, ch] = (data[epoch, ch] - mean) / std
                        
        elif method == 'minmax':
            # 最小-最大标准化
            for epoch in range(data.shape[0]):
                for ch in range(data.shape[1]):
                    min_val = np.min(data[epoch, ch])
                    max_val = np.max(data[epoch, ch])
                    if max_val > min_val:
                        data[epoch, ch] = (data[epoch, ch] - min_val) / (max_val - min_val)
        
        return data
    
    def detect_bad_epochs(self, data, threshold_std=5.0):
        """
        检测异常epochs
        基于幅度和方差的异常检测
        """
        bad_epochs = []
        
        for epoch_idx in range(data.shape[0]):
            epoch_data = data[epoch_idx]
            
            # 检查异常大的幅度
            max_amplitude = np.max(np.abs(epoch_data))
            mean_amplitude = np.mean(np.abs(epoch_data))
            
            if max_amplitude > threshold_std * mean_amplitude:
                bad_epochs.append(epoch_idx)
                continue
                
            # 检查异常的方差
            epoch_std = np.std(epoch_data, axis=1)
            median_std = np.median(epoch_std)
            
            if np.any(epoch_std > threshold_std * median_std):
                bad_epochs.append(epoch_idx)
                continue
                
            # 检查平坦信号（传感器脱落）
            if np.any(epoch_std < 0.1):
                bad_epochs.append(epoch_idx)
                
        return bad_epochs
    
    def preprocess_subject_data(self, epochs_data, subject_id=None):
        """
        预处理单个受试者的数据
        
        Args:
            epochs_data: epochs数据 (n_epochs, n_channels, n_samples)
            subject_id: 受试者ID
            
        Returns:
            cleaned_data: 清理后的数据
            processing_info: 处理信息
        """
        if self.verbose:
            print(f"开始预处理受试者 {subject_id} 的数据...")
            print(f"  输入数据形状: {epochs_data.shape}")
            
        original_quality = np.sqrt(np.mean(epochs_data ** 2))
        processing_info = {
            'subject_id': subject_id,
            'original_shape': epochs_data.shape,
            'original_quality': original_quality,
            'bad_epochs_removed': 0,
            'wica_artifacts': 0,
            'iclabel_artifacts': 0
        }
        
        # 1. 检测并移除异常epochs
        bad_epochs = self.detect_bad_epochs(epochs_data)
        if bad_epochs:
            good_epochs = [i for i in range(epochs_data.shape[0]) if i not in bad_epochs]
            epochs_data = epochs_data[good_epochs]
            processing_info['bad_epochs_removed'] = len(bad_epochs)
            
            if self.verbose:
                print(f"  移除了 {len(bad_epochs)} 个异常epochs")
        
        # 2. 基础滤波
        filtered_data = np.zeros_like(epochs_data)
        for epoch in range(epochs_data.shape[0]):
            filtered_data[epoch] = self.apply_bandpass_filter(epochs_data[epoch])
        
        # 3. wICA伪迹去除
        if self.use_wica:
            if self.verbose:
                print("  应用wICA盲源分离...")
                
            try:
                cleaned_data, wica_info = apply_wica_to_epochs(
                    filtered_data, verbose=False
                )
                processing_info['wica_artifacts'] = wica_info['n_artifacts']
                
                if self.verbose:
                    print(f"    wICA移除了 {wica_info['n_artifacts']} 个伪迹成分")
                    
            except Exception as e:
                if self.verbose:
                    print(f"    wICA处理失败: {e}，跳过wICA步骤")
                cleaned_data = filtered_data
        else:
            cleaned_data = filtered_data
        
        # 4. 信号标准化
        normalized_data = self.normalize_signals(cleaned_data.copy(), method='robust')
        
        # 5. 计算质量改善
        final_quality = np.sqrt(np.mean(normalized_data ** 2))
        processing_info['final_quality'] = final_quality
        processing_info['final_shape'] = normalized_data.shape
        processing_info['quality_improvement'] = (
            (original_quality - final_quality) / original_quality * 100
        )
        
        if self.verbose:
            print(f"  预处理完成:")
            print(f"    最终形状: {normalized_data.shape}")
            print(f"    质量改善: {processing_info['quality_improvement']:.1f}%")
            
        return normalized_data, processing_info
    
    def process_dataset(self, input_file, output_file):
        """
        处理完整数据集
        
        Args:
            input_file: 输入H5文件路径
            output_file: 输出H5文件路径
        """
        if self.verbose:
            print(f"开始处理数据集: {input_file}")
            print(f"输出文件: {output_file}")
            
        # 读取输入数据
        with h5py.File(input_file, 'r') as input_hf:
            # 获取受试者列表
            subjects = [key for key in input_hf.keys() if key.startswith('subject_')]
            subjects.sort()
            
            if self.verbose:
                print(f"找到 {len(subjects)} 个受试者")
            
            # 创建输出文件
            with h5py.File(output_file, 'w') as output_hf:
                # 复制元数据
                for attr_name, attr_value in input_hf.attrs.items():
                    output_hf.attrs[attr_name] = attr_value
                
                # 添加预处理信息
                output_hf.attrs['preprocessing_applied'] = True
                output_hf.attrs['wica_enabled'] = self.use_wica
                output_hf.attrs['iclabel_enabled'] = self.use_iclabel
                
                # 处理每个受试者
                valid_subjects = []
                all_processing_info = []
                
                for subject_key in tqdm(subjects, desc="处理受试者"):
                    try:
                        # 读取原始数据
                        subject_group = input_hf[subject_key]
                        epochs_data = subject_group['data'][:]
                        labels = subject_group['labels'][:]
                        
                        # 提取受试者ID
                        subject_id = subject_key.split('_')[1]
                        
                        # 预处理数据
                        cleaned_data, proc_info = self.preprocess_subject_data(
                            epochs_data, subject_id
                        )
                        
                        # 对应调整标签
                        if proc_info['bad_epochs_removed'] > 0:
                            bad_epochs = self.detect_bad_epochs(epochs_data)
                            good_epochs = [i for i in range(len(labels)) if i not in bad_epochs]
                            labels = labels[good_epochs]
                        
                        # 检查数据完整性
                        if len(cleaned_data) != len(labels):
                            print(f"警告：受试者 {subject_id} 数据和标签长度不匹配，跳过")
                            continue
                            
                        # 保存处理后的数据
                        output_subject_group = output_hf.create_group(subject_key)
                        
                        # 数据和标签
                        output_subject_group.create_dataset(
                            'data', data=cleaned_data,
                            compression='gzip', compression_opts=4
                        )
                        output_subject_group.create_dataset(
                            'labels', data=labels,
                            compression='gzip', compression_opts=4
                        )
                        
                        # 复制原始属性
                        for attr_name, attr_value in subject_group.attrs.items():
                            output_subject_group.attrs[attr_name] = attr_value
                        
                        # 添加处理信息
                        output_subject_group.attrs['preprocessing_applied'] = True
                        output_subject_group.attrs['bad_epochs_removed'] = proc_info['bad_epochs_removed']
                        output_subject_group.attrs['wica_artifacts_removed'] = proc_info['wica_artifacts']
                        output_subject_group.attrs['quality_improvement'] = proc_info['quality_improvement']
                        
                        valid_subjects.append(subject_key)
                        all_processing_info.append(proc_info)
                        
                    except Exception as e:
                        print(f"处理受试者 {subject_key} 时出错: {e}")
                        continue
                
                # 更新元数据
                output_hf.attrs['num_subjects'] = len(valid_subjects)
                
                # 计算总体统计
                if all_processing_info:
                    total_original_epochs = sum(info['original_shape'][0] for info in all_processing_info)
                    total_final_epochs = sum(info['final_shape'][0] for info in all_processing_info)
                    total_bad_epochs = sum(info['bad_epochs_removed'] for info in all_processing_info)
                    avg_quality_improvement = np.mean([info['quality_improvement'] for info in all_processing_info])
                    
                    output_hf.attrs['total_original_epochs'] = total_original_epochs
                    output_hf.attrs['total_final_epochs'] = total_final_epochs
                    output_hf.attrs['total_bad_epochs_removed'] = total_bad_epochs
                    output_hf.attrs['average_quality_improvement'] = avg_quality_improvement
                    
                    if self.verbose:
                        print(f"\n预处理完成:")
                        print(f"  有效受试者: {len(valid_subjects)}")
                        print(f"  原始epochs: {total_original_epochs}")
                        print(f"  最终epochs: {total_final_epochs}")
                        print(f"  移除异常epochs: {total_bad_epochs}")
                        print(f"  平均质量改善: {avg_quality_improvement:.1f}%")

def main():
    """
    主函数：处理Sleep-EDF-20数据集
    """
    # 配置参数
    input_file = "sleep_edf_20_correct.h5"
    output_file = "sleep_edf_20_advanced_preprocessed.h5"
    
    # 创建高级预处理器
    preprocessor = AdvancedSleepPreprocessor(
        sampling_rate=100,
        use_wica=True,
        use_iclabel=False,  # 暂时关闭ICLabel，因为需要预训练权重
        verbose=True
    )
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        return
    
    # 处理数据集
    try:
        preprocessor.process_dataset(input_file, output_file)
        print(f"数据集预处理完成，保存至: {output_file}")
        
        # 验证输出文件
        with h5py.File(output_file, 'r') as hf:
            print(f"\n验证预处理结果:")
            print(f"  受试者数量: {hf.attrs.get('num_subjects', 'N/A')}")
            print(f"  总epochs数: {hf.attrs.get('total_final_epochs', 'N/A')}")
            print(f"  wICA启用: {hf.attrs.get('wica_enabled', 'N/A')}")
            print(f"  ICLabel启用: {hf.attrs.get('iclabel_enabled', 'N/A')}")
            print(f"  平均质量改善: {hf.attrs.get('average_quality_improvement', 'N/A'):.1f}%")
            
    except Exception as e:
        print(f"预处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()