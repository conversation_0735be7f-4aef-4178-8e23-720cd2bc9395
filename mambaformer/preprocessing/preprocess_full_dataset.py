"""
预处理完整的Sleep-EDF数据集
将所有20个subjects的数据转换为h5格式
"""

import os
import glob
import logging
from pathlib import Path
import argparse
import numpy as np
import h5py
import mne
from mne import pick_types
mne.set_log_level('ERROR')

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def process_subject(psg_file, hypnogram_file, output_dir, subject_id=1):
    """处理单个subject的数据"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取PSG数据
        logger.info(f"读取PSG数据: {psg_file}")
        raw = mne.io.read_raw_edf(psg_file, preload=True)
        
        # 提取EEG和EOG通道
        eeg_channels = ['EEG Fpz-Cz', 'EEG Pz-Oz']
        eog_channel = ['EOG horizontal']
        
        # 检查通道是否存在
        available_channels = raw.ch_names
        if not all(ch in available_channels for ch in eeg_channels):
            logger.warning(f"EEG通道不全，使用可用通道")
            eeg_channels = [ch for ch in eeg_channels if ch in available_channels]
            
        if not eog_channel[0] in available_channels:
            logger.warning(f"EOG通道不存在，尝试其他EOG通道")
            eog_candidates = [ch for ch in available_channels if 'EOG' in ch]
            if eog_candidates:
                eog_channel = [eog_candidates[0]]
            else:
                logger.error("没有找到EOG通道")
                return None, None, None, None
        
        # 读取标注
        logger.info(f"读取标注数据: {hypnogram_file}")
        annotations = mne.read_annotations(hypnogram_file)
        raw.set_annotations(annotations)
        
        # 设置采样率为100Hz
        if raw.info['sfreq'] != 100:
            raw.resample(100)
            
        # 提取epochs
        events, event_id = mne.events_from_annotations(raw)
        
        # 标签映射 (5分类，S4合并到S3)
        label_mapping = {
            'Sleep stage W': 0,
            'Sleep stage 1': 1, 
            'Sleep stage 2': 2,
            'Sleep stage 3': 3,
            'Sleep stage 4': 3,  # S4合并到S3
            'Sleep stage R': 4
        }
        
        # 创建新的event_id
        new_event_id = {}
        for key in event_id.keys():
            if key in label_mapping:
                new_event_id[key] = event_id[key]
        
        if not new_event_id:
            logger.error("没有找到有效的睡眠分期标注")
            return None, None, None, None
            
        # 创建30秒的epochs
        tmin, tmax = 0, 30 - 1/raw.info['sfreq']
        epochs = mne.Epochs(raw, events, new_event_id, tmin, tmax, baseline=None, preload=True)
        
        # 提取数据
        eeg_data = []
        eog_data = []
        labels = []
        
        for idx, epoch in enumerate(epochs):
            label_name = epochs.events[idx, 2]
            
            # 找到对应的标签
            true_label = None
            for name, value in new_event_id.items():
                if value == label_name:
                    true_label = label_mapping.get(name)
                    break
                    
            if true_label is not None:
                # 提取EEG数据
                # 使用通道名直接提取
                eeg_chs = [ch for ch in epochs.ch_names if 'EEG' in ch]
                if len(eeg_chs) > 0:
                    eeg_picks = mne.pick_channels(epochs.ch_names, eeg_chs)
                    eeg_epoch = epoch[eeg_picks, :]
                    if len(eeg_picks) > 1:
                        eeg_epoch = np.mean(eeg_epoch, axis=0, keepdims=True)
                    
                    # 提取EOG数据
                    eog_chs = [ch for ch in epochs.ch_names if 'EOG' in ch]
                    if len(eog_chs) > 0:
                        eog_picks = mne.pick_channels(epochs.ch_names, eog_chs)
                        eog_epoch = epoch[eog_picks, :]
                        
                        eeg_data.append(eeg_epoch[0])
                        eog_data.append(eog_epoch[0])
                        labels.append(true_label)
        
        if len(eeg_data) == 0:
            logger.error("没有提取到有效数据")
            return None, None, None, None
            
        # 转换为numpy数组
        eeg_data = np.array(eeg_data)
        eog_data = np.array(eog_data) 
        labels = np.array(labels)
        
        logger.info(f"数据形状 - EEG: {eeg_data.shape}, EOG: {eog_data.shape}, Labels: {labels.shape}")
        logger.info(f"标签分布: {np.bincount(labels.astype(int))}")
        
        # 保存数据
        # 计算均值和标准差（用于归一化）
        mean_eeg = np.mean(eeg_data, axis=1, keepdims=True)
        std_eeg = np.std(eeg_data, axis=1, keepdims=True)
        mean_eog = np.mean(eog_data, axis=1, keepdims=True)
        std_eog = np.std(eog_data, axis=1, keepdims=True)
        
        # 保存文件
        h5_files = {
            f'x{subject_id:02d}.h5': eeg_data,
            f'eog{subject_id:02d}.h5': eog_data,
            f'y{subject_id:02d}.h5': labels,
            f'mean{subject_id:02d}.h5': mean_eeg,
            f'std{subject_id:02d}.h5': std_eeg,
            f'eog_m{subject_id:02d}.h5': mean_eog,
            f'eog_s{subject_id:02d}.h5': std_eog
        }
        
        for filename, data in h5_files.items():
            filepath = os.path.join(output_dir, filename)
            with h5py.File(filepath, 'w') as f:
                f.create_dataset('data', data=data)
            logger.info(f"保存: {filepath}")
        
        return eeg_data, eog_data, labels, subject_id
        
    except Exception as e:
        logger.error(f"处理出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def main():
    parser = argparse.ArgumentParser('预处理完整Sleep-EDF数据集')
    parser.add_argument('--data_dir', type=str, default='./data-edf',
                       help='原始EDF数据目录')
    parser.add_argument('--output_dir', type=str, default='./processed_data',
                       help='输出目录')
    parser.add_argument('--subjects', type=str, default='all',
                       help='要处理的subject列表，如"1,2,3"或"all"')
    args = parser.parse_args()
    
    logger = setup_logging()
    logger.info("开始预处理完整Sleep-EDF数据集")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取所有PSG文件
    psg_files = glob.glob(os.path.join(args.data_dir, '*PSG.edf'))
    psg_files.sort()
    
    logger.info(f"找到 {len(psg_files)} 个PSG文件")
    
    # 确定要处理的subjects
    if args.subjects == 'all':
        subjects_to_process = list(range(len(psg_files)))
    else:
        subjects_to_process = [int(s.strip()) - 1 for s in args.subjects.split(',')]
    
    # 处理统计
    success_count = 0
    failed_subjects = []
    
    for idx in subjects_to_process:
        if idx >= len(psg_files):
            logger.warning(f"Subject {idx+1} 超出范围，跳过")
            continue
            
        psg_file = psg_files[idx]
        base_name = os.path.basename(psg_file).replace('-PSG.edf', '')
        
        # 查找对应的Hypnogram文件（文件名可能有不同的后缀）
        psg_base = psg_file.replace('-PSG.edf', '')
        hypnogram_pattern = psg_base[:-2] + '*-Hypnogram.edf'
        hypnogram_matches = glob.glob(hypnogram_pattern)
        
        if not hypnogram_matches:
            logger.error(f"找不到Hypnogram文件: {hypnogram_pattern}")
            failed_subjects.append(idx+1)
            continue
            
        hypnogram_file = hypnogram_matches[0]
        
        logger.info(f"\n处理 Subject {idx+1}/{len(psg_files)}")
        logger.info(f"PSG: {os.path.basename(psg_file)}")
        logger.info(f"Hypnogram: {os.path.basename(hypnogram_file)}")
        
        try:
            # 处理单个subject
            x_data, eog_data, y_data, sub_id = process_subject(
                psg_file, hypnogram_file, args.output_dir, subject_id=idx+1
            )
            
            if x_data is not None:
                logger.info(f"✓ Subject {idx+1} 处理成功")
                logger.info(f"  EEG形状: {x_data.shape}")
                logger.info(f"  EOG形状: {eog_data.shape}")
                logger.info(f"  标签形状: {y_data.shape}")
                logger.info(f"  标签分布: {np.bincount(y_data)}")
                success_count += 1
            else:
                logger.error(f"✗ Subject {idx+1} 处理失败")
                failed_subjects.append(idx+1)
                
        except Exception as e:
            logger.error(f"✗ Subject {idx+1} 处理出错: {str(e)}")
            failed_subjects.append(idx+1)
    
    # 汇总结果
    logger.info("\n" + "="*60)
    logger.info("预处理完成！")
    logger.info(f"成功: {success_count}/{len(subjects_to_process)} subjects")
    if failed_subjects:
        logger.info(f"失败: {failed_subjects}")
    logger.info(f"数据保存在: {args.output_dir}")
    
    # 创建数据集划分说明
    split_info = {
        "train": list(range(1, 17)),  # Subjects 1-16
        "val": [17, 18],              # Subjects 17-18
        "test": [19, 20]              # Subjects 19-20
    }
    
    import json
    split_file = os.path.join(args.output_dir, 'dataset_split.json')
    with open(split_file, 'w') as f:
        json.dump(split_info, f, indent=2)
    logger.info(f"数据集划分信息保存在: {split_file}")

if __name__ == '__main__':
    main()