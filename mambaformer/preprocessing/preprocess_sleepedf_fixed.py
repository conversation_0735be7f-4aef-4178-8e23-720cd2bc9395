"""
Sleep-EDF-20数据集修正版预处理脚本
正确读取PSG和Hypnogram文件，确保受试者级别的数据组织
"""

import os
import numpy as np
import mne
from scipy import signal
import h5py
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Sleep-EDF-20数据集参数
SAMPLING_RATE = 100  # Hz
EPOCH_DURATION = 30  # seconds
SEQUENCE_LENGTH = EPOCH_DURATION * SAMPLING_RATE  # 3000 samples

# 睡眠阶段映射 - 这些是Hypnogram文件中的标注
stage_mapping = {
    'Sleep stage W': 0,
    'Sleep stage 1': 1, 
    'Sleep stage 2': 2,
    'Sleep stage 3': 3,
    'Sleep stage 4': 3,  # N3和N4合并为N3
    'Sleep stage R': 4,
    'Sleep stage ?': -1,  # 未知阶段将被排除
    'Movement time': -1,   # 移动时间排除
    'Sleep stage N1': 1,   # 可能的标注格式
    'Sleep stage N2': 2,
    'Sleep stage N3': 3,
    'Sleep stage REM': 4
}

def extract_subject_info(filename):
    """从文件名正确提取受试者ID和夜晚信息"""
    name = os.path.basename(filename)
    # SC4001E0-PSG.edf -> 受试者00, 第1晚
    # SC4002E0-PSG.edf -> 受试者00, 第2晚
    record_id = name[2:6]  # 例如：4001, 4002, 4011等
    
    # 正确的受试者ID计算
    first_two = int(record_id[:2])  # 40或41
    last_two = int(record_id[2:])   # 01, 02, 11, 12等
    
    if first_two == 40:
        # 4001,4002 -> 受试者00
        # 4011,4012 -> 受试者01
        subject_id = (last_two - 1) // 10
    else:  # first_two == 41
        # 4101,4102 -> 受试者10
        subject_id = 10 + (last_two - 1) // 10
    
    # 判断是第几晚
    night = 1 if last_two % 10 == 1 else 2
    
    return subject_id, night, record_id

def find_hypnogram_file(psg_file, data_dir):
    """查找对应的Hypnogram文件"""
    psg_name = os.path.basename(psg_file)
    record_id = psg_name[2:6]  # 提取记录ID，如4001
    
    # 可能的Hypnogram文件名格式
    possible_names = [
        f"SC{record_id}EC-Hypnogram.edf",  # 如SC4001EC-Hypnogram.edf
        f"SC{record_id}EH-Hypnogram.edf"   # 如SC4011EH-Hypnogram.edf
    ]
    
    for hyp_name in possible_names:
        hyp_path = os.path.join(data_dir, hyp_name)
        if os.path.exists(hyp_path):
            return hyp_path
    
    return None

def preprocess_subject_data(psg_file, hyp_file):
    """预处理单个受试者的PSG和Hypnogram数据"""
    try:
        # 读取PSG文件
        raw = mne.io.read_raw_edf(psg_file, preload=True, verbose=False)
        
        # 选择EEG和EOG通道
        channels = ['EEG Fpz-Cz', 'EEG Pz-Oz', 'EOG horizontal']
        
        # 检查通道是否存在
        available_channels = raw.ch_names
        selected_channels = [ch for ch in channels if ch in available_channels]
        
        if len(selected_channels) < 3:
            print(f"  警告：缺少通道，可用通道: {available_channels}")
            if len(selected_channels) < 2:
                return None, None
        
        # 选择通道
        raw.pick_channels(selected_channels, ordered=True)
        
        # 获取数据（单位：V）
        data = raw.get_data()
        
        # 转换为微伏
        data = data * 1e6  # V to μV
        
        # 应用带通滤波器 (0.3-35 Hz)
        nyquist = SAMPLING_RATE / 2
        low = 0.3 / nyquist
        high = 35.0 / nyquist
        b, a = signal.butter(4, [low, high], btype='band')
        
        for i in range(data.shape[0]):
            data[i] = signal.filtfilt(b, a, data[i])
        
        # 读取Hypnogram文件获取睡眠阶段标注
        annotations = mne.read_annotations(hyp_file)
        
        # 提取每个30秒epoch的标签
        epochs_data = []
        labels = []
        
        for i, (onset, duration, description) in enumerate(zip(annotations.onset, 
                                                              annotations.duration, 
                                                              annotations.description)):
            if description in stage_mapping:
                label = stage_mapping[description]
                if label == -1:  # 跳过未知阶段和移动时间
                    continue
                
                # 提取对应的数据段
                start_sample = int(onset * SAMPLING_RATE)
                end_sample = start_sample + SEQUENCE_LENGTH
                
                if end_sample <= data.shape[1]:
                    epoch_data = data[:, start_sample:end_sample]
                    
                    # 确保数据维度正确
                    if epoch_data.shape[0] == 3 and epoch_data.shape[1] == SEQUENCE_LENGTH:
                        epochs_data.append(epoch_data)
                        labels.append(label)
                    elif epoch_data.shape[0] == 2:
                        # 如果只有2个通道，复制第二个通道作为EOG
                        padded_data = np.zeros((3, SEQUENCE_LENGTH))
                        padded_data[:2] = epoch_data
                        padded_data[2] = epoch_data[1]  # 使用第二个EEG作为EOG替代
                        epochs_data.append(padded_data)
                        labels.append(label)
        
        if len(epochs_data) == 0:
            return None, None
            
        return np.array(epochs_data), np.array(labels, dtype=int)
    
    except Exception as e:
        print(f"  处理错误: {e}")
        return None, None

def main():
    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-edf/sleep_edf_20"
    output_file = "sleep_edf_20_correct.h5"
    
    # 获取所有PSG文件
    psg_files = [f for f in os.listdir(data_dir) if f.endswith('-PSG.edf')]
    psg_files.sort()
    
    print(f"找到 {len(psg_files)} 个PSG文件")
    
    # 组织数据：按受试者分组
    subject_data = {}
    
    # 处理每个文件
    for psg_file in tqdm(psg_files, desc="处理PSG文件"):
        psg_path = os.path.join(data_dir, psg_file)
        subject_id, night, record_id = extract_subject_info(psg_file)
        
        # 查找对应的Hypnogram文件
        hyp_path = find_hypnogram_file(psg_path, data_dir)
        if hyp_path is None:
            print(f"未找到 {psg_file} 对应的Hypnogram文件，跳过")
            continue
        
        print(f"\n处理: {psg_file} + {os.path.basename(hyp_path)} -> 受试者{subject_id:02d}, 第{night}晚")
        
        epochs_data, labels = preprocess_subject_data(psg_path, hyp_path)
        
        if epochs_data is not None and len(epochs_data) > 0:
            if subject_id not in subject_data:
                subject_data[subject_id] = {}
            
            subject_data[subject_id][f'night{night}'] = {
                'data': epochs_data,
                'labels': labels,
                'psg_file': psg_file,
                'hyp_file': os.path.basename(hyp_path)
            }
            
            print(f"  提取了 {len(labels)} 个epochs")
            print(f"  标签分布: {np.bincount(labels)}")
        else:
            print(f"  处理失败或无有效数据")
    
    # 显示受试者统计
    print("\n受试者数据统计:")
    print("=" * 60)
    total_epochs = 0
    valid_subjects = []
    
    for subj_id in sorted(subject_data.keys()):
        nights = list(subject_data[subj_id].keys())
        subject_epochs = sum(len(subject_data[subj_id][night]['labels']) 
                           for night in nights)
        if subject_epochs > 0:
            valid_subjects.append(subj_id)
            total_epochs += subject_epochs
            
        print(f"受试者{subj_id:02d}: {len(nights)}晚数据, 共{subject_epochs}个epochs")
        
        # 显示每晚的详细信息
        for night in sorted(nights):
            night_data = subject_data[subj_id][night]
            night_epochs = len(night_data['labels'])
            if night_epochs > 0:
                label_dist = np.bincount(night_data['labels'], minlength=5)
                print(f"  {night}: {night_epochs}个epochs, 分布{label_dist}")
    
    print(f"\n有效受试者: {len(valid_subjects)}, 总epochs: {total_epochs}")
    
    if len(valid_subjects) == 0:
        print("错误：没有成功处理任何受试者的数据！")
        return
    
    # 保存数据（保持受试者完整性）
    print(f"\n保存数据到 {output_file}")
    
    with h5py.File(output_file, 'w') as hf:
        # 保存元数据
        hf.attrs['sampling_rate'] = SAMPLING_RATE
        hf.attrs['sequence_length'] = SEQUENCE_LENGTH
        hf.attrs['num_subjects'] = len(valid_subjects)
        
        # 为每个有效受试者创建组
        for subj_id in sorted(valid_subjects):
            subj_group = hf.create_group(f'subject_{subj_id:02d}')
            
            # 合并该受试者的所有数据
            all_data = []
            all_labels = []
            
            for night in sorted(subject_data[subj_id].keys()):
                night_data = subject_data[subj_id][night]
                all_data.append(night_data['data'])
                all_labels.append(night_data['labels'])
                
                # 记录每晚的文件名
                subj_group.attrs[f'{night}_psg'] = night_data['psg_file']
                subj_group.attrs[f'{night}_hyp'] = night_data['hyp_file']
            
            # 连接所有数据
            if all_data:
                combined_data = np.concatenate(all_data, axis=0)
                combined_labels = np.concatenate(all_labels, axis=0)
                
                # 保存数据
                subj_group.create_dataset('data', data=combined_data, 
                                        compression='gzip', compression_opts=4)
                subj_group.create_dataset('labels', data=combined_labels, 
                                        compression='gzip', compression_opts=4)
                
                # 保存统计信息
                subj_group.attrs['num_epochs'] = len(combined_labels)
                subj_group.attrs['num_nights'] = len(all_data)
                
                # 计算并保存标签分布
                label_counts = np.bincount(combined_labels, minlength=5)
                for i, count in enumerate(label_counts):
                    subj_group.attrs[f'class_{i}_count'] = int(count)
    
    print("预处理完成！")
    
    # 验证数据完整性
    print("\n验证数据完整性...")
    with h5py.File(output_file, 'r') as hf:
        print(f"总受试者数: {hf.attrs['num_subjects']}")
        
        total_epochs = 0
        class_totals = np.zeros(5, dtype=int)
        
        for subj_id in sorted(valid_subjects):
            key = f'subject_{subj_id:02d}'
            if key in hf:
                subj_group = hf[key]
                num_epochs = subj_group.attrs['num_epochs']
                num_nights = subj_group.attrs['num_nights']
                total_epochs += num_epochs
                
                # 累计类别统计
                for i in range(5):
                    class_totals[i] += subj_group.attrs[f'class_{i}_count']
                
                print(f"  受试者{subj_id:02d}: {num_nights}晚, {num_epochs}个epochs")
        
        print(f"\n总epochs数: {total_epochs}")
        print(f"类别分布: Wake({class_totals[0]}), N1({class_totals[1]}), N2({class_totals[2]}), N3({class_totals[3]}), REM({class_totals[4]})")
        
        # 计算类别百分比
        percentages = class_totals / total_epochs * 100
        print(f"类别比例: Wake({percentages[0]:.1f}%), N1({percentages[1]:.1f}%), N2({percentages[2]:.1f}%), N3({percentages[3]:.1f}%), REM({percentages[4]:.1f}%)")

if __name__ == "__main__":
    main()