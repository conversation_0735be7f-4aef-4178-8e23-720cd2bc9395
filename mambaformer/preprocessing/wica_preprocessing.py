"""
wICA (weighted Independent Component Analysis) 预处理模块
用于睡眠EEG信号的盲源分离和伪迹去除
"""

import numpy as np
import torch
import torch.nn as nn
from scipy import signal
from sklearn.decomposition import FastICA
from sklearn.preprocessing import StandardScaler
import mne
import warnings
warnings.filterwarnings('ignore')

class wICAProcessor:
    """
    wICA加权独立成分分析处理器
    专门用于睡眠EEG信号的伪迹去除
    """
    
    def __init__(self, 
                 n_components=None,
                 max_iter=200,
                 random_state=42,
                 whiten='unit-variance',
                 frequency_weights=True):
        """
        初始化wICA处理器
        
        Args:
            n_components: ICA成分数量，None则自动选择
            max_iter: 最大迭代次数
            random_state: 随机种子
            whiten: 白化方法
            frequency_weights: 是否使用频域加权
        """
        self.n_components = n_components
        self.max_iter = max_iter
        self.random_state = random_state
        self.whiten = whiten
        self.frequency_weights = frequency_weights
        
        self.ica = None
        self.scaler = StandardScaler()
        self.artifact_components = []
        self.sampling_rate = 100  # Sleep-EDF数据集采样率
        
    def compute_frequency_weights(self, data, fs=100):
        """
        计算频域加权权重
        给予睡眠相关频带更高的权重
        """
        # 计算功率谱密度
        freqs, psd = signal.welch(data, fs=fs, nperseg=min(256, data.shape[-1]//4))
        
        # 定义睡眠相关频带权重
        weights = np.ones_like(freqs)
        
        # Delta (0.5-4 Hz) - 深睡眠
        delta_mask = (freqs >= 0.5) & (freqs <= 4)
        weights[delta_mask] *= 2.0
        
        # Theta (4-8 Hz) - REM睡眠  
        theta_mask = (freqs >= 4) & (freqs <= 8)
        weights[theta_mask] *= 1.8
        
        # Alpha (8-13 Hz) - 清醒/浅睡
        alpha_mask = (freqs >= 8) & (freqs <= 13)
        weights[alpha_mask] *= 1.5
        
        # Sigma (11-15 Hz) - 睡眠纺锤波
        sigma_mask = (freqs >= 11) & (freqs <= 15)
        weights[sigma_mask] *= 2.5
        
        # Beta (13-30 Hz) - 清醒状态
        beta_mask = (freqs >= 13) & (freqs <= 30)
        weights[beta_mask] *= 1.2
        
        # 高频噪声抑制
        noise_mask = freqs >= 30
        weights[noise_mask] *= 0.5
        
        return freqs, weights
    
    def apply_frequency_weighting(self, data):
        """
        应用频域加权到数据
        """
        if not self.frequency_weights:
            return data
            
        weighted_data = np.zeros_like(data)
        
        for ch_idx in range(data.shape[0]):
            # 计算频域权重
            freqs, weights = self.compute_frequency_weights(data[ch_idx])
            
            # 应用FFT
            fft_data = np.fft.fft(data[ch_idx])
            fft_freqs = np.fft.fftfreq(len(data[ch_idx]), 1/self.sampling_rate)
            
            # 应用权重
            for i, freq in enumerate(fft_freqs):
                if freq >= 0:
                    weight_idx = np.argmin(np.abs(freqs - abs(freq)))
                    fft_data[i] *= weights[weight_idx]
            
            # 逆FFT
            weighted_data[ch_idx] = np.real(np.fft.ifft(fft_data))
            
        return weighted_data
    
    def detect_artifacts(self, components, mixing_matrix):
        """
        检测伪迹成分
        基于成分的统计特征和频域特征
        """
        artifact_indices = []
        
        for i, component in enumerate(components):
            is_artifact = False
            
            # 1. 眼电伪迹检测 (低频高幅度)
            freqs, psd = signal.welch(component, fs=self.sampling_rate, nperseg=256)
            low_freq_power = np.sum(psd[freqs <= 2])
            total_power = np.sum(psd)
            
            if low_freq_power / total_power > 0.6:  # 低频功率占比大
                is_artifact = True
                
            # 2. 肌电伪迹检测 (高频特征)
            high_freq_power = np.sum(psd[freqs >= 20])
            if high_freq_power / total_power > 0.4:  # 高频功率占比大
                is_artifact = True
                
            # 3. 心电伪迹检测 (1-2Hz周期性)
            cardiac_power = np.sum(psd[(freqs >= 1) & (freqs <= 2)])
            if cardiac_power / total_power > 0.3:
                is_artifact = True
                
            # 4. 幅度异常检测
            amplitude_std = np.std(component)
            amplitude_mean = np.mean(np.abs(component))
            if amplitude_std > 5 * amplitude_mean:  # 异常尖峰
                is_artifact = True
                
            # 5. 空间分布检测（基于mixing matrix）
            spatial_pattern = mixing_matrix[:, i]
            # 眼电通常在前额区域（Fpz-Cz）
            if abs(spatial_pattern[0]) > 0.8:  # 第一个通道权重过大
                is_artifact = True
                
            if is_artifact:
                artifact_indices.append(i)
                
        return artifact_indices
    
    def fit_transform(self, data):
        """
        拟合ICA并转换数据
        
        Args:
            data: 输入数据 (n_channels, n_samples)
            
        Returns:
            cleaned_data: 去除伪迹后的数据
        """
        # 数据预处理
        if data.ndim == 3:  # (n_epochs, n_channels, n_samples)
            # 将所有epoch连接起来进行ICA
            original_shape = data.shape
            data_concat = data.reshape(data.shape[0], -1).T  # (n_samples_total, n_channels)
        else:  # (n_channels, n_samples)
            original_shape = data.shape
            data_concat = data.T  # (n_samples, n_channels)
        
        # 标准化
        data_scaled = self.scaler.fit_transform(data_concat)
        
        # 应用频域加权
        if self.frequency_weights:
            data_weighted = self.apply_frequency_weighting(data_scaled.T).T
        else:
            data_weighted = data_scaled
            
        # 确定成分数量
        if self.n_components is None:
            self.n_components = min(data_weighted.shape[1], data_weighted.shape[0] // 10)
            
        # 执行ICA
        print(f"执行wICA分解，成分数量: {self.n_components}")
        self.ica = FastICA(
            n_components=self.n_components,
            random_state=self.random_state,
            max_iter=self.max_iter,
            whiten=self.whiten,
            tol=1e-6
        )
        
        # 拟合ICA
        components = self.ica.fit_transform(data_weighted)  # (n_samples, n_components)
        mixing_matrix = self.ica.mixing_  # (n_channels, n_components)
        
        # 检测伪迹成分
        self.artifact_components = self.detect_artifacts(components.T, mixing_matrix)
        print(f"检测到 {len(self.artifact_components)} 个伪迹成分: {self.artifact_components}")
        
        # 去除伪迹成分
        cleaned_components = components.copy()
        cleaned_components[:, self.artifact_components] = 0
        
        # 重构信号
        cleaned_data_scaled = self.ica.inverse_transform(cleaned_components)
        
        # 反标准化
        cleaned_data = self.scaler.inverse_transform(cleaned_data_scaled).T
        
        # 恢复原始形状
        if len(original_shape) == 3:  # epochs格式
            cleaned_data = cleaned_data.reshape(original_shape[1], -1)
            cleaned_data = cleaned_data.reshape(original_shape[1], original_shape[0], original_shape[2])
            cleaned_data = cleaned_data.transpose(1, 0, 2)  # (n_epochs, n_channels, n_samples)
        
        return cleaned_data
    
    def transform(self, data):
        """
        使用已训练的ICA变换新数据
        """
        if self.ica is None:
            raise ValueError("wICA未拟合，请先调用fit_transform")
            
        # 处理数据格式
        if data.ndim == 3:
            original_shape = data.shape
            data_concat = data.reshape(data.shape[0], -1).T
        else:
            original_shape = data.shape
            data_concat = data.T
            
        # 标准化
        data_scaled = self.scaler.transform(data_concat)
        
        # 频域加权
        if self.frequency_weights:
            data_weighted = self.apply_frequency_weighting(data_scaled.T).T
        else:
            data_weighted = data_scaled
            
        # ICA变换
        components = self.ica.transform(data_weighted)
        
        # 去除伪迹
        cleaned_components = components.copy()
        cleaned_components[:, self.artifact_components] = 0
        
        # 重构信号
        cleaned_data_scaled = self.ica.inverse_transform(cleaned_components)
        cleaned_data = self.scaler.inverse_transform(cleaned_data_scaled).T
        
        # 恢复形状
        if len(original_shape) == 3:
            cleaned_data = cleaned_data.reshape(original_shape[1], -1)
            cleaned_data = cleaned_data.reshape(original_shape[1], original_shape[0], original_shape[2])
            cleaned_data = cleaned_data.transpose(1, 0, 2)
            
        return cleaned_data
    
    def get_artifact_info(self):
        """
        获取伪迹检测信息
        """
        if self.ica is None:
            return None
            
        info = {
            'n_components': self.n_components,
            'artifact_components': self.artifact_components,
            'n_artifacts': len(self.artifact_components),
            'clean_components': [i for i in range(self.n_components) if i not in self.artifact_components]
        }
        
        return info

def apply_wica_to_epochs(epochs_data, verbose=True):
    """
    对sleep epochs数据应用wICA处理
    
    Args:
        epochs_data: (n_epochs, n_channels, n_samples)
        verbose: 是否输出详细信息
        
    Returns:
        cleaned_epochs: 去除伪迹后的epochs数据
        wica_info: wICA处理信息
    """
    if verbose:
        print(f"输入数据形状: {epochs_data.shape}")
        print("开始wICA伪迹去除...")
    
    # 创建wICA处理器
    wica = wICAProcessor(
        n_components=None,  # 自动选择
        frequency_weights=True,  # 启用睡眠频域加权
        max_iter=500,
        random_state=42
    )
    
    # 应用wICA
    cleaned_data = wica.fit_transform(epochs_data)
    
    # 获取处理信息
    wica_info = wica.get_artifact_info()
    
    if verbose:
        print(f"输出数据形状: {cleaned_data.shape}")
        print(f"去除了 {wica_info['n_artifacts']} 个伪迹成分")
        print("wICA伪迹去除完成！")
    
    return cleaned_data, wica_info

# 使用示例和测试代码
if __name__ == "__main__":
    # 创建测试数据
    np.random.seed(42)
    n_epochs, n_channels, n_samples = 10, 3, 3000
    
    # 模拟sleep EEG数据
    test_data = np.random.randn(n_epochs, n_channels, n_samples) * 50
    
    # 添加模拟伪迹
    # 眼电伪迹（低频）
    eog_artifact = np.sin(2 * np.pi * 1 * np.linspace(0, 30, n_samples)) * 200
    test_data[:, 0, :] += eog_artifact  # 影响第一个通道
    
    # 肌电伪迹（高频）
    emg_artifact = np.random.randn(n_samples) * 100
    emg_artifact = signal.filtfilt(*signal.butter(4, [20, 40], btype='band', fs=100), emg_artifact)
    test_data[:, 1, 500:1500] += emg_artifact[500:1500]  # 部分时段
    
    print("测试wICA处理...")
    cleaned_data, wica_info = apply_wica_to_epochs(test_data)
    
    print("\n处理结果:")
    print(f"原始数据RMS: {np.sqrt(np.mean(test_data**2)):.2f}")
    print(f"清洁数据RMS: {np.sqrt(np.mean(cleaned_data**2)):.2f}")
    print(f"噪声减少: {(1 - np.sqrt(np.mean(cleaned_data**2))/np.sqrt(np.mean(test_data**2)))*100:.1f}%")