2025-08-11 03:01:06,336 - INFO - ================================================================================
2025-08-11 03:01:06,336 - INFO - 🎯 V20 ADVANCED ENSEMBLE FOR 90% TARGET
2025-08-11 03:01:06,336 - INFO - ================================================================================
2025-08-11 03:01:06,507 - INFO - Device: cuda
2025-08-11 03:01:06,507 - INFO - 
📂 Test files: 8
2025-08-11 03:01:06,600 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=6,282,911, d_model=288, n_heads=18, n_layers=6
2025-08-11 03:01:06,938 - INFO - ✅ Loaded V17_Stable (weight=0.250)
2025-08-11 03:01:07,162 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=12,808,295, d_model=384, n_heads=24, n_layers=7
2025-08-11 03:01:07,331 - INFO - ✅ Loaded V18_Fixed (weight=0.300)
2025-08-11 03:01:07,332 - INFO - ❌ Path not found for V15_Enhanced
2025-08-11 03:01:07,332 - INFO - ❌ Path not found for V14_Baseline
2025-08-11 03:01:07,332 - INFO - 
Using 2 models for ensemble
2025-08-11 03:01:07,332 - INFO -   V17_Stable: normalized weight=0.455
2025-08-11 03:01:07,332 - INFO -   V18_Fixed: normalized weight=0.545
2025-08-11 03:01:07,332 - INFO - 
📊 Processing V17_Stable with TTA...
2025-08-11 03:01:07,747 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-11 03:01:07,747 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-11 03:01:31,441 - INFO -   V17_Stable with TTA:
2025-08-11 03:01:31,442 - INFO -     Accuracy: 0.8684
2025-08-11 03:01:31,442 - INFO -     Macro F1: 0.8038
2025-08-11 03:01:31,442 - INFO -     Kappa: 0.8215
2025-08-11 03:01:31,442 - INFO - 
📊 Processing V18_Fixed with TTA...
2025-08-11 03:01:31,921 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9706 个序列
2025-08-11 03:01:31,921 - INFO - 创建序列数据集: 9706个序列, 序列长度=6, 通道数=3, 总epochs=9746
2025-08-11 03:01:58,666 - INFO -   V18_Fixed with TTA:
2025-08-11 03:01:58,666 - INFO -     Accuracy: 0.8395
2025-08-11 03:01:58,666 - INFO -     Macro F1: 0.7922
2025-08-11 03:01:58,666 - INFO -     Kappa: 0.7876
2025-08-11 03:01:58,666 - INFO - 
================================================================================
2025-08-11 03:01:58,666 - INFO - 🎯 ENSEMBLE RESULTS WITH POST-PROCESSING
2025-08-11 03:01:58,667 - INFO - ================================================================================
2025-08-11 03:01:58,667 - INFO - 
📊 Applying temporal post-processing...
2025-08-11 03:02:02,791 - INFO - 
📊 Ensemble Only:
2025-08-11 03:02:02,791 - INFO -   Accuracy: 0.8722 (87.22%)
2025-08-11 03:02:02,792 - INFO -   Macro F1: 0.8196 (81.96%)
2025-08-11 03:02:02,792 - INFO -   Kappa: 0.8282
2025-08-11 03:02:02,798 - INFO - 
📊 Ensemble + Post-Processing:
2025-08-11 03:02:02,798 - INFO -   Accuracy: 0.8723 (87.23%)
2025-08-11 03:02:02,798 - INFO -   Macro F1: 0.8184 (81.84%)
2025-08-11 03:02:02,798 - INFO -   Kappa: 0.8280
2025-08-11 03:02:02,798 - INFO - 
================================================================================
2025-08-11 03:02:02,798 - INFO - 🏆 BEST CONFIGURATION: Ensemble + Post-Processing
2025-08-11 03:02:02,798 - INFO - ================================================================================
2025-08-11 03:02:02,798 - INFO - 
🎯 TARGET ACHIEVEMENT (90% / 82% / 0.82):
2025-08-11 03:02:02,798 - INFO -   ❌ Accuracy: 0.8723 < 0.90 (gap: 0.0277)
2025-08-11 03:02:02,798 - INFO -   ❌ Macro F1: 0.8184 < 0.82 (gap: 0.0016)
2025-08-11 03:02:02,798 - INFO -   ✅ Kappa: 0.8280 ≥ 0.82
2025-08-11 03:02:02,800 - INFO - 
📊 Confusion Matrix:
2025-08-11 03:02:02,800 - INFO -      Wake   N1    N2    N3   REM
2025-08-11 03:02:02,800 - INFO - Wake  2252   131    72     3    76 
2025-08-11 03:02:02,800 - INFO - N1      51   266    72     3    79 
2025-08-11 03:02:02,800 - INFO - N2      19   109  3195   127   202 
2025-08-11 03:02:02,800 - INFO - N3       2     0   196  1277     1 
2025-08-11 03:02:02,800 - INFO - REM      7    20    75     0  1511 
2025-08-11 03:02:02,813 - INFO - 
📊 Per-Class Performance:
2025-08-11 03:02:02,813 - INFO - Wake: P=96.6%, R=88.9%, F1=92.6%
2025-08-11 03:02:02,813 - INFO - N1: P=50.6%, R=56.5%, F1=53.4%
2025-08-11 03:02:02,813 - INFO - N2: P=88.5%, R=87.5%, F1=88.0%
2025-08-11 03:02:02,813 - INFO - N3: P=90.6%, R=86.5%, F1=88.5%
2025-08-11 03:02:02,814 - INFO - REM: P=80.8%, R=93.7%, F1=86.8%
2025-08-11 03:02:02,814 - INFO - 
📈 Performance Improvements:
2025-08-11 03:02:02,814 - INFO -   Best single model: ~86.96%
2025-08-11 03:02:02,814 - INFO -   Ensemble result: 87.23%
2025-08-11 03:02:02,814 - INFO -   Improvement: +0.27%
2025-08-11 03:02:02,815 - INFO - 
💾 Results saved to ../logs/ensemble_v20_results_20250811_030106.json
2025-08-11 03:02:02,815 - INFO - 
📈 Continue optimization. Next steps:
2025-08-11 03:02:02,815 - INFO -   1. Wait for V19 MEGA model results
2025-08-11 03:02:02,815 - INFO -   2. Implement pseudo-labeling on unlabeled data
2025-08-11 03:02:02,815 - INFO -   3. Try deeper ensemble stacking
2025-08-11 03:02:02,815 - INFO -   4. Explore neural architecture search
