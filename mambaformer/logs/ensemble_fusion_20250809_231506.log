2025-08-09 23:15:06,386 - INFO - 日志文件: ../logs/ensemble_fusion_20250809_231506.log
2025-08-09 23:15:06,386 - INFO - 🚀 MAMBAFORMER集成融合训练 - 冲击ICASSP 2026
2025-08-09 23:15:06,386 - INFO - ================================================================================
2025-08-09 23:15:06,386 - INFO - 🎯 集成策略:
2025-08-09 23:15:06,386 - INFO -   • 渐进式集成: 先用已完成模型(V7,V8)验证框架
2025-08-09 23:15:06,386 - INFO -   • 智能权重分配: 基于历史性能和模态专长
2025-08-09 23:15:06,386 - INFO -   • 4种集成方法: 投票/自适应/时序/学习权重
2025-08-09 23:15:06,386 - INFO -   • HMM后处理: 时序平滑和生理约束
2025-08-09 23:15:06,386 - INFO -   • ultrathink: 不等待所有模型，先验证可行性
2025-08-09 23:15:06,386 - INFO -   • 目标: 突破85% Macro F1
2025-08-09 23:15:06,415 - INFO - 🖥️  使用设备: cuda
2025-08-09 23:15:06,415 - INFO - \n📦 创建集成学习系统...
2025-08-09 23:15:06,595 - INFO - 🌟 MAMBAFORMER集成学习系统创建完成!
2025-08-09 23:15:06,595 - INFO - 🎯 目标: 突破85% Macro F1，冲击ICASSP 2026!
2025-08-09 23:15:06,595 - INFO - \n⚙️  加载所有可用模型...
2025-08-09 23:15:06,624 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=948,007, d_model=128, n_heads=8, n_layers=4
2025-08-09 23:15:06,656 - INFO - ✅ 成功加载 V7_EEG: ../../checkpoints/sequential_v7_balanced.pth
2025-08-09 23:15:06,683 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=948,007, d_model=128, n_heads=8, n_layers=4
2025-08-09 23:15:06,712 - INFO - ✅ 成功加载 V8_Enhanced: ../../checkpoints/sequential_v8_enhanced.pth
2025-08-09 23:15:06,741 - INFO - 创建MultiModalMAMBAFORMER (EEG_EOG): 参数量=1,046,441
2025-08-09 23:15:06,741 - WARNING - ⚠️  模型文件不存在: ../../checkpoints/multimodal_v10_eeg_eog.pth
2025-08-09 23:15:06,771 - INFO - 创建MultiModalMAMBAFORMER (EEG_EOG_EMG): 参数量=1,191,115
2025-08-09 23:15:06,772 - WARNING - ⚠️  模型文件不存在: ../../checkpoints/multimodal_v11_complete.pth
2025-08-09 23:15:06,803 - INFO - 创建深度MultiModalMAMBAFORMER V12 (7层): 参数量=1,657,769
2025-08-09 23:15:06,803 - WARNING - ⚠️  模型文件不存在: ../../checkpoints/deep_v12.pth
2025-08-09 23:15:06,804 - INFO - ✅ 成功加载: ['V7_EEG', 'V8_Enhanced']
2025-08-09 23:15:06,804 - INFO - ⚠️  加载失败: ['V10_EEG_EOG', 'V11_Complete', 'V12_Deep']
2025-08-09 23:15:06,804 - INFO - 🎯 将使用 2 个模型进行集成
2025-08-09 23:15:06,804 - INFO - \n📚 准备测试数据...
2025-08-09 23:15:06,804 - INFO - 创建多模态序列数据集: 0个序列, 序列长度=5, EEG×2+EOG×1+EMG×1
2025-08-09 23:15:06,804 - INFO - 创建多模态序列数据集: 0个序列, 序列长度=5, EEG×2+EOG×1+EMG×1
2025-08-09 23:15:08,776 - INFO - 创建多模态序列数据集: 9714个序列, 序列长度=5, EEG×2+EOG×1+EMG×1
2025-08-09 23:15:08,777 - INFO - 训练集统计:
