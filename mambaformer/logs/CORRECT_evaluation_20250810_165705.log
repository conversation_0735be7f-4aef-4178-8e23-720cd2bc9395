WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.
🔬 CORRECT Rigorous Sleep Stage Classification Evaluation
================================================================================
⚠️  修复了数据加载问题：使用完整数据集而非仅150个epochs
🎯 严谨原则:
  1. 权重搜索只在验证集进行
  2. 测试集只用于最终评估
  3. 无数据泄露，符合学术标准
================================================================================
Device: cuda

📊 Loading FULL datasets (no truncation)...
Validation dataset: 4677 sequences, 4693 epochs
Test dataset: 9714 sequences, 9746 epochs

📊 Wake distribution check:
Loading V7...
✅ V7 loaded
Loading V8...
✅ V8 loaded
Loading V13...
✅ V13 loaded
Loading V14...
✅ V14 loaded

📊 Evaluating on VALIDATION set (FULL DATA)...
   Dataset has 4677 sequences from 4693 epochs

Evaluating V7...

  0%|          | 0/147 [00:00<?, ?it/s]
  1%|          | 1/147 [00:00<01:03,  2.29it/s]
 12%|█▏        | 17/147 [00:00<00:03, 40.97it/s]
 23%|██▎       | 34/147 [00:00<00:01, 74.12it/s]
 35%|███▌      | 52/147 [00:00<00:00, 101.43it/s]
 48%|████▊     | 70/147 [00:00<00:00, 121.28it/s]
 60%|█████▉    | 88/147 [00:00<00:00, 136.93it/s]
 72%|███████▏  | 106/147 [00:01<00:00, 148.78it/s]
 84%|████████▍ | 124/147 [00:01<00:00, 156.81it/s]
 97%|█████████▋| 143/147 [00:01<00:00, 164.50it/s]
100%|██████████| 147/147 [00:01<00:00, 110.37it/s]
V7: ACC=0.8189, F1=0.7493, Kappa=0.7490
  Wake F1: 0.8155, REM F1: 0.8194
  Wake: 1132 predicted, 852 true, 809 correct

Evaluating V8...

  0%|          | 0/147 [00:00<?, ?it/s]
  1%|          | 1/147 [00:00<00:15,  9.57it/s]
 12%|█▏        | 18/147 [00:00<00:01, 99.31it/s]
 26%|██▌       | 38/147 [00:00<00:00, 143.50it/s]
 39%|███▉      | 58/147 [00:00<00:00, 164.90it/s]
 53%|█████▎    | 78/147 [00:00<00:00, 176.21it/s]
 67%|██████▋   | 98/147 [00:00<00:00, 181.85it/s]
 80%|████████  | 118/147 [00:00<00:00, 187.26it/s]
 94%|█████████▍| 138/147 [00:00<00:00, 189.49it/s]
100%|██████████| 147/147 [00:00<00:00, 161.53it/s]
V8: ACC=0.8332, F1=0.7670, Kappa=0.7718
  Wake F1: 0.8229, REM F1: 0.8624
  Wake: 1102 predicted, 852 true, 804 correct

Evaluating V13...

  0%|          | 0/147 [00:00<?, ?it/s]
  1%|          | 1/147 [00:00<00:16,  8.59it/s]
 12%|█▏        | 17/147 [00:00<00:01, 91.83it/s]
 22%|██▏       | 33/147 [00:00<00:00, 119.80it/s]
 34%|███▍      | 50/147 [00:00<00:00, 137.05it/s]
 46%|████▌     | 67/147 [00:00<00:00, 145.71it/s]
 57%|█████▋    | 84/147 [00:00<00:00, 150.88it/s]
 69%|██████▊   | 101/147 [00:00<00:00, 153.72it/s]
 80%|███████▉  | 117/147 [00:00<00:00, 155.32it/s]
 90%|█████████ | 133/147 [00:00<00:00, 156.60it/s]
100%|██████████| 147/147 [00:01<00:00, 136.46it/s]
V13: ACC=0.6316, F1=0.4864, Kappa=0.4969
  Wake F1: 0.0000, REM F1: 0.7408
  Wake: 0 predicted, 852 true, 0 correct

Evaluating V14...

  0%|          | 0/147 [00:00<?, ?it/s]
  1%|          | 1/147 [00:00<00:17,  8.57it/s]
 10%|█         | 15/147 [00:00<00:01, 79.11it/s]
 20%|█▉        | 29/147 [00:00<00:01, 104.09it/s]
 29%|██▉       | 43/147 [00:00<00:00, 117.45it/s]
 39%|███▉      | 57/147 [00:00<00:00, 125.22it/s]
 49%|████▉     | 72/147 [00:00<00:00, 130.40it/s]
 59%|█████▊    | 86/147 [00:00<00:00, 132.97it/s]
 68%|██████▊   | 100/147 [00:00<00:00, 134.97it/s]
 78%|███████▊  | 115/147 [00:00<00:00, 136.82it/s]
 88%|████████▊ | 130/147 [00:01<00:00, 137.66it/s]
 99%|█████████▊| 145/147 [00:01<00:00, 139.02it/s]
100%|██████████| 147/147 [00:01<00:00, 120.47it/s]
V14: ACC=0.6725, F1=0.5286, Kappa=0.5503
  Wake F1: 0.0000, REM F1: 0.7913
  Wake: 0 predicted, 852 true, 0 correct

🔍 Optimizing weights on VALIDATION set...
  Tested 1000 combinations, best F1: 0.7906
  Tested 2000 combinations, best F1: 0.7937
  Tested 3000 combinations, best F1: 0.7946
  Tested 4000 combinations, best F1: 0.7953
  Tested 5000 combinations, best F1: 0.7962
  Tested 6000 combinations, best F1: 0.7981
  Tested 7000 combinations, best F1: 0.7981
  Tested 8000 combinations, best F1: 0.7981
  Tested 9000 combinations, best F1: 0.7981
  Tested 10000 combinations, best F1: 0.7981
  Tested 11000 combinations, best F1: 0.7981
  Tested 12000 combinations, best F1: 0.7981
  Tested 13000 combinations, best F1: 0.7981
  Tested 14000 combinations, best F1: 0.7981

Best validation weights: {'V7': 0.26666666666666666, 'V8': 0.06666666666666667, 'V13': 0.4666666666666666, 'V14': 0.2}
Best validation F1: 0.7981

📊 Evaluating on TEST set (FULL DATA)...
   Dataset has 9714 sequences from 9746 epochs

Evaluating V7...

  0%|          | 0/304 [00:00<?, ?it/s]
  0%|          | 1/304 [00:00<00:47,  6.39it/s]
  7%|▋         | 21/304 [00:00<00:02, 98.04it/s]
 13%|█▎        | 41/304 [00:00<00:01, 138.29it/s]
 20%|██        | 61/304 [00:00<00:01, 159.42it/s]
 27%|██▋       | 81/304 [00:00<00:01, 172.20it/s]
 33%|███▎      | 101/304 [00:00<00:01, 180.36it/s]
 40%|████      | 122/304 [00:00<00:00, 186.73it/s]
 47%|████▋     | 143/304 [00:00<00:00, 191.34it/s]
 54%|█████▍    | 164/304 [00:00<00:00, 193.93it/s]
 61%|██████    | 184/304 [00:01<00:00, 195.67it/s]
 67%|██████▋   | 205/304 [00:01<00:00, 197.99it/s]
 74%|███████▍  | 226/304 [00:01<00:00, 199.81it/s]
 81%|████████▏ | 247/304 [00:01<00:00, 200.27it/s]
 88%|████████▊ | 268/304 [00:01<00:00, 200.54it/s]
 95%|█████████▌| 289/304 [00:01<00:00, 139.40it/s]
100%|██████████| 304/304 [00:01<00:00, 159.31it/s]
V7: ACC=0.8564, F1=0.7890, Kappa=0.8051
  Wake F1: 0.8152, REM F1: 0.9129
  Wake: 1981 predicted, 1613 true, 1465 correct

Evaluating V8...

  0%|          | 0/304 [00:00<?, ?it/s]
  0%|          | 1/304 [00:00<00:38,  7.78it/s]
  6%|▌         | 18/304 [00:00<00:03, 91.28it/s]
 12%|█▏        | 36/304 [00:00<00:02, 127.04it/s]
 18%|█▊        | 54/304 [00:00<00:01, 144.84it/s]
 24%|██▎       | 72/304 [00:00<00:01, 154.41it/s]
 30%|██▉       | 90/304 [00:00<00:01, 160.90it/s]
 36%|███▌      | 108/304 [00:00<00:01, 165.50it/s]
 41%|████▏     | 126/304 [00:00<00:01, 168.17it/s]
 47%|████▋     | 144/304 [00:00<00:00, 169.23it/s]
 53%|█████▎    | 162/304 [00:01<00:00, 170.49it/s]
 59%|█████▉    | 180/304 [00:01<00:00, 170.97it/s]
 65%|██████▌   | 198/304 [00:01<00:00, 171.66it/s]
 71%|███████   | 216/304 [00:01<00:00, 171.58it/s]
 77%|███████▋  | 234/304 [00:01<00:00, 172.41it/s]
 83%|████████▎ | 252/304 [00:01<00:00, 172.92it/s]
 89%|████████▉ | 270/304 [00:01<00:00, 172.97it/s]
 95%|█████████▍| 288/304 [00:01<00:00, 173.31it/s]
100%|██████████| 304/304 [00:01<00:00, 157.44it/s]
V8: ACC=0.8374, F1=0.7880, Kappa=0.7815
  Wake F1: 0.7971, REM F1: 0.8518
  Wake: 1960 predicted, 1613 true, 1424 correct

Evaluating V13...

  0%|          | 0/304 [00:00<?, ?it/s]
  0%|          | 1/304 [00:00<00:34,  8.73it/s]
  5%|▍         | 14/304 [00:00<00:03, 73.56it/s]
  9%|▉         | 27/304 [00:00<00:02, 95.61it/s]
 13%|█▎        | 40/304 [00:00<00:02, 107.91it/s]
 17%|█▋        | 53/304 [00:00<00:02, 114.95it/s]
 22%|██▏       | 67/304 [00:00<00:01, 119.37it/s]
 27%|██▋       | 82/304 [00:00<00:01, 128.09it/s]
 33%|███▎      | 99/304 [00:00<00:01, 138.44it/s]
 38%|███▊      | 116/304 [00:00<00:01, 145.70it/s]
 44%|████▍     | 133/304 [00:01<00:01, 150.26it/s]
 49%|████▉     | 149/304 [00:01<00:01, 153.12it/s]
 54%|█████▍    | 165/304 [00:01<00:00, 154.38it/s]
 60%|█████▉    | 181/304 [00:01<00:00, 154.94it/s]
 65%|██████▍   | 197/304 [00:01<00:00, 156.06it/s]
 70%|███████   | 213/304 [00:01<00:00, 156.91it/s]
 75%|███████▌  | 229/304 [00:01<00:00, 157.72it/s]
 81%|████████  | 245/304 [00:01<00:00, 157.62it/s]
 86%|████████▌ | 261/304 [00:01<00:00, 157.93it/s]
 91%|█████████▏| 278/304 [00:01<00:00, 158.64it/s]
 97%|█████████▋| 294/304 [00:02<00:00, 158.64it/s]
100%|██████████| 304/304 [00:02<00:00, 139.10it/s]
V13: ACC=0.6808, F1=0.5192, Kappa=0.5646
  Wake F1: 0.0000, REM F1: 0.8821
  Wake: 0 predicted, 1613 true, 0 correct

Evaluating V14...

  0%|          | 0/304 [00:00<?, ?it/s]
  0%|          | 1/304 [00:00<00:34,  8.69it/s]
  5%|▌         | 16/304 [00:00<00:03, 85.97it/s]
 11%|█         | 32/304 [00:00<00:02, 118.00it/s]
 16%|█▌        | 49/304 [00:00<00:01, 136.27it/s]
 22%|██▏       | 66/304 [00:00<00:01, 146.34it/s]
 27%|██▋       | 83/304 [00:00<00:01, 152.03it/s]
 33%|███▎      | 99/304 [00:00<00:01, 153.54it/s]
 38%|███▊      | 115/304 [00:00<00:01, 155.36it/s]
 43%|████▎     | 131/304 [00:00<00:01, 156.42it/s]
 48%|████▊     | 147/304 [00:01<00:00, 157.37it/s]
 54%|█████▎    | 163/304 [00:01<00:00, 157.92it/s]
 59%|█████▉    | 179/304 [00:01<00:00, 158.08it/s]
 64%|██████▍   | 195/304 [00:01<00:00, 158.57it/s]
 69%|██████▉   | 211/304 [00:01<00:00, 158.43it/s]
 75%|███████▍  | 227/304 [00:01<00:00, 158.46it/s]
 80%|███████▉  | 243/304 [00:01<00:00, 158.26it/s]
 85%|████████▌ | 259/304 [00:01<00:00, 157.93it/s]
 90%|█████████ | 275/304 [00:01<00:00, 157.79it/s]
 96%|█████████▌| 291/304 [00:01<00:00, 157.58it/s]
100%|██████████| 304/304 [00:02<00:00, 146.54it/s]
V14: ACC=0.7023, F1=0.5651, Kappa=0.5994
  Wake F1: 0.0400, REM F1: 0.8941
  Wake: 239 predicted, 1613 true, 37 correct

🎯 Final evaluation on TEST set with fixed weights...
⚠️  This is the ONLY time we touch the test set!

================================================================================
🎯 CORRECT RIGOROUS FINAL RESULTS (FULL DATA)
================================================================================
Ensemble weights (optimized on validation): {'V7': 0.26666666666666666, 'V8': 0.06666666666666667, 'V13': 0.4666666666666666, 'V14': 0.2}

Final test performance:
  Accuracy: 0.8604
  Macro F1: 0.8035
  Kappa: 0.8092

Per-class F1 scores:
  REM: 0.9259
  N1: 0.5196
  N2: 0.8679
  N3: 0.8566
  Wake: 0.8472

🎯 Target Achievement (with FULL data):
  ACCURACY: 0.8604 (Target: 0.87) ❌
  KAPPA: 0.8092 (Target: 0.8) ✅
  MACRO_F1: 0.8035 (Target: 0.8) ✅
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/CORRECT_rigorous_evaluation.py", line 359, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/CORRECT_rigorous_evaluation.py", line 352, in main
    json.dump(results, f, indent=2)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/__init__.py", line 179, in dump
    for chunk in iterable:
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 438, in _iterencode
    o = _default(o)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type bool_ is not JSON serializable
