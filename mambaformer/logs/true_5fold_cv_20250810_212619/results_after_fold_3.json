{"timestamp": "20250810_212619", "config": {"d_model": 256, "n_heads": 16, "n_layers": 6, "dropout": 0.15, "seq_len": 5, "batch_size": 32, "num_workers": 4, "learning_rate": 0.0001, "num_epochs": 20, "patience": 5}, "completed_folds": [{"fold": 1, "train_subjects": ["18", "16", "13", "10", "11", "04", "19", "06", "09", "12", "14", "05", "08"], "val_subjects": ["02", "03", "07"], "test_subjects": ["00", "01", "15", "17"], "test_accuracy": 0.8446542171147138, "test_macro_f1": 0.7956131710056016, "test_kappa": 0.7952445801705609, "best_val_f1": 0.8314676410573828, "confusion_matrix": [[2111, 302, 37, 5, 79], [26, 310, 53, 3, 79], [17, 120, 2880, 349, 286], [2, 0, 62, 1411, 1], [8, 22, 63, 0, 1520]], "per_class_metrics": {"Wake": {"precision": 0.9755083179297597, "recall": 0.8330702446724546, "f1": 0.8986802894848871, "support": 2534}, "N1": {"precision": 0.41114058355437666, "recall": 0.6581740976645435, "f1": 0.5061224489795918, "support": 471}, "N2": {"precision": 0.9305331179321487, "recall": 0.7886089813800657, "f1": 0.8537127612272121, "support": 3652}, "N3": {"precision": 0.7980769230769231, "recall": 0.9559620596205962, "f1": 0.8699136868064119, "support": 1476}, "REM": {"precision": 0.7735368956743003, "recall": 0.9423434593924365, "f1": 0.8496366685299049, "support": 1613}}, "training_history": [{"epoch": 1, "train_loss": 0.9535112791760414, "val_acc": 0.8249267816465994, "val_f1": 0.7701420271153323, "val_kappa": 0.7593206309267299}, {"epoch": 2, "train_loss": 0.6388976435084123, "val_acc": 0.876505043931012, "val_f1": 0.829721928962875, "val_kappa": 0.8298424226940385}, {"epoch": 3, "train_loss": 0.5647636120481793, "val_acc": 0.8573055645948584, "val_f1": 0.8072843859938204, "val_kappa": 0.8017469743936696}, {"epoch": 4, "train_loss": 0.5362878123846657, "val_acc": 0.8743898470549951, "val_f1": 0.8314676410573828, "val_kappa": 0.828815014105128}, {"epoch": 5, "train_loss": 0.4953577478219123, "val_acc": 0.8569801496908558, "val_f1": 0.8072289676227463, "val_kappa": 0.8037881081806693}, {"epoch": 6, "train_loss": 0.4713233177280484, "val_acc": 0.833875691506671, "val_f1": 0.7908337467091128, "val_kappa": 0.775408880386211}, {"epoch": 7, "train_loss": 0.4436134346318941, "val_acc": 0.8659290595509275, "val_f1": 0.8188825672912818, "val_kappa": 0.8156642293995211}, {"epoch": 8, "train_loss": 0.42248773576641896, "val_acc": 0.8734136023429873, "val_f1": 0.8252339931632854, "val_kappa": 0.8248667625575069}, {"epoch": 9, "train_loss": 0.387611052905121, "val_acc": 0.8603970061828832, "val_f1": 0.8190827165901705, "val_kappa": 0.8101213123799725}], "training_time": 511.9755370616913}, {"fold": 2, "train_subjects": ["09", "15", "02", "10", "13", "17", "04", "18", "14", "07", "01", "00", "06"], "val_subjects": ["19", "12", "16"], "test_subjects": ["03", "05", "08", "11"], "test_accuracy": 0.7555526691778153, "test_macro_f1": 0.7059772848125914, "test_kappa": 0.6770637903809058, "best_val_f1": 0.7956479441120301, "confusion_matrix": [[1365, 33, 7, 5, 15], [109, 200, 33, 5, 55], [543, 212, 2216, 115, 121], [54, 21, 105, 1118, 11], [239, 152, 46, 1, 918]], "per_class_metrics": {"Wake": {"precision": 0.5909090909090909, "recall": 0.9578947368421052, "f1": 0.7309236947791165, "support": 1425}, "N1": {"precision": 0.32362459546925565, "recall": 0.4975124378109453, "f1": 0.3921568627450981, "support": 402}, "N2": {"precision": 0.9206481096800997, "recall": 0.6909884627377612, "f1": 0.789454934093338, "support": 3207}, "N3": {"precision": 0.8987138263665595, "recall": 0.854087089381207, "f1": 0.8758323540932237, "support": 1309}, "REM": {"precision": 0.8196428571428571, "recall": 0.6769911504424779, "f1": 0.7415185783521809, "support": 1356}}, "training_history": [{"epoch": 1, "train_loss": 0.9225349537161893, "val_acc": 0.7700787401574803, "val_f1": 0.7003756413165678, "val_kappa": 0.6750655565857684}, {"epoch": 2, "train_loss": 0.6245193810657014, "val_acc": 0.8296349319971367, "val_f1": 0.7650711702268558, "val_kappa": 0.7652698034003467}, {"epoch": 3, "train_loss": 0.5491394072771072, "val_acc": 0.8365068002863278, "val_f1": 0.7763107412804944, "val_kappa": 0.772616587777656}, {"epoch": 4, "train_loss": 0.5081083010795504, "val_acc": 0.839226914817466, "val_f1": 0.7796164701918293, "val_kappa": 0.7774889913265255}, {"epoch": 5, "train_loss": 0.4707486853003502, "val_acc": 0.8177523264137437, "val_f1": 0.7547713972072367, "val_kappa": 0.7438150864505111}, {"epoch": 6, "train_loss": 0.4569266610020815, "val_acc": 0.8254831782390838, "val_f1": 0.7600695681317424, "val_kappa": 0.7569990677755567}, {"epoch": 7, "train_loss": 0.4273219081204991, "val_acc": 0.8336435218324982, "val_f1": 0.7670657601470191, "val_kappa": 0.7686210750302659}, {"epoch": 8, "train_loss": 0.4163131629138492, "val_acc": 0.8450966356478168, "val_f1": 0.7862361049992557, "val_kappa": 0.7838148357479082}, {"epoch": 9, "train_loss": 0.40101506602625514, "val_acc": 0.8491052254831782, "val_f1": 0.7883557261123213, "val_kappa": 0.7888118976439394}, {"epoch": 10, "train_loss": 0.38385727445400036, "val_acc": 0.862992125984252, "val_f1": 0.7956479441120301, "val_kappa": 0.807955632723409}, {"epoch": 11, "train_loss": 0.37539044376029523, "val_acc": 0.8481030780243378, "val_f1": 0.7934825267783665, "val_kappa": 0.7911918043990882}, {"epoch": 12, "train_loss": 0.3632141603806684, "val_acc": 0.8459556191839657, "val_f1": 0.7845155973924892, "val_kappa": 0.7866375561744015}, {"epoch": 13, "train_loss": 0.34990200356168805, "val_acc": 0.8452397995705082, "val_f1": 0.7716078791381373, "val_kappa": 0.7808628850125419}, {"epoch": 14, "train_loss": 0.34145999183488446, "val_acc": 0.8186113099498926, "val_f1": 0.7388145684353121, "val_kappa": 0.7431255272823194}, {"epoch": 15, "train_loss": 0.3005501694630745, "val_acc": 0.8513958482462419, "val_f1": 0.7896501694299174, "val_kappa": 0.7927793221233309}], "training_time": 772.7327568531036}, {"fold": 3, "train_subjects": ["09", "15", "11", "03", "08", "14", "19", "00", "17", "12", "01", "04", "05"], "val_subjects": ["06", "10", "07"], "test_subjects": ["02", "13", "16", "18"], "test_accuracy": 0.8343437191597349, "test_macro_f1": 0.8061284572148532, "test_kappa": 0.7772271127522925, "best_val_f1": 0.862134265291661, "confusion_matrix": [[1019, 64, 3, 1, 1], [99, 383, 89, 0, 41], [62, 223, 2462, 215, 67], [2, 4, 104, 1051, 0], [12, 100, 88, 0, 1003]], "per_class_metrics": {"Wake": {"precision": 0.8534338358458962, "recall": 0.9365808823529411, "f1": 0.8930762489044697, "support": 1088}, "N1": {"precision": 0.49483204134366926, "recall": 0.6258169934640523, "f1": 0.5526695526695526, "support": 612}, "N2": {"precision": 0.8965768390386016, "recall": 0.8128095080884781, "f1": 0.8526406926406926, "support": 3029}, "N3": {"precision": 0.829518547750592, "recall": 0.905254091300603, "f1": 0.8657331136738056, "support": 1161}, "REM": {"precision": 0.9019784172661871, "recall": 0.8337489609310058, "f1": 0.8665226781857452, "support": 1203}}, "training_history": [{"epoch": 1, "train_loss": 0.9628353880180253, "val_acc": 0.8261421319796954, "val_f1": 0.789986512008357, "val_kappa": 0.7596693717432832}, {"epoch": 2, "train_loss": 0.6608217561576102, "val_acc": 0.8767449238578681, "val_f1": 0.8384646546155062, "val_kappa": 0.8281869597265307}, {"epoch": 3, "train_loss": 0.5793238353729248, "val_acc": 0.8837246192893401, "val_f1": 0.8432051098518689, "val_kappa": 0.8354979026911044}, {"epoch": 4, "train_loss": 0.5335804004967213, "val_acc": 0.894511421319797, "val_f1": 0.8585760255925525, "val_kappa": 0.8513861435488415}, {"epoch": 5, "train_loss": 0.5095472804539734, "val_acc": 0.8902284263959391, "val_f1": 0.855703411373157, "val_kappa": 0.8446867629741123}, {"epoch": 6, "train_loss": 0.48677036143011515, "val_acc": 0.886738578680203, "val_f1": 0.8508469780160898, "val_kappa": 0.8428194069137718}, {"epoch": 7, "train_loss": 0.4572017672989104, "val_acc": 0.8938769035532995, "val_f1": 0.862134265291661, "val_kappa": 0.8521158089815173}, {"epoch": 8, "train_loss": 0.45166688077979616, "val_acc": 0.8851522842639594, "val_f1": 0.8526615893697194, "val_kappa": 0.840081182670785}, {"epoch": 9, "train_loss": 0.4248446284068955, "val_acc": 0.8843591370558376, "val_f1": 0.8541525258843958, "val_kappa": 0.8402282271127889}, {"epoch": 10, "train_loss": 0.4104337622390853, "val_acc": 0.8805520304568528, "val_f1": 0.8483134793372658, "val_kappa": 0.8317803826845754}, {"epoch": 11, "train_loss": 0.39958883504072823, "val_acc": 0.875, "val_f1": 0.8419552550240452, "val_kappa": 0.8268086220267701}, {"epoch": 12, "train_loss": 0.35605895567271445, "val_acc": 0.8868972081218274, "val_f1": 0.8561778172077841, "val_kappa": 0.8429516735461938}], "training_time": 710.2232708930969}]}