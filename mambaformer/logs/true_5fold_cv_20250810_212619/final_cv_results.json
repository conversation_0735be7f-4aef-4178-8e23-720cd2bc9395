{
  "timestamp": "20250810_212619",
  "config": {
    "d_model": 256,
    "n_heads": 16,
    "n_layers": 6,
    "dropout": 0.15,
    "seq_len": 5,
    "batch_size": 32,
    "num_workers": 4,
    "learning_rate": 0.0001,
    "num_epochs": 20,
    "patience": 5
  },
  "fold_results": [
    {
      "fold": 1,
      "train_subjects": [
        "18",
        "16",
        "13",
        "10",
        "11",
        "04",
        "19",
        "06",
        "09",
        "12",
        "14",
        "05",
        "08"
      ],
      "val_subjects": [
        "02",
        "03",
        "07"
      ],
      "test_subjects": [
        "00",
        "01",
        "15",
        "17"
      ],
      "test_accuracy": 0.8446542171147138,
      "test_macro_f1": 0.7956131710056016,
      "test_kappa": 0.7952445801705609,
      "best_val_f1": 0.8314676410573828,
      "confusion_matrix": [
        [
          2111,
          302,
          37,
          5,
          79
        ],
        [
          26,
          310,
          53,
          3,
          79
        ],
        [
          17,
          120,
          2880,
          349,
          286
        ],
        [
          2,
          0,
          62,
          1411,
          1
        ],
        [
          8,
          22,
          63,
          0,
          1520
        ]
      ],
      "per_class_metrics": {
        "Wake": {
          "precision": 0.9755083179297597,
          "recall": 0.8330702446724546,
          "f1": 0.8986802894848871,
          "support": 2534
        },
        "N1": {
          "precision": 0.41114058355437666,
          "recall": 0.6581740976645435,
          "f1": 0.5061224489795918,
          "support": 471
        },
        "N2": {
          "precision": 0.9305331179321487,
          "recall": 0.7886089813800657,
          "f1": 0.8537127612272121,
          "support": 3652
        },
        "N3": {
          "precision": 0.7980769230769231,
          "recall": 0.9559620596205962,
          "f1": 0.8699136868064119,
          "support": 1476
        },
        "REM": {
          "precision": 0.7735368956743003,
          "recall": 0.9423434593924365,
          "f1": 0.8496366685299049,
          "support": 1613
        }
      },
      "training_history": [
        {
          "epoch": 1,
          "train_loss": 0.9535112791760414,
          "val_acc": 0.8249267816465994,
          "val_f1": 0.7701420271153323,
          "val_kappa": 0.7593206309267299
        },
        {
          "epoch": 2,
          "train_loss": 0.6388976435084123,
          "val_acc": 0.876505043931012,
          "val_f1": 0.829721928962875,
          "val_kappa": 0.8298424226940385
        },
        {
          "epoch": 3,
          "train_loss": 0.5647636120481793,
          "val_acc": 0.8573055645948584,
          "val_f1": 0.8072843859938204,
          "val_kappa": 0.8017469743936696
        },
        {
          "epoch": 4,
          "train_loss": 0.5362878123846657,
          "val_acc": 0.8743898470549951,
          "val_f1": 0.8314676410573828,
          "val_kappa": 0.828815014105128
        },
        {
          "epoch": 5,
          "train_loss": 0.4953577478219123,
          "val_acc": 0.8569801496908558,
          "val_f1": 0.8072289676227463,
          "val_kappa": 0.8037881081806693
        },
        {
          "epoch": 6,
          "train_loss": 0.4713233177280484,
          "val_acc": 0.833875691506671,
          "val_f1": 0.7908337467091128,
          "val_kappa": 0.775408880386211
        },
        {
          "epoch": 7,
          "train_loss": 0.4436134346318941,
          "val_acc": 0.8659290595509275,
          "val_f1": 0.8188825672912818,
          "val_kappa": 0.8156642293995211
        },
        {
          "epoch": 8,
          "train_loss": 0.42248773576641896,
          "val_acc": 0.8734136023429873,
          "val_f1": 0.8252339931632854,
          "val_kappa": 0.8248667625575069
        },
        {
          "epoch": 9,
          "train_loss": 0.387611052905121,
          "val_acc": 0.8603970061828832,
          "val_f1": 0.8190827165901705,
          "val_kappa": 0.8101213123799725
        }
      ],
      "training_time": 511.9755370616913
    },
    {
      "fold": 2,
      "train_subjects": [
        "09",
        "15",
        "02",
        "10",
        "13",
        "17",
        "04",
        "18",
        "14",
        "07",
        "01",
        "00",
        "06"
      ],
      "val_subjects": [
        "19",
        "12",
        "16"
      ],
      "test_subjects": [
        "03",
        "05",
        "08",
        "11"
      ],
      "test_accuracy": 0.7555526691778153,
      "test_macro_f1": 0.7059772848125914,
      "test_kappa": 0.6770637903809058,
      "best_val_f1": 0.7956479441120301,
      "confusion_matrix": [
        [
          1365,
          33,
          7,
          5,
          15
        ],
        [
          109,
          200,
          33,
          5,
          55
        ],
        [
          543,
          212,
          2216,
          115,
          121
        ],
        [
          54,
          21,
          105,
          1118,
          11
        ],
        [
          239,
          152,
          46,
          1,
          918
        ]
      ],
      "per_class_metrics": {
        "Wake": {
          "precision": 0.5909090909090909,
          "recall": 0.9578947368421052,
          "f1": 0.7309236947791165,
          "support": 1425
        },
        "N1": {
          "precision": 0.32362459546925565,
          "recall": 0.4975124378109453,
          "f1": 0.3921568627450981,
          "support": 402
        },
        "N2": {
          "precision": 0.9206481096800997,
          "recall": 0.6909884627377612,
          "f1": 0.789454934093338,
          "support": 3207
        },
        "N3": {
          "precision": 0.8987138263665595,
          "recall": 0.854087089381207,
          "f1": 0.8758323540932237,
          "support": 1309
        },
        "REM": {
          "precision": 0.8196428571428571,
          "recall": 0.6769911504424779,
          "f1": 0.7415185783521809,
          "support": 1356
        }
      },
      "training_history": [
        {
          "epoch": 1,
          "train_loss": 0.9225349537161893,
          "val_acc": 0.7700787401574803,
          "val_f1": 0.7003756413165678,
          "val_kappa": 0.6750655565857684
        },
        {
          "epoch": 2,
          "train_loss": 0.6245193810657014,
          "val_acc": 0.8296349319971367,
          "val_f1": 0.7650711702268558,
          "val_kappa": 0.7652698034003467
        },
        {
          "epoch": 3,
          "train_loss": 0.5491394072771072,
          "val_acc": 0.8365068002863278,
          "val_f1": 0.7763107412804944,
          "val_kappa": 0.772616587777656
        },
        {
          "epoch": 4,
          "train_loss": 0.5081083010795504,
          "val_acc": 0.839226914817466,
          "val_f1": 0.7796164701918293,
          "val_kappa": 0.7774889913265255
        },
        {
          "epoch": 5,
          "train_loss": 0.4707486853003502,
          "val_acc": 0.8177523264137437,
          "val_f1": 0.7547713972072367,
          "val_kappa": 0.7438150864505111
        },
        {
          "epoch": 6,
          "train_loss": 0.4569266610020815,
          "val_acc": 0.8254831782390838,
          "val_f1": 0.7600695681317424,
          "val_kappa": 0.7569990677755567
        },
        {
          "epoch": 7,
          "train_loss": 0.4273219081204991,
          "val_acc": 0.8336435218324982,
          "val_f1": 0.7670657601470191,
          "val_kappa": 0.7686210750302659
        },
        {
          "epoch": 8,
          "train_loss": 0.4163131629138492,
          "val_acc": 0.8450966356478168,
          "val_f1": 0.7862361049992557,
          "val_kappa": 0.7838148357479082
        },
        {
          "epoch": 9,
          "train_loss": 0.40101506602625514,
          "val_acc": 0.8491052254831782,
          "val_f1": 0.7883557261123213,
          "val_kappa": 0.7888118976439394
        },
        {
          "epoch": 10,
          "train_loss": 0.38385727445400036,
          "val_acc": 0.862992125984252,
          "val_f1": 0.7956479441120301,
          "val_kappa": 0.807955632723409
        },
        {
          "epoch": 11,
          "train_loss": 0.37539044376029523,
          "val_acc": 0.8481030780243378,
          "val_f1": 0.7934825267783665,
          "val_kappa": 0.7911918043990882
        },
        {
          "epoch": 12,
          "train_loss": 0.3632141603806684,
          "val_acc": 0.8459556191839657,
          "val_f1": 0.7845155973924892,
          "val_kappa": 0.7866375561744015
        },
        {
          "epoch": 13,
          "train_loss": 0.34990200356168805,
          "val_acc": 0.8452397995705082,
          "val_f1": 0.7716078791381373,
          "val_kappa": 0.7808628850125419
        },
        {
          "epoch": 14,
          "train_loss": 0.34145999183488446,
          "val_acc": 0.8186113099498926,
          "val_f1": 0.7388145684353121,
          "val_kappa": 0.7431255272823194
        },
        {
          "epoch": 15,
          "train_loss": 0.3005501694630745,
          "val_acc": 0.8513958482462419,
          "val_f1": 0.7896501694299174,
          "val_kappa": 0.7927793221233309
        }
      ],
      "training_time": 772.7327568531036
    },
    {
      "fold": 3,
      "train_subjects": [
        "09",
        "15",
        "11",
        "03",
        "08",
        "14",
        "19",
        "00",
        "17",
        "12",
        "01",
        "04",
        "05"
      ],
      "val_subjects": [
        "06",
        "10",
        "07"
      ],
      "test_subjects": [
        "02",
        "13",
        "16",
        "18"
      ],
      "test_accuracy": 0.8343437191597349,
      "test_macro_f1": 0.8061284572148532,
      "test_kappa": 0.7772271127522925,
      "best_val_f1": 0.862134265291661,
      "confusion_matrix": [
        [
          1019,
          64,
          3,
          1,
          1
        ],
        [
          99,
          383,
          89,
          0,
          41
        ],
        [
          62,
          223,
          2462,
          215,
          67
        ],
        [
          2,
          4,
          104,
          1051,
          0
        ],
        [
          12,
          100,
          88,
          0,
          1003
        ]
      ],
      "per_class_metrics": {
        "Wake": {
          "precision": 0.8534338358458962,
          "recall": 0.9365808823529411,
          "f1": 0.8930762489044697,
          "support": 1088
        },
        "N1": {
          "precision": 0.49483204134366926,
          "recall": 0.6258169934640523,
          "f1": 0.5526695526695526,
          "support": 612
        },
        "N2": {
          "precision": 0.8965768390386016,
          "recall": 0.8128095080884781,
          "f1": 0.8526406926406926,
          "support": 3029
        },
        "N3": {
          "precision": 0.829518547750592,
          "recall": 0.905254091300603,
          "f1": 0.8657331136738056,
          "support": 1161
        },
        "REM": {
          "precision": 0.9019784172661871,
          "recall": 0.8337489609310058,
          "f1": 0.8665226781857452,
          "support": 1203
        }
      },
      "training_history": [
        {
          "epoch": 1,
          "train_loss": 0.9628353880180253,
          "val_acc": 0.8261421319796954,
          "val_f1": 0.789986512008357,
          "val_kappa": 0.7596693717432832
        },
        {
          "epoch": 2,
          "train_loss": 0.6608217561576102,
          "val_acc": 0.8767449238578681,
          "val_f1": 0.8384646546155062,
          "val_kappa": 0.8281869597265307
        },
        {
          "epoch": 3,
          "train_loss": 0.5793238353729248,
          "val_acc": 0.8837246192893401,
          "val_f1": 0.8432051098518689,
          "val_kappa": 0.8354979026911044
        },
        {
          "epoch": 4,
          "train_loss": 0.5335804004967213,
          "val_acc": 0.894511421319797,
          "val_f1": 0.8585760255925525,
          "val_kappa": 0.8513861435488415
        },
        {
          "epoch": 5,
          "train_loss": 0.5095472804539734,
          "val_acc": 0.8902284263959391,
          "val_f1": 0.855703411373157,
          "val_kappa": 0.8446867629741123
        },
        {
          "epoch": 6,
          "train_loss": 0.48677036143011515,
          "val_acc": 0.886738578680203,
          "val_f1": 0.8508469780160898,
          "val_kappa": 0.8428194069137718
        },
        {
          "epoch": 7,
          "train_loss": 0.4572017672989104,
          "val_acc": 0.8938769035532995,
          "val_f1": 0.862134265291661,
          "val_kappa": 0.8521158089815173
        },
        {
          "epoch": 8,
          "train_loss": 0.45166688077979616,
          "val_acc": 0.8851522842639594,
          "val_f1": 0.8526615893697194,
          "val_kappa": 0.840081182670785
        },
        {
          "epoch": 9,
          "train_loss": 0.4248446284068955,
          "val_acc": 0.8843591370558376,
          "val_f1": 0.8541525258843958,
          "val_kappa": 0.8402282271127889
        },
        {
          "epoch": 10,
          "train_loss": 0.4104337622390853,
          "val_acc": 0.8805520304568528,
          "val_f1": 0.8483134793372658,
          "val_kappa": 0.8317803826845754
        },
        {
          "epoch": 11,
          "train_loss": 0.39958883504072823,
          "val_acc": 0.875,
          "val_f1": 0.8419552550240452,
          "val_kappa": 0.8268086220267701
        },
        {
          "epoch": 12,
          "train_loss": 0.35605895567271445,
          "val_acc": 0.8868972081218274,
          "val_f1": 0.8561778172077841,
          "val_kappa": 0.8429516735461938
        }
      ],
      "training_time": 710.2232708930969
    },
    {
      "fold": 4,
      "train_subjects": [
        "02",
        "13",
        "08",
        "18",
        "01",
        "05",
        "16",
        "06",
        "00",
        "15",
        "03",
        "17",
        "14"
      ],
      "val_subjects": [
        "11",
        "10",
        "07"
      ],
      "test_subjects": [
        "04",
        "09",
        "12",
        "19"
      ],
      "test_accuracy": 0.8425867507886435,
      "test_macro_f1": 0.7980401253179181,
      "test_kappa": 0.7806160085543987,
      "best_val_f1": 0.7732916804361358,
      "confusion_matrix": [
        [
          1414,
          250,
          27,
          3,
          11
        ],
        [
          89,
          446,
          145,
          1,
          81
        ],
        [
          18,
          164,
          3719,
          155,
          168
        ],
        [
          0,
          0,
          69,
          712,
          0
        ],
        [
          46,
          106,
          164,
          0,
          1722
        ]
      ],
      "per_class_metrics": {
        "Wake": {
          "precision": 0.9023611997447352,
          "recall": 0.829325513196481,
          "f1": 0.8643031784841075,
          "support": 1705
        },
        "N1": {
          "precision": 0.4616977225672878,
          "recall": 0.5853018372703412,
          "f1": 0.5162037037037037,
          "support": 762
        },
        "N2": {
          "precision": 0.9017943743937924,
          "recall": 0.8804450757575758,
          "f1": 0.890991854336368,
          "support": 4224
        },
        "N3": {
          "precision": 0.817451205510907,
          "recall": 0.911651728553137,
          "f1": 0.8619854721549636,
          "support": 781
        },
        "REM": {
          "precision": 0.8688193743693239,
          "recall": 0.844946025515211,
          "f1": 0.8567164179104478,
          "support": 2038
        }
      },
      "training_history": [
        {
          "epoch": 1,
          "train_loss": 0.933154042540253,
          "val_acc": 0.8132793522267207,
          "val_f1": 0.7685879493660001,
          "val_kappa": 0.7364880632167349
        },
        {
          "epoch": 2,
          "train_loss": 0.6228041188512448,
          "val_acc": 0.8089068825910931,
          "val_f1": 0.7662070386663778,
          "val_kappa": 0.7337539483816267
        },
        {
          "epoch": 3,
          "train_loss": 0.5434504626453787,
          "val_acc": 0.8080971659919028,
          "val_f1": 0.7723483316947357,
          "val_kappa": 0.7338253534926984
        },
        {
          "epoch": 4,
          "train_loss": 0.5039522887212067,
          "val_acc": 0.7902834008097166,
          "val_f1": 0.7482488419484532,
          "val_kappa": 0.7047477692217772
        },
        {
          "epoch": 5,
          "train_loss": 0.46967554716888255,
          "val_acc": 0.8071255060728745,
          "val_f1": 0.772433406184976,
          "val_kappa": 0.7333066655410638
        },
        {
          "epoch": 6,
          "train_loss": 0.4405225109362948,
          "val_acc": 0.8092307692307692,
          "val_f1": 0.770726445041717,
          "val_kappa": 0.7332693496097522
        },
        {
          "epoch": 7,
          "train_loss": 0.42834942055425207,
          "val_acc": 0.7851012145748988,
          "val_f1": 0.7509401133554794,
          "val_kappa": 0.7051457985923311
        },
        {
          "epoch": 8,
          "train_loss": 0.4101839636582971,
          "val_acc": 0.8103643724696357,
          "val_f1": 0.7732916804361358,
          "val_kappa": 0.7350038901436624
        },
        {
          "epoch": 9,
          "train_loss": 0.392075779825305,
          "val_acc": 0.8084210526315789,
          "val_f1": 0.7692617862297124,
          "val_kappa": 0.7332775354037717
        },
        {
          "epoch": 10,
          "train_loss": 0.3783700258655537,
          "val_acc": 0.8045344129554656,
          "val_f1": 0.7670640167945997,
          "val_kappa": 0.7272638584511053
        },
        {
          "epoch": 11,
          "train_loss": 0.36482816043755284,
          "val_acc": 0.7862348178137651,
          "val_f1": 0.7495191647544777,
          "val_kappa": 0.7042614863989672
        },
        {
          "epoch": 12,
          "train_loss": 0.35005913444475273,
          "val_acc": 0.7931983805668016,
          "val_f1": 0.7555911771315488,
          "val_kappa": 0.7149594580008574
        },
        {
          "epoch": 13,
          "train_loss": 0.3192313638862204,
          "val_acc": 0.7797570850202429,
          "val_f1": 0.7458852256082293,
          "val_kappa": 0.697714323622121
        }
      ],
      "training_time": 566.8927743434906
    },
    {
      "fold": 5,
      "train_subjects": [
        "09",
        "18",
        "19",
        "12",
        "15",
        "00",
        "02",
        "03",
        "13",
        "04",
        "11",
        "05",
        "17"
      ],
      "val_subjects": [
        "01",
        "16",
        "08"
      ],
      "test_subjects": [
        "06",
        "07",
        "10",
        "14"
      ],
      "test_accuracy": 0.8800242130750605,
      "test_macro_f1": 0.838510696542121,
      "test_kappa": 0.8312026856590847,
      "best_val_f1": 0.8250268753585361,
      "confusion_matrix": [
        [
          1299,
          185,
          20,
          1,
          28
        ],
        [
          31,
          397,
          91,
          0,
          38
        ],
        [
          26,
          163,
          3452,
          8,
          38
        ],
        [
          3,
          1,
          183,
          789,
          0
        ],
        [
          1,
          52,
          122,
          0,
          1332
        ]
      ],
      "per_class_metrics": {
        "Wake": {
          "precision": 0.9551470588235295,
          "recall": 0.8473581213307241,
          "f1": 0.8980297269270654,
          "support": 1533
        },
        "N1": {
          "precision": 0.4974937343358396,
          "recall": 0.7127468581687613,
          "f1": 0.5859778597785977,
          "support": 557
        },
        "N2": {
          "precision": 0.8924508790072389,
          "recall": 0.9362625440737727,
          "f1": 0.9138318994043679,
          "support": 3687
        },
        "N3": {
          "precision": 0.9887218045112782,
          "recall": 0.8084016393442623,
          "f1": 0.8895152198421646,
          "support": 976
        },
        "REM": {
          "precision": 0.9275766016713092,
          "recall": 0.8838752488387525,
          "f1": 0.9051987767584098,
          "support": 1507
        }
      },
      "training_history": [
        {
          "epoch": 1,
          "train_loss": 0.9886357826546516,
          "val_acc": 0.8402777777777778,
          "val_f1": 0.7913762383555414,
          "val_kappa": 0.781793542554838
        },
        {
          "epoch": 2,
          "train_loss": 0.6702330448563185,
          "val_acc": 0.8644323671497585,
          "val_f1": 0.8198222535505979,
          "val_kappa": 0.8153480061931122
        },
        {
          "epoch": 3,
          "train_loss": 0.5968947076748572,
          "val_acc": 0.8685084541062802,
          "val_f1": 0.8250268753585361,
          "val_kappa": 0.8214124180113476
        },
        {
          "epoch": 4,
          "train_loss": 0.5542632267665192,
          "val_acc": 0.831219806763285,
          "val_f1": 0.7926271683719959,
          "val_kappa": 0.773724262071002
        },
        {
          "epoch": 5,
          "train_loss": 0.5145678751493814,
          "val_acc": 0.8532608695652174,
          "val_f1": 0.8090622851421283,
          "val_kappa": 0.8024518204376011
        },
        {
          "epoch": 6,
          "train_loss": 0.5016260968318159,
          "val_acc": 0.8494867149758454,
          "val_f1": 0.7988659101543477,
          "val_kappa": 0.7942404648006596
        },
        {
          "epoch": 7,
          "train_loss": 0.47300322519796534,
          "val_acc": 0.8573369565217391,
          "val_f1": 0.8124926700365382,
          "val_kappa": 0.8059718531357497
        },
        {
          "epoch": 8,
          "train_loss": 0.42123871627684073,
          "val_acc": 0.8410326086956522,
          "val_f1": 0.7963371604977945,
          "val_kappa": 0.7836277743751421
        }
      ],
      "training_time": 355.9304313659668
    }
  ],
  "average_metrics": {
    "accuracy": {
      "mean": 0.8314323138631936,
      "std": 0.04105267254665066
    },
    "macro_f1": {
      "mean": 0.7888539469786171,
      "std": 0.04418781640865349
    },
    "kappa": {
      "mean": 0.7722708355034487,
      "std": 0.05130174255360072
    }
  },
  "confidence_intervals_95": {
    "accuracy": {
      "lower": 0.7509690756717583,
      "upper": 0.9118955520546289
    },
    "macro_f1": {
      "lower": 0.7022458268176562,
      "upper": 0.875462067139578
    },
    "kappa": {
      "lower": 0.6717194200983913,
      "upper": 0.8728222509085061
    }
  },
  "targets_achieved": {
    "accuracy": 