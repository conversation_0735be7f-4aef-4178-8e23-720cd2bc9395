2025-08-11 17:13:34,191 - INFO - ================================================================================
2025-08-11 17:13:34,191 - INFO - 🔍 GRID SEARCH ENSEMBLE OPTIMIZATION
2025-08-11 17:13:34,191 - INFO - 📊 Current Best: 87.23% (V20 Ensemble)
2025-08-11 17:13:34,191 - INFO - 🎯 Target: 90% Test Accuracy
2025-08-11 17:13:34,191 - INFO - ================================================================================
2025-08-11 17:13:34,376 - INFO - Device: cuda
2025-08-11 17:13:34,377 - INFO - 
Loading V17_Stable...
2025-08-11 17:13:34,498 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=6,282,911, d_model=288, n_heads=18, n_layers=6
2025-08-11 17:13:35,278 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-11 17:13:35,278 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-11 17:13:46,274 - INFO -   V17_Stable: Acc=0.8696, F1=0.8046
2025-08-11 17:13:46,274 - INFO - 
Loading V18_Fixed...
2025-08-11 17:13:46,626 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=12,808,295, d_model=384, n_heads=24, n_layers=7
2025-08-11 17:13:47,419 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9706 个序列
2025-08-11 17:13:47,420 - INFO - 创建序列数据集: 9706个序列, 序列长度=6, 通道数=3, 总epochs=9746
2025-08-11 17:13:59,431 - INFO -   V18_Fixed: Acc=0.8395, F1=0.7925
2025-08-11 17:13:59,432 - INFO - 
Loading V22_Deep...
2025-08-11 17:13:59,633 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=24,603,159, d_model=448, n_heads=28, n_layers=10
2025-08-11 17:14:00,406 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9682 个序列
2025-08-11 17:14:00,406 - INFO - 创建序列数据集: 9682个序列, 序列长度=9, 通道数=3, 总epochs=9746
2025-08-11 17:14:16,093 - INFO -   V22_Deep: Acc=0.8308, F1=0.7812
2025-08-11 17:14:16,093 - INFO - 
================================================================================
2025-08-11 17:14:16,093 - INFO - OPTIMIZATION STRATEGIES:
2025-08-11 17:14:16,093 - INFO - ================================================================================
2025-08-11 17:14:16,093 - INFO - 
1. Grid Search Optimization...
2025-08-11 17:14:16,229 - INFO - 
   Best Weights Found:
2025-08-11 17:14:16,229 - INFO -      V17_Stable: 0.550
2025-08-11 17:14:16,229 - INFO -      V18_Fixed: 0.250
2025-08-11 17:14:16,229 - INFO -      V22_Deep: 0.200
2025-08-11 17:14:16,229 - INFO -    Accuracy: 0.8732 (87.32%)
2025-08-11 17:14:16,229 - INFO -    Macro F1: 0.8211
2025-08-11 17:14:16,229 - INFO -    Kappa: 0.8292
2025-08-11 17:14:16,229 - INFO - 
2. Grid Search + Smart Post-Processing...
2025-08-11 17:14:17,160 - INFO -    Accuracy: 0.8594 (85.94%)
2025-08-11 17:14:17,160 - INFO -    Macro F1: 0.8036
2025-08-11 17:14:17,160 - INFO -    Kappa: 0.8116
2025-08-11 17:14:17,160 - INFO - 
3. Pseudo-Stacking (confidence-weighted)...
2025-08-11 17:14:17,536 - INFO -    Accuracy: 0.8719 (87.19%)
2025-08-11 17:14:17,536 - INFO -    Macro F1: 0.8150
2025-08-11 17:14:17,536 - INFO -    Kappa: 0.8268
2025-08-11 17:14:17,537 - INFO - 
================================================================================
2025-08-11 17:14:17,537 - INFO - 🏆 BEST STRATEGY: Grid Search
2025-08-11 17:14:17,537 - INFO -    Test Accuracy: 0.8732 (87.32%)
2025-08-11 17:14:17,537 - INFO - 
📊 Comparison with V20 Ensemble (87.23%):
2025-08-11 17:14:17,537 - INFO -    ✅ Improvement: +0.09%
2025-08-11 17:14:17,537 - INFO - 
📈 Gap to 90%: 0.0268 (2.68%)
2025-08-11 17:14:17,537 - INFO - 
🔮 Suggested Next Steps:
2025-08-11 17:14:17,537 - INFO -    1. Train more diverse models (different architectures)
2025-08-11 17:14:17,537 - INFO -    2. Use validation set for pseudo-labeling
2025-08-11 17:14:17,537 - INFO -    3. Implement true stacking with a meta-learner
2025-08-11 17:14:17,537 - INFO -    4. Fine-tune on difficult classes (N1)
2025-08-11 17:14:17,537 - INFO -    5. Collect more training data
2025-08-11 17:14:17,537 - INFO - ================================================================================
2025-08-11 17:14:17,538 - INFO - 
💾 Results saved to ../logs/optimized_ensemble_20250811_171334/optimization_results.json
