2025-08-11 01:19:51,769 - INFO - ================================================================================
2025-08-11 01:19:51,770 - INFO - 🎯 FINAL ENSEMBLE EVALUATION FOR 87% TARGET
2025-08-11 01:19:51,770 - INFO - ================================================================================
2025-08-11 01:19:51,968 - INFO - Device: cuda
2025-08-11 01:19:51,969 - INFO - 
📂 Test files: 8
2025-08-11 01:19:51,969 - INFO - ❌ Path not found for V14_Original: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/checkpoints/mambaformer_v14_20250809_184458_best.pth
2025-08-11 01:19:51,969 - INFO - ❌ Path not found for V15_Targeted: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v15_targeted_20250810_230143/best_model.pth
2025-08-11 01:19:52,113 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=6,282,911, d_model=288, n_heads=18, n_layers=6
2025-08-11 01:19:52,495 - INFO - ❌ Failed to load V17_Stable: Weights only load failed. This file can still be loaded, to do so you have two options, [1mdo those steps only if you trust the source of the checkpoint[0m. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL numpy.core.multiarray.scalar was not an allowed global by default. Please use `torch.serialization.add_safe_globals([numpy.core.multiarray.scalar])` or the `torch.serialization.safe_globals([numpy.core.multiarray.scalar])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.
2025-08-11 01:19:52,496 - ERROR - No models could be loaded!
