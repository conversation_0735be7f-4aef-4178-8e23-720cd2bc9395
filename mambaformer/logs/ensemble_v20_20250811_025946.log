2025-08-11 02:59:46,633 - INFO - ================================================================================
2025-08-11 02:59:46,633 - INFO - 🎯 V20 ADVANCED ENSEMBLE FOR 90% TARGET
2025-08-11 02:59:46,633 - INFO - ================================================================================
2025-08-11 02:59:46,805 - INFO - Device: cuda
2025-08-11 02:59:46,805 - INFO - 
📂 Test files: 8
2025-08-11 02:59:46,963 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=6,282,911, d_model=288, n_heads=18, n_layers=6
2025-08-11 02:59:47,311 - INFO - ✅ Loaded V17_Stable (weight=0.250)
2025-08-11 02:59:47,456 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=12,808,295, d_model=384, n_heads=24, n_layers=7
2025-08-11 02:59:47,652 - INFO - ✅ Loaded V18_Fixed (weight=0.300)
2025-08-11 02:59:47,653 - INFO - ❌ Path not found for V15_Enhanced
2025-08-11 02:59:47,653 - INFO - ❌ Path not found for V14_Baseline
2025-08-11 02:59:47,653 - INFO - 
Using 2 models for ensemble
2025-08-11 02:59:47,653 - INFO -   V17_Stable: normalized weight=0.455
2025-08-11 02:59:47,653 - INFO -   V18_Fixed: normalized weight=0.545
2025-08-11 02:59:47,653 - INFO - 
📊 Processing V17_Stable with TTA...
2025-08-11 02:59:48,067 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-11 02:59:48,068 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-11 03:00:14,764 - INFO -   V17_Stable with TTA:
2025-08-11 03:00:14,764 - INFO -     Accuracy: 0.8702
2025-08-11 03:00:14,764 - INFO -     Macro F1: 0.8051
2025-08-11 03:00:14,764 - INFO -     Kappa: 0.8240
2025-08-11 03:00:14,765 - INFO - 
📊 Processing V18_Fixed with TTA...
2025-08-11 03:00:15,206 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9706 个序列
2025-08-11 03:00:15,206 - INFO - 创建序列数据集: 9706个序列, 序列长度=6, 通道数=3, 总epochs=9746
2025-08-11 03:00:43,374 - INFO -   V18_Fixed with TTA:
2025-08-11 03:00:43,374 - INFO -     Accuracy: 0.8393
2025-08-11 03:00:43,374 - INFO -     Macro F1: 0.7925
2025-08-11 03:00:43,374 - INFO -     Kappa: 0.7873
2025-08-11 03:00:43,374 - INFO - 
================================================================================
2025-08-11 03:00:43,374 - INFO - 🎯 ENSEMBLE RESULTS WITH POST-PROCESSING
2025-08-11 03:00:43,375 - INFO - ================================================================================
2025-08-11 03:00:43,375 - INFO - 
📊 Applying temporal post-processing...
