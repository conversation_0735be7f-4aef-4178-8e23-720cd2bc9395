WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.
WARNING:root:加载文件 /media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz 失败: 文件不存在
WARNING:root:HMM not available. Using fallback smoothing.

Epoch 1 - Train:   0%|          | 0/868 [00:00<?, ?it/s]
Epoch 1 - Train:   0%|          | 0/868 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_breakthrough.py", line 540, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_breakthrough.py", line 487, in main
    test_metrics, test_hmm_metrics = train_v13(config, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_breakthrough.py", line 379, in train_v13
    train_loss, train_metrics = train_epoch_v13(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_breakthrough.py", line 171, in train_epoch_v13
    main_output, aux_output = model(eeg_data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_breakthrough.py", line 139, in forward
    main_out, aux_out = self.backbone(x)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/models/sequential_mambaformer_v2.py", line 172, in forward
    batch_size, seq_len, time_steps, channels = x.shape
ValueError: not enough values to unpack (expected 4, got 3)
