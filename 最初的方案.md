方法概述

  本方法名为"CrossModal MAMBAFORMER with Calibrated Uncertainty-Guided Progressive
  Classification"，是一个专门针对多模态睡眠分期任务的端到端深度学习框架。该方法通过三个层次的创新实现了高精度、高效率和高可靠性的睡眠阶段自动识别。

  核心创新点

  2. 架构创新：提出跨模态MAMBAFORMER统一架构，将多模态融合直接嵌入到Mamba-Transformer交错结构中
  3. 决策创新：基于经过温度缩放校准的不确定性估计，实现渐进式分类策略，并使用Focal Loss解决类别不平衡

  技术细节


  2. 特征提取层：跨模态MAMBAFORMER架构

  整体设计理念：
  将多模态融合直接嵌入到MAMBAFORMER的每一层中，实现深度的模态交互，而不是在特征提取后再进行后期融合。

  2.1 Mamba预处理块：
  - 功能：替代传统Transformer的显式位置编码
  - 原理：Mamba的选择性状态空间模型（SSM）通过其递归结构自然地编码序列的位置信息
  - 优势：
    - 线性时间复杂度O(L)，能处理长序列
    - 隐状态包含了丰富的上下文信息
    - 对睡眠信号的长程依赖建模特别有效

  2.2 跨模态MAMBAFORMER核心层（核心创新）：

  每层包含三个紧密集成的模块：

  A. 跨模态注意力模块：

  EEG轻量级自查询：
  - 设计：Query、Key、Value都来自EEG特征
  - 实现：局部注意力机制，窗口大小32个时间步（约3.2秒@10Hz下采样）
  - 目的：捕获EEG内部的关键模式
    - 睡眠纺锤波（1-2秒持续时间）
    - K复合波（0.5秒持续时间）
    - 慢波振荡（0.5-2秒周期）
  - 计算优化：局部窗口将复杂度从O(L²)降至O(L×W)

  EEG跨模态查询：
  - EEG→EOG注意力：
    - Query来自EEG，Key/Value来自EOG
    - 目的：EEG主动查询眼动信息
    - 重要性：REM期的快速眼动是关键特征
  - EEG→EMG注意力：
    - Query来自EEG，Key/Value来自EMG
    - 目的：EEG主动查询肌张力信息
    - 重要性：区分觉醒（高肌张力）和REM（极低肌张力）

  自适应门控融合：
  - 输入：三个模态的原始特征拼接
  - 门控网络：2层MLP，输出3个门控权重
  - 融合公式：Output = g₁×EEG_self + g₂×EEG_EOG + g₃×EEG_EMG
  - 实现了上下文相关的动态模态重要性调整

  B. Mamba时序建模模块：
  - 接收融合后的多模态特征
  - 选择性扫描机制：自动识别重要的时间点
  - 状态空间参数：d_state=16, d_conv=4
  - 特别适合建模睡眠阶段的缓慢转换过程

  C. 前馈网络模块：
  - 标准的2层MLP：d_model → 4×d_model → d_model
  - 激活函数：GELU
  - Dropout：0.1
  - 残差连接和层归一化

  2.3 层堆叠策略：
  - 总共6层跨模态MAMBAFORMER层
  - 渐进式抽象：底层关注局部特征，高层关注全局模式
  - 每层都进行完整的多模态交互

  3. 决策优化层：校准的渐进式分类

  3.1 渐进式分类策略：

  粗分类阶段：
  - 目标：3类分类 - Wake(W), NREM(N1+N2+N3), REM
  - 网络：轻量级分类头（2层MLP）
  - 设计理由：
    - 大类之间的生理特征差异明显
    - W：高频活动+高肌张力
    - NREM：慢波主导+中等肌张力
    - REM：混合频率+极低肌张力+快速眼动
  - 准确率通常>95%

  不确定性估计：
  - 方法：Monte Carlo Dropout（10次前向传播）
  - 保持训练模式的Dropout（p=0.1）
  - 不确定性度量：预测概率的方差
  - 温度缩放校准：
    - 原始logits除以温度参数T
    - T在验证集上通过最小化期望校准误差（ECE）确定
    - 确保模型的置信度与实际准确率匹配
    - 典型T值范围：1.2-2.0

  细分类阶段：
  - 触发条件：不确定性 > 阈值（阈值作为超参数）
  - NREM细分类器：区分N1、N2、N3
  - Wake/REM二分类器：精确区分觉醒和REM
  - 网络：更深的分类头（3层MLP）

  3.2 训练策略：

  损失函数设计：
  - 粗分类损失：标准交叉熵
  - 细分类损失：Focal Loss
    - FL(p_t) = -α(1-p_t)^γ log(p_t)
    - α=0.25（平衡因子）
    - γ=2.0（聚焦参数）
    - 专门解决N1（约5-10%）和REM（约20%）的类别不平衡
  - 总损失：L_total = λ₁×L_coarse + λ₂×L_fine
    - λ₁=1.0, λ₂=2.0（细分类更重要）

  阈值优化：
  - 不作为可学习参数（避免训练不稳定）
  - 在验证集上网格搜索
  - 优化目标：平衡Macro F1-score和推理效率
  - 典型值：0.3-0.5

  4. 端到端工作流程

  训练时数据流：
  原始PSG信号 → wICA+ICLabel预处理 → 清洁信号
  → 跨模态MAMBAFORMER×6层 → 特征表示
  → 粗分类器 + 细分类器（并行）
  → 两个损失函数联合优化

  推理时数据流：
  原始PSG信号 → wICA+ICLabel预处理 → 清洁信号
  → 跨模态MAMBAFORMER×6层 → 特征表示
  → 粗分类器 → 不确定性估计
  → [低不确定性] → 直接输出粗分类映射
  → [高不确定性] → 细分类器 → 最终5类输出


数据处理方面先不用管，直接用处理好的数据集