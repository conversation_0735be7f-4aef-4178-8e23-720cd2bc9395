{"V8": {"version": "V8", "epochs": [{"accuracy": 0.0756, "macro_f1": 0.047, "epoch": 1}, {"accuracy": 0.6836, "macro_f1": 0.5474, "kappa": 0.5324, "epoch": 2}, {"accuracy": 0.7241, "macro_f1": 0.5983, "kappa": 0.5909, "epoch": 3}, {"accuracy": 0.7854, "macro_f1": 0.6844, "kappa": 0.6951, "epoch": 4}, {"accuracy": 0.7767, "macro_f1": 0.6889, "kappa": 0.6799, "epoch": 5}, {"accuracy": 0.8033, "macro_f1": 0.7218, "kappa": 0.7248, "epoch": 6}, {"accuracy": 0.7765, "macro_f1": 0.6809, "kappa": 0.6797, "epoch": 7}, {"accuracy": 0.801, "macro_f1": 0.7364, "kappa": 0.7201, "epoch": 8}, {"accuracy": 0.8125, "macro_f1": 0.7415, "kappa": 0.7383, "epoch": 9}, {"accuracy": 0.814, "macro_f1": 0.7321, "kappa": 0.7389, "epoch": 10}, {"accuracy": 0.8104, "macro_f1": 0.7284, "kappa": 0.7385, "epoch": 11}, {"accuracy": 0.8118, "macro_f1": 0.7423, "kappa": 0.7381, "epoch": 12}, {"accuracy": 0.8351, "macro_f1": 0.7526, "kappa": 0.7722, "epoch": 13}, {"accuracy": 0.8272, "macro_f1": 0.7515, "kappa": 0.7623, "epoch": 14}, {"accuracy": 0.8278, "macro_f1": 0.7633, "kappa": 0.7651, "epoch": 15}, {"accuracy": 0.8393, "macro_f1": 0.7711, "kappa": 0.7784, "epoch": 16}, {"accuracy": 0.8055, "macro_f1": 0.7249, "kappa": 0.7342, "epoch": 17}, {"accuracy": 0.8238, "macro_f1": 0.755, "kappa": 0.7599, "epoch": 18}, {"accuracy": 0.831, "macro_f1": 0.768, "kappa": 0.7692, "epoch": 19}, {"accuracy": 0.8234, "macro_f1": 0.7472, "kappa": 0.754, "epoch": 20}, {"accuracy": 0.8182, "macro_f1": 0.7578, "kappa": 0.7476, "epoch": 21}, {"accuracy": 0.8055, "macro_f1": 0.7533, "kappa": 0.7292, "epoch": 22}, {"accuracy": 0.8315, "macro_f1": 0.7705, "kappa": 0.7679, "epoch": 23}, {"accuracy": 0.8353, "macro_f1": 0.7786, "kappa": 0.7711, "epoch": 24}, {"accuracy": 0.8246, "macro_f1": 0.7539, "kappa": 0.7552, "epoch": 25}, {"accuracy": 0.8172, "macro_f1": 0.7411, "kappa": 0.7499, "epoch": 26}, {"accuracy": 0.8118, "macro_f1": 0.7551, "kappa": 0.7394, "epoch": 27}, {"accuracy": 0.8395, "macro_f1": 0.765, "kappa": 0.7792, "epoch": 28}, {"accuracy": 0.8276, "macro_f1": 0.749, "kappa": 0.7633, "epoch": 29}, {"accuracy": 0.8302, "macro_f1": 0.7654, "kappa": 0.766, "epoch": 30}, {"accuracy": 0.8195, "macro_f1": 0.7295, "kappa": 0.7529, "epoch": 31}, {"accuracy": 0.8342, "macro_f1": 0.7705, "kappa": 0.7725, "epoch": 32}, {"accuracy": 0.8216, "macro_f1": 0.7355, "kappa": 0.7555, "epoch": 33}, {"accuracy": 0.8329, "macro_f1": 0.7578, "kappa": 0.7711, "epoch": 34}, {"accuracy": 0.8266, "macro_f1": 0.7547, "kappa": 0.7624, "epoch": 35}, {"accuracy": 0.8332, "macro_f1": 0.767, "kappa": 0.7718, "epoch": 36}], "current_epoch": 36, "best_metrics": {"macro_f1": 0.7786, "kappa": 0.7711}, "status": "early_stopped", "architecture": "Sequential MAMBAFORMER", "features": [], "total_epochs": 60}, "V9": {"version": "V9", "epochs": [{"accuracy": 0.1426, "macro_f1": 0.0602, "kappa": 0.0072, "epoch": 1}], "current_epoch": 1, "best_metrics": {"macro_f1": 0.0602, "kappa": 0.0072}, "status": "unknown", "architecture": "MultiModal MAMBAFORMER", "features": [], "total_epochs": 50}, "V10": {"version": "V10", "epochs": [{"accuracy": 0.4144, "macro_f1": 0.1172, "kappa": 0.0, "epoch": 1}, {"accuracy": 0.6301, "macro_f1": 0.489, "kappa": 0.4291, "epoch": 2}, {"accuracy": 0.716, "macro_f1": 0.5929, "kappa": 0.5867, "epoch": 3}], "current_epoch": 3, "best_metrics": {"macro_f1": 0.5929, "kappa": 0.5867}, "status": "unknown", "architecture": "MultiModal MAMBAFORMER", "features": [], "total_epochs": 60}}