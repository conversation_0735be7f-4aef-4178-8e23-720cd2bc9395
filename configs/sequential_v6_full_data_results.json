{"config": {"batch_size": 32, "seq_len": 5, "learning_rate": 2e-05, "weight_decay": 0.0001, "num_epochs": 50, "patience": 8, "max_samples_per_file": null, "d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.15, "temp_loss_weight": 0.1, "use_class_weights": true, "class_weights": [1.0, 1.3, 1.0, 1.3, 1.5]}, "result": {"test_acc": 0.12513897467572577, "test_f1": 0.07121315159531474, "test_kappa": -0.04186238755825644, "test_macro_f1": 0.07121315159531474, "rem_f1": 0.0656598287837183, "val_f1": 0.17080931263858093, "val_kappa": 0.07504360616466743, "val_test_gap": 0.09959616104326618, "confusion_matrix": [[0, 0, 140, 3420, 8950], [0, 0, 28, 1793, 534], [0, 0, 316, 13773, 4171], [0, 1, 199, 4949, 2231], [0, 3, 77, 7172, 813]], "classification_report": {"Wake": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 12510.0}, "N1": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 2355.0}, "N2": {"precision": 0.41578947368421054, "recall": 0.017305585980284775, "f1-score": 0.03322818086225026, "support": 18260.0}, "N3": {"precision": 0.15909602340309256, "recall": 0.6705962059620596, "f1-score": 0.25717774833060514, "support": 7380.0}, "REM": {"precision": 0.048685550032936106, "recall": 0.10080595164290143, "f1-score": 0.0656598287837183, "support": 8065.0}, "accuracy": 0.12513897467572577, "macro avg": {"precision": 0.12471420942404783, "recall": 0.15774154871704915, "f1-score": 0.07121315159531474, "support": 48570.0}, "weighted avg": {"precision": 0.18857511639292027, "recall": 0.12513897467572577, "f1-score": 0.06247199679566078, "support": 48570.0}}}, "log_file": "../logs/sequential_v6_full_data_20250809_204306.log", "data_split": "../../configs/subject_aware_splits.json"}