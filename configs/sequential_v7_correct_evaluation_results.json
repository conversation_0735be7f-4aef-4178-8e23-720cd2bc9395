{
  "method": "epoch_level_evaluation",
  "model": "sequential_v7_balanced",
  "config": {
    "batch_size": 32,
    "seq_len": 5,
    "learning_rate": 2e-05,
    "weight_decay": 0.0001,
    "num_epochs": 50,
    "patience": 10,
    "max_samples_per_file": null,
    "d_model": 128,
    "n_heads": 8,
    "n_layers": 4,
    "dropout": 0.15,
    "temp_loss_weight": 0.1,
    "focal_gamma": 1.5
  },
  "correct_metrics": {
    "accuracy": 0.8563513236199466,
    "macro_f1": 0.7890224499145189,
    "kappa": 0.8051258622363645,
    "confusion_matrix": [
      [
        2149,
        98,
        74,
        10,
        203
      ],
      [
        23,
        191,
        138,
        4,
        115
      ],
      [
        1,
        39,
        3284,
        131,
        197
      ],
      [
        0,
        0,
        218,
        1257,
        1
      ],
      [
        1,
        19,
        128,
        0,
        1465
      ]
    ],
    "per_class_metrics": {
      "Wake": {
        "precision": 0.9885004599816007,
        "recall": 0.8480662983425414,
        "f1": 0.9129141886151233,
        "support": 2534.0
      },
      "N1": {
        "precision": 0.5504322766570605,
        "recall": 0.40552016985138006,
        "f1": 0.4669926650366748,
        "support": 471.0
      },
      "N2": {
        "precision": 0.8547631441957314,
        "recall": 0.8992332968236583,
        "f1": 0.8764344809180677,
        "support": 3652.0
      },
      "N3": {
        "precision": 0.8965763195435092,
        "recall": 0.8516260162601627,
        "f1": 0.8735232800555941,
        "support": 1476.0
      },
      "REM": {
        "precision": 0.7395254921756689,
        "recall": 0.9082455052696838,
        "f1": 0.8152476349471341,
        "support": 1613.0
      }
    },
    "total_epochs": 9746,
    "avg_predictions_per_epoch": 4.983583008413708
  },
  "comparison_with_original": {
    "original_total_predictions": 