{"config": {"batch_size": 32, "seq_len": 5, "learning_rate": 2e-05, "weight_decay": 0.0001, "num_epochs": 50, "patience": 8, "max_samples_per_file": 150, "d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.15, "temp_loss_weight": 0.1}, "result": {"test_acc": 0.8606164383561644, "test_f1": 0.6468997360178604, "test_kappa": 0.7986777534165843, "test_macro_f1": 0.6468997360178604, "val_f1": 0.7839016922166353, "val_kappa": 0.7940459172024449, "val_test_gap": 0.13700195619877487, "confusion_matrix": [[2293, 119, 144, 9, 0], [21, 284, 150, 0, 0], [5, 92, 1363, 96, 0], [0, 0, 148, 1086, 0], [0, 5, 25, 0, 0]], "classification_report": {"Wake": {"precision": 0.9887882708063821, "recall": 0.8939571150097466, "f1-score": 0.938984438984439, "support": 2565.0}, "N1": {"precision": 0.568, "recall": 0.6241758241758242, "f1-score": 0.5947643979057592, "support": 455.0}, "N2": {"precision": 0.7448087431693989, "recall": 0.8759640102827764, "f1-score": 0.8050797401063201, "support": 1556.0}, "N3": {"precision": 0.9118387909319899, "recall": 0.880064829821718, "f1-score": 0.8956701030927835, "support": 1234.0}, "REM": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 30.0}, "accuracy": 0.8606164383561644, "macro avg": {"precision": 0.6426871609815542, "recall": 0.6548323558580129, "f1-score": 0.6468997360178604, "support": 5840.0}, "weighted avg": {"precision": 0.8696598265411011, "recall": 0.8606164383561644, "f1-score": 0.8625126489493382, "support": 5840.0}}}, "log_file": "../logs/sequential_v5_correct_split_20250809_202418.log", "data_split": "../../configs/subject_aware_splits.json"}