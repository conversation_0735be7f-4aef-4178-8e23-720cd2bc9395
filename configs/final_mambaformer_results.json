{"config": {"batch_size": 16, "seq_len": 5, "learning_rate": 5e-06, "weight_decay": 0.001, "l2_weight": 5e-07, "num_epochs": 60, "patience": 12, "max_samples_per_file": 250, "d_model": 64, "n_heads": 4, "n_layers": 2, "dropout": 0.4, "temp_loss_weight": 0.02}, "fold_results": [{"fold_id": 0, "test_acc": 0.44331892114473953, "test_f1": 0.4516312761224105, "position_acc": [0.4428659666460778, 0.44471896232242125, 0.44718962322421246, 0.4408070825612518, 0.4410129709697344], "val_f1": 0.4675462356379049, "val_test_gap": 0.015914959515494376, "confusion_matrix": [[3333, 7970, 33, 1142, 32], [1, 1874, 94, 76, 310], [0, 6307, 4548, 4876, 2529], [1, 35, 16, 7324, 4], [0, 3104, 124, 384, 4453]], "classification_report": {"Wake": {"precision": 0.999400299850075, "recall": 0.2664268585131894, "f1-score": 0.42070053644682864, "support": 12510.0}, "N1": {"precision": 0.09714878175220322, "recall": 0.7957537154989385, "f1-score": 0.17315777315777317, "support": 2355.0}, "N2": {"precision": 0.9445482866043614, "recall": 0.24906900328587075, "f1-score": 0.39419284940411703, "support": 18260.0}, "N3": {"precision": 0.5306477322127228, "recall": 0.9924119241192412, "f1-score": 0.6915305448021906, "support": 7380.0}, "REM": {"precision": 0.6076692139737991, "recall": 0.5521388716676999, "f1-score": 0.5785746768011434, "support": 8065.0}, "accuracy": 0.44331892114473953, "macro avg": {"precision": 0.6358828628786324, "recall": 0.5711600746169879, "f1-score": 0.4516312761224105, "support": 48570.0}, "weighted avg": {"precision": 0.7987598789371033, "recall": 0.44331892114473953, "f1-score": 0.4660986593760953, "support": 48570.0}}}], "summary": {"mean_accuracy": 0.44331892114473953, "mean_f1_score": 0.4516312761224105, "mean_val_test_gap": 0.015914959515494376, "log_file": "../logs/final_mambaformer_20250809_140934.log"}}