{"config": {"batch_size": 32, "seq_len": 5, "learning_rate": 2e-05, "weight_decay": 0.0001, "num_epochs": 50, "patience": 8, "max_samples_per_file": 150, "d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.15, "temp_loss_weight": 0.1}, "fold_results": [{"fold_id": 0, "test_acc": 0.7221330039118797, "test_f1": 0.5589233655921271, "position_acc": [0.7143298332303891, 0.7235948116121063, 0.7263743051266214, 0.725550751492691, 0.7208153180975911], "val_f1": 0.6639698620756678, "val_test_gap": 0.1050464964835407, "confusion_matrix": [[11381, 556, 519, 54, 0], [386, 867, 1081, 21, 0], [170, 318, 16737, 1035, 0], [11, 0, 1280, 6089, 0], [890, 1816, 5199, 160, 0]], "classification_report": {"Wake": {"precision": 0.8865088019940801, "recall": 0.9097521982414069, "f1-score": 0.897980116774499, "support": 12510.0}, "N1": {"precision": 0.24374472870396402, "recall": 0.3681528662420382, "f1-score": 0.29330175913396483, "support": 2355.0}, "N2": {"precision": 0.6744439071566731, "recall": 0.9165936473165389, "f1-score": 0.7770916519639707, "support": 18260.0}, "N3": {"precision": 0.8274222041038185, "recall": 0.8250677506775068, "f1-score": 0.8262433000882013, "support": 7380.0}, "REM": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 8065.0}, "accuracy": 0.7221330039118797, "macro avg": {"precision": 0.5264239283917072, "recall": 0.6039132924954982, "f1-score": 0.5589233655921271, "support": 48570.0}, "weighted avg": {"precision": 0.6194351566813014, "recall": 0.7221330039118797, "f1-score": 0.6632041594219169, "support": 48570.0}}}], "summary": {"mean_accuracy": 0.7221330039118797, "mean_f1_score": 0.5589233655921271, "mean_val_test_gap": 0.1050464964835407, "log_file": "../logs/sequential_v3_20250809_150451.log"}}