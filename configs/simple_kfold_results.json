{"config": {"batch_size": 64, "learning_rate": 0.0001, "num_epochs": 20, "d_model": 128, "nhead": 8, "num_layers": 4, "dropout": 0.2, "k_folds": 5, "device": "cuda"}, "fold_results": [{"fold": 1, "test_acc": 0.601593625498008, "test_f1": 0.5096912945747228, "val_acc": 0.6039823008849557, "confusion_matrix": [[40, 36, 21, 14, 6], [15, 89, 58, 9, 36], [6, 18, 162, 80, 25], [1, 1, 30, 294, 0], [1, 14, 28, 1, 19]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.6349    0.3419    0.4444       117\n          N1     0.5633    0.4300    0.4877       207\n          N2     0.5418    0.5567    0.5492       291\n          N3     0.7387    0.9018    0.8122       326\n         REM     0.2209    0.3016    0.2550        63\n\n    accuracy                         0.6016      1004\n   macro avg     0.5399    0.5064    0.5097      1004\nweighted avg     0.6009    0.6016    0.5912      1004\n"}, {"fold": 2, "test_acc": 0.5882352941176471, "test_f1": 0.5021528247392402, "val_acc": 0.6623376623376623, "confusion_matrix": [[29, 15, 13, 10, 2], [16, 56, 28, 0, 5], [18, 33, 111, 58, 17], [1, 1, 45, 220, 0], [1, 20, 16, 2, 14]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.4462    0.4203    0.4328        69\n          N1     0.4480    0.5333    0.4870       105\n          N2     0.5211    0.4684    0.4933       237\n          N3     0.7586    0.8240    0.7899       267\n         REM     0.3684    0.2642    0.3077        53\n\n    accuracy                         0.5882       731\n   macro avg     0.5085    0.5020    0.5022       731\nweighted avg     0.5792    0.5882    0.5816       731\n"}, {"fold": 3, "test_acc": 0.584061135371179, "test_f1": 0.5011256585012289, "val_acc": 0.6252676659528907, "confusion_matrix": [[30, 14, 5, 5, 2], [21, 52, 22, 5, 11], [11, 51, 193, 46, 27], [12, 13, 83, 237, 6], [3, 20, 23, 1, 23]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.3896    0.5357    0.4511        56\n          N1     0.3467    0.4685    0.3985       111\n          N2     0.5920    0.5884    0.5902       328\n          N3     0.8061    0.6752    0.7349       351\n         REM     0.3333    0.3286    0.3309        70\n\n    accuracy                         0.5841       916\n   macro avg     0.4936    0.5193    0.5011       916\nweighted avg     0.6122    0.5841    0.5941       916\n"}, {"fold": 4, "test_acc": 0.5438829787234043, "test_f1": 0.5158790681685661, "val_acc": 0.5994550408719346, "confusion_matrix": [[38, 21, 10, 1, 9], [33, 106, 36, 1, 14], [6, 53, 113, 20, 17], [3, 1, 34, 128, 1], [5, 54, 24, 0, 24]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.4471    0.4810    0.4634        79\n          N1     0.4511    0.5579    0.4988       190\n          N2     0.5207    0.5407    0.5305       209\n          N3     0.8533    0.7665    0.8076       167\n         REM     0.3692    0.2243    0.2791       107\n\n    accuracy                         0.5439       752\n   macro avg     0.5283    0.5141    0.5159       752\nweighted avg     0.5477    0.5439    0.5412       752\n"}, {"fold": 5, "test_acc": 0.5970695970695971, "test_f1": 0.5463756815409124, "val_acc": 0.6076294277929155, "confusion_matrix": [[38, 23, 3, 0, 5], [6, 70, 11, 1, 31], [2, 36, 83, 25, 35], [1, 2, 19, 126, 0], [2, 7, 10, 1, 9]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.7755    0.5507    0.6441        69\n          N1     0.5072    0.5882    0.5447       119\n          N2     0.6587    0.4586    0.5407       181\n          N3     0.8235    0.8514    0.8372       148\n         REM     0.1125    0.3103    0.1651        29\n\n    accuracy                         0.5971       546\n   macro avg     0.5755    0.5518    0.5464       546\nweighted avg     0.6561    0.5971    0.6151       546\n"}], "summary": {"mean_test_acc": 0.5829685261559671, "std_test_acc": 0.020505351419444505, "mean_test_f1": 0.5150449055049341, "std_test_f1": 0.016558536698497484}}