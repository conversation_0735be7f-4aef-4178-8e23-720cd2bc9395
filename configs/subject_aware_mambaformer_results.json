{"config": {"batch_size": 32, "learning_rate": 5e-05, "weight_decay": 0.0001, "num_epochs": 30, "max_samples_per_file": 150, "use_channels": 3, "patience": 8}, "fold_results": [{"fold_id": 0, "test_acc": 0.8215678226964909, "test_f1": 0.751962075100561, "val_f1": 0.821425312358436, "confusion_matrix": [[2126, 111, 77, 14, 206], [57, 192, 85, 4, 133], [39, 99, 3029, 324, 161], [0, 2, 125, 1349, 0], [34, 54, 198, 16, 1311]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.9424    0.8390    0.8877      2534\n          N1     0.4192    0.4076    0.4133       471\n          N2     0.8620    0.8294    0.8454      3652\n          N3     0.7903    0.9140    0.8476      1476\n         REM     0.7239    0.8128    0.7658      1613\n\n    accuracy                         0.8216      9746\n   macro avg     0.7476    0.7606    0.7520      9746\nweighted avg     0.8278    0.8216    0.8227      9746\n", "model_params": 1293223, "train_subjects": ["02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "16", "18", "19"], "test_subjects": ["00", "17", "15", "01"]}, {"fold_id": 1, "test_acc": 0.788414079750617, "test_f1": 0.7131576239925195, "val_f1": 0.7971463442443858, "confusion_matrix": [[1248, 81, 36, 6, 54], [63, 130, 91, 7, 111], [265, 86, 2547, 258, 51], [16, 0, 125, 1167, 1], [100, 95, 172, 11, 978]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.7376    0.8758    0.8008      1425\n          N1     0.3316    0.3234    0.3275       402\n          N2     0.8573    0.7942    0.8245      3207\n          N3     0.8054    0.8915    0.8463      1309\n         REM     0.8184    0.7212    0.7668      1356\n\n    accuracy                         0.7884      7699\n   macro avg     0.7101    0.7212    0.7132      7699\nweighted avg     0.7920    0.7884    0.7877      7699\n", "model_params": 1293223, "train_subjects": ["00", "01", "02", "04", "06", "07", "09", "10", "12", "13", "14", "15", "16", "17", "18", "19"], "test_subjects": ["08", "05", "11", "03"]}, {"fold_id": 2, "test_acc": 0.8051600169180883, "test_f1": 0.7621779956054369, "val_f1": 0.8197580460127594, "confusion_matrix": [[989, 70, 6, 7, 16], [92, 284, 145, 0, 91], [27, 162, 2483, 229, 128], [2, 3, 90, 1061, 5], [10, 140, 158, 1, 894]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.8830    0.9090    0.8958      1088\n          N1     0.4310    0.4641    0.4469       612\n          N2     0.8616    0.8197    0.8401      3029\n          N3     0.8174    0.9139    0.8630      1161\n         REM     0.7884    0.7431    0.7651      1203\n\n    accuracy                         0.8052      7093\n   macro avg     0.7563    0.7700    0.7622      7093\nweighted avg     0.8081    0.8052    0.8058      7093\n", "model_params": 1293223, "train_subjects": ["00", "01", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "14", "15", "17", "19"], "test_subjects": ["18", "16", "13", "02"]}, {"fold_id": 3, "test_acc": 0.8198738170347003, "test_f1": 0.7577340337081913, "val_f1": 0.8412652472959156, "confusion_matrix": [[1357, 207, 62, 24, 55], [63, 286, 229, 1, 183], [6, 86, 3767, 140, 225], [0, 0, 95, 686, 0], [23, 71, 240, 3, 1701]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.9365    0.7959    0.8605      1705\n          N1     0.4400    0.3753    0.4051       762\n          N2     0.8575    0.8918    0.8743      4224\n          N3     0.8033    0.8784    0.8391       781\n         REM     0.7860    0.8346    0.8096      2038\n\n    accuracy                         0.8199      9510\n   macro avg     0.7647    0.7552    0.7577      9510\nweighted avg     0.8184    0.8199    0.8175      9510\n", "model_params": 1293223, "train_subjects": ["00", "01", "02", "03", "05", "06", "07", "08", "10", "11", "13", "14", "15", "16", "17", "18"], "test_subjects": ["09", "19", "04", "12"]}, {"fold_id": 4, "test_acc": 0.8526634382566586, "test_f1": 0.8028851270135124, "val_f1": 0.7699608461387885, "confusion_matrix": [[1357, 76, 30, 1, 69], [64, 292, 107, 1, 93], [35, 205, 3218, 89, 140], [1, 0, 107, 868, 0], [10, 66, 123, 0, 1308]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake     0.9250    0.8852    0.9047      1533\n          N1     0.4570    0.5242    0.4883       557\n          N2     0.8976    0.8728    0.8850      3687\n          N3     0.9051    0.8893    0.8972       976\n         REM     0.8124    0.8679    0.8393      1507\n\n    accuracy                         0.8527      8260\n   macro avg     0.7994    0.8079    0.8029      8260\nweighted avg     0.8583    0.8527    0.8550      8260\n", "model_params": 1293223, "train_subjects": ["00", "01", "02", "03", "04", "05", "08", "09", "11", "12", "13", "15", "16", "17", "18", "19"], "test_subjects": ["07", "10", "14", "06"]}], "subject_fold_map": {"00": 0, "17": 0, "15": 0, "01": 0, "08": 1, "05": 1, "11": 1, "03": 1, "18": 2, "16": 2, "13": 2, "02": 2, "09": 3, "19": 3, "04": 3, "12": 3, "07": 4, "10": 4, "14": 4, "06": 4}, "summary_statistics": {"mean_accuracy": 0.8175358349313109, "std_accuracy": 0.021245748268008773, "mean_f1_score": 0.7575833710840442, "std_f1_score": 0.02856091153022274, "num_folds_completed": 5, "total_subjects": 20, "architecture": "Subject-Aware-Simplified-MAMBAFORMER"}}