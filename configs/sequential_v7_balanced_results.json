{"config": {"batch_size": 32, "seq_len": 5, "learning_rate": 2e-05, "weight_decay": 0.0001, "num_epochs": 50, "patience": 10, "max_samples_per_file": null, "d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.15, "temp_loss_weight": 0.1, "focal_gamma": 1.5}, "result": {"test_acc": 0.8458513485690755, "test_f1": 0.7787515953301797, "test_kappa": 0.7910791439618505, "test_macro_f1": 0.7787515953301797, "rem_f1": 0.8011101859561477, "val_f1": 0.7454077090722798, "val_kappa": 0.7399200011549997, "val_test_gap": -0.03334388625789986, "confusion_matrix": [[10414, 548, 384, 46, 1118], [141, 956, 695, 16, 547], [25, 226, 16234, 714, 1061], [1, 1, 1107, 6263, 8], [4, 126, 719, 0, 7216]], "classification_report": {"Wake": {"precision": 0.9838450637694851, "recall": 0.8324540367705835, "f1-score": 0.9018402251569604, "support": 12510.0}, "N1": {"precision": 0.5148088314485729, "recall": 0.4059447983014862, "f1-score": 0.4539411206077873, "support": 2355.0}, "N2": {"precision": 0.848215685250013, "recall": 0.8890470974808324, "f1-score": 0.8681515548544079, "support": 18260.0}, "N3": {"precision": 0.8897570677653076, "recall": 0.8486449864498645, "f1-score": 0.8687148900755947, "support": 7380.0}, "REM": {"precision": 0.7252261306532664, "recall": 0.8947303161810292, "f1-score": 0.8011101859561477, "support": 8065.0}, "accuracy": 0.8458513485690755, "macro avg": {"precision": 0.7923705557773288, "recall": 0.7741642470367592, "f1-score": 0.7787515953301797, "support": 48570.0}, "weighted avg": {"precision": 0.8528731904943269, "recall": 0.8458513485690755, "f1-score": 0.8456983628964508, "support": 48570.0}}}, "log_file": "../logs/sequential_v7_balanced_20250809_205004.log", "data_split": "../../configs/subject_aware_splits.json"}