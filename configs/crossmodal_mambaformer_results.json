{"config": {"batch_size": 8, "seq_len": 5, "learning_rate": 2e-05, "weight_decay": 0.0001, "num_epochs": 50, "patience": 10, "max_samples_per_file": 150, "d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.15, "temp_loss_weight": 0.1}, "fold_results": [{"fold_id": 0, "test_acc": 0.7228536133415688, "test_f1": 0.5766660462902374, "position_acc": [0.7176240477661108, 0.723183034795141, 0.7254478072884497, 0.725241918879967, 0.7227712579781759], "val_f1": 0.806572199330714, "confusion_matrix": [[11246, 794, 401, 69, 0], [253, 1151, 936, 15, 0], [107, 458, 15827, 1868, 0], [20, 3, 472, 6885, 0], [129, 1619, 6218, 99, 0]], "classification_report": "              precision    recall  f1-score   support\n\n        Wake       0.96      0.90      0.93     12510\n          N1       0.29      0.49      0.36      2355\n          N2       0.66      0.87      0.75     18260\n          N3       0.77      0.93      0.84      7380\n         REM       0.00      0.00      0.00      8065\n\n    accuracy                           0.72     48570\n   macro avg       0.54      0.64      0.58     48570\nweighted avg       0.63      0.72      0.67     48570\n"}], "summary": {"mean_accuracy": 0.7228536133415688, "std_accuracy": 0.0, "mean_f1_score": 0.5766660462902374, "std_f1_score": 0.0, "log_file": "../logs/crossmodal_mambaformer_20250809_133214.log"}}