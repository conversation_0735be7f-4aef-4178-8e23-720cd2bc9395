{"config": {"batch_size": 32, "seq_len": 5, "learning_rate": 2e-05, "weight_decay": 0.0001, "num_epochs": 50, "patience": 8, "max_samples_per_file": 150, "d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.15, "temp_loss_weight": 0.1}, "fold_results": [{"fold_id": 0, "test_acc": 0.7337862878319951, "test_f1": 0.5681094852504207, "position_acc": [0.7221535927527281, 0.733065678402306, 0.7391393864525427, 0.741610047354334, 0.7329627341980647], "val_f1": 0.6765404217651205, "val_test_gap": 0.10843093651469982, "confusion_matrix": [[11316, 475, 672, 47, 0], [278, 941, 1118, 18, 0], [102, 269, 16973, 916, 0], [4, 0, 966, 6410, 0], [1189, 2827, 4049, 0, 0]], "classification_report": {"Wake": {"precision": 0.8779579486383738, "recall": 0.9045563549160671, "f1-score": 0.8910587030985472, "support": 12510.0}, "N1": {"precision": 0.2085549645390071, "recall": 0.39957537154989387, "f1-score": 0.27406436580748506, "support": 2355.0}, "N2": {"precision": 0.713811085877702, "recall": 0.9295180722891566, "f1-score": 0.8075074932204196, "support": 18260.0}, "N3": {"precision": 0.8672710052766879, "recall": 0.8685636856368564, "f1-score": 0.8679168641256516, "support": 7380.0}, "REM": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 8065.0}, "accuracy": 0.7337862878319951, "macro avg": {"precision": 0.5335190008663542, "recall": 0.6204426968783949, "f1-score": 0.5681094852504207, "support": 48570.0}, "weighted avg": {"precision": 0.6363815385222198, "recall": 0.7337862878319951, "f1-score": 0.6782556977700561, "support": 48570.0}}}], "summary": {"mean_accuracy": 0.7337862878319951, "mean_f1_score": 0.5681094852504207, "mean_val_test_gap": 0.10843093651469982, "log_file": "../logs/sequential_v2_20250809_160523.log"}}