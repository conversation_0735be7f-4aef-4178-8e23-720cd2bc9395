{"config": {"d_model": 256, "n_heads": 16, "n_layers": 6, "dropout": 0.15, "seq_len": 5, "batch_size": 32, "num_epochs": 100, "learning_rate": 0.0002, "weight_decay": 0.0001, "patience": 15, "num_workers": 4, "max_samples_per_file": null}, "best_val_metrics": {"accuracy": 0.8188791817600682, "macro_f1": 0.7664401427204993, "kappa": 0.7590947826647285, "confusion_matrix": [[680, 123, 25, 8, 13], [77, 165, 38, 1, 89], [21, 138, 1573, 102, 111], [2, 4, 30, 641, 0], [16, 44, 8, 0, 784]], "per_class_metrics": {"Wake": {"precision": 0.8542713567839196, "recall": 0.800942285041225, "f1": 0.8267477203647416, "support": 849.0}, "N1": {"precision": 0.34810126582278483, "recall": 0.44594594594594594, "f1": 0.39099526066350715, "support": 370.0}, "N2": {"precision": 0.9396654719235364, "recall": 0.8087403598971722, "f1": 0.8693009118541032, "support": 1945.0}, "N3": {"precision": 0.8523936170212766, "recall": 0.946824224519941, "f1": 0.8971308607417774, "support": 677.0}, "REM": {"precision": 0.7863590772316951, "recall": 0.92018779342723, "f1": 0.8480259599783666, "support": 852.0}}, "total_epochs": 4693, "total_predictions_collected": 23385, "avg_predictions_per_epoch": 4.982953334753889}, "test_metrics": {"accuracy": 0.8300841370818798, "macro_f1": 0.7783250712817982, "kappa": 0.7698365431158315, "confusion_matrix": [[1885, 76, 280, 7, 286], [47, 216, 107, 1, 100], [9, 53, 3227, 190, 173], [2, 1, 126, 1347, 0], [7, 18, 171, 2, 1415]], "per_class_metrics": {"Wake": {"precision": 0.9666666666666667, "recall": 0.7438831886345698, "f1": 0.8407671721677074, "support": 2534.0}, "N1": {"precision": 0.5934065934065934, "recall": 0.4585987261146497, "f1": 0.5173652694610779, "support": 471.0}, "N2": {"precision": 0.8251086678598823, "recall": 0.8836254107338445, "f1": 0.8533650667724447, "support": 3652.0}, "N3": {"precision": 0.8707175177763413, "recall": 0.9126016260162602, "f1": 0.8911677141912008, "support": 1476.0}, "REM": {"precision": 0.7168186423505573, "recall": 0.8772473651580905, "f1": 0.7889601338165598, "support": 1613.0}}, "total_epochs": 9746, "total_predictions_collected": 48570, "avg_predictions_per_epoch": 4.983583008413708}, "data_info": {"train_epochs": 27869, "val_epochs": 4693, "test_epochs": 9746}}