{"timestamp": "20250810_205329", "model": "V14_FIXED", "config": {"d_model": 256, "n_heads": 16, "n_layers": 6, "dropout": 0.15, "seq_len": 5, "batch_size": 32, "num_workers": 4}, "group_results": [{"group": 1, "subjects": ["00", "01", "15", "17"], "n_files": 8, "n_epochs": 9746, "accuracy": 0.8635337574389493, "macro_f1": 0.8078403271059799, "kappa": 0.8140080232283277, "confusion_matrix": [[2275, 75, 150, 3, 31], [98, 234, 85, 1, 53], [24, 87, 3287, 157, 97], [3, 0, 132, 1341, 0], [9, 32, 293, 0, 1279]], "per_class_metrics": {"Wake": {"precision": 0.9443752594437526, "recall": 0.8977900552486188, "f1": 0.9204936273518106, "support": 2534}, "N1": {"precision": 0.5467289719626168, "recall": 0.4968152866242038, "f1": 0.5205784204671857, "support": 471}, "N2": {"precision": 0.832784393210033, "recall": 0.9000547645125958, "f1": 0.8651138307672062, "support": 3652}, "N3": {"precision": 0.892809587217044, "recall": 0.9085365853658537, "f1": 0.900604432505037, "support": 1476}, "REM": {"precision": 0.876027397260274, "recall": 0.7929324240545568, "f1": 0.8324113244386592, "support": 1613}}}, {"group": 2, "subjects": ["02", "03", "04", "05"], "n_files": 8, "n_epochs": 8250, "accuracy": 0.9036363636363637, "macro_f1": 0.8656093226455054, "kappa": 0.8634149195993629, "confusion_matrix": [[1319, 19, 1, 0, 5], [153, 459, 125, 0, 108], [9, 85, 3582, 84, 39], [2, 0, 94, 702, 0], [2, 8, 61, 0, 1393]], "per_class_metrics": {"Wake": {"precision": 0.8882154882154882, "recall": 0.9813988095238095, "f1": 0.9324849770236833, "support": 1344}, "N1": {"precision": 0.8038528896672504, "recall": 0.5431952662721894, "f1": 0.6483050847457628, "support": 845}, "N2": {"precision": 0.9272586073000258, "recall": 0.9428797051855752, "f1": 0.9350039154267815, "support": 3799}, "N3": {"precision": 0.8931297709923665, "recall": 0.8796992481203008, "f1": 0.8863636363636365, "support": 798}, "REM": {"precision": 0.9016181229773462, "recall": 0.9515027322404371, "f1": 0.9258889996676637, "support": 1464}}}, {"group": 3, "subjects": ["06", "07", "08", "09"], "n_files": 8, "n_epochs": 8533, "accuracy": 0.9035509199578109, "macro_f1": 0.8578259483285449, "kappa": 0.8693495421439998, "confusion_matrix": [[1610, 12, 1, 0, 0], [121, 255, 90, 1, 59], [9, 50, 3071, 84, 69], [0, 0, 143, 1415, 0], [3, 4, 177, 0, 1359]], "per_class_metrics": {"Wake": {"precision": 0.9236947791164659, "recall": 0.9919901417128774, "f1": 0.9566250742721332, "support": 1623}, "N1": {"precision": 0.794392523364486, "recall": 0.4847908745247148, "f1": 0.6021251475796929, "support": 526}, "N2": {"precision": 0.8819643882825962, "recall": 0.9354249162351508, "f1": 0.9079083518107908, "support": 3283}, "N3": {"precision": 0.9433333333333334, "recall": 0.9082156611039794, "f1": 0.9254414650098104, "support": 1558}, "REM": {"precision": 0.9139206455951581, "recall": 0.8807517822423849, "f1": 0.897029702970297, "support": 1543}}}, {"group": 4, "subjects": ["10", "11", "12", "13"], "n_files": 7, "n_epochs": 6983, "accuracy": 0.907489617642847, "macro_f1": 0.8619105039187165, "kappa": 0.8641384492045625, "confusion_matrix": [[1128, 35, 9, 0, 0], [34, 275, 121, 0, 9], [16, 134, 3170, 60, 43], [1, 0, 91, 513, 0], [5, 10, 78, 0, 1251]], "per_class_metrics": {"Wake": {"precision": 0.9527027027027027, "recall": 0.962457337883959, "f1": 0.9575551782682513, "support": 1172}, "N1": {"precision": 0.6057268722466961, "recall": 0.6264236902050114, "f1": 0.6159014557670773, "support": 439}, "N2": {"precision": 0.9138080138368406, "recall": 0.9260882267017236, "f1": 0.9199071387115497, "support": 3423}, "N3": {"precision": 0.8952879581151832, "recall": 0.8479338842975207, "f1": 0.8709677419354839, "support": 605}, "REM": {"precision": 0.9600920951650038, "recall": 0.9308035714285714, "f1": 0.9452210049112202, "support": 1344}}}, {"group": 5, "subjects": ["14", "16", "18", "19"], "n_files": 8, "n_epochs": 8796, "accuracy": 0.8910868576625739, "macro_f1": 0.8140416944119, "kappa": 0.8487274194755794, "confusion_matrix": [[1560, 19, 27, 1, 5], [167, 161, 138, 0, 57], [20, 39, 3439, 59, 85], [3, 0, 173, 1090, 0], [46, 10, 109, 0, 1588]], "per_class_metrics": {"Wake": {"precision": 0.8685968819599109, "recall": 0.967741935483871, "f1": 0.9154929577464788, "support": 1612}, "N1": {"precision": 0.7030567685589519, "recall": 0.3078393881453155, "f1": 0.4281914893617021, "support": 523}, "N2": {"precision": 0.8849716932578486, "recall": 0.9442613948380011, "f1": 0.9136556854410203, "support": 3642}, "N3": {"precision": 0.9478260869565217, "recall": 0.8609794628751974, "f1": 0.9023178807947019, "support": 1266}, "REM": {"precision": 0.9152737752161383, "recall": 0.905875641756988, "f1": 0.9105504587155963, "support": 1753}}}], "average_metrics": {"accuracy": {"mean": 0.8938595032677089, "std": 0.016140811613614157}, "macro_f1": {"mean": 0.8414455592821293, "std": 0.025104987566658715}, "kappa": {"mean": 0.8519276707303665, "std": 0.020162950557147973}}, "performance_range": {"accuracy": {"min": 0.8635337574389493, "max": 0.907489617642847}, "macro_f1": {"min": 0.8078403271059799, "max": 0.8656093226455054}, "kappa": {"min": 0.8140080232283277, "max": 0.8693495421439998}}, "targets_achieved": {"accuracy": true, "macro_f1": true, "kappa": true}}