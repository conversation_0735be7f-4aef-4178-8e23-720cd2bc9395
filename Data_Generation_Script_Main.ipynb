{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Jathurshan0330/Cross-Modal-Transformer/blob/master/Data_Generation_Script_Main.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "Le3cAJ6Jc-KE"}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fcH3N_c1cSmH", "outputId": "0e56e57b-d78d-4146-f067-2eabf212403a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cloning into 'Cross-Modal-Transformer'...\n", "remote: Enumerating objects: 349, done.\u001b[K\n", "remote: Counting objects: 100% (57/57), done.\u001b[K\n", "remote: Compressing objects: 100% (51/51), done.\u001b[K\n", "remote: Total 349 (delta 32), reused 12 (delta 6), pack-reused 292\u001b[K\n", "Receiving objects: 100% (349/349), 100.56 MiB | 15.72 MiB/s, done.\n", "Resolving deltas: 100% (125/125), done.\n"]}], "source": ["!git clone https://github.com/Jathurshan0330/Cross-Modal-Transformer.git"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "byBArJ2mcRif", "outputId": "0b094819-1b80-4fb7-caf8-15a58e11f306"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/content/Cross-Modal-Transformer\n"]}], "source": ["cd  /content/Cross-Modal-Transformer"]}, {"cell_type": "markdown", "metadata": {"id": "1bnLsjYad8IK"}, "source": ["## Install Requirements "]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QKaW2Tohe8N6", "outputId": "d862bd1a-fd31-45d5-e18d-524ce4b98177"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.9/dist-packages (from -r requirements.txt (line 2)) (1.22.4)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.9/dist-packages (from -r requirements.txt (line 3)) (3.7.1)\n", "Requirement already satisfied: h5py in /usr/local/lib/python3.9/dist-packages (from -r requirements.txt (line 4)) (3.8.0)\n", "Collecting neptune-client\n", "  Downloading neptune_client-1.1.1-py3-none-any.whl (442 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m442.6/442.6 KB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: scikit-image in /usr/local/lib/python3.9/dist-packages (from -r requirements.txt (line 6)) (0.19.3)\n", "Collecting sklearn\n", "  Downloading sklearn-0.0.post1.tar.gz (3.6 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting e<PERSON>ps\n", "  Downloading einops-0.6.0-py3-none-any.whl (41 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.6/41.6 KB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting mne\n", "  Downloading mne-1.3.1-py3-none-any.whl (7.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.6/7.6 MB\u001b[0m \u001b[31m44.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (2.8.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (23.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (1.0.7)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (4.39.3)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (3.0.9)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (1.4.4)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (0.11.0)\n", "Requirement already satisfied: pillow>=6.2.0 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (8.4.0)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in /usr/local/lib/python3.9/dist-packages (from matplotlib->-r requirements.txt (line 3)) (5.12.0)\n", "Collecting GitPython>=2.0.8\n", "  Downloading GitPython-3.1.31-py3-none-any.whl (184 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m184.3/184.3 KB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.20.0 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (2.27.1)\n", "Requirement already satisfied: click>=7.0 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (8.1.3)\n", "Requirement already satisfied: six>=1.12.0 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (1.16.0)\n", "Collecting boto3>=1.16.0\n", "  Downloading boto3-1.26.104-py3-none-any.whl (135 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m135.6/135.6 KB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (1.4.4)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (5.9.4)\n", "Collecting swagger-spec-validator>=2.7.4\n", "  Downloading swagger_spec_validator-3.0.3-py2.py3-none-any.whl (27 kB)\n", "Requirement already satisfied: urllib3 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (1.26.15)\n", "Requirement already satisfied: requests-oauthlib>=1.0.0 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (1.3.1)\n", "Collecting websocket-client!=1.0.0,>=0.35.0\n", "  Downloading websocket_client-1.5.1-py3-none-any.whl (55 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m55.9/55.9 KB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: future>=0.17.1 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (0.18.3)\n", "Collecting PyJWT\n", "  Downloading PyJWT-2.6.0-py3-none-any.whl (20 kB)\n", "Collecting bravado<12.0.0,>=11.0.0\n", "  Downloading bravado-11.0.3-py2.py3-none-any.whl (38 kB)\n", "Requirement already satisfied: oauthlib>=2.1.0 in /usr/local/lib/python3.9/dist-packages (from neptune-client->-r requirements.txt (line 5)) (3.2.2)\n", "Requirement already satisfied: PyWavelets>=1.1.1 in /usr/local/lib/python3.9/dist-packages (from scikit-image->-r requirements.txt (line 6)) (1.4.1)\n", "Requirement already satisfied: tifffile>=2019.7.26 in /usr/local/lib/python3.9/dist-packages (from scikit-image->-r requirements.txt (line 6)) (2023.3.21)\n", "Requirement already satisfied: scipy>=1.4.1 in /usr/local/lib/python3.9/dist-packages (from scikit-image->-r requirements.txt (line 6)) (1.10.1)\n", "Requirement already satisfied: networkx>=2.2 in /usr/local/lib/python3.9/dist-packages (from scikit-image->-r requirements.txt (line 6)) (3.0)\n", "Requirement already satisfied: imageio>=2.4.1 in /usr/local/lib/python3.9/dist-packages (from scikit-image->-r requirements.txt (line 6)) (2.25.1)\n", "Requirement already satisfied: pooch>=1.5 in /usr/local/lib/python3.9/dist-packages (from mne->-r requirements.txt (line 9)) (1.6.0)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.9/dist-packages (from mne->-r requirements.txt (line 9)) (3.1.2)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.9/dist-packages (from mne->-r requirements.txt (line 9)) (4.65.0)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.9/dist-packages (from mne->-r requirements.txt (line 9)) (4.4.2)\n", "Collecting s3transfer<0.7.0,>=0.6.0\n", "  Downloading s3transfer-0.6.0-py3-none-any.whl (79 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.6/79.6 KB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting jmespath<2.0.0,>=0.7.1\n", "  Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)\n", "Collecting botocore<1.30.0,>=1.29.104\n", "  Downloading botocore-1.29.104-py3-none-any.whl (10.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.6/10.6 MB\u001b[0m \u001b[31m50.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hColl<PERSON>ting simple<PERSON>son\n", "  Downloading simplejson-3.18.4-cp39-cp39-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (136 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m136.8/136.8 KB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing-extensions in /usr/local/lib/python3.9/dist-packages (from bravado<12.0.0,>=11.0.0->neptune-client->-r requirements.txt (line 5)) (4.5.0)\n", "Collecting monotonic\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.9/dist-packages (from bravado<12.0.0,>=11.0.0->neptune-client->-r requirements.txt (line 5)) (6.0)\n", "Requirement already satisfied: msgpack in /usr/local/lib/python3.9/dist-packages (from bravado<12.0.0,>=11.0.0->neptune-client->-r requirements.txt (line 5)) (1.0.5)\n", "Collecting bravado-core>=5.16.1\n", "  Downloading bravado_core-5.17.1-py2.py3-none-any.whl (67 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.7/67.7 KB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting gitdb<5,>=4.0.1\n", "  Downloading gitdb-4.0.10-py3-none-any.whl (62 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.7/62.7 KB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: zipp>=3.1.0 in /usr/local/lib/python3.9/dist-packages (from importlib-resources>=3.2.0->matplotlib->-r requirements.txt (line 3)) (3.15.0)\n", "Requirement already satisfied: appdirs>=1.3.0 in /usr/local/lib/python3.9/dist-packages (from pooch>=1.5->mne->-r requirements.txt (line 9)) (1.4.4)\n", "Requirement already satisfied: charset-normalizer~=2.0.0 in /usr/local/lib/python3.9/dist-packages (from requests>=2.20.0->neptune-client->-r requirements.txt (line 5)) (2.0.12)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.9/dist-packages (from requests>=2.20.0->neptune-client->-r requirements.txt (line 5)) (2022.12.7)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.9/dist-packages (from requests>=2.20.0->neptune-client->-r requirements.txt (line 5)) (3.4)\n", "Requirement already satisfied: jsonschema in /usr/local/lib/python3.9/dist-packages (from swagger-spec-validator>=2.7.4->neptune-client->-r requirements.txt (line 5)) (4.3.3)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.9/dist-packages (from jinja2->mne->-r requirements.txt (line 9)) (2.1.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.9/dist-packages (from pandas->neptune-client->-r requirements.txt (line 5)) (2022.7.1)\n", "Collecting jsonref\n", "  Downloading jsonref-1.1.0-py3-none-any.whl (9.4 kB)\n", "Collecting smmap<6,>=3.0.1\n", "  Downloading smmap-5.0.0-py3-none-any.whl (24 kB)\n", "Requirement already satisfied: attrs>=17.4.0 in /usr/local/lib/python3.9/dist-packages (from jsonschema->swagger-spec-validator>=2.7.4->neptune-client->-r requirements.txt (line 5)) (22.2.0)\n", "Requirement already satisfied: pyrsistent!=0.17.0,!=0.17.1,!=0.17.2,>=0.14.0 in /usr/local/lib/python3.9/dist-packages (from jsonschema->swagger-spec-validator>=2.7.4->neptune-client->-r requirements.txt (line 5)) (0.19.3)\n", "Collecting isoduration\n", "  Downloading isoduration-20.11.0-py3-none-any.whl (11 kB)\n", "Requirement already satisfied: webcolors>=1.11 in /usr/local/lib/python3.9/dist-packages (from jsonschema->swagger-spec-validator>=2.7.4->neptune-client->-r requirements.txt (line 5)) (1.13)\n", "Collecting rfc3987\n", "  Downloading rfc3987-1.3.8-py2.py3-none-any.whl (13 kB)\n", "Collecting rfc3339-validator\n", "  Downloading rfc3339_validator-0.1.4-py2.py3-none-any.whl (3.5 kB)\n", "Collecting jsonpointer>1.13\n", "  Downloading jsonpointer-2.3-py2.py3-none-any.whl (7.8 kB)\n", "Collecting uri-template\n", "  Downloading uri_template-1.2.0-py3-none-any.whl (10 kB)\n", "Collecting fqdn\n", "  Downloading fqdn-1.5.1-py3-none-any.whl (9.1 kB)\n", "Collecting arrow>=0.15.0\n", "  Downloading arrow-1.2.3-py3-none-any.whl (66 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m66.4/66.4 KB\u001b[0m \u001b[31m877.9 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: sklearn\n", "  Building wheel for sklearn (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for sklearn: filename=sklearn-0.0.post1-py3-none-any.whl size=2955 sha256=3779edb280cb12d3612c245cf743fb67d2eed8d4128d0cb38d6a044adbba7a66\n", "  Stored in directory: /root/.cache/pip/wheels/f8/e0/3d/9d0c2020c44a519b9f02ab4fa6d2a4a996c98d79ab2f569fa1\n", "Successfully built sklearn\n", "Installing collected packages: sklearn, rfc3987, monotonic, websocket-client, uri-template, smmap, simplejson, rfc3339-validator, PyJWT, jsonref, jsonpointer, jmespath, fqdn, einops, swagger-spec-validator, gitdb, botocore, arrow, s3transfer, mne, isoduration, GitPython, boto3, bravado-core, bravado, neptune-client\n", "Successfully installed GitPython-3.1.31 PyJWT-2.6.0 arrow-1.2.3 boto3-1.26.104 botocore-1.29.104 bravado-11.0.3 bravado-core-5.17.1 einops-0.6.0 fqdn-1.5.1 gitdb-4.0.10 isoduration-20.11.0 jmespath-1.0.1 jsonpointer-2.3 jsonref-1.1.0 mne-1.3.1 monotonic-1.6 neptune-client-1.1.1 rfc3339-validator-0.1.4 rfc3987-1.3.8 s3transfer-0.6.0 simplejson-3.18.4 sklearn-0.0.post1 smmap-5.0.0 swagger-spec-validator-3.0.3 uri-template-1.2.0 websocket-client-1.5.1\n"]}], "source": ["pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {"id": "vMqJ5TA4eq2h"}, "source": ["#Create dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/"}, "id": "r_U5_fW9d4fE", "outputId": "65ffc234-f16e-430d-8d7c-4fae5736091a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1;30;43mStreaming output truncated to the last 5000 lines.\u001b[0m\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1107 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1107 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4671G0-PSG.edf' to file '/root/mne_data/physionet-sleep-data/SC4671G0-PSG.edf'.\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4671GJ-Hypnogram.edf' to file '/root/mne_data/physionet-sleep-data/SC4671GJ-Hypnogram.edf'.\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4671G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8339999  =      0.000 ... 83399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1968 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1968 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4672G0-PSG.edf' to file '/root/mne_data/physionet-sleep-data/SC4672G0-PSG.edf'.\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4672GV-Hypnogram.edf' to file '/root/mne_data/physionet-sleep-data/SC4672GV-Hypnogram.edf'.\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4672G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7739999  =      0.000 ... 77399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1021 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1021 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4721E0-PSG.edf' to file '/root/mne_data/physionet-sleep-data/SC4721E0-PSG.edf'.\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4721EC-Hypnogram.edf' to file '/root/mne_data/physionet-sleep-data/SC4721EC-Hypnogram.edf'.\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4721E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7025999  =      0.000 ... 70259.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1031 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1031 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4722E0-PSG.edf' to file '/root/mne_data/physionet-sleep-data/SC4722E0-PSG.edf'.\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4722EM-Hypnogram.edf' to file '/root/mne_data/physionet-sleep-data/SC4722EM-Hypnogram.edf'.\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4722E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8237999  =      0.000 ... 82379.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1130 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1130 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4751E0-PSG.edf' to file '/root/mne_data/physionet-sleep-data/SC4751E0-PSG.edf'.\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4751EC-Hypnogram.edf' to file '/root/mne_data/physionet-sleep-data/SC4751EC-Hypnogram.edf'.\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4751E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8039999  =      0.000 ... 80399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2044 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2044 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4752E0-PSG.edf' to file '/root/mne_data/physionet-sleep-data/SC4752E0-PSG.edf'.\n", "Downloading data from 'https://physionet.org/physiobank/database/sleep-edfx/sleep-cassette//SC4752EM-Hypnogram.edf' to file '/root/mne_data/physionet-sleep-data/SC4752EM-Hypnogram.edf'.\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4752E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7409999  =      0.000 ... 74099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1049 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1049 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Train data shape : (36657, 1, 3000), Train label shape : (36657,)\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4001E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7949999  =      0.000 ... 79499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "841 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 841 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4002E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8489999  =      0.000 ... 84899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1127 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1127 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4111E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7925999  =      0.000 ... 79259.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "928 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 928 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4112E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8339999  =      0.000 ... 83399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "802 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 802 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4131E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8441999  =      0.000 ... 84419.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1028 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1028 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4141E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8267999  =      0.000 ... 82679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1004 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1004 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4142E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8321999  =      0.000 ... 83219.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "952 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 952 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4231E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8237999  =      0.000 ... 82379.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "904 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 904 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4232E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7919999  =      0.000 ... 79199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1729 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1729 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4251E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8279999  =      0.000 ... 82799.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "972 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 972 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4252E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7997999  =      0.000 ... 79979.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1020 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1020 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4271F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7319999  =      0.000 ... 73199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1052 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1052 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4272F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8609999  =      0.000 ... 86099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1090 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1090 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4281G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8363999  =      0.000 ... 83639.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1127 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1127 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4282G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8441999  =      0.000 ... 84419.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1070 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1070 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4451F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8369999  =      0.000 ... 83699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1208 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1208 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4452F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8009999  =      0.000 ... 80099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1166 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1166 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4481F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8639999  =      0.000 ... 86399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "2027 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2027 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4482F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8639999  =      0.000 ... 86399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "Not setting metadata\n", "1907 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1907 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4501E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8249999  =      0.000 ... 82499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1326 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1326 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4502E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8309999  =      0.000 ... 83099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1103 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1103 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4561F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8111999  =      0.000 ... 81119.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1237 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1237 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4562F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8399999  =      0.000 ... 83999.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1148 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1148 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4591G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8459999  =      0.000 ... 84599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1840 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1840 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4592G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 6119999  =      0.000 ... 61199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.2s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.2s finished\n", "Not setting metadata\n", "1231 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1231 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4621E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7835999  =      0.000 ... 78359.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1445 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1445 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4622E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8567999  =      0.000 ... 85679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1823 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1823 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4701E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8039999  =      0.000 ... 80399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1717 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1717 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4702E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7871999  =      0.000 ... 78719.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1515 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1515 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4761E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7889999  =      0.000 ... 78899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1683 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1683 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4762E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2662 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2662 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4821G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8165999  =      0.000 ... 81659.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1704 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1704 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4822G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1366 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1366 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Train data shape : (43754, 1, 3000), Train label shape : (43754,)\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4011E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8405999  =      0.000 ... 84059.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1103 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1103 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4012E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8549999  =      0.000 ... 85499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1186 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1186 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4101E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8159999  =      0.000 ... 81599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1104 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1104 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4102E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8573999  =      0.000 ... 85739.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1092 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1092 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4181E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8267999  =      0.000 ... 82679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "964 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 964 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4182E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8525999  =      0.000 ... 85259.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "920 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 920 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4211E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1578 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1578 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4212E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8081999  =      0.000 ... 80819.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "808 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 808 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4291G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8267999  =      0.000 ... 82679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1131 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1131 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4292G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1605 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1605 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4301E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7937999  =      0.000 ... 79379.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "929 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 929 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4302E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8423999  =      0.000 ... 84239.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "854 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 854 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4321E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8069999  =      0.000 ... 80699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1560 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1560 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4322E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7847999  =      0.000 ... 78479.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1021 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1021 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4351F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8129999  =      0.000 ... 81299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "976 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 976 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4352F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7595999  =      0.000 ... 75959.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "963 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 963 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4362F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 6797999  =      0.000 ... 67979.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "824 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 824 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4531E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7805999  =      0.000 ... 78059.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1096 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1096 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4532E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8231999  =      0.000 ... 82319.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1056 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1056 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4571F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8549999  =      0.000 ... 85499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1287 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1287 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4572F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8639999  =      0.000 ... 86399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1085 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1085 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4601E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8189999  =      0.000 ... 81899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1349 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1349 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4602E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8399999  =      0.000 ... 83999.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "2043 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2043 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4611E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7949999  =      0.000 ... 79499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1652 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1652 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4612E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8189999  =      0.000 ... 81899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1062 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1062 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4731E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8339999  =      0.000 ... 83399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2667 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2667 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4732E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7559999  =      0.000 ... 75599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2318 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2318 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4741E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8069999  =      0.000 ... 80699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2210 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2210 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4742E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7949999  =      0.000 ... 79499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1063 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1063 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4771G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8267999  =      0.000 ... 82679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1325 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1325 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4772G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 6725999  =      0.000 ... 67259.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1324 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1324 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Train data shape : (40155, 1, 3000), Train label shape : (40155,)\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4021E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8411999  =      0.000 ... 84119.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1025 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1025 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4022E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8267999  =      0.000 ... 82679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1009 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1009 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4081E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8387999  =      0.000 ... 83879.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1134 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1134 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4082E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7901999  =      0.000 ... 79019.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1054 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1054 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4091E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8195999  =      0.000 ... 81959.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1132 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1132 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4092E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8573999  =      0.000 ... 85739.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1207 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1207 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4121E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8357999  =      0.000 ... 83579.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    1.1s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    1.1s finished\n", "Not setting metadata\n", "1783 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1783 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4122E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7817999  =      0.000 ... 78179.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "977 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 977 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4161E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7877999  =      0.000 ... 78779.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1144 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1144 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4162E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8255999  =      0.000 ... 82559.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1003 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1003 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4171E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8225999  =      0.000 ... 82259.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1002 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1002 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4172E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8171999  =      0.000 ... 81719.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1773 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1773 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4261F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8399999  =      0.000 ... 83999.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1597 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1597 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4262F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8159999  =      0.000 ... 81599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "Not setting metadata\n", "980 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 980 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4411E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8183999  =      0.000 ... 81839.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1078 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1078 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4412E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8573999  =      0.000 ... 85739.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "924 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 924 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4441E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7859999  =      0.000 ... 78599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1195 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1195 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4442E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8333999  =      0.000 ... 83339.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1092 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1092 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4522E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8321999  =      0.000 ... 83219.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "997 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 997 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4541F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8219999  =      0.000 ... 82199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1716 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1716 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4542F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8039999  =      0.000 ... 80399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1148 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1148 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4551F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8315999  =      0.000 ... 83159.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1047 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1047 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4552F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1090 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1090 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4641E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8039999  =      0.000 ... 80399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1271 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1271 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4642E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8369999  =      0.000 ... 83699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2049 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2049 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4801G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8321999  =      0.000 ... 83219.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1241 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1241 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4802G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1229 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1229 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4811G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7205999  =      0.000 ... 72059.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1293 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1293 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4812G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7247999  =      0.000 ... 72479.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1183 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1183 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Train data shape : (35373, 1, 3000), Train label shape : (35373,)\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4031E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8459999  =      0.000 ... 84599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "952 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 952 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4032E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8195999  =      0.000 ... 81959.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "911 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 911 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4041E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7709999  =      0.000 ... 77099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1235 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1235 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4042E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8375999  =      0.000 ... 83759.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "Not setting metadata\n", "1200 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1200 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4051E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8165999  =      0.000 ... 81659.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "672 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 672 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4052E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8417999  =      0.000 ... 84179.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1246 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1246 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4061E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8309999  =      0.000 ... 83099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "843 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 843 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4062E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8489999  =      0.000 ... 84899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1016 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1016 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4191E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8321999  =      0.000 ... 83219.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1535 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1535 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4192E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7829999  =      0.000 ... 78299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1274 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1274 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4241E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8105999  =      0.000 ... 81059.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1673 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1673 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4242E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8129999  =      0.000 ... 81299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.7s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.7s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1775 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1775 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4331F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1888 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1888 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4332F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8279999  =      0.000 ... 82799.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1312 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1312 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4381F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8249999  =      0.000 ... 82499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1776 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1776 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4382F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8309999  =      0.000 ... 83099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1871 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1871 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4421E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8291999  =      0.000 ... 82919.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "785 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 785 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4422E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8045999  =      0.000 ... 80459.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "884 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 884 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4461F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8087999  =      0.000 ... 80879.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "983 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 983 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4462F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8567999  =      0.000 ... 85679.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1022 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1022 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4511E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8135999  =      0.000 ... 81359.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1087 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1087 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4512E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8249999  =      0.000 ... 82499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "954 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 954 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4581G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8213999  =      0.000 ... 82139.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1095 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1095 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4582G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7901999  =      0.000 ... 79019.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1175 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1175 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4651E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8579999  =      0.000 ... 85799.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "2644 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2644 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4652E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8519999  =      0.000 ... 85199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "Not setting metadata\n", "1929 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1929 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4661E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8219999  =      0.000 ... 82199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2026 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2026 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4662E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8459999  =      0.000 ... 84599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1994 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1994 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4711E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8189999  =      0.000 ... 81899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1413 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1413 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4712E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8459999  =      0.000 ... 84599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1241 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1241 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Train data shape : (40411, 1, 3000), Train label shape : (40411,)\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4071E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "976 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 976 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4072E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8309999  =      0.000 ... 83099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1273 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1273 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4151E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7859999  =      0.000 ... 78599.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "952 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 952 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4152E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8591999  =      0.000 ... 85919.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1762 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1762 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4201E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8411999  =      0.000 ... 84119.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1022 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1022 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4202E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8009999  =      0.000 ... 80099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1021 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1021 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4221E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8099999  =      0.000 ... 80999.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1099 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1099 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4222E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8279999  =      0.000 ... 82799.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.7s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.7s finished\n", "Not setting metadata\n", "1108 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1108 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4311E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8009999  =      0.000 ... 80099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1054 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1054 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4312E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8099999  =      0.000 ... 80999.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.6s finished\n", "Not setting metadata\n", "1181 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1181 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4341F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8249999  =      0.000 ... 82499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1501 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1501 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4342F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8369999  =      0.000 ... 83699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1582 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1582 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4371F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8549999  =      0.000 ... 85499.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "918 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 918 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4372F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8519999  =      0.000 ... 85199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1509 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1509 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4401E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7889999  =      0.000 ... 78899.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1064 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1064 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4402E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8369999  =      0.000 ... 83699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1072 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1072 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4431E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8429999  =      0.000 ... 84299.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "699 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 699 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4432E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8339999  =      0.000 ... 83399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.5s finished\n", "Not setting metadata\n", "962 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 962 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4471F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8219999  =      0.000 ... 82199.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1187 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1187 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4472F0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8369999  =      0.000 ... 83699.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "2161 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2161 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4491G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8399999  =      0.000 ... 83999.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1101 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1101 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4492G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 6407999  =      0.000 ... 64079.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "Not setting metadata\n", "1040 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1040 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4631E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8273999  =      0.000 ... 82739.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1063 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1063 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4632E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8543999  =      0.000 ... 85439.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1107 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1107 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4671G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8339999  =      0.000 ... 83399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1968 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1968 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4672G0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7739999  =      0.000 ... 77399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage 4', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "Not setting metadata\n", "1021 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1021 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4721E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7025999  =      0.000 ... 70259.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1031 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1031 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4722E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8237999  =      0.000 ... 82379.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1130 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1130 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4751E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 8039999  =      0.000 ... 80399.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.4s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 3 (event id 3)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "2044 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 2044 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Using default location ~/mne_data for PHYSIONET_SLEEP...\n", "Extracting EDF parameters from /root/mne_data/physionet-sleep-data/SC4752E0-PSG.edf...\n", "EDF file detected\n", "Setting channel info structure...\n", "Creating raw.info structure...\n", "Reading 0 ... 7409999  =      0.000 ... 74099.990 secs...\n", "Used Annotations descriptions: ['Sleep stage 1', 'Sleep stage 2', 'Sleep stage 3', 'Sleep stage R', 'Sleep stage W']\n", "Filtering raw data in 1 contiguous segment\n", "Setting up band-pass filter from 0.2 - 40 Hz\n", "\n", "FIR filter parameters\n", "---------------------\n", "Designing a one-pass, zero-phase, non-causal bandpass filter:\n", "- Windowed time-domain design (firwin) method\n", "- Hamming window with 0.0194 passband ripple and 53 dB stopband attenuation\n", "- Lower passband edge: 0.20\n", "- Lower transition bandwidth: 0.20 Hz (-6 dB cutoff frequency: 0.10 Hz)\n", "- Upper passband edge: 40.00 Hz\n", "- Upper transition bandwidth: 10.00 Hz (-6 dB cutoff frequency: 45.00 Hz)\n", "- Filter length: 1651 samples (16.510 sec)\n", "\n", "[Parallel(n_jobs=1)]: Using backend SequentialBackend with 1 concurrent workers.\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s remaining:    0.0s\n", "[Parallel(n_jobs=1)]: Done   1 out of   1 | elapsed:    0.3s finished\n", "/content/Cross-Modal-Transformer/./data_preparations/single_epoch.py:53: RuntimeWarning: No matching events found for Sleep stage 4 (event id 4)\n", "  epochs_data = mne.Epochs(raw = sleep_signals, events = events,\n", "Not setting metadata\n", "1049 matching events found\n", "No baseline correction applied\n", "0 projection items activated\n", "Using data from preloaded Raw for 1049 events and 3000 original time points ...\n", "0 bad epochs dropped\n", "Train data shape : (36657, 1, 3000), Train label shape : (36657,)\n"]}], "source": ["!python \"./data_preparations/single_epoch.py\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true}, "id": "0sot7fTwd2GA"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyOjnbGHxA1sfxf7i4z0eims", "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}