#!/bin/bash

# 运行训练脚本并监控进度

echo "🚀 开始训练 MAMBAFORMER (20个受试者)"
echo "目标: 达到80%+准确率"
echo "=================================="

# 创建日志目录
mkdir -p log

# 获取Python路径
PYTHON_CMD=$(which python3 || which python)

if [ -z "$PYTHON_CMD" ]; then
    echo "错误: 找不到Python"
    exit 1
fi

echo "使用Python: $PYTHON_CMD"

# 检查依赖
echo "检查依赖..."
$PYTHON_CMD -c "import torch; print(f'PyTorch版本: {torch.__version__}')" || {
    echo "错误: PyTorch未安装"
    exit 1
}

# 运行训练
echo "开始训练..."
$PYTHON_CMD train_with_correct_config.py 2>&1 | tee log/training_20subjects_$(date +%Y%m%d_%H%M%S).log

echo "=================================="
echo "训练完成!"