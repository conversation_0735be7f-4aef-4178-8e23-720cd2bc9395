#!/usr/bin/env python
"""
快速运行完整实验的脚本
"""

import os
import sys
import subprocess

def run_command(cmd, description):
    """运行命令并显示描述"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    print(f"命令: {cmd}")
    print()
    
    result = subprocess.run(cmd, shell=True)
    if result.returncode != 0:
        print(f"❌ 错误: {description} 失败")
        sys.exit(1)
    print(f"✅ {description} 完成")

def main():
    """主函数"""
    print("🚀 Cross-Modal-Transformer 完整实验流程")
    print("=" * 60)
    
    # 检查环境
    if not os.path.exists("src"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 1. 生成K折分割
    if not os.path.exists("configs/subject_aware_folds.json"):
        run_command(
            "python src/evaluation/subject_aware_kfold.py",
            "生成受试者感知K折分割"
        )
    else:
        print("✅ K折分割文件已存在，跳过生成")
    
    # 2. 运行数据泄露审计
    run_command(
        "python src/evaluation/data_leakage_audit.py",
        "运行数据泄露审计"
    )
    
    # 3. 开始训练
    run_command(
        "python src/training/train_subject_aware_mambaformer.py",
        "开始5折交叉验证训练"
    )
    
    # 4. 显示结果
    print("\n" + "="*60)
    print("📊 实验结果汇总")
    print("="*60)
    
    if os.path.exists("configs/subject_aware_mambaformer_results.json"):
        import json
        with open("configs/subject_aware_mambaformer_results.json", 'r') as f:
            results = json.load(f)
        
        stats = results['summary_statistics']
        print(f"平均准确率: {stats['mean_accuracy']:.4f} ± {stats['std_accuracy']:.4f}")
        print(f"平均F1分数: {stats['mean_f1_score']:.4f} ± {stats['std_f1_score']:.4f}")
        print(f"完成折数: {stats['num_folds_completed']}/5")
        
        print("\n各Fold结果:")
        for fold in results['fold_results']:
            print(f"  Fold {fold['fold_id']+1}: Acc={fold['test_acc']:.4f}, F1={fold['test_f1']:.4f}")
    
    print("\n✅ 实验完成！")
    print(f"日志文件位于: logs/")
    print(f"结果文件位于: configs/subject_aware_mambaformer_results.json")

if __name__ == "__main__":
    main()