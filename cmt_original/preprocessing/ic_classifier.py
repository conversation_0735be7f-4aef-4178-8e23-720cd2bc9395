"""
Simplified ICLabel Implementation
Classifies ICA components into different categories
"""

import numpy as np
from typing import List, Dict, Tuple
from scipy import stats
from scipy.signal import welch, find_peaks
from sklearn.metrics.pairwise import cosine_similarity

class SimpleICLabel:
    """
    简化版ICLabel分类器
    基于时域、频域和空间特征对ICA成分进行分类
    """
    
    def __init__(self, sfreq: float = 100.0):
        self.sfreq = sfreq
        self.component_labels = [
            'brain', 'eye', 'muscle', 'heart', 
            'line_noise', 'channel_noise', 'other'
        ]
        
        # 预定义的特征阈值
        self.thresholds = {
            'eye_freq_ratio': 0.3,      # 眼动：低频能量占比
            'muscle_high_freq': 0.4,    # 肌电：高频能量占比
            'heart_peak_freq': [0.8, 2.0],  # 心电：特征频率范围
            'line_noise_freq': [48, 52, 58, 62],  # 工频噪声
            'brain_alpha_ratio': 0.15,   # 脑电：alpha波能量占比
            'kurtosis_threshold': 5.0,   # 峰度阈值
            'variance_threshold': 0.01   # 方差阈值
        }
    
    def _extract_temporal_features(self, component: np.ndarray) -> Dict:
        """提取时域特征"""
        features = {}
        
        # 统计特征
        features['mean'] = np.mean(component)
        features['std'] = np.std(component)
        features['skewness'] = stats.skew(component)
        features['kurtosis'] = stats.kurtosis(component)
        features['variance'] = np.var(component)
        
        # 峰值特征
        peaks, _ = find_peaks(np.abs(component), height=np.std(component))
        features['n_peaks'] = len(peaks)
        features['peak_rate'] = len(peaks) / (len(component) / self.sfreq)
        
        return features
    
    def _extract_spectral_features(self, component: np.ndarray) -> Dict:
        """提取频域特征"""
        # 计算功率谱密度
        freqs, psd = welch(component, fs=self.sfreq, nperseg=min(256, len(component)//4))
        
        features = {}
        
        # 频带能量
        delta_mask = (freqs >= 0.5) & (freqs <= 4)
        theta_mask = (freqs >= 4) & (freqs <= 8) 
        alpha_mask = (freqs >= 8) & (freqs <= 13)
        beta_mask = (freqs >= 13) & (freqs <= 30)
        gamma_mask = (freqs >= 30) & (freqs <= 50)
        
        total_power = np.sum(psd)
        
        features['delta_ratio'] = np.sum(psd[delta_mask]) / total_power
        features['theta_ratio'] = np.sum(psd[theta_mask]) / total_power
        features['alpha_ratio'] = np.sum(psd[alpha_mask]) / total_power  
        features['beta_ratio'] = np.sum(psd[beta_mask]) / total_power
        features['gamma_ratio'] = np.sum(psd[gamma_mask]) / total_power
        
        # 低频/高频比率
        low_freq_mask = freqs <= 8
        high_freq_mask = freqs >= 13
        features['low_high_ratio'] = np.sum(psd[low_freq_mask]) / np.sum(psd[high_freq_mask])
        
        # 主频特征
        peak_freq_idx = np.argmax(psd)
        features['peak_frequency'] = freqs[peak_freq_idx]
        features['peak_power'] = psd[peak_freq_idx]
        
        # 工频噪声检测
        for line_freq in [50, 60]:  # 50Hz/60Hz工频
            line_mask = (freqs >= line_freq-1) & (freqs <= line_freq+1)
            if np.any(line_mask):
                features[f'line_{line_freq}hz_ratio'] = np.sum(psd[line_mask]) / total_power
            else:
                features[f'line_{line_freq}hz_ratio'] = 0.0
                
        return features
    
    def _classify_component(self, temp_features: Dict, spec_features: Dict) -> str:
        """根据特征分类单个成分"""
        
        # 1. 眼动伪影检测 (高峰度 + 低频为主)
        if (temp_features['kurtosis'] > self.thresholds['kurtosis_threshold'] and 
            spec_features['delta_ratio'] + spec_features['theta_ratio'] > self.thresholds['eye_freq_ratio']):
            return 'eye'
        
        # 2. 肌电伪影检测 (高频为主 + 高方差)
        if (spec_features['beta_ratio'] + spec_features['gamma_ratio'] > self.thresholds['muscle_high_freq'] and
            temp_features['variance'] > self.thresholds['variance_threshold']):
            return 'muscle'
        
        # 3. 心电伪影检测 (特定频率峰值)
        heart_freq_range = self.thresholds['heart_peak_freq']
        if (heart_freq_range[0] <= spec_features['peak_frequency'] <= heart_freq_range[1] and
            temp_features['peak_rate'] > 1.0):  # 规律的峰值
            return 'heart'
        
        # 4. 工频噪声检测
        for line_freq in [50, 60]:
            if spec_features.get(f'line_{line_freq}hz_ratio', 0) > 0.05:  # 5%阈值
                return 'line_noise'
        
        # 5. 脑电信号检测 (alpha波为主要特征)
        if (spec_features['alpha_ratio'] > self.thresholds['brain_alpha_ratio'] and
            temp_features['kurtosis'] < self.thresholds['kurtosis_threshold']):
            return 'brain'
        
        # 6. 通道噪声检测 (极低方差)
        if temp_features['variance'] < 1e-6:
            return 'channel_noise'
        
        # 7. 默认分类
        return 'other'
    
    def predict_single(self, component: np.ndarray) -> str:
        """预测单个ICA成分的标签"""
        # 提取特征
        temp_features = self._extract_temporal_features(component)
        spec_features = self._extract_spectral_features(component)
        
        # 分类
        return self._classify_component(temp_features, spec_features)
    
    def predict(self, components: np.ndarray) -> List[str]:
        """
        预测多个ICA成分的标签
        
        Args:
            components: [n_components, n_samples]
            
        Returns:
            labels: 成分标签列表
        """
        labels = []
        for component in components:
            label = self.predict_single(component)
            labels.append(label)
            
        return labels
    
    def get_brain_components_mask(self, components: np.ndarray) -> np.ndarray:
        """返回脑电成分的掩码"""
        labels = self.predict(components)
        return np.array([label == 'brain' for label in labels])
    
    def get_artifact_summary(self, labels: List[str]) -> Dict:
        """获取伪影分类汇总"""
        summary = {label: 0 for label in self.component_labels}
        for label in labels:
            summary[label] += 1
            
        return summary


class ICLabelVisualizer:
    """ICLabel结果可视化工具"""
    
    @staticmethod
    def plot_component_classification(components: np.ndarray, labels: List[str], 
                                    sfreq: float = 100.0, save_path: str = None):
        """可视化成分分类结果"""
        try:
            import matplotlib.pyplot as plt
            from scipy.signal import welch
            
            n_components = len(components)
            fig, axes = plt.subplots(n_components, 3, figsize=(15, 3*n_components))
            
            if n_components == 1:
                axes = axes.reshape(1, -1)
            
            for i, (component, label) in enumerate(zip(components, labels)):
                # 时域波形
                time = np.arange(len(component)) / sfreq
                axes[i, 0].plot(time, component)
                axes[i, 0].set_title(f'IC{i+1}: {label} (Time Domain)')
                axes[i, 0].set_xlabel('Time (s)')
                axes[i, 0].set_ylabel('Amplitude')
                
                # 频域功率谱
                freqs, psd = welch(component, fs=sfreq, nperseg=min(256, len(component)//4))
                axes[i, 1].semilogy(freqs, psd)
                axes[i, 1].set_title(f'IC{i+1}: {label} (Frequency Domain)')
                axes[i, 1].set_xlabel('Frequency (Hz)')
                axes[i, 1].set_ylabel('Power')
                axes[i, 1].set_xlim([0, 50])
                
                # 直方图
                axes[i, 2].hist(component, bins=50, alpha=0.7)
                axes[i, 2].set_title(f'IC{i+1}: {label} (Distribution)')
                axes[i, 2].set_xlabel('Amplitude')
                axes[i, 2].set_ylabel('Count')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                
            return fig
            
        except ImportError:
            print("Matplotlib not available for visualization")
            return None