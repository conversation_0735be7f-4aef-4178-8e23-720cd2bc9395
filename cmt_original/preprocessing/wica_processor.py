"""
wICA Preprocessing Pipeline
Combines ICA with wavelet denoising for EEG artifact removal
"""

import numpy as np
import torch
import pywt
from sklearn.decomposition import FastICA
from sklearn.preprocessing import StandardScaler
from scipy import signal
from typing import Tuple, Optional, List
import warnings
warnings.filterwarnings('ignore')

class wICAProcessor:
    """
    Wavelet-enhanced ICA processor for EEG artifact removal
    
    Args:
        n_components (int): Number of ICA components
        wavelet (str): Wavelet type for denoising
        threshold_mode (str): Wavelet thresholding mode
        random_state (int): Random seed for reproducibility
    """
    
    def __init__(
        self, 
        n_components: int = 20,
        wavelet: str = 'db4',
        threshold_mode: str = 'soft',
        random_state: int = 42
    ):
        self.n_components = n_components
        self.wavelet = wavelet
        self.threshold_mode = threshold_mode
        self.random_state = random_state
        
        # Initialize components
        self.ica = FastICA(
            n_components=n_components,
            random_state=random_state,
            whiten='unit-variance',
            max_iter=1000,
            tol=1e-4
        )
        self.scaler = StandardScaler()
        self.is_fitted = False
        
        # IC classification (simplified ICLabel)
        from .ic_classifier import SimpleICLabel
        self.ic_classifier = SimpleICLabel()
        
    def _preprocess_signal(self, data: np.ndarray) -> np.ndarray:
        """预处理原始信号"""
        # 1. 带通滤波 (0.5-30 Hz for sleep EEG)
        nyquist = 50  # Assuming 100Hz sampling rate
        low = 0.5 / nyquist
        high = 30.0 / nyquist
        b, a = signal.butter(4, [low, high], btype='band')
        filtered_data = signal.filtfilt(b, a, data, axis=-1)
        
        # 2. 标准化
        return self.scaler.fit_transform(filtered_data.T).T
        
    def _wavelet_denoise(self, data: np.ndarray, threshold_factor: float = 0.1) -> np.ndarray:
        """小波去噪"""
        denoised_data = np.zeros_like(data)
        
        for i, signal_1d in enumerate(data):
            # 小波分解
            coeffs = pywt.wavedec(signal_1d, self.wavelet, level=5)
            
            # 计算阈值 (使用改进的阈值估计)
            sigma = np.median(np.abs(coeffs[-1])) / 0.6745
            threshold = threshold_factor * sigma * np.sqrt(2 * np.log(len(signal_1d)))
            
            # 软阈值处理
            denoised_coeffs = [coeffs[0]]  # 保留低频系数
            for coeff in coeffs[1:]:
                denoised_coeffs.append(pywt.threshold(coeff, threshold, self.threshold_mode))
            
            # 重构信号
            denoised_data[i] = pywt.waverec(denoised_coeffs, self.wavelet)
            
        return denoised_data
    
    def fit(self, data: np.ndarray) -> 'wICAProcessor':
        """
        拟合ICA模型
        
        Args:
            data: EEG数据 [n_channels, n_samples]
        """
        # 预处理
        preprocessed_data = self._preprocess_signal(data)
        
        # 拟合ICA
        self.ica.fit(preprocessed_data.T)  # ICA期望 [n_samples, n_features]
        self.is_fitted = True
        
        return self
    
    def transform(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        应用wICA处理
        
        Args:
            data: EEG数据 [n_channels, n_samples]
            
        Returns:
            clean_data: 去伪影后的数据
            ic_components: ICA成分
            ic_labels: 成分标签
        """
        if not self.is_fitted:
            raise ValueError("wICAProcessor must be fitted before transform")
            
        # 预处理
        preprocessed_data = self._preprocess_signal(data)
        
        # ICA分解
        ic_components = self.ica.transform(preprocessed_data.T).T
        
        # IC分类
        ic_labels = self.ic_classifier.predict(ic_components)
        
        # 去除伪影成分
        clean_components = self._remove_artifacts(ic_components, ic_labels)
        
        # 重构信号
        reconstructed_data = self.ica.inverse_transform(clean_components.T).T
        
        # 小波去噪
        clean_data = self._wavelet_denoise(reconstructed_data)
        
        return clean_data, ic_components, ic_labels
    
    def _remove_artifacts(self, components: np.ndarray, labels: List[str]) -> np.ndarray:
        """去除伪影成分"""
        clean_components = components.copy()
        
        artifact_types = ['eye', 'muscle', 'heart', 'line_noise', 'channel_noise']
        
        for i, label in enumerate(labels):
            if label in artifact_types:
                # 将伪影成分置零
                clean_components[i] = np.zeros_like(clean_components[i])
                
        return clean_components
    
    def fit_transform(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """拟合并变换数据"""
        return self.fit(data).transform(data)
    
    def get_mixing_matrix(self) -> Optional[np.ndarray]:
        """获取ICA混合矩阵"""
        if self.is_fitted:
            return self.ica.mixing_
        return None
    
    def get_unmixing_matrix(self) -> Optional[np.ndarray]:
        """获取ICA解混矩阵"""
        if self.is_fitted:
            return self.ica.components_
        return None


class BatchwICAProcessor:
    """批量wICA处理器，用于处理多个epoch"""
    
    def __init__(self, wica_processor: wICAProcessor):
        self.wica_processor = wica_processor
        
    def process_epochs(self, epochs: np.ndarray) -> Tuple[np.ndarray, List]:
        """
        处理多个epoch
        
        Args:
            epochs: [n_epochs, n_channels, n_samples]
            
        Returns:
            clean_epochs: 处理后的数据
            processing_info: 处理信息
        """
        n_epochs, n_channels, n_samples = epochs.shape
        clean_epochs = np.zeros_like(epochs)
        processing_info = []
        
        # 使用第一个epoch拟合模型
        self.wica_processor.fit(epochs[0])
        
        for i, epoch in enumerate(epochs):
            try:
                clean_data, ic_components, ic_labels = self.wica_processor.transform(epoch)
                clean_epochs[i] = clean_data
                
                processing_info.append({
                    'epoch_idx': i,
                    'n_artifacts_removed': len([l for l in ic_labels if l in ['eye', 'muscle', 'heart', 'line_noise', 'channel_noise']]),
                    'ic_labels': ic_labels
                })
                
            except Exception as e:
                print(f"Warning: Failed to process epoch {i}: {e}")
                clean_epochs[i] = epoch  # 使用原始数据
                processing_info.append({
                    'epoch_idx': i,
                    'error': str(e),
                    'n_artifacts_removed': 0
                })
                
        return clean_epochs, processing_info