"""
Progressive Classifier with Uncertainty Estimation
Addresses N1/REM confusion through coarse-to-fine classification
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from typing import Tuple, Optional, Dict
import numpy as np

class MCDropout(nn.Module):
    """Monte Carlo Dropout for uncertainty estimation"""
    
    def __init__(self, p: float = 0.1):
        super().__init__()
        self.p = p
        
    def forward(self, x: Tensor) -> Tensor:
        return F.dropout(x, p=self.p, training=True)  # Always enabled for MC sampling


class TemperatureScaling(nn.Module):
    """
    Temperature scaling for probability calibration
    """
    
    def __init__(self, init_temperature: float = 1.0):
        super().__init__()
        self.temperature = nn.Parameter(torch.ones(1) * init_temperature)
        
    def forward(self, logits: Tensor) -> Tensor:
        """Apply temperature scaling"""
        return logits / self.temperature.clamp(min=1e-7)
    
    def calibrate(self, logits: Tensor, labels: Tensor, max_iter: int = 50):
        """Calibrate temperature on validation set"""
        self.train()
        optimizer = torch.optim.LBFGS([self.temperature], lr=0.01, max_iter=max_iter)
        
        def closure():
            loss = F.cross_entropy(self.forward(logits), labels)
            loss.backward()
            return loss
            
        optimizer.step(closure)


class ProgressiveClassifier(nn.Module):
    """
    渐进式分类器：粗分类 -> 细分类
    
    解决N1/REM混淆问题的策略：
    1. 第一阶段：3类粗分类 (Wake/NREM/REM) 
    2. 第二阶段：5类细分类 (W/S1/S2/S3/REM)
    3. 不确定性估计：Monte Carlo Dropout
    4. 概率标定：Temperature Scaling
    
    Args:
        d_model: 输入特征维度
        hidden_dim: 隐层维度
        num_coarse_classes: 粗分类类别数
        num_fine_classes: 细分类类别数
        dropout_rate: Dropout率
        use_uncertainty: 是否使用不确定性估计
    """
    
    def __init__(
        self,
        d_model: int = 128,  # EEG + EOG融合后的维度
        hidden_dim: int = 256,
        num_coarse_classes: int = 3,  # Wake, NREM, REM  
        num_fine_classes: int = 5,    # W, N1, N2, N3, REM
        dropout_rate: float = 0.1,
        use_uncertainty: bool = True,
        use_temperature_scaling: bool = True,
        device=None,
        dtype=None
    ):
        super().__init__()
        
        factory_kwargs = {'device': device, 'dtype': dtype}
        
        self.d_model = d_model
        self.hidden_dim = hidden_dim
        self.num_coarse_classes = num_coarse_classes
        self.num_fine_classes = num_fine_classes
        self.use_uncertainty = use_uncertainty
        self.use_temperature_scaling = use_temperature_scaling
        
        # 共享特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(d_model, hidden_dim, **factory_kwargs),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim, **factory_kwargs),
            nn.Dropout(dropout_rate)
        )
        
        # 粗分类器 (Wake/NREM/REM)
        self.coarse_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2, **factory_kwargs),
            nn.ReLU(),
            MCDropout(dropout_rate) if use_uncertainty else nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_coarse_classes, **factory_kwargs)
        )
        
        # 细分类器 (W/S1/S2/S3/REM)
        self.fine_classifier = nn.Sequential(
            nn.Linear(hidden_dim + num_coarse_classes, hidden_dim, **factory_kwargs),  # 加入粗分类信息
            nn.ReLU(),
            MCDropout(dropout_rate) if use_uncertainty else nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2, **factory_kwargs),
            nn.ReLU(),
            MCDropout(dropout_rate) if use_uncertainty else nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_fine_classes, **factory_kwargs)
        )
        
        # 不确定性估计器
        if use_uncertainty:
            self.uncertainty_head = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 4, **factory_kwargs),
                nn.ReLU(),
                nn.Linear(hidden_dim // 4, 1, **factory_kwargs),
                nn.Sigmoid()  # 输出0-1的不确定性分数
            )
        
        # 温度标定
        if use_temperature_scaling:
            self.temperature_scaling = TemperatureScaling()
            
        # 类别映射：细分类到粗分类
        self.fine_to_coarse_mapping = torch.tensor([
            0,  # W -> Wake
            1,  # N1 -> NREM  
            1,  # N2 -> NREM
            1,  # N3 -> NREM
            2   # REM -> REM
        ], **factory_kwargs)
        
    def forward(
        self, 
        features: Tensor, 
        stage: str = "both",
        mc_samples: int = 10
    ) -> Dict[str, Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, d_model]
            stage: "coarse", "fine", or "both"
            mc_samples: Monte Carlo采样次数
            
        Returns:
            Dict containing various outputs
        """
        batch_size = features.size(0)
        
        # 特征提取
        if len(features.shape) == 3:
            # 如果是[batch, seq_len, d_model]，取第一个token (CLS token)
            features = features[:, 0, :]
            
        shared_features = self.feature_extractor(features)
        
        results = {}
        
        # 第一阶段：粗分类
        if stage in ["coarse", "both"]:
            if self.use_uncertainty and self.training:
                # Monte Carlo Dropout采样
                coarse_logits_list = []
                for _ in range(mc_samples):
                    coarse_logits = self.coarse_classifier(shared_features)
                    coarse_logits_list.append(coarse_logits)
                
                coarse_logits_mc = torch.stack(coarse_logits_list, dim=0)  # [mc_samples, batch, classes]
                coarse_logits = coarse_logits_mc.mean(dim=0)  # 平均预测
                coarse_uncertainty = coarse_logits_mc.var(dim=0).mean(dim=-1)  # 不确定性
                
                results['coarse_logits_mc'] = coarse_logits_mc
                results['coarse_uncertainty'] = coarse_uncertainty
            else:
                coarse_logits = self.coarse_classifier(shared_features)
                
            results['coarse_logits'] = coarse_logits
            results['coarse_probs'] = F.softmax(coarse_logits, dim=-1)
            
        # 第二阶段：细分类
        if stage in ["fine", "both"]:
            # 获取粗分类信息作为额外输入
            if 'coarse_logits' in results:
                coarse_info = F.softmax(results['coarse_logits'], dim=-1)
            else:
                # 如果只进行细分类，需要先计算粗分类
                coarse_logits = self.coarse_classifier(shared_features)
                coarse_info = F.softmax(coarse_logits, dim=-1)
                results['coarse_logits'] = coarse_logits
                results['coarse_probs'] = coarse_info
            
            # 融合粗分类信息
            fine_input = torch.cat([shared_features, coarse_info], dim=-1)
            
            if self.use_uncertainty and self.training:
                # Monte Carlo Dropout采样
                fine_logits_list = []
                for _ in range(mc_samples):
                    fine_logits = self.fine_classifier(fine_input)
                    fine_logits_list.append(fine_logits)
                
                fine_logits_mc = torch.stack(fine_logits_list, dim=0)
                fine_logits = fine_logits_mc.mean(dim=0)
                fine_uncertainty = fine_logits_mc.var(dim=0).mean(dim=-1)
                
                results['fine_logits_mc'] = fine_logits_mc
                results['fine_uncertainty'] = fine_uncertainty
            else:
                fine_logits = self.fine_classifier(fine_input)
            
            results['fine_logits'] = fine_logits
            
            # 温度标定
            if self.use_temperature_scaling:
                calibrated_logits = self.temperature_scaling(fine_logits)
                results['calibrated_logits'] = calibrated_logits
                results['fine_probs'] = F.softmax(calibrated_logits, dim=-1)
            else:
                results['fine_probs'] = F.softmax(fine_logits, dim=-1)
        
        # 不确定性估计
        if self.use_uncertainty:
            uncertainty_score = self.uncertainty_head(shared_features)
            results['uncertainty_score'] = uncertainty_score.squeeze(-1)
            
        return results
    
    def predict_with_uncertainty(
        self, 
        features: Tensor, 
        mc_samples: int = 20,
        uncertainty_threshold: float = 0.5
    ) -> Dict[str, Tensor]:
        """带不确定性的预测"""
        self.train()  # 启用MC Dropout
        
        with torch.no_grad():
            results = self.forward(features, stage="both", mc_samples=mc_samples)
            
            # 基于不确定性决定是否使用粗分类结果
            fine_uncertainty = results.get('fine_uncertainty', torch.zeros(features.size(0)))
            use_coarse = fine_uncertainty > uncertainty_threshold
            
            # 混合预测结果
            final_probs = results['fine_probs'].clone()
            coarse_probs = results['coarse_probs']
            
            # 将粗分类结果映射到细分类空间
            for i in range(features.size(0)):
                if use_coarse[i]:
                    coarse_pred = coarse_probs[i].argmax()
                    # 根据粗分类结果调整细分类概率
                    fine_probs_adjusted = self._map_coarse_to_fine(coarse_probs[i])
                    final_probs[i] = fine_probs_adjusted
            
            results['final_probs'] = final_probs
            results['use_coarse_mask'] = use_coarse
            
        return results
    
    def _map_coarse_to_fine(self, coarse_probs: Tensor) -> Tensor:
        """将粗分类概率映射到细分类空间"""
        fine_probs = torch.zeros(self.num_fine_classes, device=coarse_probs.device)
        
        # Wake -> W
        fine_probs[0] = coarse_probs[0] 
        
        # NREM -> N1, N2, N3 (根据先验分布分配)
        nrem_prob = coarse_probs[1]
        fine_probs[1] = nrem_prob * 0.05  # N1 (很少)
        fine_probs[2] = nrem_prob * 0.65  # N2 (最多)  
        fine_probs[3] = nrem_prob * 0.30  # N3 (中等, 包含S4)
        
        # REM -> REM
        fine_probs[4] = coarse_probs[2]
        
        return fine_probs
    
    def compute_consistency_loss(self, results: Dict[str, Tensor], labels: Tensor) -> Tensor:
        """计算粗细分类一致性损失"""
        if 'coarse_logits' not in results or 'fine_logits' not in results:
            return torch.tensor(0.0, device=labels.device)
        
        # 将细分类标签映射到粗分类
        if self.fine_to_coarse_mapping.device != labels.device:
            self.fine_to_coarse_mapping = self.fine_to_coarse_mapping.to(labels.device)
        coarse_labels = self.fine_to_coarse_mapping[labels]
        
        # 从细分类预测推导粗分类预测
        fine_probs = F.softmax(results['fine_logits'], dim=-1)
        predicted_coarse_probs = torch.zeros(
            fine_probs.size(0), self.num_coarse_classes, 
            device=fine_probs.device
        )
        
        predicted_coarse_probs[:, 0] = fine_probs[:, 0]  # Wake
        predicted_coarse_probs[:, 1] = fine_probs[:, 1:4].sum(dim=-1)  # NREM (N1,N2,N3)
        predicted_coarse_probs[:, 2] = fine_probs[:, 4]  # REM
        
        # 一致性损失：KL散度
        coarse_probs = F.softmax(results['coarse_logits'], dim=-1)
        consistency_loss = F.kl_div(
            predicted_coarse_probs.log(), coarse_probs, reduction='batchmean'
        )
        
        return consistency_loss


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance
    Especially useful for N1 and REM which are minority classes
    """
    
    def __init__(self, alpha: Optional[Tensor] = None, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs: Tensor, targets: Tensor) -> Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if self.alpha.device != targets.device:
                self.alpha = self.alpha.to(targets.device)
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss
            
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss