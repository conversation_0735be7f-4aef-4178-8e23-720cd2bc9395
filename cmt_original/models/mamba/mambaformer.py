"""
MambaFormer: Interleaved Mamba-Transformer Architecture
Combines Mamba's efficient long-range modeling with Transformer's attention
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional, List
from ..model_blocks import PositionalEncoding
from .mamba_block import MambaBlock

class MambaFormerLayer(nn.Module):
    """
    Single MambaFormer layer with interleaved Mamba and Transformer blocks
    
    Args:
        d_model: Model dimension
        nhead: Number of attention heads
        use_mamba: Whether to use Mamba block
        mamba_config: Configuration for Mamba block
        dropout: Dropout rate
    """
    
    def __init__(
        self,
        d_model: int = 64,
        nhead: int = 8,
        use_mamba: bool = True,
        mamba_config: dict = None,
        dropout: float = 0.1,
        layer_norm_eps: float = 1e-5,
        device=None,
        dtype=None
    ):
        super().__init__()
        
        factory_kwargs = {'device': device, 'dtype': dtype}
        
        self.d_model = d_model
        self.use_mamba = use_mamba
        
        # Mamba block for long-range dependencies
        if use_mamba:
            mamba_config = mamba_config or {}
            mamba_config.setdefault('d_model', d_model)
            mamba_config.setdefault('d_state', 16)
            mamba_config.setdefault('d_conv', 4)
            
            self.mamba_block = MambaBlock(**mamba_config)
            self.mamba_norm = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        else:
            self.mamba_block = None
            self.mamba_norm = None
        
        # Transformer block for local attention
        self.self_attn = nn.MultiheadAttention(
            d_model, nhead, dropout=dropout, batch_first=True, **factory_kwargs
        )
        self.transformer_norm1 = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        
        # Feed-forward network
        self.linear1 = nn.Linear(d_model, d_model * 4, **factory_kwargs)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(d_model * 4, d_model, **factory_kwargs)
        self.transformer_norm2 = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        
        # Activation
        self.activation = nn.GELU()
        
    def forward(
        self, 
        src: Tensor, 
        src_mask: Optional[Tensor] = None,
        src_key_padding_mask: Optional[Tensor] = None,
        cache_params=None
    ) -> Tensor:
        """
        Forward pass through MambaFormer layer
        
        Args:
            src: Input tensor [batch, seq_len, d_model]
            src_mask: Attention mask
            src_key_padding_mask: Key padding mask
            cache_params: Cache for inference
            
        Returns:
            output: [batch, seq_len, d_model]
        """
        # Stage 1: Mamba block for long-range dependencies
        if self.use_mamba and self.mamba_block is not None:
            src2 = self.mamba_block(src, cache_params=cache_params)
            src = src + src2
            src = self.mamba_norm(src)
        
        # Stage 2: Self-attention for local dependencies
        src2 = self.self_attn(
            src, src, src,
            attn_mask=src_mask,
            key_padding_mask=src_key_padding_mask,
            need_weights=False
        )[0]
        src = src + src2
        src = self.transformer_norm1(src)
        
        # Stage 3: Feed-forward network
        src2 = self.linear2(self.dropout(self.activation(self.linear1(src))))
        src = src + src2
        src = self.transformer_norm2(src)
        
        return src


class MambaFormerEncoder(nn.Module):
    """
    MambaFormer encoder with multiple layers
    
    Args:
        encoder_layer: MambaFormer layer
        num_layers: Number of layers
        norm: Final normalization layer
    """
    
    def __init__(
        self,
        d_model: int = 64,
        num_layers: int = 6,
        nhead: int = 8,
        use_mamba: bool = True,
        mamba_config: dict = None,
        dropout: float = 0.1,
        layer_norm_eps: float = 1e-5,
        device=None,
        dtype=None
    ):
        super().__init__()
        
        factory_kwargs = {'device': device, 'dtype': dtype}
        
        self.d_model = d_model
        self.num_layers = num_layers
        
        # Create encoder layers
        self.layers = nn.ModuleList([
            MambaFormerLayer(
                d_model=d_model,
                nhead=nhead,
                use_mamba=use_mamba,
                mamba_config=mamba_config,
                dropout=dropout,
                layer_norm_eps=layer_norm_eps,
                **factory_kwargs
            )
            for _ in range(num_layers)
        ])
        
        # Final normalization
        self.norm = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        
    def forward(
        self,
        src: Tensor,
        mask: Optional[Tensor] = None,
        src_key_padding_mask: Optional[Tensor] = None,
        cache_params_list: Optional[List] = None
    ) -> Tensor:
        """
        Forward pass through MambaFormer encoder
        
        Args:
            src: Input tensor [batch, seq_len, d_model]
            mask: Attention mask
            src_key_padding_mask: Key padding mask
            cache_params_list: Cache list for inference
            
        Returns:
            output: [batch, seq_len, d_model]
        """
        output = src
        
        for i, layer in enumerate(self.layers):
            cache_params = cache_params_list[i] if cache_params_list else None
            output = layer(
                output,
                src_mask=mask,
                src_key_padding_mask=src_key_padding_mask,
                cache_params=cache_params
            )
        
        output = self.norm(output)
        return output


class AdaptiveMambaFormerEncoder(nn.Module):
    """
    Adaptive MambaFormer that adjusts Mamba usage based on sequence length
    Short sequences use more attention, long sequences use more Mamba
    """
    
    def __init__(
        self,
        d_model: int = 64,
        num_layers: int = 6,
        nhead: int = 8,
        dropout: float = 0.1,
        short_seq_threshold: int = 100,
        mamba_config: dict = None,
        **kwargs
    ):
        super().__init__()
        
        self.short_seq_threshold = short_seq_threshold
        self.num_layers = num_layers
        
        # Short sequence encoder (more attention)
        self.short_encoder = MambaFormerEncoder(
            d_model=d_model,
            num_layers=num_layers,
            nhead=nhead,
            use_mamba=False,  # Pure attention for short sequences
            dropout=dropout,
            **kwargs
        )
        
        # Long sequence encoder (more Mamba)
        self.long_encoder = MambaFormerEncoder(
            d_model=d_model,
            num_layers=num_layers,
            nhead=nhead,
            use_mamba=True,
            mamba_config=mamba_config,
            dropout=dropout,
            **kwargs
        )
        
    def forward(
        self,
        src: Tensor,
        mask: Optional[Tensor] = None,
        src_key_padding_mask: Optional[Tensor] = None,
        force_mode: Optional[str] = None
    ) -> Tensor:
        """
        Adaptive forward pass
        
        Args:
            src: Input tensor [batch, seq_len, d_model]
            mask: Attention mask
            src_key_padding_mask: Key padding mask
            force_mode: Force 'short' or 'long' mode
            
        Returns:
            output: [batch, seq_len, d_model]
        """
        seq_len = src.size(1)
        
        # Determine which encoder to use
        if force_mode == "short" or (force_mode is None and seq_len <= self.short_seq_threshold):
            return self.short_encoder(src, mask, src_key_padding_mask)
        else:
            return self.long_encoder(src, mask, src_key_padding_mask)