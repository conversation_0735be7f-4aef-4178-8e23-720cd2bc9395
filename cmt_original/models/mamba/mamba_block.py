"""
Mamba Block Implementation for Time Series
Selective State Space Model with efficient O(n) complexity
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from typing import Optional, Tuple
from einops import rearrange, repeat

class MambaBlock(nn.Module):
    """
    Mamba State Space Model Block
    
    Implements selective state space mechanism with:
    - Input-dependent selection mechanism
    - Efficient parallel scanning
    - Hardware-efficient implementation
    
    Args:
        d_model: Model dimension
        d_state: State dimension  
        d_conv: Convolution kernel size
        expand: Expansion factor
        dt_rank: Rank for dt projection
        dt_min: Minimum dt value
        dt_max: Maximum dt value
    """
    
    def __init__(
        self,
        d_model: int,
        d_state: int = 16,
        d_conv: int = 4, 
        expand: int = 2,
        dt_rank: str = "auto",
        dt_min: float = 0.001,
        dt_max: float = 0.1,
        dt_init: str = "random",
        dt_scale: float = 1.0,
        bias: bool = False,
        conv_bias: bool = True,
        use_fast_path: bool = True,
    ):
        super().__init__()
        
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        self.d_inner = int(self.expand * self.d_model)
        self.dt_rank = math.ceil(self.d_model / 16) if dt_rank == "auto" else dt_rank
        self.use_fast_path = use_fast_path
        
        # Input projection
        self.in_proj = nn.Linear(self.d_model, self.d_inner * 2, bias=bias)
        
        # Convolution layer for local dependencies
        self.conv1d = nn.Conv1d(
            in_channels=self.d_inner,
            out_channels=self.d_inner,
            bias=conv_bias,
            kernel_size=d_conv,
            groups=self.d_inner,
            padding=d_conv - 1,
        )
        
        # Activation function
        self.activation = "silu"
        self.act = nn.SiLU()
        
        # State space parameters
        self.x_proj = nn.Linear(self.d_inner, self.dt_rank + self.d_state * 2, bias=False)
        
        # dt (time step) projection
        self.dt_proj = nn.Linear(self.dt_rank, self.d_inner, bias=True)
        
        # Initialize dt
        dt = torch.exp(
            torch.rand(self.d_inner) * (math.log(dt_max) - math.log(dt_min)) + math.log(dt_min)
        ).clamp(min=dt_min)
        
        inv_dt = dt + torch.log(-torch.expm1(-dt))
        with torch.no_grad():
            self.dt_proj.weight.copy_(inv_dt.unsqueeze(1))
            
        # A parameter (state transition matrix)
        A = repeat(torch.arange(1, self.d_state + 1), "n -> d n", d=self.d_inner)
        self.A_log = nn.Parameter(torch.log(A))
        self.A_log._no_weight_decay = True
        
        # D parameter (skip connection)
        self.D = nn.Parameter(torch.ones(self.d_inner))
        self.D._no_weight_decay = True
        
        # Output projection
        self.out_proj = nn.Linear(self.d_inner, self.d_model, bias=bias)
        
    def forward(self, hidden_states: Tensor, cache_params=None):
        """
        Forward pass of Mamba block
        
        Args:
            hidden_states: [batch, seq_len, d_model]
            cache_params: Optional cache for inference
            
        Returns:
            output: [batch, seq_len, d_model]
        """
        batch_size, seq_len, _ = hidden_states.shape
        
        # Input projection and split
        xz = self.in_proj(hidden_states)  # [B, L, 2*d_inner]
        x, z = xz.chunk(2, dim=-1)  # Each: [B, L, d_inner]
        
        # Convolution for local dependencies
        x = rearrange(x, "b l d -> b d l")
        if cache_params is not None:
            # Inference mode with caching
            if cache_params.get("conv_state") is not None:
                conv_state = cache_params["conv_state"]
                x = torch.cat([conv_state, x], dim=-1)
                cache_params["conv_state"] = x[:, :, -(self.d_conv-1):]
                x = x[:, :, -(self.d_conv):]
            x = self.conv1d(x)[:, :, :seq_len]
        else:
            # Training mode
            x = self.conv1d(x)[..., :seq_len]
            
        x = rearrange(x, "b d l -> b l d")
        x = self.act(x)
        
        # State space computation
        y = self.selective_scan(
            x,
            cache_params=cache_params,
        )
        
        # Gated output
        y = y * self.act(z)
        
        # Output projection
        output = self.out_proj(y)
        
        return output
    
    def selective_scan(
        self,
        x: Tensor,
        cache_params=None,
    ) -> Tensor:
        """
        Selective scan mechanism - core of Mamba
        
        Args:
            x: Input tensor [B, L, d_inner]
            cache_params: Optional cache for inference
            
        Returns:
            y: Output tensor [B, L, d_inner] 
        """
        batch_size, seq_len, d_inner = x.shape
        
        # Project to get dt, B, C
        x_dbl = self.x_proj(x)  # [B, L, dt_rank + 2*d_state]
        
        dt, B, C = torch.split(
            x_dbl, [self.dt_rank, self.d_state, self.d_state], dim=-1
        )
        
        # dt projection and activation
        dt = self.dt_proj(dt)  # [B, L, d_inner]
        dt = F.softplus(dt)
        
        # Get A matrix
        A = -torch.exp(self.A_log.float())  # [d_inner, d_state]
        
        # Selective scan computation
        if self.use_fast_path and not self.training:
            y = self._selective_scan_inference(x, dt, A, B, C, self.D, cache_params)
        else:
            y = self._selective_scan_training(x, dt, A, B, C, self.D)
            
        return y
    
    def _selective_scan_training(
        self, x: Tensor, dt: Tensor, A: Tensor, B: Tensor, C: Tensor, D: Tensor
    ) -> Tensor:
        """Training mode selective scan using parallel scan"""
        batch_size, seq_len, d_inner = x.shape
        
        # Discretization
        dt = dt.unsqueeze(-1)  # [B, L, d_inner, 1]
        A_disc = torch.exp(A.unsqueeze(0).unsqueeze(0) * dt)  # [B, L, d_inner, d_state]
        B_disc = dt * B.unsqueeze(2)  # [B, L, d_inner, d_state] 
        
        # State computation using parallel scan
        states = torch.zeros(batch_size, d_inner, self.d_state, device=x.device, dtype=x.dtype)
        outputs = []
        
        for i in range(seq_len):
            # Update state
            states = A_disc[:, i] * states + B_disc[:, i] * x[:, i].unsqueeze(-1)
            
            # Compute output
            y = (states * C[:, i].unsqueeze(1)).sum(dim=-1) + D * x[:, i]
            outputs.append(y)
        
        return torch.stack(outputs, dim=1)
    
    def _selective_scan_inference(
        self, x: Tensor, dt: Tensor, A: Tensor, B: Tensor, C: Tensor, D: Tensor, cache_params
    ) -> Tensor:
        """Inference mode with state caching"""
        batch_size, seq_len, d_inner = x.shape
        
        # Get cached state or initialize
        if cache_params is not None and "ssm_state" in cache_params:
            states = cache_params["ssm_state"]
        else:
            states = torch.zeros(
                batch_size, d_inner, self.d_state, 
                device=x.device, dtype=x.dtype
            )
        
        outputs = []
        for i in range(seq_len):
            # Discretization for current timestep
            dt_i = dt[:, i].unsqueeze(-1)
            A_disc = torch.exp(A.unsqueeze(0) * dt_i) 
            B_disc = dt_i * B[:, i].unsqueeze(1)
            
            # State update
            states = A_disc * states + B_disc * x[:, i].unsqueeze(-1)
            
            # Output computation
            y = (states * C[:, i].unsqueeze(1)).sum(dim=-1) + D * x[:, i]
            outputs.append(y)
        
        # Update cache
        if cache_params is not None:
            cache_params["ssm_state"] = states
            
        return torch.stack(outputs, dim=1)