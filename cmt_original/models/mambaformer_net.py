"""
MambaFormer Network for Sleep Stage Classification
Integrates wICA preprocessing, MambaFormer encoding, enhanced cross-modal attention, and progressive classification
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Tuple, Optional, Dict
import numpy as np

# Import our custom modules
from .model_blocks import Window_Embedding, PositionalEncoding
from .mamba import MambaFormerEncoder, AdaptiveMambaFormerEncoder
from .enhanced_cross_attention import EnhancedCrossModalAttention, MultiModalFusionBlock
from .progressive_classifier import ProgressiveClassifier, FocalLoss
from preprocessing import wICAProcessor

class MambaFormerSleepNet(nn.Module):
    """
    Complete MambaFormer network for sleep stage classification
    
    Pipeline:
    1. wICA preprocessing (optional, can be done offline)
    2. Multi-scale CNN embedding  
    3. MambaFormer encoding for each modality
    4. Enhanced cross-modal attention
    5. Progressive classification with uncertainty
    
    Args:
        d_model: Model dimension
        num_mambaformer_layers: Number of MambaFormer layers
        window_size: CNN embedding window size
        use_wica_online: Whether to apply wICA online
        use_progressive: Whether to use progressive classifier
        **kwargs: Additional arguments
    """
    
    def __init__(
        self,
        d_model: int = 64,
        num_mambaformer_layers: int = 4,
        nhead: int = 8,
        window_size: int = 25,
        use_wica_online: bool = False,
        use_progressive: bool = True,
        use_adaptive_mambaformer: bool = True,
        dropout: float = 0.1,
        device=None,
        dtype=None
    ):
        super().__init__()
        
        factory_kwargs = {'device': device, 'dtype': dtype}
        
        self.d_model = d_model
        self.use_wica_online = use_wica_online
        self.use_progressive = use_progressive
        self.use_adaptive_mambaformer = use_adaptive_mambaformer
        
        # 1. wICA Preprocessing (optional online processing)
        if use_wica_online:
            self.wica_processor = wICAProcessor(
                n_components=min(20, d_model//2),
                random_state=42
            )
        
        # 2. Multi-scale CNN Embedding
        self.eeg_embedding = Window_Embedding(
            in_channels=1, 
            window_size=window_size, 
            emb_size=d_model
        )
        self.eog_embedding = Window_Embedding(
            in_channels=1,
            window_size=window_size, 
            emb_size=d_model
        )
        
        # 3. MambaFormer Encoders
        mamba_config = {
            'd_state': 16,
            'd_conv': 4,
            'expand': 2
        }
        
        if use_adaptive_mambaformer:
            self.eeg_encoder = AdaptiveMambaFormerEncoder(
                d_model=d_model,
                num_layers=num_mambaformer_layers,
                nhead=nhead,
                dropout=dropout,
                mamba_config=mamba_config,
                **factory_kwargs
            )
            self.eog_encoder = AdaptiveMambaFormerEncoder(
                d_model=d_model,
                num_layers=num_mambaformer_layers,
                nhead=nhead,
                dropout=dropout,
                mamba_config=mamba_config,
                **factory_kwargs
            )
        else:
            self.eeg_encoder = MambaFormerEncoder(
                d_model=d_model,
                num_layers=num_mambaformer_layers,
                nhead=nhead,
                use_mamba=True,
                mamba_config=mamba_config,
                dropout=dropout,
                **factory_kwargs
            )
            self.eog_encoder = MambaFormerEncoder(
                d_model=d_model,
                num_layers=num_mambaformer_layers,
                nhead=nhead,
                use_mamba=True,
                mamba_config=mamba_config,
                dropout=dropout,
                **factory_kwargs
            )
        
        # 4. Enhanced Cross-Modal Attention
        self.cross_modal_attention = EnhancedCrossModalAttention(
            d_model=d_model,
            nhead=nhead,
            lightweight=True,
            dropout=dropout,
            use_dynamic_weight=True,
            **factory_kwargs
        )
        
        # 5. Classification Head
        if use_progressive:
            self.classifier = ProgressiveClassifier(
                d_model=d_model * 2,  # EEG + cross-modal features
                hidden_dim=d_model * 4,
                num_coarse_classes=3,  # Wake, NREM, REM
                num_fine_classes=5,    # W, N1, N2, N3, REM
                dropout_rate=dropout,
                use_uncertainty=True,
                use_temperature_scaling=True,
                **factory_kwargs
            )
        else:
            # Simple classifier
            self.classifier = nn.Sequential(
                nn.Flatten(),
                nn.Linear(d_model * 2, d_model),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(d_model, 5)  # 5-class classification
            )
        
        # Loss functions
        self.setup_loss_functions()
        
    def setup_loss_functions(self):
        """Setup loss functions for different stages"""
        # Class weights for focal loss (addressing class imbalance)
        # Typical sleep distribution: W(20%), N1(5%), N2(50%), N3(15%), REM(10%)
        class_weights = torch.tensor([0.8, 2.0, 0.6, 1.2, 1.5])  # Inverse frequency weighting
        
        # Register class_weights as buffer so it moves with the model
        self.register_buffer('class_weights_buffer', class_weights)
        
        # Use the registered buffer for FocalLoss (will have correct device)
        self.focal_loss = FocalLoss(alpha=self.class_weights_buffer, gamma=2.0)
        self.ce_loss = nn.CrossEntropyLoss()
        
    def forward(
        self,
        eeg: Tensor,
        eog: Tensor,
        stage: str = "both",
        return_attention: bool = False,
        finetune: bool = False
    ) -> Dict[str, Tensor]:
        """
        Forward pass through the complete network
        
        Args:
            eeg: EEG data [batch, 1, seq_len] or [batch, seq_len]
            eog: EOG data [batch, 1, seq_len] or [batch, seq_len] 
            stage: Classification stage ("coarse", "fine", "both")
            return_attention: Whether to return attention weights
            finetune: Whether in finetune mode (for compatibility)
            
        Returns:
            Dictionary with outputs and optionally attention weights
        """
        batch_size = eeg.size(0)
        
        # Ensure correct input dimensions
        if len(eeg.shape) == 2:
            eeg = eeg.unsqueeze(1)  # Add channel dimension
        if len(eog.shape) == 2:
            eog = eog.unsqueeze(1)
            
        # 1. Optional online wICA preprocessing
        if self.use_wica_online and hasattr(self, 'wica_processor'):
            # Apply wICA (this might be slow, better to preprocess offline)
            eeg_clean_list = []
            eog_clean_list = []
            
            for i in range(batch_size):
                try:
                    eeg_clean, _, _ = self.wica_processor.fit_transform(eeg[i].cpu().numpy())
                    eog_clean, _, _ = self.wica_processor.fit_transform(eog[i].cpu().numpy())
                    eeg_clean_list.append(torch.from_numpy(eeg_clean).to(eeg.device))
                    eog_clean_list.append(torch.from_numpy(eog_clean).to(eog.device))
                except:
                    # Fallback to original data if processing fails
                    eeg_clean_list.append(eeg[i])
                    eog_clean_list.append(eog[i])
                    
            eeg = torch.stack(eeg_clean_list)
            eog = torch.stack(eog_clean_list)
        
        # 2. Multi-scale CNN Embedding
        eeg_embedded = self.eeg_embedding(eeg)  # [batch, seq_len, d_model]
        eog_embedded = self.eog_embedding(eog)  # [batch, seq_len, d_model]
        
        # 3. MambaFormer Encoding
        eeg_encoded = self.eeg_encoder(eeg_embedded)  # [batch, seq_len, d_model]
        eog_encoded = self.eog_encoder(eog_embedded)  # [batch, seq_len, d_model]
        
        # 4. Enhanced Cross-Modal Attention
        cross_modal_features, attention_weights = self.cross_modal_attention(
            eeg_encoded, eog_encoded
        )
        
        # 5. Feature Fusion for Classification
        # Use CLS tokens (first token) for classification
        eeg_cls = eeg_encoded[:, 0, :]  # [batch, d_model]
        cross_cls = cross_modal_features[:, 0, :]  # [batch, d_model]
        
        # Concatenate features
        fused_features = torch.cat([eeg_cls, cross_cls], dim=-1)  # [batch, d_model*2]
        
        # 6. Classification
        results = {'features': fused_features}
        
        if self.use_progressive:
            # Progressive classification with uncertainty
            classifier_outputs = self.classifier(fused_features, stage=stage)
            results.update(classifier_outputs)
        else:
            # Simple classification
            logits = self.classifier(fused_features)
            results['logits'] = logits
            results['probs'] = torch.softmax(logits, dim=-1)
        
        # Return attention weights if requested
        if return_attention:
            results['attention_weights'] = attention_weights
            
        # Legacy compatibility
        if finetune:
            if 'fine_logits' in results:
                return results['fine_logits'], fused_features.unsqueeze(1), []
            elif 'logits' in results:
                return results['logits'], fused_features.unsqueeze(1), []
        
        return results
    
    def compute_loss(
        self, 
        outputs: Dict[str, Tensor], 
        labels: Tensor,
        stage: str = "both",
        loss_weights: Dict[str, float] = None
    ) -> Dict[str, Tensor]:
        """
        Compute comprehensive loss including consistency and uncertainty terms
        
        Args:
            outputs: Model outputs
            labels: Ground truth labels
            stage: Loss computation stage
            loss_weights: Weights for different loss terms
            
        Returns:
            Dictionary of losses
        """
        if loss_weights is None:
            loss_weights = {
                'coarse': 0.3,
                'fine': 1.0, 
                'consistency': 0.2,
                'uncertainty': 0.1
            }
        
        losses = {}
        total_loss = 0.0
        
        # Main classification losses
        if stage in ["coarse", "both"] and 'coarse_logits' in outputs:
            # Map fine labels to coarse labels
            coarse_labels = self._map_fine_to_coarse_labels(labels)
            coarse_loss = self.ce_loss(outputs['coarse_logits'], coarse_labels)
            losses['coarse_loss'] = coarse_loss
            total_loss += loss_weights['coarse'] * coarse_loss
            
        if stage in ["fine", "both"]:
            if 'calibrated_logits' in outputs:
                fine_loss = self.focal_loss(outputs['calibrated_logits'], labels)
            elif 'fine_logits' in outputs:
                fine_loss = self.focal_loss(outputs['fine_logits'], labels)
            elif 'logits' in outputs:
                fine_loss = self.focal_loss(outputs['logits'], labels)
            else:
                fine_loss = torch.tensor(0.0, device=labels.device)
                
            losses['fine_loss'] = fine_loss
            total_loss += loss_weights['fine'] * fine_loss
        
        # Consistency loss (if progressive classifier)
        if self.use_progressive and hasattr(self.classifier, 'compute_consistency_loss'):
            consistency_loss = self.classifier.compute_consistency_loss(outputs, labels)
            losses['consistency_loss'] = consistency_loss
            total_loss += loss_weights['consistency'] * consistency_loss
        
        # Uncertainty regularization
        if 'uncertainty_score' in outputs:
            # Encourage lower uncertainty for correct predictions
            probs = outputs.get('fine_probs', outputs.get('probs'))
            if probs is not None:
                pred_correct = (probs.argmax(dim=-1) == labels).float()
                uncertainty_loss = (outputs['uncertainty_score'] * pred_correct).mean()
                losses['uncertainty_loss'] = uncertainty_loss
                total_loss += loss_weights['uncertainty'] * uncertainty_loss
        
        losses['total_loss'] = total_loss
        return losses
    
    def _map_fine_to_coarse_labels(self, fine_labels: Tensor) -> Tensor:
        """Map 5-class labels to 3-class labels"""
        # W(0)->0, N1(1)->1, N2(2)->1, N3(3)->1, REM(4)->2
        mapping = torch.tensor([0, 1, 1, 1, 2], device=fine_labels.device)
        return mapping[fine_labels]
    
    def predict(
        self, 
        eeg: Tensor, 
        eog: Tensor,
        with_uncertainty: bool = True,
        mc_samples: int = 10
    ) -> Dict[str, Tensor]:
        """Prediction with optional uncertainty estimation"""
        self.eval()
        
        with torch.no_grad():
            if with_uncertainty and self.use_progressive:
                # Enable uncertainty estimation
                outputs = self.forward(eeg, eog, stage="both")
                uncertainty_outputs = self.classifier.predict_with_uncertainty(
                    outputs['features'], 
                    mc_samples=mc_samples
                )
                outputs.update(uncertainty_outputs)
            else:
                outputs = self.forward(eeg, eog, stage="fine")
        
        return outputs
    
    def get_attention_visualization(self, eeg: Tensor, eog: Tensor) -> Tensor:
        """Get attention weights for visualization"""
        self.eval()
        with torch.no_grad():
            outputs = self.forward(eeg, eog, return_attention=True)
            return outputs.get('attention_weights')


# Compatibility wrapper for the original interface
class Epoch_Cross_Transformer_Network(MambaFormerSleepNet):
    """
    Wrapper to maintain compatibility with original Cross-Modal Transformer interface
    """
    
    def __init__(self, d_model=64, dim_feedforward=512, window_size=25):
        super().__init__(
            d_model=d_model,
            window_size=window_size,
            use_progressive=False  # Use simple classifier for compatibility
        )
        
        # Add simple MLP head for backward compatibility
        self.mlp = nn.Sequential(
            nn.Flatten(),
            nn.Linear(d_model * 2, 5)
        )
    
    def forward(self, eeg: Tensor, eog: Tensor, finetune: bool = False):
        """Original interface forward pass"""
        results = super().forward(eeg, eog, finetune=finetune)
        
        if finetune:
            if isinstance(results, tuple):
                return results
            else:
                logits = results.get('logits', results.get('fine_logits'))
                features = results.get('features')
                return logits, features.unsqueeze(1), []
        else:
            features = results.get('features')
            return features.unsqueeze(1)