"""
Enhanced Cross-Modal Attention with EEG Self-Query
Lightweight cross-modal fusion for EEG-EOG sleep stage classification
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional, Tuple
import torch.nn.functional as F
from einops import rearrange

class EnhancedCrossModalAttention(nn.Module):
    """
    增强的跨模态注意力机制
    
    Features:
    - EEG自查询机制：EEG作为Query，EOG作为Key/Value
    - 轻量化设计：减少参数量的同时保持性能
    - 多尺度融合：不同时间尺度的特征融合
    - 动态权重：基于信号质量的自适应权重
    
    Args:
        d_model: 模型维度
        nhead: 注意力头数
        lightweight: 是否使用轻量化设计
        dropout: Dropout率
        temperature: 注意力温度参数
    """
    
    def __init__(
        self,
        d_model: int = 64,
        nhead: int = 8,
        lightweight: bool = True,
        dropout: float = 0.1,
        temperature: float = 1.0,
        use_dynamic_weight: bool = True,
        layer_norm_eps: float = 1e-5,
        device=None,
        dtype=None
    ):
        super().__init__()
        
        factory_kwargs = {'device': device, 'dtype': dtype}
        
        self.d_model = d_model
        self.nhead = nhead
        self.lightweight = lightweight
        self.temperature = temperature
        self.use_dynamic_weight = use_dynamic_weight
        
        # EEG Query投影 - 保持d_model维度以匹配注意力机制
        self.eeg_query_proj = nn.Linear(d_model, d_model, **factory_kwargs)
        
        # 轻量化设计通过减少key维度实现
        if lightweight:
            self.key_dim = d_model // 2
        else:
            self.key_dim = d_model
            
        # EOG的Key和Value投影
        self.eog_key_proj = nn.Linear(d_model, self.key_dim, **factory_kwargs)
        self.eog_value_proj = nn.Linear(d_model, d_model, **factory_kwargs)
        
        # 跨模态注意力 - 需要保证key和value维度匹配
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=d_model,  # query的维度
            kdim=self.key_dim,  # key的维度
            vdim=d_model,  # value的维度
            num_heads=nhead,
            dropout=dropout,
            batch_first=True,
            **factory_kwargs
        )
        
        # 输出投影
        self.output_proj = nn.Linear(d_model, d_model, **factory_kwargs)
        
        # 动态权重网络
        if use_dynamic_weight:
            self.weight_net = nn.Sequential(
                nn.Linear(d_model * 2, d_model // 2, **factory_kwargs),
                nn.ReLU(),
                nn.Linear(d_model // 2, 2, **factory_kwargs),
                nn.Softmax(dim=-1)
            )
        
        # 多尺度融合
        self.multiscale_conv = nn.ModuleList([
            nn.Conv1d(d_model, d_model, kernel_size=k, padding=k//2, groups=d_model//8)
            for k in [3, 5, 7]
        ])
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        self.norm2 = nn.LayerNorm(d_model, eps=layer_norm_eps, **factory_kwargs)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化参数
        self._init_parameters()
        
    def _init_parameters(self):
        """初始化参数"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(
        self, 
        eeg_features: Tensor, 
        eog_features: Tensor,
        attention_mask: Optional[Tensor] = None
    ) -> Tuple[Tensor, Tensor]:
        """
        前向传播
        
        Args:
            eeg_features: EEG特征 [batch, seq_len, d_model]
            eog_features: EOG特征 [batch, seq_len, d_model]
            attention_mask: 注意力掩码
            
        Returns:
            fused_features: 融合后的特征 [batch, seq_len, d_model]
            attention_weights: 注意力权重
        """
        batch_size, seq_len, _ = eeg_features.shape
        
        # Stage 1: 多尺度特征提取
        eeg_multiscale = self._extract_multiscale_features(eeg_features)
        eog_multiscale = self._extract_multiscale_features(eog_features)
        
        # Stage 2: EEG自查询跨模态注意力
        eeg_query = self.eeg_query_proj(eeg_multiscale)  # [B, L, d_model]
        eog_key = self.eog_key_proj(eog_multiscale)      # [B, L, key_dim] 
        eog_value = self.eog_value_proj(eog_multiscale)  # [B, L, d_model]
        
        # 应用温度缩放
        if self.temperature != 1.0:
            eeg_query = eeg_query / self.temperature
            
        # 跨模态注意力计算
        cross_attended, attention_weights = self.cross_attention(
            query=eeg_query,
            key=eog_key, 
            value=eog_value,
            attn_mask=attention_mask,
            need_weights=True
        )
        
        # Stage 3: 残差连接和归一化
        cross_attended = self.dropout(cross_attended)
        cross_output = self.norm1(cross_attended + eog_features)
        
        # Stage 4: 动态权重融合
        if self.use_dynamic_weight:
            fused_features = self._dynamic_fusion(eeg_features, cross_output)
        else:
            # 简单融合
            fused_features = eeg_features + cross_output
            
        # Stage 5: 输出投影和最终归一化
        output = self.output_proj(fused_features)
        output = self.norm2(output + fused_features)
        
        return output, attention_weights
    
    def _extract_multiscale_features(self, features: Tensor) -> Tensor:
        """多尺度特征提取"""
        # 转换为卷积格式 [B, D, L]
        x = rearrange(features, 'b l d -> b d l')
        
        multiscale_features = []
        for conv in self.multiscale_conv:
            ms_feat = conv(x)
            multiscale_features.append(ms_feat)
        
        # 融合多尺度特征
        combined = torch.stack(multiscale_features, dim=0).mean(dim=0)
        
        # 转换回原格式 [B, L, D]
        return rearrange(combined, 'b d l -> b l d')
    
    def _dynamic_fusion(self, eeg_features: Tensor, cross_output: Tensor) -> Tensor:
        """动态权重融合"""
        # 计算融合权重
        combined_features = torch.cat([eeg_features, cross_output], dim=-1)
        weights = self.weight_net(combined_features)  # [B, L, 2]
        
        # 加权融合
        w_eeg = weights[..., 0:1]  # [B, L, 1]
        w_cross = weights[..., 1:2]  # [B, L, 1]
        
        fused_features = w_eeg * eeg_features + w_cross * cross_output
        
        return fused_features
    
    def get_attention_maps(
        self, 
        eeg_features: Tensor, 
        eog_features: Tensor
    ) -> Tensor:
        """获取注意力图用于可视化"""
        with torch.no_grad():
            _, attention_weights = self.forward(eeg_features, eog_features)
            return attention_weights


class MultiModalFusionBlock(nn.Module):
    """
    多模态融合块
    整合EEG、EOG和可能的EMG信号
    """
    
    def __init__(
        self,
        d_model: int = 64,
        nhead: int = 8,
        num_modalities: int = 2,
        fusion_strategy: str = "hierarchical",
        **kwargs
    ):
        super().__init__()
        
        self.num_modalities = num_modalities
        self.fusion_strategy = fusion_strategy
        
        if fusion_strategy == "hierarchical":
            # 层次化融合：先EEG-EOG，再加入其他模态
            self.eeg_eog_fusion = EnhancedCrossModalAttention(
                d_model=d_model, nhead=nhead, **kwargs
            )
            
            if num_modalities > 2:
                self.secondary_fusion = EnhancedCrossModalAttention(
                    d_model=d_model, nhead=nhead, **kwargs
                )
                
        elif fusion_strategy == "parallel":
            # 并行融合：所有模态同时处理
            self.parallel_attention = nn.MultiheadAttention(
                d_model, nhead, batch_first=True
            )
            
    def forward(self, *modal_features):
        """多模态融合前向传播"""
        if self.fusion_strategy == "hierarchical":
            if len(modal_features) >= 2:
                # EEG-EOG融合
                fused_features, _ = self.eeg_eog_fusion(
                    modal_features[0], modal_features[1]
                )
                
                # 加入第三模态
                if len(modal_features) > 2 and hasattr(self, 'secondary_fusion'):
                    fused_features, _ = self.secondary_fusion(
                        fused_features, modal_features[2]
                    )
                    
                return fused_features
            else:
                return modal_features[0]
                
        elif self.fusion_strategy == "parallel":
            # 并行处理所有模态
            all_features = torch.cat(modal_features, dim=1)
            fused_features, _ = self.parallel_attention(
                all_features, all_features, all_features
            )
            return fused_features


class CrossModalityGate(nn.Module):
    """跨模态门控机制"""
    
    def __init__(self, d_model: int = 64):
        super().__init__()
        
        self.gate_net = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.Sigmoid()
        )
        
    def forward(self, eeg_features: Tensor, eog_features: Tensor) -> Tensor:
        """门控融合"""
        combined = torch.cat([eeg_features, eog_features], dim=-1)
        gate = self.gate_net(combined)
        
        # 门控融合
        gated_eeg = gate * eeg_features
        gated_eog = (1 - gate) * eog_features
        
        return gated_eeg + gated_eog