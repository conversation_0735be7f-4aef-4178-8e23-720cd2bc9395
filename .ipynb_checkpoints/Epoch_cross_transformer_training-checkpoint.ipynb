{"cells": [{"cell_type": "markdown", "metadata": {"id": "Ij4Cglx7hnfe"}, "source": ["\n", "## Get Requirements"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gLKQsBn-hgDN", "outputId": "dbe85e77-445f-4c6d-8e06-95495a5b7263"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.10.0\n"]}], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "import torch\n", "from torchvision import transforms, datasets\n", "import torch.nn as nn\n", "from torch import optim as optim\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "import torch\n", "from torch.utils import data\n", "import math\n", "import random\n", "from torch.utils.data import Dataset, DataLoader\n", "import time\n", "import glob\n", "import scipy.signal\n", "import os\n", "from einops import rearrange, reduce, repeat\n", "from einops.layers.torch import Rearrange, Reduce\n", "print(torch.__version__)\n", "\n", "\n", "from datasets.sleep_edf import split_data, SleepEDF_MultiChan_Dataset\n", "from models.epoch_cmt import Epoch_Cross_Transformer_Network\n", "from models.sequence_cmt import Seq_Cross_Transformer_Network \n", "from utils.metrics import accuracy, kappa, g_mean, plot_confusion_matrix, confusion_matrix, AverageMeter "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6YSLmONqhyVM", "outputId": "eaa51650-2d1e-4039-a2fe-0e2eb0202362"}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "device \n", "project_path = \"mmsm/Experiments/testing\"\n", "if not os.path.isdir(project_path):\n", "        os.makedirs(project_path)\n", "        print(f\"Project directory created at {project_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "mp-EzMvdOlRO"}, "source": ["### <PERSON> (Ignore this block)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EheutgQjObJ4", "outputId": "e20c8f8a-d449-457b-b93a-22fd43cce685"}, "outputs": [], "source": ["is_neptune = False\n", "if is_neptune:\n", "    import neptune.new as neptune\n", "\n", "    run = neptune.init(\n", "        project=\"jathurshan0330/V2-Cros\",\n", "        api_token=\"eyJhcGlfYWRkcmVzcyI6Imh0dHBzOi8vYXBwLm5lcHR1bmUuYWkiLCJhcGlfdXJsIjoiaHR0cHM6Ly9hcHAubmVwdHVuZS5haSIsImFwaV9rZXkiOiJmYmRmNjE0Zi0xMDRkLTRlNzUtYmIxNi03NzM2ODBlZDc5NTMifQ==\",\n", "    )  # your credentials\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "pV_9lNe_x0Hd"}, "outputs": [], "source": [" if is_neptune:\n", "    experiment = \"V2-Cros-76\"   #Change This\n", "    !mkdir \"/home/<USER>/fyp_g15_sleep_monitoring/Experiments/Sleep_edfx/V2-Cros-76\"  # Change This"]}, {"cell_type": "markdown", "metadata": {"id": "ARyAHPPkl0JS"}, "source": ["## Data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['/home/<USER>/Sleep_EDF_Dataset/x1.h5' '/home/<USER>/Sleep_EDF_Dataset/x2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/x3.h5' '/home/<USER>/Sleep_EDF_Dataset/x4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/x5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/mean1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/mean2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/mean3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/mean4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/mean5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/std1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/std2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/std3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/std4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/std5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_m1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_m2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_m3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_m4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_m5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_s1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_s2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_s3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_s4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_s5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/y1.h5' '/home/<USER>/Sleep_EDF_Dataset/y2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/y3.h5' '/home/<USER>/Sleep_EDF_Dataset/y4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/y5.h5']\n"]}], "source": ["train_data_list = [0,1,2,3]  #4\n", "val_data_list = [4]  #4\n", "data_path = \"/home/<USER>/Sleep_EDF_Dataset\"\n", "\n", "eeg_list = glob.glob(f'{data_path}/x*.h5')\n", "eeg_list.sort()\n", "[train_eeg_list, val_eeg_list] = split_data(eeg_list,train_data_list,val_data_list)\n", "print(train_eeg_list)\n", "print(val_eeg_list)\n", "\n", "mean_eeg_list = glob.glob(f'{data_path}/mean*.h5')\n", "mean_eeg_list.sort()\n", "[train_mean_eeg_list, val_mean_eeg_list] = split_data(mean_eeg_list,train_data_list,val_data_list)\n", "print(train_mean_eeg_list)\n", "print(val_mean_eeg_list)\n", "\n", "sd_eeg_list = glob.glob(f'{data_path}/std*.h5')\n", "sd_eeg_list.sort()\n", "[train_sd_eeg_list, val_sd_eeg_list] = split_data(sd_eeg_list,train_data_list,val_data_list)\n", "print(train_sd_eeg_list)\n", "print(val_sd_eeg_list)\n", "\n", "#########################################################################################################################\n", "\n", "eog_list = glob.glob(f'{data_path}/eog*.h5')\n", "eog_list.sort()\n", "[train_eog_list, val_eog_list] = split_data(eog_list,train_data_list,val_data_list)\n", "print(train_eog_list)\n", "print(val_eog_list)\n", "\n", "mean_eog_list = glob.glob(f'{data_path}/eog_m*.h5')\n", "mean_eog_list.sort()\n", "[train_mean_eog_list, val_mean_eog_list] = split_data(mean_eog_list,train_data_list,val_data_list)\n", "print(train_mean_eog_list)\n", "print(val_mean_eog_list)\n", "\n", "sd_eog_list = glob.glob(f'{data_path}/eog_s*.h5')\n", "sd_eog_list.sort()\n", "[train_sd_eog_list, val_sd_eog_list] = split_data(sd_eog_list,train_data_list,val_data_list)\n", "print(train_sd_eog_list)\n", "print(val_sd_eog_list)\n", "\n", "\n", "label_list = glob.glob(f'{data_path}/y*.h5')\n", "label_list.sort()\n", "[train_label_list, val_label_list] = split_data(label_list,train_data_list,val_data_list)\n", "print(train_label_list)\n", "print(val_label_list)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ELCDezM1Qkjs", "outputId": "3b314d02-e1cb-4164-8457-93800f41952f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading from /home/<USER>/Sleep_EDF_Dataset/x1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 43754\n", "Shape of each data : (43754, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 43754\n", "Shape of each data : (43754, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 40155\n", "Shape of each data : (40155, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 40155\n", "Shape of each data : (40155, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 35373\n", "Shape of each data : (35373, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 35373\n", "Shape of each data : (35373, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 40411\n", "Shape of each data : (40411, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 40411\n", "Shape of each data : (40411, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Labels count: [54292 17253 56137 10814 21197]\n", "Shape of EEG : (159693, 1, 3000) , EOG : (159693, 1, 3000)\n", "Shape of Labels : <PERSON>.<PERSON>ze([159693])\n", "Reading Subject wise mean and sd\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Shapes of Mean  : EEG: (159693,), EOG : (159693,)\n", "Shapes of Sd  : EEG: (159693,), EOG : (159693,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 36657\n", "Shape of each data : (36657, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 36657\n", "Shape of each data : (36657, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Labels count: [12530  4269 12995  2225  4638]\n", "Shape of EEG : (36657, 1, 3000) , EOG : (36657, 1, 3000)\n", "Shape of Labels : torch.Size([36657])\n", "Reading Subject wise mean and sd\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Shapes of Mean  : EEG: (36657,), EOG : (36657,)\n", "Shapes of Sd  : EEG: (36657,), EOG : (36657,)\n"]}], "source": ["train_dataset = SleepEDF_MultiChan_Dataset(eeg_file = train_eeg_list , \n", "                                           eog_file = train_eog_list, \n", "                                           label_file = train_label_list, \n", "                                           device = device, mean_eeg_l = train_mean_eeg_list, sd_eeg_l = train_sd_eeg_list, \n", "                                           mean_eog_l = train_mean_eog_list, sd_eog_l = train_sd_eog_list, \n", "                                           sub_wise_norm = True, \n", "                                           transform=transforms.Compose([\n", "                                               transforms.To<PERSON><PERSON><PERSON>(),\n", "                                                ]) )\n", "\n", "val_dataset = SleepEDF_MultiChan_Dataset(eeg_file = val_eeg_list ,\n", "                                         eog_file = val_eog_list, \n", "                                         label_file = val_label_list, \n", "                                         device = device, mean_eeg_l = val_mean_eeg_list, sd_eeg_l = val_sd_eeg_list,\n", "                                         mean_eog_l = val_mean_eog_list, sd_eog_l = val_sd_eog_list,\n", "                                         sub_wise_norm = True,\n", "                                         transform=transforms.Compose([\n", "                                               transforms.To<PERSON><PERSON><PERSON>(),\n", "                                                ]) )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "IpXQ66ipml-q"}, "outputs": [], "source": ["batch_size = 64\n", "train_data_loader = data.DataLoader(train_dataset, batch_size = batch_size, shuffle = True)\n", "val_data_loader = data.DataLoader(val_dataset, batch_size = 32, shuffle = True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "O3c5Neaqmu6-", "outputId": "2dd0adc7-2e82-48aa-81b8-ac34d19b7f74"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EEG batch shape: torch.Size([64, 1, 1, 3000])\n", "EOG batch shape: torch.Size([64, 1, 1, 3000])\n", "Labels batch shape: torch.Size([64])\n"]}, {"data": {"image/png": "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******************************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*************************************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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["EEG batch shape: torch.Size([32, 1, 1, 3000])\n", "EOG batch shape: torch.Size([32, 1, 1, 3000])\n", "Labels batch shape: torch.Size([32])\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["EEG Minimum :-5.9242736939407035\n", "EEG Maximum :7.373364648818294\n", "EOG Minimum :-9.597861970446278\n", "EOG Maximum :9.308358448596389\n", "EEG Mean :0.0014975272005030812\n", "EEG Standard Deviation :0.9427593302541899\n", "EOG Mean :-0.00022797915340683813\n", "EOG Standard Deviation :1.0004089582760118\n"]}], "source": ["eeg_data, eog_data, label = next(iter(train_data_loader))\n", "print(f\"EEG batch shape: {eeg_data.size()}\")\n", "print(f\"EOG batch shape: {eog_data.size()}\")\n", "print(f\"Labels batch shape: {label.size()}\")\n", "\n", "t = np.arange(0,30,1/100)\n", "plt.figure(figsize = (15,5))\n", "plt.plot(t,eeg_data[0].squeeze(),label=\"EEG1\")\n", "plt.plot(t,eog_data[0].squeeze()+5,label=\"E0G\")\n", "plt.title(f\"Label {label[0].squeeze()}\")\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "eeg_data, eog_data, label = next(iter(val_data_loader))\n", "print(f\"EEG batch shape: {eeg_data.size()}\")\n", "print(f\"EOG batch shape: {eog_data.size()}\")\n", "print(f\"Labels batch shape: {label.size()}\")\n", "\n", "t = np.arange(0,30,1/100)\n", "plt.figure(figsize = (10,10))\n", "plt.plot(t,eeg_data[0].squeeze())\n", "plt.plot(t,eog_data[0].squeeze()+5)\n", "plt.title(f\"Label {label[0].squeeze()}\")\n", "plt.show()\n", "\n", "\n", "print(f\"EEG Minimum :{eeg_data.min()}\")\n", "print(f\"EEG Maximum :{eeg_data.max()}\")\n", "print(f\"EOG Minimum :{eog_data.min()}\")\n", "print(f\"EOG Maximum :{eog_data.max()}\")\n", "\n", "\n", "print(f\"EEG Mean :{torch.mean(eeg_data)}\")\n", "print(f\"EEG Standard Deviation :{torch.std(eeg_data)}\")\n", "print(f\"EOG Mean :{torch.mean(eog_data)}\")\n", "print(f\"EOG Standard Deviation :{torch.std(eog_data)}\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "MAv7VEk-z9Ch"}, "source": ["### Classification Model Cross Transformer"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BBI0JVRcz9Ck", "outputId": "5a21dd30-94a8-4779-b291-d4cb1a97eae2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["weights: tensor([1., 2., 1., 2., 2.])\n", "torch.<PERSON><PERSON>([32, 5]) torch.<PERSON><PERSON>([32, 1, 512]) 3\n"]}], "source": ["import torch.optim as optim\n", "\n", "d_model = 256 \n", "dim_feedforward=1024 \n", "window_size = 50\n", "Net = Epoch_Cross_Transformer_Network(d_model = d_model, dim_feedforward=dim_feedforward,\n", "                                window_size = window_size ).to(device)\n", "# Net = torch.load(\"/home/<USER>/fyp_g15_sleep_monitoring/Experiments/Sleep_edfx/Pretraining/VCROSPRE-23/Net_2_best_loss1.pth.tar\")\n", "\n", "lr = 0.001\n", "beta_1 =  0.9    \n", "beta_2 =  0.999    \n", "eps = 1e-9\n", "n_epochs = 1000\n", "weights = torch.tensor([1., 2., 1., 2., 2.])\n", "print(f\"weights: {weights}\")\n", "criterion = nn.CrossEntropyLoss(weight=weights)\n", "optimizer = torch.optim.Adam(Net.parameters(), lr=lr, betas=(beta_1, beta_2),eps = eps, weight_decay = 0.0001)\n", "lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.5) \n", "pred,cls_outs,feat_list = Net(eeg_data.float().to(device), eog_data.float().to(device),finetune = True)\n", "print(pred.shape,cls_outs.shape,len(feat_list))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "wCowMlFVz9Cl"}, "outputs": [], "source": ["if is_neptune:\n", "    parameters = {\n", "        \"Experiment\" : \"Training test\",\n", "        'Model Type' : \"Epoch Cross-Modal Transformer\",\n", "        'd_model' : d_model,\n", "        'dim_feedforward' : dim_feedforward,\n", "        'window_size ':window_size ,\n", "        'Batch Size': batch_size,\n", "        'Loss': f\"Weighted Categorical Loss,{weights}\",  # Check this every time\n", "        'Optimizer' : \"<PERSON>\",        # Check this every time   \n", "        'Learning Rate': lr,\n", "        'eps' : eps,\n", "        \"LR Schduler\": \"StepLR\",\n", "        'Beta 1': beta_1,\n", "        'Beta 2': beta_2,\n", "        'n_epochs': n_epochs,\n", "        'val_set' : val_data_list[0]+1,\n", "        'threshold': 0.5\n", "    }\n", "    run['model/parameters'] = parameters\n", "    run['model/model_architecture'] = Net"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_4Pl9HUrz9Cn", "outputId": "378a408d-cc32-4248-a2b6-a036a00d34e2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===========================================================Training Epoch : [1/1000] ===========================================================================================================>\n", "Epoch: [1/1000][0/2496]\tTrain_Loss 1.87022 (1.87022)\tTrain_Acc 0.12500 (0.12500)\tTrain_G-Mean 0.45154(0.45154)\tTrain_Kappa 0.05160(0.05160)\tTrain_MF1 0.10677(0.10677)\tTrain_Precision 0.31053(0.31053)\tTrain_Sensitivity 0.25072(0.25072)\tTrain_Specificity 0.81319(0.81319)\tTime 0.261s (0.261s)\tSpeed 245.1 samples/s\tData 0.067s (0.067s)\t\n", "Epoch: [1/1000][1000/2496]\tTrain_Loss 0.74587 (0.89480)\tTrain_Acc 0.78125 (0.68073)\tTrain_G-Mean 0.81036(0.74815)\tTrain_Kappa 0.69586(0.56398)\tTrain_MF1 0.73147(0.57726)\tTrain_Precision 0.79762(0.59870)\tTrain_Sensitivity 0.69931(0.61316)\tTrain_Specificity 0.93905(0.91799)\tTime 0.057s (0.062s)\tSpeed 1124.2 samples/s\tData 0.020s (0.023s)\t\n", "Epoch: [1/1000][2000/2496]\tTrain_Loss 0.97272 (0.81506)\tTrain_Acc 0.59375 (0.70983)\tTrain_G-Mean 0.75776(0.77300)\tTrain_Kappa 0.46201(0.60375)\tTrain_MF1 0.55691(0.61351)\tTrain_Precision 0.62569(0.63202)\tTrain_Sensitivity 0.64071(0.64822)\tTrain_Specificity 0.89620(0.92608)\tTime 0.059s (0.062s)\tSpeed 1077.2 samples/s\tData 0.013s (0.021s)\t\n", "1145\n", "===========================================================Epoch : [1/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.7934994372085501, Training Accuracy      : 0.7167675010786488, Training G-Mean      : 0.7791252070558804\n", "Training Kappa      : 0.6133388971399777,Training MF1     : 0.6225812481411808, Training Precision      : 0.6395939850030492, Training Sensitivity      : 0.6569650195055425, Training Specificity      : 0.9280362317690827\n", "Validation Results : \n", "Validation Loss   : 0.734643474845362, Validation Accuracy : 0.7418386202648598, Validation G-Mean      : 0.7692625510089218\n", "Validation Kappa     : 0.640705718195114, Validation MF1      : 0.6211541020459335, Validation Precision      : 0.6520398353523921,  Validation Sensitivity      : 0.6382277861913666, Validation Specificity      : 0.934101085575463\n", "Class wise sensitivity W: 0.8722635373395151, S1: 0.4982687506636192, S2: 0.7660116318476762, S3: 0.3933682396981937, R: 0.6612267714078306\n", "Class wise specificity W: 0.9626264493606893, S1: 0.8719638159553834, S2: 0.8985624964532636, S3: 0.9961577244037941, R: 0.9411949417041858\n", "Class wise F1  W: 0.8923830140323539, S1: 0.3810985653578299, S2: 0.776777462523437, S3: 0.44169953592888767, R: 0.6138119323871523\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [2/1000] ===========================================================================================================>\n", "Epoch: [2/1000][0/2496]\tTrain_Loss 0.65947 (0.65947)\tTrain_Acc 0.70312 (0.70312)\tTrain_G-Mean 0.77234(0.77234)\tTrain_Kappa 0.59372(0.59372)\tTrain_MF1 0.64642(0.64642)\tTrain_Precision 0.68077(0.68077)\tTrain_Sensitivity 0.64416(0.64416)\tTrain_Specificity 0.92603(0.92603)\tTime 0.118s (0.118s)\tSpeed 540.3 samples/s\tData 0.056s (0.056s)\t\n", "Epoch: [2/1000][1000/2496]\tTrain_Loss 0.68213 (0.68200)\tTrain_Acc 0.76562 (0.75553)\tTrain_G-Mean 0.83643(0.81542)\tTrain_Kappa 0.68892(0.66686)\tTrain_MF1 0.74499(0.67500)\tTrain_Precision 0.74983(0.68514)\tTrain_Sensitivity 0.74636(0.71003)\tTrain_Specificity 0.93737(0.93872)\tTime 0.059s (0.058s)\tSpeed 1085.8 samples/s\tData 0.021s (0.020s)\t\n", "Epoch: [2/1000][2000/2496]\tTrain_Loss 0.68070 (0.67450)\tTrain_Acc 0.73438 (0.75800)\tTrain_G-Mean 0.80406(0.81713)\tTrain_Kappa 0.63539(0.67008)\tTrain_MF1 0.61256(0.67720)\tTrain_Precision 0.62356(0.68638)\tTrain_Sensitivity 0.69154(0.71245)\tTrain_Specificity 0.93488(0.93933)\tTime 0.081s (0.063s)\tSpeed 792.9 samples/s\tData 0.044s (0.026s)\t\n", "1145\n", "===========================================================Epoch : [2/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.6688209760169952, Training Accuracy      : 0.7597791081114398, Training G-Mean      : 0.8182356730171811\n", "Training Kappa      : 0.6722851673371929,Training MF1     : 0.6791106010960318, Training Precision      : 0.6880176751916185, Training Sensitivity      : 0.7140330850027308, Training Specificity      : 0.9397838659489006\n", "Validation Results : \n", "Validation Loss   : 0.6116265666526441, Validation Accuracy : 0.7722673493481162, Validation G-Mean      : 0.8127745400355896\n", "Validation Kappa     : 0.6843572467728171, Validation MF1      : 0.6779628639341863, Validation Precision      : 0.6944125988070468,  Validation Sensitivity      : 0.7050900727120372, Validation Specificity      : 0.9426543095661063\n", "Class wise sensitivity W: 0.8263135107191861, S1: 0.4861791742149656, S2: 0.8213746449138065, S3: 0.6498815777596171, R: 0.7417014559526094\n", "Class wise specificity W: 0.9847297459892785, S1: 0.8969987443604395, S2: 0.9047879219887353, S3: 0.9816766231471955, R: 0.9450785123448929\n", "Class wise F1  W: 0.8851739550708269, S1: 0.4001608847089463, S2: 0.816696621854684, S3: 0.6167882254662522, R: 0.6709946325702193\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [3/1000] ===========================================================================================================>\n", "Epoch: [3/1000][0/2496]\tTrain_Loss 0.68829 (0.68829)\tTrain_Acc 0.79688 (0.79688)\tTrain_G-Mean 0.86462(0.86462)\tTrain_Kappa 0.72304(0.72304)\tTrain_MF1 0.75580(0.75580)\tTrain_Precision 0.75111(0.75111)\tTrain_Sensitivity 0.79000(0.79000)\tTrain_Specificity 0.94629(0.94629)\tTime 0.457s (0.457s)\tSpeed 140.2 samples/s\tData 0.396s (0.396s)\t\n", "Epoch: [3/1000][1000/2496]\tTrain_Loss 0.61258 (0.63917)\tTrain_Acc 0.78125 (0.76848)\tTrain_G-Mean 0.80210(0.82625)\tTrain_Kappa 0.69545(0.68432)\tTrain_MF1 0.68937(0.68985)\tTrain_Precision 0.70833(0.69491)\tTrain_Sensitivity 0.68182(0.72598)\tTrain_Specificity 0.94360(0.94221)\tTime 0.059s (0.060s)\tSpeed 1077.3 samples/s\tData 0.016s (0.018s)\t\n", "Epoch: [3/1000][2000/2496]\tTrain_Loss 0.72708 (0.62934)\tTrain_Acc 0.75000 (0.77341)\tTrain_G-Mean 0.81873(0.82959)\tTrain_Kappa 0.66904(0.69071)\tTrain_MF1 0.70455(0.69636)\tTrain_Precision 0.71792(0.70136)\tTrain_Sensitivity 0.71439(0.73099)\tTrain_Specificity 0.93831(0.94340)\tTime 0.062s (0.061s)\tSpeed 1036.3 samples/s\tData 0.013s (0.018s)\t\n", "1145\n", "===========================================================Epoch : [3/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.6266008289530873, Training Accuracy      : 0.7735824434479783, Training G-Mean      : 0.8300558875789593\n", "Training Kappa      : 0.691106484295362,Training MF1     : 0.6975579154044882, Training Precision      : 0.7026918360551541, Training Sensitivity      : 0.7317706871640458, Training Specificity      : 0.943426515735114\n", "Validation Results : \n", "Validation Loss   : 0.6147655823753975, Validation Accuracy : 0.7618266220100606, Validation G-Mean      : 0.8145912733163545\n", "Validation Kappa     : 0.6738536105515875, Validation MF1      : 0.6773190591009719, Validation Precision      : 0.6941400478959717,  Validation Sensitivity      : 0.7093278193036929, Validation Specificity      : 0.9414070862438044\n", "Class wise sensitivity W: 0.8775815302424822, S1: 0.5946418243422974, S2: 0.7169176676354483, S3: 0.6355605438923753, R: 0.7219375304058584\n", "Class wise specificity W: 0.9735170581473938, S1: 0.8679577062267283, S2: 0.937502543911676, S3: 0.9827620245607319, R: 0.9452960983724911\n", "Class wise F1  W: 0.9051773224877645, S1: 0.4377930638287704, S2: 0.7746421469807833, S3: 0.6094471274194085, R: 0.659535634788127\n", "===========================================================Training Epoch : [4/1000] ===========================================================================================================>\n", "Epoch: [4/1000][0/2496]\tTrain_Loss 0.52837 (0.52837)\tTrain_Acc 0.78125 (0.78125)\tTrain_G-Mean 0.85004(0.85004)\tTrain_Kappa 0.70400(0.70400)\tTrain_MF1 0.70307(0.70307)\tTrain_Precision 0.68778(0.68778)\tTrain_Sensitivity 0.76044(0.76044)\tTrain_Specificity 0.95019(0.95019)\tTime 0.263s (0.263s)\tSpeed 243.2 samples/s\tData 0.058s (0.058s)\t\n", "Epoch: [4/1000][1000/2496]\tTrain_Loss 0.59823 (0.60220)\tTrain_Acc 0.73438 (0.78120)\tTrain_G-Mean 0.80585(0.83571)\tTrain_Kappa 0.65284(0.70142)\tTrain_MF1 0.64833(0.70737)\tTrain_Precision 0.65815(0.71095)\tTrain_Sensitivity 0.69100(0.74027)\tTrain_Specificity 0.93978(0.94529)\tTime 0.062s (0.052s)\tSpeed 1031.4 samples/s\tData 0.017s (0.014s)\t\n", "Epoch: [4/1000][2000/2496]\tTrain_Loss 0.65470 (0.60030)\tTrain_Acc 0.71875 (0.78269)\tTrain_G-Mean 0.82449(0.83733)\tTrain_Kappa 0.61905(0.70326)\tTrain_MF1 0.65075(0.70927)\tTrain_Precision 0.62464(0.71308)\tTrain_Sensitivity 0.72966(0.74282)\tTrain_Specificity 0.93164(0.94569)\tTime 0.053s (0.054s)\tSpeed 1198.1 samples/s\tData 0.016s (0.015s)\t\n", "1145\n", "===========================================================Epoch : [4/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5989857678277752, Training Accuracy      : 0.7830369938054733, Training G-Mean      : 0.8379344973116333\n", "Training Kappa      : 0.7038926220319502,Training MF1     : 0.7101983770435584, Training Precision      : 0.7139920269515231, Training Sensitivity      : 0.7438050598556836, Training Specificity      : 0.94580512970495\n", "Validation Results : \n", "Validation Loss   : 0.64558053867043, Validation Accuracy : 0.745792590596448, Validation G-Mean      : 0.8169099192785606\n", "Validation Kappa     : 0.6549087530318981, Validation MF1      : 0.6593473173014782, Validation Precision      : 0.6638541615056112,  Validation Sensitivity      : 0.7152521635420346, Validation Specificity      : 0.9380095628855755\n", "Class wise sensitivity W: 0.8195392465248158, S1: 0.4202696424023123, S2: 0.7300963908251875, S3: 0.803065529044386, R: 0.803290008913472\n", "Class wise specificity W: 0.9890829235783423, S1: 0.9118320031731957, S2: 0.9212329510410835, S3: 0.9565546879281548, R: 0.9113452487070939\n", "Class wise F1  W: 0.8850110457725758, S1: 0.36759427248339377, S2: 0.7710696265595538, S3: 0.6310213447969414, R: 0.6420402968949256\n", "===========================================================Training Epoch : [5/1000] ===========================================================================================================>\n", "Epoch: [5/1000][0/2496]\tTrain_Loss 0.57192 (0.57192)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.90972(0.90972)\tTrain_Kappa 0.76865(0.76865)\tTrain_MF1 0.79267(0.79267)\tTrain_Precision 0.76061(0.76061)\tTrain_Sensitivity 0.86210(0.86210)\tTrain_Specificity 0.95997(0.95997)\tTime 0.234s (0.234s)\tSpeed 273.3 samples/s\tData 0.050s (0.050s)\t\n", "Epoch: [5/1000][1000/2496]\tTrain_Loss 0.59892 (0.57624)\tTrain_Acc 0.78125 (0.79197)\tTrain_G-Mean 0.87621(0.84827)\tTrain_Kappa 0.69689(0.71611)\tTrain_MF1 0.69568(0.72479)\tTrain_Precision 0.66234(0.72631)\tTrain_Sensitivity 0.80889(0.76025)\tTrain_Specificity 0.94912(0.94816)\tTime 0.060s (0.057s)\tSpeed 1065.8 samples/s\tData 0.020s (0.019s)\t\n", "Epoch: [5/1000][2000/2496]\tTrain_Loss 0.38151 (0.57599)\tTrain_Acc 0.87500 (0.79147)\tTrain_G-Mean 0.90096(0.84660)\tTrain_Kappa 0.82454(0.71528)\tTrain_MF1 0.79271(0.72221)\tTrain_Precision 0.78615(0.72415)\tTrain_Sensitivity 0.83647(0.75738)\tTrain_Specificity 0.97043(0.94798)\tTime 0.068s (0.064s)\tSpeed 938.0 samples/s\tData 0.028s (0.023s)\t\n", "1145\n", "===========================================================Epoch : [5/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5745610238936467, Training Accuracy      : 0.7923441930165187, Training G-Mean      : 0.8469881804883744\n", "Training Kappa      : 0.7164545630848405,Training MF1     : 0.7234721805667513, Training Precision      : 0.725571183819706, Training Sensitivity      : 0.7578965716434141, Training Specificity      : 0.9481854987211358\n", "Validation Results : \n", "Validation Loss   : 0.590813431414218, Validation Accuracy : 0.7921671286315574, Validation G-Mean      : 0.8230259105590859\n", "Validation Kappa     : 0.7107098522236586, Validation MF1      : 0.6990426883725592, Validation Precision      : 0.7226916635860852,  Validation Sensitivity      : 0.7191583606871209, Validation Specificity      : 0.9472064890578569\n", "Class wise sensitivity W: 0.9057561595116402, S1: 0.5791802082802404, S2: 0.7860449327588289, S3: 0.5913051632322358, R: 0.733505339652664\n", "Class wise specificity W: 0.9636037951468589, S1: 0.8875263799859591, S2: 0.9263108673312069, S3: 0.9897781155392346, R: 0.9688132872860261\n", "Class wise F1  W: 0.9134345056706906, S1: 0.4461965247156524, S2: 0.8110622515281042, S3: 0.6010820389730144, R: 0.7234381209753362\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [6/1000] ===========================================================================================================>\n", "Epoch: [6/1000][0/2496]\tTrain_Loss 0.58011 (0.58011)\tTrain_Acc 0.75000 (0.75000)\tTrain_G-Mean 0.81917(0.81917)\tTrain_Kappa 0.64871(0.64871)\tTrain_MF1 0.66961(0.66961)\tTrain_Precision 0.66008(0.66008)\tTrain_Sensitivity 0.71611(0.71611)\tTrain_Specificity 0.93705(0.93705)\tTime 0.086s (0.086s)\tSpeed 744.9 samples/s\tData 0.047s (0.047s)\t\n", "Epoch: [6/1000][1000/2496]\tTrain_Loss 0.51948 (0.56216)\tTrain_Acc 0.85938 (0.79609)\tTrain_G-Mean 0.89448(0.84845)\tTrain_Kappa 0.80736(0.72155)\tTrain_MF1 0.78950(0.72752)\tTrain_Precision 0.77636(0.73010)\tTrain_Sensitivity 0.82935(0.75987)\tTrain_Specificity 0.96473(0.94908)\tTime 0.056s (0.059s)\tSpeed 1145.8 samples/s\tData 0.020s (0.021s)\t\n", "Epoch: [6/1000][2000/2496]\tTrain_Loss 0.70464 (0.55923)\tTrain_Acc 0.78125 (0.79664)\tTrain_G-Mean 0.81222(0.85020)\tTrain_Kappa 0.68693(0.72244)\tTrain_MF1 0.68650(0.72920)\tTrain_Precision 0.68484(0.73145)\tTrain_Sensitivity 0.70000(0.76279)\tTrain_Specificity 0.94242(0.94931)\tTime 0.078s (0.062s)\tSpeed 819.5 samples/s\tData 0.032s (0.024s)\t\n", "1145\n", "===========================================================Epoch : [6/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5616378366397933, Training Accuracy      : 0.7956494814780571, Training G-Mean      : 0.8494403410726832\n", "Training Kappa      : 0.7209398992380873,Training MF1     : 0.728042628618484, Training Precision      : 0.7302507192069565, Training Sensitivity      : 0.7616770204807367, Training Specificity      : 0.9490224041044726\n", "Validation Results : \n", "Validation Loss   : 0.5512592012085423, Validation Accuracy : 0.7934824453341547, Validation G-Mean      : 0.832575344699121\n", "Validation Kappa     : 0.7137159840194547, Validation MF1      : 0.7052451559984455, Validation Precision      : 0.7152865180794479,  Validation Sensitivity      : 0.7351453496653572, Validation Specificity      : 0.948403637810735\n", "Class wise sensitivity W: 0.8908997796748945, S1: 0.5255057485757906, S2: 0.7789887197406712, S3: 0.7090937436979271, R: 0.7712387566375066\n", "Class wise specificity W: 0.9748900284629842, S1: 0.8994716664363368, S2: 0.929667462853237, S3: 0.9774164825833905, R: 0.9605725487177285\n", "Class wise F1  W: 0.914570566247271, S1: 0.43226050117409043, S2: 0.8088774735962949, S3: 0.6440988537476741, R: 0.7264183852268972\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [7/1000] ===========================================================================================================>\n", "Epoch: [7/1000][0/2496]\tTrain_Loss 0.68846 (0.68846)\tTrain_Acc 0.76562 (0.76562)\tTrain_G-Mean 0.83914(0.83914)\tTrain_Kappa 0.67568(0.67568)\tTrain_MF1 0.69973(0.69973)\tTrain_Precision 0.69773(0.69773)\tTrain_Sensitivity 0.74889(0.74889)\tTrain_Specificity 0.94026(0.94026)\tTime 0.101s (0.101s)\tSpeed 633.5 samples/s\tData 0.054s (0.054s)\t\n", "Epoch: [7/1000][1000/2496]\tTrain_Loss 0.62766 (0.54953)\tTrain_Acc 0.78125 (0.79947)\tTrain_G-Mean 0.81545(0.85141)\tTrain_Kappa 0.70449(0.72572)\tTrain_MF1 0.71462(0.72966)\tTrain_Precision 0.72867(0.73067)\tTrain_Sensitivity 0.70481(0.76444)\tTrain_Specificity 0.94345(0.94995)\tTime 0.055s (0.059s)\tSpeed 1162.3 samples/s\tData 0.018s (0.019s)\t\n", "Epoch: [7/1000][2000/2496]\tTrain_Loss 0.41869 (0.55047)\tTrain_Acc 0.81250 (0.79939)\tTrain_G-Mean 0.87871(0.85219)\tTrain_Kappa 0.73572(0.72572)\tTrain_MF1 0.75463(0.73146)\tTrain_Precision 0.73500(0.73246)\tTrain_Sensitivity 0.80885(0.76584)\tTrain_Specificity 0.95460(0.94995)\tTime 0.069s (0.062s)\tSpeed 926.1 samples/s\tData 0.033s (0.023s)\t\n", "1145\n", "===========================================================Epoch : [7/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5498846311910222, Training Accuracy      : 0.7993621525209565, Training G-Mean      : 0.8522614014033715\n", "Training Kappa      : 0.7257230901543037,Training MF1     : 0.7317163313673335, Training Precision      : 0.7328997577403278, Training Sensitivity      : 0.765972395675877, Training Specificity      : 0.94994938712185\n", "Validation Results : \n", "Validation Loss   : 0.5972870414975842, Validation Accuracy : 0.7875170028744483, Validation G-Mean      : 0.826448103412894\n", "Validation Kappa     : 0.7051705305676282, Validation MF1      : 0.6881209274627151, Validation Precision      : 0.6935340722583972,  Validation Sensitivity      : 0.7257027283713967, Validation Specificity      : 0.9463504991294208\n", "Class wise sensitivity W: 0.8916466089443387, S1: 0.3675295446022539, S2: 0.7930419810415354, S3: 0.768606125944364, R: 0.807689381324496\n", "Class wise specificity W: 0.9756085988114641, S1: 0.9318885421461668, S2: 0.9159814847493463, S3: 0.9698758048328846, R: 0.9383980651072391\n", "Class wise F1  W: 0.9157914295142442, S1: 0.35946121804065106, S2: 0.8077821719604845, S3: 0.6576860961593675, R: 0.6998837216388285\n", "===========================================================Training Epoch : [8/1000] ===========================================================================================================>\n", "Epoch: [8/1000][0/2496]\tTrain_Loss 0.64895 (0.64895)\tTrain_Acc 0.75000 (0.75000)\tTrain_G-Mean 0.82883(0.82883)\tTrain_Kappa 0.66850(0.66850)\tTrain_MF1 0.69207(0.69207)\tTrain_Precision 0.67841(0.67841)\tTrain_Sensitivity 0.73058(0.73058)\tTrain_Specificity 0.94030(0.94030)\tTime 0.077s (0.077s)\tSpeed 831.1 samples/s\tData 0.038s (0.038s)\t\n", "Epoch: [8/1000][1000/2496]\tTrain_Loss 0.37009 (0.53808)\tTrain_Acc 0.85938 (0.80310)\tTrain_G-Mean 0.89835(0.85682)\tTrain_Kappa 0.79704(0.73087)\tTrain_MF1 0.74221(0.73791)\tTrain_Precision 0.71231(0.73721)\tTrain_Sensitivity 0.83385(0.77323)\tTrain_Specificity 0.96785(0.95091)\tTime 0.062s (0.059s)\tSpeed 1024.2 samples/s\tData 0.025s (0.020s)\t\n", "Epoch: [8/1000][2000/2496]\tTrain_Loss 0.50866 (0.53933)\tTrain_Acc 0.84375 (0.80269)\tTrain_G-Mean 0.90436(0.85674)\tTrain_Kappa 0.78007(0.73058)\tTrain_MF1 0.79329(0.73775)\tTrain_Precision 0.83273(0.73718)\tTrain_Sensitivity 0.84921(0.77318)\tTrain_Specificity 0.96310(0.95088)\tTime 0.084s (0.066s)\tSpeed 764.8 samples/s\tData 0.036s (0.026s)\t\n", "1145\n", "===========================================================Epoch : [8/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.540475372869808, Training Accuracy      : 0.8026414378390039, Training G-Mean      : 0.8562523481941551\n", "Training Kappa      : 0.7304372282501491,Training MF1     : 0.7374728864834941, Training Precision      : 0.7373073771900434, Training Sensitivity      : 0.77235163847904, Training Specificity      : 0.9508294261848692\n", "Validation Results : \n", "Validation Loss   : 0.5618200446454642, Validation Accuracy : 0.773658056154399, Validation G-Mean      : 0.8411583051708621\n", "Validation Kappa     : 0.6928721678648098, Validation MF1      : 0.6981302320385064, Validation Precision      : 0.703283197790332,  Validation Sensitivity      : 0.7523824043545935, Validation Specificity      : 0.9454877926833141\n", "Class wise sensitivity W: 0.8602985993261737, S1: 0.569894254701716, S2: 0.7078919796042313, S3: 0.8161431071697938, R: 0.807684080971057\n", "Class wise specificity W: 0.9844618727295394, S1: 0.8907495268976501, S2: 0.9441543886174706, S3: 0.9488060411462401, R: 0.9592671340256669\n", "Class wise F1  W: 0.9064479768796325, S1: 0.4491350738555973, S2: 0.773137348991726, S3: 0.6156748871060567, R: 0.7462558733595187\n", "===========================================================Training Epoch : [9/1000] ===========================================================================================================>\n", "Epoch: [9/1000][0/2496]\tTrain_Loss 0.37364 (0.37364)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.91682(0.91682)\tTrain_Kappa 0.77889(0.77889)\tTrain_MF1 0.79573(0.79573)\tTrain_Precision 0.78052(0.78052)\tTrain_Sensitivity 0.87429(0.87429)\tTrain_Specificity 0.96143(0.96143)\tTime 0.080s (0.080s)\tSpeed 795.1 samples/s\tData 0.038s (0.038s)\t\n", "Epoch: [9/1000][1000/2496]\tTrain_Loss 0.43717 (0.53699)\tTrain_Acc 0.81250 (0.80209)\tTrain_G-Mean 0.83675(0.85598)\tTrain_Kappa 0.73913(0.72998)\tTrain_MF1 0.70880(0.73709)\tTrain_Precision 0.69765(0.73812)\tTrain_Sensitivity 0.73333(0.77191)\tTrain_Specificity 0.95476(0.95078)\tTime 0.062s (0.057s)\tSpeed 1039.6 samples/s\tData 0.018s (0.017s)\t\n", "Epoch: [9/1000][2000/2496]\tTrain_Loss 0.52378 (0.53514)\tTrain_Acc 0.79688 (0.80361)\tTrain_G-Mean 0.88438(0.85620)\tTrain_Kappa 0.72118(0.73175)\tTrain_MF1 0.78707(0.73774)\tTrain_Precision 0.81298(0.73839)\tTrain_Sensitivity 0.82007(0.77206)\tTrain_Specificity 0.95374(0.95108)\tTime 0.060s (0.058s)\tSpeed 1058.8 samples/s\tData 0.023s (0.018s)\t\n", "1145\n", "===========================================================Epoch : [9/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5350196172650425, Training Accuracy      : 0.8044202454696745, Training G-Mean      : 0.8567306469209092\n", "Training Kappa      : 0.732781076754958,Training MF1     : 0.738614419080578, Training Precision      : 0.7390702289027653, Training Sensitivity      : 0.7728322844271777, Training Specificity      : 0.9512585095297055\n", "Validation Results : \n", "Validation Loss   : 0.5665972168845032, Validation Accuracy : 0.7887072040858228, Validation G-Mean      : 0.8258189481738972\n", "Validation Kappa     : 0.7074298893534138, Validation MF1      : 0.7043560279731559, Validation Precision      : 0.7302344990234315,  Validation Sensitivity      : 0.7243173488589675, Validation Specificity      : 0.9469322267523199\n", "Class wise sensitivity W: 0.8482618913392538, S1: 0.6235670767737517, S2: 0.8136213802412334, S3: 0.6040700597600787, R: 0.7320663361805272\n", "Class wise specificity W: 0.9858696641722274, S1: 0.872123451133049, S2: 0.9163670645438355, S3: 0.9887379279935548, R: 0.9715630259189306\n", "Class wise F1  W: 0.8999170338504185, S1: 0.4584359340824798, S2: 0.821096352777764, S3: 0.6107002320141901, R: 0.7316305871409271\n", "===========================================================Training Epoch : [10/1000] ===========================================================================================================>\n", "Epoch: [10/1000][0/2496]\tTrain_Loss 0.46588 (0.46588)\tTrain_Acc 0.87500 (0.87500)\tTrain_G-Mean 0.89049(0.89049)\tTrain_Kappa 0.82508(0.82508)\tTrain_MF1 0.82847(0.82847)\tTrain_Precision 0.85606(0.85606)\tTrain_Sensitivity 0.81916(0.81916)\tTrain_Specificity 0.96805(0.96805)\tTime 0.080s (0.080s)\tSpeed 795.8 samples/s\tData 0.038s (0.038s)\t\n", "Epoch: [10/1000][1000/2496]\tTrain_Loss 0.63292 (0.52566)\tTrain_Acc 0.70312 (0.80880)\tTrain_G-Mean 0.76530(0.86205)\tTrain_Kappa 0.61100(0.73883)\tTrain_MF1 0.62423(0.74670)\tTrain_Precision 0.63754(0.74610)\tTrain_Sensitivity 0.63157(0.78157)\tTrain_Specificity 0.92734(0.95237)\tTime 0.067s (0.060s)\tSpeed 953.4 samples/s\tData 0.025s (0.019s)\t\n", "Epoch: [10/1000][2000/2496]\tTrain_Loss 0.36382 (0.52789)\tTrain_Acc 0.89062 (0.80771)\tTrain_G-Mean 0.91977(0.86038)\tTrain_Kappa 0.85215(0.73725)\tTrain_MF1 0.84692(0.74497)\tTrain_Precision 0.84286(0.74419)\tTrain_Sensitivity 0.86756(0.77875)\tTrain_Specificity 0.97512(0.95209)\tTime 0.077s (0.066s)\tSpeed 833.8 samples/s\tData 0.032s (0.025s)\t\n", "1145\n", "===========================================================Epoch : [10/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5276721778791398, Training Accuracy      : 0.8080496101454635, Training G-Mean      : 0.860003694005026\n", "Training Kappa      : 0.7376421033037529,Training MF1     : 0.7446430651650109, Training Precision      : 0.74404951385879, Training Sensitivity      : 0.7780729256665868, Training Specificity      : 0.9521318675042719\n", "Validation Results : \n", "Validation Loss   : 0.5672746641889709, Validation Accuracy : 0.7709921337645006, Validation G-Mean      : 0.837062803355386\n", "Validation Kappa     : 0.6890697554459682, Validation MF1      : 0.7106196149585546, Validation Precision      : 0.7410658388575755,  Validation Sensitivity      : 0.7454750460427872, Validation Specificity      : 0.9447751218111728\n", "Class wise sensitivity W: 0.8337572099572701, S1: 0.7255104065045428, S2: 0.7253012597482866, S3: 0.697544255233768, R: 0.745262098770075\n", "Class wise specificity W: 0.9872326549747228, S1: 0.8292169763363677, S2: 0.9459264651330977, S3: 0.9821998332391443, R: 0.9792996793725312\n", "Class wise F1  W: 0.8919547210345093, S1: 0.4624665529055953, S2: 0.7868608137751957, S3: 0.6523916512259638, R: 0.7594243358515111\n", "===========================================================Training Epoch : [11/1000] ===========================================================================================================>\n", "Epoch: [11/1000][0/2496]\tTrain_Loss 0.41042 (0.41042)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.91165(0.91165)\tTrain_Kappa 0.76565(0.76565)\tTrain_MF1 0.83951(0.83951)\tTrain_Precision 0.85139(0.85139)\tTrain_Sensitivity 0.86656(0.86656)\tTrain_Specificity 0.95909(0.95909)\tTime 0.080s (0.080s)\tSpeed 796.5 samples/s\tData 0.040s (0.040s)\t\n", "Epoch: [11/1000][1000/2496]\tTrain_Loss 0.51350 (0.52392)\tTrain_Acc 0.81250 (0.80896)\tTrain_G-Mean 0.87299(0.86116)\tTrain_Kappa 0.72881(0.73896)\tTrain_MF1 0.73962(0.74560)\tTrain_Precision 0.70454(0.74562)\tTrain_Sensitivity 0.80073(0.77993)\tTrain_Specificity 0.95178(0.95242)\tTime 0.051s (0.059s)\tSpeed 1250.2 samples/s\tData 0.013s (0.018s)\t\n", "Epoch: [11/1000][2000/2496]\tTrain_Loss 0.74809 (0.52376)\tTrain_Acc 0.79688 (0.80985)\tTrain_G-Mean 0.82367(0.86135)\tTrain_Kappa 0.71681(0.74006)\tTrain_MF1 0.72332(0.74667)\tTrain_Precision 0.74154(0.74619)\tTrain_Sensitivity 0.71868(0.78010)\tTrain_Specificity 0.94400(0.95260)\tTime 0.060s (0.061s)\tSpeed 1061.3 samples/s\tData 0.023s (0.021s)\t\n", "1145\n", "===========================================================Epoch : [11/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5231798796807058, Training Accuracy      : 0.8099155764607988, Training G-Mean      : 0.8614385004614379\n", "Training Kappa      : 0.7401603821867244,Training MF1     : 0.7467879788985912, Training Precision      : 0.7464269032922733, Training Sensitivity      : 0.780211170457708, Training Specificity      : 0.9526545967524606\n", "Validation Results : \n", "Validation Loss   : 0.5810041351546168, Validation Accuracy : 0.7857589698182939, Validation G-Mean      : 0.8273266662187636\n", "Validation Kappa     : 0.7040795996241531, Validation MF1      : 0.6939684965847683, Validation Precision      : 0.7062595155291641,  Validation Sensitivity      : 0.7273301057686569, Validation Specificity      : 0.9465448407810619\n", "Class wise sensitivity W: 0.8252013040150648, S1: 0.46645821737577364, S2: 0.821021156395293, S3: 0.647856590817528, R: 0.8761132602396228\n", "Class wise specificity W: 0.9907337508276495, S1: 0.9112023469873332, S2: 0.915208771486765, S3: 0.9816144177426842, R: 0.93396491686087\n", "Class wise F1  W: 0.8905101749025298, S1: 0.4091371443467198, S2: 0.8243507901255372, S3: 0.6167895388488787, R: 0.7290548347001716\n", "===========================================================Training Epoch : [12/1000] ===========================================================================================================>\n", "Epoch: [12/1000][0/2496]\tTrain_Loss 0.55905 (0.55905)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.87230(0.87230)\tTrain_Kappa 0.76910(0.76910)\tTrain_MF1 0.80512(0.80512)\tTrain_Precision 0.82129(0.82129)\tTrain_Sensitivity 0.79606(0.79606)\tTrain_Specificity 0.95584(0.95584)\tTime 0.078s (0.078s)\tSpeed 819.7 samples/s\tData 0.040s (0.040s)\t\n", "Epoch: [12/1000][1000/2496]\tTrain_Loss 0.52300 (0.52062)\tTrain_Acc 0.81250 (0.80808)\tTrain_G-Mean 0.88271(0.86146)\tTrain_Kappa 0.74124(0.73812)\tTrain_MF1 0.79303(0.74673)\tTrain_Precision 0.77789(0.74568)\tTrain_Sensitivity 0.81704(0.78054)\tTrain_Specificity 0.95366(0.95228)\tTime 0.058s (0.057s)\tSpeed 1102.7 samples/s\tData 0.022s (0.019s)\t\n", "Epoch: [12/1000][2000/2496]\tTrain_Loss 0.54745 (0.51993)\tTrain_Acc 0.76562 (0.80953)\tTrain_G-Mean 0.80595(0.86147)\tTrain_Kappa 0.66999(0.73962)\tTrain_MF1 0.69763(0.74630)\tTrain_Precision 0.73051(0.74553)\tTrain_Sensitivity 0.69385(0.78038)\tTrain_Specificity 0.93616(0.95258)\tTime 0.081s (0.064s)\tSpeed 787.3 samples/s\tData 0.035s (0.024s)\t\n", "1145\n", "===========================================================Epoch : [12/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5196265646984849, Training Accuracy      : 0.809377696622288, Training G-Mean      : 0.8615916586903842\n", "Training Kappa      : 0.7396741595835148,Training MF1     : 0.7467433822478571, Training Precision      : 0.746046799666081, Training Sensitivity      : 0.7805799009236627, Training Specificity      : 0.9525859996867504\n", "Validation Results : \n", "Validation Loss   : 0.5729325497831765, Validation Accuracy : 0.8010840134483113, Validation G-Mean      : 0.8309037679573524\n", "Validation Kappa     : 0.7215369699903496, Validation MF1      : 0.71223157724889, Validation Precision      : 0.7345237829506296,  Validation Sensitivity      : 0.7317080469395686, Validation Specificity      : 0.9488666350824875\n", "Class wise sensitivity W: 0.8855244391719709, S1: 0.5310562672597368, S2: 0.8356421716758302, S3: 0.7201633025578388, R: 0.6861540540324664\n", "Class wise specificity W: 0.9718377518404216, S1: 0.9020618650717677, S2: 0.9087792924145337, S3: 0.9789464556942019, R: 0.9827078103915143\n", "Class wise F1  W: 0.9079750235636197, S1: 0.44156168910949434, S2: 0.8279667305020554, S3: 0.6578871549007989, R: 0.7257672881684795\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [13/1000] ===========================================================================================================>\n", "Epoch: [13/1000][0/2496]\tTrain_Loss 0.49188 (0.49188)\tTrain_Acc 0.79688 (0.79688)\tTrain_G-Mean 0.86094(0.86094)\tTrain_Kappa 0.73256(0.73256)\tTrain_MF1 0.74845(0.74845)\tTrain_Precision 0.75317(0.75317)\tTrain_Sensitivity 0.78064(0.78064)\tTrain_Specificity 0.94949(0.94949)\tTime 0.094s (0.094s)\tSpeed 684.1 samples/s\tData 0.049s (0.049s)\t\n", "Epoch: [13/1000][1000/2496]\tTrain_Loss 0.42733 (0.51752)\tTrain_Acc 0.84375 (0.80960)\tTrain_G-Mean 0.90601(0.86160)\tTrain_Kappa 0.79241(0.73990)\tTrain_MF1 0.81959(0.74661)\tTrain_Precision 0.80975(0.74551)\tTrain_Sensitivity 0.85282(0.78039)\tTrain_Specificity 0.96252(0.95267)\tTime 0.066s (0.060s)\tSpeed 968.0 samples/s\tData 0.029s (0.022s)\t\n", "Epoch: [13/1000][2000/2496]\tTrain_Loss 0.39399 (0.51555)\tTrain_Acc 0.84375 (0.81040)\tTrain_G-Mean 0.86040(0.86248)\tTrain_Kappa 0.78037(0.74102)\tTrain_MF1 0.76394(0.74837)\tTrain_Precision 0.76325(0.74733)\tTrain_Sensitivity 0.77130(0.78189)\tTrain_Specificity 0.95979(0.95283)\tTime 0.072s (0.062s)\tSpeed 888.3 samples/s\tData 0.031s (0.023s)\t\n", "1145\n", "===========================================================Epoch : [13/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5149745486366252, Training Accuracy      : 0.8114670973557693, Training G-Mean      : 0.8632497209093167\n", "Training Kappa      : 0.7424397726015741,Training MF1     : 0.7494391221194897, Training Precision      : 0.7485811279244874, Training Sensitivity      : 0.7830653170326676, Training Specificity      : 0.9530968763889401\n", "Validation Results : \n", "Validation Loss   : 0.5564994145490321, Validation Accuracy : 0.8012171491633302, Validation G-Mean      : 0.8320260159142453\n", "Validation Kappa     : 0.7225974110466374, Validation MF1      : 0.7086652022797826, Validation Precision      : 0.7240441658239912,  Validation Sensitivity      : 0.7334723090067056, Validation Specificity      : 0.9489580737774709\n", "Class wise sensitivity W: 0.8495563832201051, S1: 0.4661119474361392, S2: 0.8566966220918543, S3: 0.6573776303941666, R: 0.8376189618912667\n", "Class wise specificity W: 0.9860898492431973, S1: 0.9202166543997187, S2: 0.8977492216057802, S3: 0.9845246667429207, R: 0.9562099768957334\n", "Class wise F1  W: 0.9015063253372751, S1: 0.4165526561400029, S2: 0.8324422896376455, S3: 0.6340914958434579, R: 0.7587332444405265\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [14/1000] ===========================================================================================================>\n", "Epoch: [14/1000][0/2496]\tTrain_Loss 0.60239 (0.60239)\tTrain_Acc 0.75000 (0.75000)\tTrain_G-Mean 0.82035(0.82035)\tTrain_Kappa 0.66448(0.66448)\tTrain_MF1 0.72854(0.72854)\tTrain_Precision 0.74167(0.74167)\tTrain_Sensitivity 0.72235(0.72235)\tTrain_Specificity 0.93166(0.93166)\tTime 0.174s (0.174s)\tSpeed 366.8 samples/s\tData 0.051s (0.051s)\t\n", "Epoch: [14/1000][1000/2496]\tTrain_Loss 0.50243 (0.50999)\tTrain_Acc 0.79688 (0.81401)\tTrain_G-Mean 0.88786(0.86381)\tTrain_Kappa 0.72668(0.74590)\tTrain_MF1 0.77070(0.75227)\tTrain_Precision 0.75359(0.75100)\tTrain_Sensitivity 0.82852(0.78366)\tTrain_Specificity 0.95146(0.95373)\tTime 0.066s (0.059s)\tSpeed 975.4 samples/s\tData 0.023s (0.019s)\t\n", "Epoch: [14/1000][2000/2496]\tTrain_Loss 0.59086 (0.51283)\tTrain_Acc 0.71875 (0.81269)\tTrain_G-Mean 0.77253(0.86350)\tTrain_Kappa 0.61028(0.74404)\tTrain_MF1 0.66718(0.75132)\tTrain_Precision 0.75030(0.75029)\tTrain_Sensitivity 0.63940(0.78339)\tTrain_Specificity 0.93338(0.95336)\tTime 0.070s (0.061s)\tSpeed 916.6 samples/s\tData 0.032s (0.022s)\t\n", "1145\n", "===========================================================Epoch : [14/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5121132785287232, Training Accuracy      : 0.8128327439287475, Training G-Mean      : 0.8635736470699217\n", "Training Kappa      : 0.7441906623672411,Training MF1     : 0.7512684827849554, Training Precision      : 0.7501356576521621, Training Sensitivity      : 0.783493596432398, Training Specificity      : 0.9533937726169815\n", "Validation Results : \n", "Validation Loss   : 0.5504580486767371, Validation Accuracy : 0.8020352119905554, Validation G-Mean      : 0.8349182402685318\n", "Validation Kappa     : 0.7239496143463664, Validation MF1      : 0.7134295092463274, Validation Precision      : 0.7303978656924102,  Validation Sensitivity      : 0.7380608581903717, Validation Specificity      : 0.9495593793209111\n", "Class wise sensitivity W: 0.8744369754876558, S1: 0.5383975373590804, S2: 0.8290225719386163, S3: 0.6807591643763046, R: 0.7676880417902016\n", "Class wise specificity W: 0.9798441874107141, S1: 0.9037120165937234, S2: 0.910654118755933, S3: 0.9818186568028016, R: 0.9717679170413791\n", "Class wise F1  W: 0.9091100805092857, S1: 0.44256077038472025, S2: 0.8259259254312432, S3: 0.6364111374237983, R: 0.7531396324825953\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [15/1000] ===========================================================================================================>\n", "Epoch: [15/1000][0/2496]\tTrain_Loss 0.55453 (0.55453)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.88759(0.88759)\tTrain_Kappa 0.76063(0.76063)\tTrain_MF1 0.77321(0.77321)\tTrain_Precision 0.74772(0.74772)\tTrain_Sensitivity 0.82391(0.82391)\tTrain_Specificity 0.95618(0.95618)\tTime 0.105s (0.105s)\tSpeed 609.6 samples/s\tData 0.067s (0.067s)\t\n", "Epoch: [15/1000][1000/2496]\tTrain_Loss 0.51477 (0.50411)\tTrain_Acc 0.76562 (0.81504)\tTrain_G-Mean 0.85826(0.86579)\tTrain_Kappa 0.67709(0.74738)\tTrain_MF1 0.71711(0.75486)\tTrain_Precision 0.70344(0.75438)\tTrain_Sensitivity 0.78143(0.78693)\tTrain_Specificity 0.94265(0.95393)\tTime 0.063s (0.062s)\tSpeed 1011.9 samples/s\tData 0.025s (0.022s)\t\n", "Epoch: [15/1000][2000/2496]\tTrain_Loss 0.38879 (0.50420)\tTrain_Acc 0.89062 (0.81473)\tTrain_G-Mean 0.92793(0.86589)\tTrain_Kappa 0.85002(0.74677)\tTrain_MF1 0.87562(0.75378)\tTrain_Precision 0.87836(0.75294)\tTrain_Sensitivity 0.88637(0.78714)\tTrain_Specificity 0.97145(0.95388)\tTime 0.061s (0.064s)\tSpeed 1053.4 samples/s\tData 0.024s (0.025s)\t\n", "1145\n", "===========================================================Epoch : [15/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5074370566349572, Training Accuracy      : 0.8141545703895463, Training G-Mean      : 0.8655572218654154\n", "Training Kappa      : 0.7460433413844718,Training MF1     : 0.7533474598641104, Training Precision      : 0.7524168099599098, Training Sensitivity      : 0.7866873415927271, Training Specificity      : 0.9537213600025723\n", "Validation Results : \n", "Validation Loss   : 0.553943457468746, Validation Accuracy : 0.7820536264243917, Validation G-Mean      : 0.8423349312934633\n", "Validation Kappa     : 0.7026233323596854, Validation MF1      : 0.7097667025359929, Validation Precision      : 0.7167585036192351,  Validation Sensitivity      : 0.7532594192896633, Validation Specificity      : 0.9468500616766822\n", "Class wise sensitivity W: 0.8655445730706992, S1: 0.6017903470634166, S2: 0.7284700085797443, S3: 0.7807058377051644, R: 0.7897863300292903\n", "Class wise specificity W: 0.9818609875430195, S1: 0.8829297939832298, S2: 0.941702779154919, S3: 0.9667976811190134, R: 0.9609590665832239\n", "Class wise F1  W: 0.9060576271012192, S1: 0.46001062258739955, S2: 0.7864104280115006, S3: 0.6542491860570708, R: 0.7421056489227746\n", "===========================================================Training Epoch : [16/1000] ===========================================================================================================>\n", "Epoch: [16/1000][0/2496]\tTrain_Loss 0.42627 (0.42627)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.90066(0.90066)\tTrain_Kappa 0.79388(0.79388)\tTrain_MF1 0.80919(0.80919)\tTrain_Precision 0.79266(0.79266)\tTrain_Sensitivity 0.84274(0.84274)\tTrain_Specificity 0.96255(0.96255)\tTime 0.093s (0.093s)\tSpeed 686.8 samples/s\tData 0.049s (0.049s)\t\n", "Epoch: [16/1000][1000/2496]\tTrain_Loss 0.43972 (0.50370)\tTrain_Acc 0.84375 (0.81439)\tTrain_G-Mean 0.91046(0.86569)\tTrain_Kappa 0.79126(0.74671)\tTrain_MF1 0.82450(0.75302)\tTrain_Precision 0.80375(0.75196)\tTrain_Sensitivity 0.86286(0.78682)\tTrain_Specificity 0.96068(0.95385)\tTime 0.078s (0.059s)\tSpeed 820.5 samples/s\tData 0.033s (0.020s)\t\n", "Epoch: [16/1000][2000/2496]\tTrain_Loss 0.55286 (0.50487)\tTrain_Acc 0.78125 (0.81366)\tTrain_G-Mean 0.83526(0.86538)\tTrain_Kappa 0.68561(0.74536)\tTrain_MF1 0.72466(0.75294)\tTrain_Precision 0.73029(0.75194)\tTrain_Sensitivity 0.74058(0.78650)\tTrain_Specificity 0.94203(0.95360)\tTime 0.082s (0.064s)\tSpeed 781.8 samples/s\tData 0.035s (0.023s)\t\n", "1145\n", "===========================================================Epoch : [16/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5062147881059597, Training Accuracy      : 0.813639804456361, Training G-Mean      : 0.8653366453340146\n", "Training Kappa      : 0.745274715519153,Training MF1     : 0.7531612439379579, Training Precision      : 0.7521425849835707, Training Sensitivity      : 0.7864786551196655, Training Specificity      : 0.9535813936104008\n", "Validation Results : \n", "Validation Loss   : 0.5527065685490247, Validation Accuracy : 0.8011690278205523, Validation G-Mean      : 0.8317832201799944\n", "Validation Kappa     : 0.7221779568859389, Validation MF1      : 0.710822056426323, Validation Precision      : 0.7283264709444777,  Validation Sensitivity      : 0.7334405241004243, Validation Specificity      : 0.9491153649530701\n", "Class wise sensitivity W: 0.878581988665862, S1: 0.5422269371297554, S2: 0.8273170138164757, S3: 0.6943769233909161, R: 0.7246997574991164\n", "Class wise specificity W: 0.9790503618812894, S1: 0.9028018678670154, S2: 0.9097786644054333, S3: 0.979001117730432, R: 0.9749448128811768\n", "Class wise F1  W: 0.9121574905634342, S1: 0.45001093384015, S2: 0.8251270797188162, S3: 0.6362488742846261, R: 0.7305659037245922\n", "===========================================================Training Epoch : [17/1000] ===========================================================================================================>\n", "Epoch: [17/1000][0/2496]\tTrain_Loss 0.46903 (0.46903)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.83043(0.83043)\tTrain_Kappa 0.74794(0.74794)\tTrain_MF1 0.73077(0.73077)\tTrain_Precision 0.77852(0.77852)\tTrain_Sensitivity 0.72391(0.72391)\tTrain_Specificity 0.95261(0.95261)\tTime 0.075s (0.075s)\tSpeed 854.1 samples/s\tData 0.035s (0.035s)\t\n", "Epoch: [17/1000][1000/2496]\tTrain_Loss 0.55583 (0.50271)\tTrain_Acc 0.81250 (0.81528)\tTrain_G-Mean 0.84640(0.86666)\tTrain_Kappa 0.74159(0.74760)\tTrain_MF1 0.76967(0.75478)\tTrain_Precision 0.80357(0.75333)\tTrain_Sensitivity 0.75378(0.78842)\tTrain_Specificity 0.95040(0.95403)\tTime 0.058s (0.059s)\tSpeed 1111.2 samples/s\tData 0.020s (0.020s)\t\n", "Epoch: [17/1000][2000/2496]\tTrain_Loss 0.44366 (0.50214)\tTrain_Acc 0.85938 (0.81578)\tTrain_G-Mean 0.87293(0.86677)\tTrain_Kappa 0.80308(0.74821)\tTrain_MF1 0.77051(0.75519)\tTrain_Precision 0.76954(0.75328)\tTrain_Sensitivity 0.78827(0.78856)\tTrain_Specificity 0.96670(0.95416)\tTime 0.070s (0.065s)\tSpeed 910.3 samples/s\tData 0.033s (0.024s)\t\n", "1145\n", "===========================================================Epoch : [17/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.5023252043025329, Training Accuracy      : 0.8157190928562623, Training G-Mean      : 0.8667494267649957\n", "Training Kappa      : 0.7481538857949472,Training MF1     : 0.7550575837683984, Training Precision      : 0.7531393431670328, Training Sensitivity      : 0.7885380875796854, Training Specificity      : 0.9541391743347057\n", "Validation Results : \n", "Validation Loss   : 0.5583180408359198, Validation Accuracy : 0.79676592495637, Validation G-Mean      : 0.8346697933621979\n", "Validation Kappa     : 0.718428279298011, Validation MF1      : 0.7137186355043653, Validation Precision      : 0.7308856374553265,  Validation Sensitivity      : 0.738270207019897, Validation Specificity      : 0.948912819372213\n", "Class wise sensitivity W: 0.9136280555271561, S1: 0.590051668580706, S2: 0.7550511618013157, S3: 0.6440517765065138, R: 0.7885683726837914\n", "Class wise specificity W: 0.9649660721499258, S1: 0.8863053485153025, S2: 0.9372296701551108, S3: 0.9858359158247137, R: 0.9702270902160158\n", "Class wise F1  W: 0.9193963698691723, S1: 0.4594267522087272, S2: 0.7992288141948598, S3: 0.6262178346384675, R: 0.7643234066106054\n", "===========================================================Training Epoch : [18/1000] ===========================================================================================================>\n", "Epoch: [18/1000][0/2496]\tTrain_Loss 0.55438 (0.55438)\tTrain_Acc 0.73438 (0.73438)\tTrain_G-Mean 0.81168(0.81168)\tTrain_Kappa 0.64915(0.64915)\tTrain_MF1 0.66986(0.66986)\tTrain_Precision 0.70485(0.70485)\tTrain_Sensitivity 0.70567(0.70567)\tTrain_Specificity 0.93363(0.93363)\tTime 0.082s (0.082s)\tSpeed 781.6 samples/s\tData 0.039s (0.039s)\t\n", "Epoch: [18/1000][1000/2496]\tTrain_Loss 0.56271 (0.49749)\tTrain_Acc 0.78125 (0.81700)\tTrain_G-Mean 0.84933(0.86653)\tTrain_Kappa 0.70795(0.74955)\tTrain_MF1 0.74028(0.75528)\tTrain_Precision 0.73167(0.75313)\tTrain_Sensitivity 0.76399(0.78813)\tTrain_Specificity 0.94421(0.95438)\tTime 0.068s (0.057s)\tSpeed 940.3 samples/s\tData 0.025s (0.019s)\t\n", "Epoch: [18/1000][2000/2496]\tTrain_Loss 0.47926 (0.50091)\tTrain_Acc 0.89062 (0.81534)\tTrain_G-Mean 0.93750(0.86614)\tTrain_Kappa 0.84720(0.74766)\tTrain_MF1 0.84655(0.75459)\tTrain_Precision 0.82167(0.75234)\tTrain_Sensitivity 0.90514(0.78761)\tTrain_Specificity 0.97101(0.95404)\tTime 0.069s (0.060s)\tSpeed 928.9 samples/s\tData 0.025s (0.020s)\t\n", "1145\n", "===========================================================Epoch : [18/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.500401896257431, Training Accuracy      : 0.8158495901134122, Training G-Mean      : 0.8662514590089572\n", "Training Kappa      : 0.7483160218583369,Training MF1     : 0.7547816667090864, Training Precision      : 0.7525514639776733, Training Sensitivity      : 0.7877189824548664, Training Specificity      : 0.9541613070150964\n", "Validation Results : \n", "Validation Loss   : 0.5648295564307176, Validation Accuracy : 0.7937455086746741, Validation G-Mean      : 0.8317170750787627\n", "Validation Kappa     : 0.7139674976940293, Validation MF1      : 0.7123711037802228, Validation Precision      : 0.7378543638486081,  Validation Sensitivity      : 0.7339432248686406, Validation Specificity      : 0.9480894097482976\n", "Class wise sensitivity W: 0.8664069852513793, S1: 0.6275490877229922, S2: 0.8081450383024482, S3: 0.6633757190164471, R: 0.7042392940499396\n", "Class wise specificity W: 0.9807963431089545, S1: 0.8768556768669508, S2: 0.9182535909753403, S3: 0.9832694816027636, R: 0.9812719561874762\n", "Class wise F1  W: 0.9060988110761576, S1: 0.4692301508461826, S2: 0.8180761753368544, S3: 0.6330620960883445, R: 0.7353882855535802\n", "===========================================================Training Epoch : [19/1000] ===========================================================================================================>\n", "Epoch: [19/1000][0/2496]\tTrain_Loss 0.51144 (0.51144)\tTrain_Acc 0.79688 (0.79688)\tTrain_G-Mean 0.87652(0.87652)\tTrain_Kappa 0.72052(0.72052)\tTrain_MF1 0.75161(0.75161)\tTrain_Precision 0.74522(0.74522)\tTrain_Sensitivity 0.80927(0.80927)\tTrain_Specificity 0.94937(0.94937)\tTime 0.075s (0.075s)\tSpeed 858.9 samples/s\tData 0.032s (0.032s)\t\n", "Epoch: [19/1000][1000/2496]\tTrain_Loss 0.61930 (0.49322)\tTrain_Acc 0.75000 (0.81887)\tTrain_G-Mean 0.84697(0.86996)\tTrain_Kappa 0.67471(0.75199)\tTrain_MF1 0.72506(0.75911)\tTrain_Precision 0.70953(0.75682)\tTrain_Sensitivity 0.76508(0.79367)\tTrain_Specificity 0.93763(0.95488)\tTime 0.066s (0.055s)\tSpeed 971.8 samples/s\tData 0.025s (0.017s)\t\n", "Epoch: [19/1000][2000/2496]\tTrain_Loss 0.30472 (0.49702)\tTrain_Acc 0.85938 (0.81741)\tTrain_G-Mean 0.92025(0.86832)\tTrain_Kappa 0.80321(0.75040)\tTrain_MF1 0.80649(0.75708)\tTrain_Precision 0.79091(0.75512)\tTrain_Sensitivity 0.87424(0.79106)\tTrain_Specificity 0.96867(0.95460)\tTime 0.075s (0.059s)\tSpeed 853.6 samples/s\tData 0.031s (0.022s)\t\n", "1145\n", "===========================================================Epoch : [19/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.49726176796815336, Training Accuracy      : 0.8171020748582347, Training G-Mean      : 0.8679484644390348\n", "Training Kappa      : 0.7500033451791681,Training MF1     : 0.7565491580899857, Training Precision      : 0.7544288440726873, Training Sensitivity      : 0.7904580529062796, Training Specificity      : 0.9545315380661896\n", "Validation Results : \n", "Validation Loss   : 0.6221763105058545, Validation Accuracy : 0.795875680114978, Validation G-Mean      : 0.8194051958930787\n", "Validation Kappa     : 0.7117766019688624, Validation MF1      : 0.6948525307430216, Validation Precision      : 0.7259895881472154,  Validation Sensitivity      : 0.7139220827914149, Validation Specificity      : 0.9458083660502717\n", "Class wise sensitivity W: 0.9053560851652168, S1: 0.4371877637664165, S2: 0.860146862778156, S3: 0.7560292541668677, R: 0.6108904480804116\n", "Class wise specificity W: 0.9662430140776576, S1: 0.9243855057690572, S2: 0.8770194395882505, S3: 0.9709550169854055, R: 0.9904388538309833\n", "Class wise F1  W: 0.9159648723211172, S1: 0.4010004994132756, S2: 0.8189701060350029, S3: 0.6524442200781371, R: 0.6858829558675826\n", "===========================================================Training Epoch : [20/1000] ===========================================================================================================>\n", "Epoch: [20/1000][0/2496]\tTrain_Loss 0.37023 (0.37023)\tTrain_Acc 0.92188 (0.92188)\tTrain_G-Mean 0.93343(0.93343)\tTrain_Kappa 0.88973(0.88973)\tTrain_MF1 0.89826(0.89826)\tTrain_Precision 0.91270(0.91270)\tTrain_Sensitivity 0.89101(0.89101)\tTrain_Specificity 0.97788(0.97788)\tTime 0.077s (0.077s)\tSpeed 827.9 samples/s\tData 0.038s (0.038s)\t\n", "Epoch: [20/1000][1000/2496]\tTrain_Loss 0.50613 (0.49097)\tTrain_Acc 0.73438 (0.81793)\tTrain_G-Mean 0.81325(0.86862)\tTrain_Kappa 0.62968(0.75102)\tTrain_MF1 0.61814(0.76000)\tTrain_Precision 0.61118(0.75854)\tTrain_Sensitivity 0.70500(0.79149)\tTrain_Specificity 0.93813(0.95463)\tTime 0.062s (0.054s)\tSpeed 1035.0 samples/s\tData 0.022s (0.015s)\t\n", "Epoch: [20/1000][2000/2496]\tTrain_Loss 0.56830 (0.49453)\tTrain_Acc 0.85938 (0.81811)\tTrain_G-Mean 0.89796(0.86879)\tTrain_Kappa 0.80755(0.75138)\tTrain_MF1 0.81658(0.75949)\tTrain_Precision 0.80795(0.75786)\tTrain_Sensitivity 0.83613(0.79174)\tTrain_Specificity 0.96437(0.95474)\tTime 0.068s (0.055s)\tSpeed 942.0 samples/s\tData 0.030s (0.018s)\t\n", "1145\n", "===========================================================Epoch : [20/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4955338240863803, Training Accuracy      : 0.8179091353858481, Training G-Mean      : 0.8683781968322176\n", "Training Kappa      : 0.7510927583442477,Training MF1     : 0.7586817569385929, Training Precision      : 0.7570250216901356, Training Sensitivity      : 0.7910466748892752, Training Specificity      : 0.9546849300607294\n", "Validation Results : \n", "Validation Loss   : 0.5606189580409938, Validation Accuracy : 0.8079798018683914, Validation G-Mean      : 0.8340545478077802\n", "Validation Kappa     : 0.7306160692632344, Validation MF1      : 0.7122588713157652, Validation Precision      : 0.7279737498331642,  Validation Sensitivity      : 0.7359540298997213, Validation Specificity      : 0.9502623161824392\n", "Class wise sensitivity W: 0.8930997364548072, S1: 0.46088396287141253, S2: 0.8447884816097756, S3: 0.7102904528197401, R: 0.7707075157428703\n", "Class wise specificity W: 0.9775510886472766, S1: 0.9259243202875214, S2: 0.9008702961354147, S3: 0.9802405185100296, R: 0.9667253573319466\n", "Class wise F1  W: 0.9193681010401061, S1: 0.42269466435097897, S2: 0.8262428721416267, S3: 0.6518917694609827, R: 0.7410969495851332\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [21/1000] ===========================================================================================================>\n", "Epoch: [21/1000][0/2496]\tTrain_Loss 0.65046 (0.65046)\tTrain_Acc 0.75000 (0.75000)\tTrain_G-Mean 0.82498(0.82498)\tTrain_Kappa 0.65901(0.65901)\tTrain_MF1 0.70898(0.70898)\tTrain_Precision 0.69500(0.69500)\tTrain_Sensitivity 0.72865(0.72865)\tTrain_Specificity 0.93404(0.93404)\tTime 0.070s (0.070s)\tSpeed 919.1 samples/s\tData 0.031s (0.031s)\t\n", "Epoch: [21/1000][1000/2496]\tTrain_Loss 0.47891 (0.49157)\tTrain_Acc 0.84375 (0.81876)\tTrain_G-Mean 0.90369(0.86869)\tTrain_Kappa 0.79676(0.75221)\tTrain_MF1 0.83848(0.75851)\tTrain_Precision 0.84266(0.75592)\tTrain_Sensitivity 0.84878(0.79142)\tTrain_Specificity 0.96215(0.95496)\tTime 0.051s (0.055s)\tSpeed 1255.4 samples/s\tData 0.016s (0.016s)\t\n", "Epoch: [21/1000][2000/2496]\tTrain_Loss 0.49413 (0.49116)\tTrain_Acc 0.76562 (0.81894)\tTrain_G-Mean 0.83569(0.86939)\tTrain_Kappa 0.68648(0.75263)\tTrain_MF1 0.72103(0.75984)\tTrain_Precision 0.71855(0.75723)\tTrain_Sensitivity 0.74141(0.79264)\tTrain_Specificity 0.94195(0.95500)\tTime 0.062s (0.057s)\tSpeed 1025.2 samples/s\tData 0.026s (0.018s)\t\n", "1145\n", "===========================================================Epoch : [21/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.49137240601703525, Training Accuracy      : 0.8192064033222386, Training G-Mean      : 0.8697408638109343\n", "Training Kappa      : 0.7529358763716166,Training MF1     : 0.7602812648703087, Training Precision      : 0.7576153906593576, Training Sensitivity      : 0.7931873156301056, Training Specificity      : 0.9550714280408559\n", "Validation Results : \n", "Validation Loss   : 0.5619863563495157, Validation Accuracy : 0.7885435915203778, Validation G-Mean      : 0.8327904072575588\n", "Validation Kappa     : 0.7075020889839038, Validation MF1      : 0.7039722762442785, Validation Precision      : 0.7124950438243756,  Validation Sensitivity      : 0.7365404770246337, Validation Specificity      : 0.9467352652008827\n", "Class wise sensitivity W: 0.9156818591955445, S1: 0.5129773231128956, S2: 0.743950913908906, S3: 0.7359116610536193, R: 0.7741806278522102\n", "Class wise specificity W: 0.9587636188998896, S1: 0.9055414884294307, S2: 0.9356254989876173, S3: 0.9758728614132234, R: 0.9578728582742535\n", "Class wise F1  W: 0.914036547742381, S1: 0.4329133034792662, S2: 0.791727262693758, S3: 0.6554167342175572, R: 0.7257675330884378\n", "===========================================================Training Epoch : [22/1000] ===========================================================================================================>\n", "Epoch: [22/1000][0/2496]\tTrain_Loss 0.36853 (0.36853)\tTrain_Acc 0.89062 (0.89062)\tTrain_G-Mean 0.93022(0.93022)\tTrain_Kappa 0.84341(0.84341)\tTrain_MF1 0.82852(0.82852)\tTrain_Precision 0.79369(0.79369)\tTrain_Sensitivity 0.88932(0.88932)\tTrain_Specificity 0.97300(0.97300)\tTime 0.065s (0.065s)\tSpeed 984.3 samples/s\tData 0.028s (0.028s)\t\n", "Epoch: [22/1000][1000/2496]\tTrain_Loss 0.47872 (0.49030)\tTrain_Acc 0.84375 (0.82109)\tTrain_G-Mean 0.91825(0.87127)\tTrain_Kappa 0.78899(0.75548)\tTrain_MF1 0.81748(0.76227)\tTrain_Precision 0.80400(0.76030)\tTrain_Sensitivity 0.87639(0.79565)\tTrain_Specificity 0.96210(0.95556)\tTime 0.056s (0.056s)\tSpeed 1153.1 samples/s\tData 0.014s (0.017s)\t\n", "Epoch: [22/1000][2000/2496]\tTrain_Loss 0.32054 (0.48992)\tTrain_Acc 0.89062 (0.82018)\tTrain_G-Mean 0.94196(0.87092)\tTrain_Kappa 0.85417(0.75417)\tTrain_MF1 0.86275(0.76171)\tTrain_Precision 0.84286(0.75923)\tTrain_Sensitivity 0.91111(0.79511)\tTrain_Specificity 0.97386(0.95531)\tTime 0.058s (0.057s)\tSpeed 1105.8 samples/s\tData 0.016s (0.018s)\t\n", "1145\n", "===========================================================Epoch : [22/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4908602523904007, Training Accuracy      : 0.8200134638498521, Training G-Mean      : 0.8707796242338325\n", "Training Kappa      : 0.7539626509883987,Training MF1     : 0.7616919642350151, Training Precision      : 0.759884804906921, Training Sensitivity      : 0.7948804130144895, Training Specificity      : 0.9552668866008887\n", "Validation Results : \n", "Validation Loss   : 0.5567660570586747, Validation Accuracy : 0.79246708500154, Validation G-Mean      : 0.8396315128168068\n", "Validation Kappa     : 0.7137608707236631, Validation MF1      : 0.7087534730060969, Validation Precision      : 0.7147030186135642,  Validation Sensitivity      : 0.7469712221367615, Validation Specificity      : 0.948459751610148\n", "Class wise sensitivity W: 0.8581961436265426, S1: 0.5019934817134398, S2: 0.7906493899163776, S3: 0.7633133893653776, R: 0.8207037060620689\n", "Class wise specificity W: 0.9862454069118434, S1: 0.9097084586116864, S2: 0.9227372038634868, S3: 0.9683470870083747, R: 0.955260601655351\n", "Class wise F1  W: 0.9064623925698365, S1: 0.43191291429557516, S2: 0.810136462641428, S3: 0.6504987180233002, R: 0.7447568775003494\n", "===========================================================Training Epoch : [23/1000] ===========================================================================================================>\n", "Epoch: [23/1000][0/2496]\tTrain_Loss 0.40802 (0.40802)\tTrain_Acc 0.82812 (0.82812)\tTrain_G-Mean 0.87843(0.87843)\tTrain_Kappa 0.77195(0.77195)\tTrain_MF1 0.77334(0.77334)\tTrain_Precision 0.76556(0.76556)\tTrain_Sensitivity 0.80489(0.80489)\tTrain_Specificity 0.95869(0.95869)\tTime 0.082s (0.082s)\tSpeed 782.0 samples/s\tData 0.042s (0.042s)\t\n", "Epoch: [23/1000][1000/2496]\tTrain_Loss 0.55745 (0.48569)\tTrain_Acc 0.73438 (0.81890)\tTrain_G-Mean 0.85719(0.87045)\tTrain_Kappa 0.64994(0.75269)\tTrain_MF1 0.67107(0.76101)\tTrain_Precision 0.65429(0.75900)\tTrain_Sensitivity 0.78471(0.79457)\tTrain_Specificity 0.93637(0.95509)\tTime 0.062s (0.060s)\tSpeed 1025.0 samples/s\tData 0.025s (0.021s)\t\n", "Epoch: [23/1000][2000/2496]\tTrain_Loss 0.47417 (0.48788)\tTrain_Acc 0.82812 (0.81958)\tTrain_G-Mean 0.86040(0.86981)\tTrain_Kappa 0.76256(0.75340)\tTrain_MF1 0.77559(0.76070)\tTrain_Precision 0.79117(0.75914)\tTrain_Sensitivity 0.77312(0.79336)\tTrain_Specificity 0.95752(0.95516)\tTime 0.075s (0.064s)\tSpeed 849.2 samples/s\tData 0.029s (0.025s)\t\n", "1145\n", "===========================================================Epoch : [23/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4883431593935268, Training Accuracy      : 0.8194996609960552, Training G-Mean      : 0.8695558662481787\n", "Training Kappa      : 0.7532976021050568,Training MF1     : 0.7602063975279, Training Precision      : 0.7586206137423572, Training Sensitivity      : 0.7929019382032458, Training Specificity      : 0.9551386557567191\n", "Validation Results : \n", "Validation Loss   : 0.5542010601746474, Validation Accuracy : 0.8093673005851556, Validation G-Mean      : 0.8226145932020076\n", "Validation Kappa     : 0.7312549129760326, Validation MF1      : 0.697567962466317, Validation Precision      : 0.7175364226050814,  Validation Sensitivity      : 0.7162600894027872, Validation Specificity      : 0.9499312103106717\n", "Class wise sensitivity W: 0.888562915966981, S1: 0.40760166980131135, S2: 0.870585968903222, S3: 0.577254220258191, R: 0.8372956720842325\n", "Class wise specificity W: 0.9773227169875699, S1: 0.9372055780721168, S2: 0.8928787527804183, S3: 0.989346879575057, R: 0.9529021241381946\n", "Class wise F1  W: 0.9155661784645567, S1: 0.3965133723996281, S2: 0.8375525242059019, S3: 0.586142344104474, R: 0.7520653931570302\n", "================================================================================================\n", "                                          Saving Best Model (ACC)                                     \n", "================================================================================================\n", "================================================================================================\n", "                                          Saving Best Model (Kappa)                                    \n", "================================================================================================\n", "===========================================================Training Epoch : [24/1000] ===========================================================================================================>\n", "Epoch: [24/1000][0/2496]\tTrain_Loss 0.65233 (0.65233)\tTrain_Acc 0.81250 (0.81250)\tTrain_G-Mean 0.87082(0.87082)\tTrain_Kappa 0.74477(0.74477)\tTrain_MF1 0.77245(0.77245)\tTrain_Precision 0.80979(0.80979)\tTrain_Sensitivity 0.79897(0.79897)\tTrain_Specificity 0.94913(0.94913)\tTime 0.085s (0.085s)\tSpeed 753.1 samples/s\tData 0.042s (0.042s)\t\n", "Epoch: [24/1000][1000/2496]\tTrain_Loss 0.41616 (0.47809)\tTrain_Acc 0.87500 (0.82486)\tTrain_G-Mean 0.92432(0.87369)\tTrain_Kappa 0.83864(0.75980)\tTrain_MF1 0.86140(0.76538)\tTrain_Precision 0.85477(0.76202)\tTrain_Sensitivity 0.88056(0.79926)\tTrain_Specificity 0.97026(0.95639)\tTime 0.072s (0.056s)\tSpeed 886.9 samples/s\tData 0.030s (0.019s)\t\n", "Epoch: [24/1000][2000/2496]\tTrain_Loss 0.34636 (0.48335)\tTrain_Acc 0.87500 (0.82239)\tTrain_G-Mean 0.93235(0.87241)\tTrain_Kappa 0.83125(0.75687)\tTrain_MF1 0.82689(0.76359)\tTrain_Precision 0.79311(0.76059)\tTrain_Sensitivity 0.89727(0.79744)\tTrain_Specificity 0.96879(0.95578)\tTime 0.071s (0.061s)\tSpeed 905.1 samples/s\tData 0.029s (0.022s)\t\n", "1145\n", "===========================================================Epoch : [24/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.486848679669679, Training Accuracy      : 0.8213969273915188, Training G-Mean      : 0.8711442789912934\n", "Training Kappa      : 0.755667823330423,Training MF1     : 0.762335590471944, Training Precision      : 0.760000407905915, Training Sensitivity      : 0.7953529402219629, Training Specificity      : 0.9555303349708891\n", "Validation Results : \n", "Validation Loss   : 0.5575492062051675, Validation Accuracy : 0.7773457550559492, Validation G-Mean      : 0.8424541082528683\n", "Validation Kappa     : 0.6972133536705668, Validation MF1      : 0.7063298655143999, Validation Precision      : 0.713287921991023,  Validation Sensitivity      : 0.7538765666476516, Validation Specificity      : 0.9462658466156864\n", "Class wise sensitivity W: 0.8683675862263635, S1: 0.614396872502763, S2: 0.7036362488544424, S3: 0.7728392764223808, R: 0.8101428492323058\n", "Class wise specificity W: 0.985219914043553, S1: 0.8788866698741913, S2: 0.9459586167418728, S3: 0.9701960921599603, R: 0.951067940258855\n", "Class wise F1  W: 0.9108938078085581, S1: 0.4619452231555501, S2: 0.7715186525337775, S3: 0.6597423686354988, R: 0.7275492754386149\n", "===========================================================Training Epoch : [25/1000] ===========================================================================================================>\n", "Epoch: [25/1000][0/2496]\tTrain_Loss 0.67769 (0.67769)\tTrain_Acc 0.81250 (0.81250)\tTrain_G-Mean 0.86064(0.86064)\tTrain_Kappa 0.74168(0.74168)\tTrain_MF1 0.77844(0.77844)\tTrain_Precision 0.78190(0.78190)\tTrain_Sensitivity 0.78004(0.78004)\tTrain_Specificity 0.94958(0.94958)\tTime 0.077s (0.077s)\tSpeed 826.8 samples/s\tData 0.037s (0.037s)\t\n", "Epoch: [25/1000][1000/2496]\tTrain_Loss 0.43564 (0.48014)\tTrain_Acc 0.85938 (0.82244)\tTrain_G-Mean 0.90983(0.87341)\tTrain_Kappa 0.81592(0.75756)\tTrain_MF1 0.82987(0.76538)\tTrain_Precision 0.83324(0.76213)\tTrain_Sensitivity 0.85615(0.79915)\tTrain_Specificity 0.96688(0.95591)\tTime 0.059s (0.054s)\tSpeed 1077.1 samples/s\tData 0.022s (0.018s)\t\n", "Epoch: [25/1000][2000/2496]\tTrain_Loss 0.44960 (0.48484)\tTrain_Acc 0.85938 (0.82203)\tTrain_G-Mean 0.89085(0.87211)\tTrain_Kappa 0.80368(0.75677)\tTrain_MF1 0.77798(0.76359)\tTrain_Precision 0.75758(0.76107)\tTrain_Sensitivity 0.82103(0.79693)\tTrain_Specificity 0.96662(0.95578)\tTime 0.071s (0.059s)\tSpeed 895.4 samples/s\tData 0.027s (0.022s)\t\n", "1145\n", "===========================================================Epoch : [25/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.48626164281263184, Training Accuracy      : 0.8215722078402368, Training G-Mean      : 0.8719210736325704\n", "Training Kappa      : 0.756086912905983,Training MF1     : 0.7630843957199542, Training Precision      : 0.7606134722874958, Training Sensitivity      : 0.7966952475140067, Training Specificity      : 0.9556254544414775\n", "Validation Results : \n", "Validation Loss   : 0.5570221606635506, Validation Accuracy : 0.7914886176983883, Validation G-Mean      : 0.8402204759947914\n", "Validation Kappa     : 0.7134822447864544, Validation MF1      : 0.7079585293876779, Validation Precision      : 0.7136744185423972,  Validation Sensitivity      : 0.7478712798556203, Validation Specificity      : 0.9484892141132453\n", "Class wise sensitivity W: 0.8633940822296949, S1: 0.5083454897721504, S2: 0.7711214532012715, S3: 0.7141652141305996, R: 0.8823301599443895\n", "Class wise specificity W: 0.9822975917220323, S1: 0.9130297710446162, S2: 0.9330066161109931, S3: 0.9770543213290069, R: 0.9370577703595786\n", "Class wise F1  W: 0.9061156011220672, S1: 0.4408390917547086, S2: 0.8069531578200025, S3: 0.6427844470240058, R: 0.7431003492176013\n", "===========================================================Training Epoch : [26/1000] ===========================================================================================================>\n", "Epoch: [26/1000][0/2496]\tTrain_Loss 0.39262 (0.39262)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.85649(0.85649)\tTrain_Kappa 0.78400(0.78400)\tTrain_MF1 0.74861(0.74861)\tTrain_Precision 0.74190(0.74190)\tTrain_Sensitivity 0.76333(0.76333)\tTrain_Specificity 0.96101(0.96101)\tTime 0.075s (0.075s)\tSpeed 851.5 samples/s\tData 0.035s (0.035s)\t\n", "Epoch: [26/1000][1000/2496]\tTrain_Loss 0.51364 (0.47908)\tTrain_Acc 0.76562 (0.82336)\tTrain_G-Mean 0.86329(0.87215)\tTrain_Kappa 0.69349(0.75843)\tTrain_MF1 0.79255(0.76395)\tTrain_Precision 0.80779(0.76165)\tTrain_Sensitivity 0.79169(0.79682)\tTrain_Specificity 0.94136(0.95610)\tTime 0.050s (0.054s)\tSpeed 1283.2 samples/s\tData 0.018s (0.016s)\t\n", "Epoch: [26/1000][2000/2496]\tTrain_Loss 0.45882 (0.48154)\tTrain_Acc 0.82812 (0.82201)\tTrain_G-Mean 0.88345(0.87267)\tTrain_Kappa 0.77594(0.75673)\tTrain_MF1 0.80399(0.76432)\tTrain_Precision 0.79748(0.76125)\tTrain_Sensitivity 0.81521(0.79795)\tTrain_Specificity 0.95741(0.95576)\tTime 0.054s (0.054s)\tSpeed 1194.8 samples/s\tData 0.017s (0.017s)\t\n", "1145\n", "===========================================================Epoch : [26/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.48399074899199873, Training Accuracy      : 0.8215288692677515, Training G-Mean      : 0.8722888239627304\n", "Training Kappa      : 0.756120447879634,Training MF1     : 0.7638900674670583, Training Precision      : 0.7609542854428754, Training Sensitivity      : 0.7973387843642673, Training Specificity      : 0.955647324106823\n", "Validation Results : \n", "Validation Loss   : 0.5664175666907695, Validation Accuracy : 0.7899583589980496, Validation G-Mean      : 0.8379724351262247\n", "Validation Kappa     : 0.7106002928142472, Validation MF1      : 0.7174701963679749, Validation Precision      : 0.7383464426920465,  Validation Sensitivity      : 0.7448866882899455, Validation Specificity      : 0.9478692387307073\n", "Class wise sensitivity W: 0.848455089575125, S1: 0.6427494588764758, S2: 0.7911998323225018, S3: 0.7124255534658049, R: 0.7296035072098228\n", "Class wise specificity W: 0.984844682832455, S1: 0.8716577678450739, S2: 0.9251560279108051, S3: 0.9797235412643426, R: 0.9779641738008662\n", "Class wise F1  W: 0.899395208916323, S1: 0.47257594746028775, S2: 0.8135101746046106, S3: 0.6548032672461415, R: 0.7470663836125095\n", "===========================================================Training Epoch : [27/1000] ===========================================================================================================>\n", "Epoch: [27/1000][0/2496]\tTrain_Loss 0.40622 (0.40622)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.88802(0.88802)\tTrain_Kappa 0.78631(0.78631)\tTrain_MF1 0.77996(0.77996)\tTrain_Precision 0.77083(0.77083)\tTrain_Sensitivity 0.81819(0.81819)\tTrain_Specificity 0.96381(0.96381)\tTime 0.065s (0.065s)\tSpeed 977.8 samples/s\tData 0.033s (0.033s)\t\n", "Epoch: [27/1000][1000/2496]\tTrain_Loss 0.41782 (0.47927)\tTrain_Acc 0.87500 (0.82318)\tTrain_G-Mean 0.89170(0.87393)\tTrain_Kappa 0.82865(0.75812)\tTrain_MF1 0.81742(0.76549)\tTrain_Precision 0.81864(0.76314)\tTrain_Sensitivity 0.82197(0.79981)\tTrain_Specificity 0.96734(0.95610)\tTime 0.051s (0.051s)\tSpeed 1263.4 samples/s\tData 0.013s (0.015s)\t\n", "Epoch: [27/1000][2000/2496]\tTrain_Loss 0.45213 (0.48123)\tTrain_Acc 0.85938 (0.82264)\tTrain_G-Mean 0.86811(0.87272)\tTrain_Kappa 0.80794(0.75750)\tTrain_MF1 0.78108(0.76441)\tTrain_Precision 0.83030(0.76210)\tTrain_Sensitivity 0.78100(0.79781)\tTrain_Specificity 0.96493(0.95596)\tTime 0.050s (0.052s)\tSpeed 1280.5 samples/s\tData 0.014s (0.016s)\t\n", "1145\n", "===========================================================Epoch : [27/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.48141725478956526, Training Accuracy      : 0.8227736493774654, Training G-Mean      : 0.8728839805750945\n", "Training Kappa      : 0.7577747784580904,Training MF1     : 0.7648884611443084, Training Precision      : 0.762364220480101, Training Sensitivity      : 0.7981154488519017, Training Specificity      : 0.9559799921245132\n", "Validation Results : \n", "Validation Loss   : 0.5439467291505341, Validation Accuracy : 0.7882195744790063, Validation G-Mean      : 0.8403311834284147\n", "Validation Kappa     : 0.7092077945556283, Validation MF1      : 0.7126333860410644, Validation Precision      : 0.7265618807930389,  Validation Sensitivity      : 0.7489960126877857, Validation Specificity      : 0.9479317987880587\n", "Class wise sensitivity W: 0.8814590812821663, S1: 0.6310618075998874, S2: 0.7457670747687679, S3: 0.7381973891254079, R: 0.7484947106627061\n", "Class wise specificity W: 0.9789494918903131, S1: 0.8773656515030752, S2: 0.9367439784304634, S3: 0.9693075266079961, R: 0.9772923455084389\n", "Class wise F1  W: 0.9135617783608445, S1: 0.4708471440077868, S2: 0.7927938533677065, S3: 0.6348029244528599, R: 0.7511612300161292\n", "===========================================================Training Epoch : [28/1000] ===========================================================================================================>\n", "Epoch: [28/1000][0/2496]\tTrain_Loss 0.52848 (0.52848)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.89182(0.89182)\tTrain_Kappa 0.77192(0.77192)\tTrain_MF1 0.71390(0.71390)\tTrain_Precision 0.72833(0.72833)\tTrain_Sensitivity 0.82690(0.82690)\tTrain_Specificity 0.96184(0.96184)\tTime 0.071s (0.071s)\tSpeed 898.6 samples/s\tData 0.032s (0.032s)\t\n", "Epoch: [28/1000][1000/2496]\tTrain_Loss 0.41736 (0.47586)\tTrain_Acc 0.76562 (0.82416)\tTrain_G-Mean 0.84623(0.87341)\tTrain_Kappa 0.68463(0.75967)\tTrain_MF1 0.75039(0.76615)\tTrain_Precision 0.79083(0.76351)\tTrain_Sensitivity 0.75814(0.79890)\tTrain_Specificity 0.94454(0.95632)\tTime 0.048s (0.053s)\tSpeed 1328.2 samples/s\tData 0.013s (0.015s)\t\n", "Epoch: [28/1000][2000/2496]\tTrain_Loss 0.51212 (0.47979)\tTrain_Acc 0.85938 (0.82306)\tTrain_G-Mean 0.90304(0.87219)\tTrain_Kappa 0.80626(0.75802)\tTrain_MF1 0.82373(0.76481)\tTrain_Precision 0.80641(0.76229)\tTrain_Sensitivity 0.84664(0.79695)\tTrain_Specificity 0.96320(0.95601)\tTime 0.062s (0.055s)\tSpeed 1035.2 samples/s\tData 0.020s (0.017s)\t\n", "1145\n", "===========================================================Epoch : [28/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.48029005503616273, Training Accuracy      : 0.8227731678377713, Training G-Mean      : 0.87196296396176\n", "Training Kappa      : 0.7576540756757181,Training MF1     : 0.7646585521246433, Training Precision      : 0.76219844265411, Training Sensitivity      : 0.7966221383336966, Training Specificity      : 0.9559043996322609\n", "Validation Results : \n", "Validation Loss   : 0.5517537365513114, Validation Accuracy : 0.8010872215378297, Validation G-Mean      : 0.842778370636452\n", "Validation Kappa     : 0.7247287260599283, Validation MF1      : 0.7237863966385729, Validation Precision      : 0.7396674402890302,  Validation Sensitivity      : 0.7514008400821977, Validation Specificity      : 0.9500956791857761\n", "Class wise sensitivity W: 0.8856269576786701, S1: 0.5944617632743991, S2: 0.797999159151763, S3: 0.7220200025078306, R: 0.7568963177983258\n", "Class wise specificity W: 0.9781438682819952, S1: 0.8952938776989882, S2: 0.9242866410634905, S3: 0.9772103791357543, R: 0.9755436297486589\n", "Class wise F1  W: 0.9148243314100601, S1: 0.4707063476426647, S2: 0.8168885416593435, S3: 0.6574397430109936, R: 0.7590730194698038\n", "===========================================================Training Epoch : [29/1000] ===========================================================================================================>\n", "Epoch: [29/1000][0/2496]\tTrain_Loss 0.48362 (0.48362)\tTrain_Acc 0.81250 (0.81250)\tTrain_G-Mean 0.88077(0.88077)\tTrain_Kappa 0.74935(0.74935)\tTrain_MF1 0.79911(0.79911)\tTrain_Precision 0.79170(0.79170)\tTrain_Sensitivity 0.81389(0.81389)\tTrain_Specificity 0.95315(0.95315)\tTime 0.072s (0.072s)\tSpeed 894.6 samples/s\tData 0.029s (0.029s)\t\n", "Epoch: [29/1000][1000/2496]\tTrain_Loss 0.42890 (0.47518)\tTrain_Acc 0.84375 (0.82466)\tTrain_G-Mean 0.88159(0.87445)\tTrain_Kappa 0.78356(0.76033)\tTrain_MF1 0.78626(0.76724)\tTrain_Precision 0.79956(0.76485)\tTrain_Sensitivity 0.80926(0.80068)\tTrain_Specificity 0.96039(0.95642)\tTime 0.051s (0.049s)\tSpeed 1249.2 samples/s\tData 0.013s (0.014s)\t\n", "Epoch: [29/1000][2000/2496]\tTrain_Loss 0.83359 (0.48021)\tTrain_Acc 0.71875 (0.82203)\tTrain_G-Mean 0.79731(0.87181)\tTrain_Kappa 0.62646(0.75689)\tTrain_MF1 0.69436(0.76388)\tTrain_Precision 0.71476(0.76205)\tTrain_Sensitivity 0.68613(0.79636)\tTrain_Specificity 0.92651(0.95580)\tTime 0.046s (0.051s)\tSpeed 1384.3 samples/s\tData 0.013s (0.015s)\t\n", "1145\n", "===========================================================Epoch : [29/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4796797691677243, Training Accuracy      : 0.8224991717517258, Training G-Mean      : 0.8718362142033792\n", "Training Kappa      : 0.7574823484367373,Training MF1     : 0.7638389097962471, Training Precision      : 0.7619261135400927, Training Sensitivity      : 0.7963631190365573, Training Specificity      : 0.9559183502904113\n", "Validation Results : \n", "Validation Loss   : 0.5535059607803926, Validation Accuracy : 0.7830994636074325, Validation G-Mean      : 0.8423652549028317\n", "Validation Kappa     : 0.7036603484423672, Validation MF1      : 0.7113339109336518, Validation Precision      : 0.7217383126534803,  Validation Sensitivity      : 0.7528045344857225, Validation Specificity      : 0.9471702362438363\n", "Class wise sensitivity W: 0.8579284967188228, S1: 0.6244012104513134, S2: 0.7348794625847751, S3: 0.733489156367475, R: 0.8133243463062283\n", "Class wise specificity W: 0.9829368705212758, S1: 0.8772100202388164, S2: 0.9412627894216807, S3: 0.974578882023094, R: 0.9598626190143106\n", "Class wise F1  W: 0.9026959479167199, S1: 0.4599113228615458, S2: 0.7900045978565282, S3: 0.6522169578657933, R: 0.751840728167672\n", "===========================================================Training Epoch : [30/1000] ===========================================================================================================>\n", "Epoch: [30/1000][0/2496]\tTrain_Loss 0.43464 (0.43464)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.90761(0.90761)\tTrain_Kappa 0.77992(0.77992)\tTrain_MF1 0.76811(0.76811)\tTrain_Precision 0.73452(0.73452)\tTrain_Sensitivity 0.85476(0.85476)\tTrain_Specificity 0.96373(0.96373)\tTime 0.066s (0.066s)\tSpeed 965.7 samples/s\tData 0.030s (0.030s)\t\n", "Epoch: [30/1000][1000/2496]\tTrain_Loss 0.39004 (0.47959)\tTrain_Acc 0.89062 (0.82110)\tTrain_G-Mean 0.92925(0.87170)\tTrain_Kappa 0.84890(0.75586)\tTrain_MF1 0.83425(0.76299)\tTrain_Precision 0.82058(0.76065)\tTrain_Sensitivity 0.88565(0.79628)\tTrain_Specificity 0.97500(0.95562)\tTime 0.050s (0.049s)\tSpeed 1285.2 samples/s\tData 0.018s (0.014s)\t\n", "Epoch: [30/1000][2000/2496]\tTrain_Loss 0.25735 (0.47986)\tTrain_Acc 0.96875 (0.82230)\tTrain_G-Mean 0.97533(0.87270)\tTrain_Kappa 0.95836(0.75730)\tTrain_MF1 0.96800(0.76443)\tTrain_Precision 0.98091(0.76178)\tTrain_Sensitivity 0.96000(0.79787)\tTrain_Specificity 0.99090(0.95588)\tTime 0.050s (0.050s)\tSpeed 1273.5 samples/s\tData 0.014s (0.016s)\t\n", "1145\n", "===========================================================Epoch : [30/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.47916221296271455, Training Accuracy      : 0.8231116902428501, Training G-Mean      : 0.8732337094590361\n", "Training Kappa      : 0.7582842750893746,Training MF1     : 0.7656533535325118, Training Precision      : 0.7630574927999615, Training Sensitivity      : 0.7987027641361903, Training Specificity      : 0.9560559963616446\n", "Validation Results : \n", "Validation Loss   : 0.5565216087494013, Validation Accuracy : 0.7877832743044862, Validation G-Mean      : 0.836449431234447\n", "Validation Kappa     : 0.708230089830421, Validation MF1      : 0.7092508338638831, Validation Precision      : 0.7255403747239245,  Validation Sensitivity      : 0.7424523822014034, Validation Specificity      : 0.9476117219080267\n", "Class wise sensitivity W: 0.8680557356871027, S1: 0.6159521279820805, S2: 0.7649160677511863, S3: 0.7368382796463125, R: 0.7264996999403361\n", "Class wise specificity W: 0.9817639816597911, S1: 0.8765397315250018, S2: 0.9314335701145219, S3: 0.9726270427878615, R: 0.9756942834529577\n", "Class wise F1  W: 0.9074709877293772, S1: 0.45670533930495977, S2: 0.8016703574159174, S3: 0.6473653377610767, R: 0.7330421471080855\n", "===========================================================Training Epoch : [31/1000] ===========================================================================================================>\n", "Epoch: [31/1000][0/2496]\tTrain_Loss 0.43678 (0.43678)\tTrain_Acc 0.85938 (0.85938)\tTrain_G-Mean 0.89402(0.89402)\tTrain_Kappa 0.81090(0.81090)\tTrain_MF1 0.81033(0.81033)\tTrain_Precision 0.81190(0.81190)\tTrain_Sensitivity 0.82879(0.82879)\tTrain_Specificity 0.96438(0.96438)\tTime 0.071s (0.071s)\tSpeed 899.9 samples/s\tData 0.038s (0.038s)\t\n", "Epoch: [31/1000][1000/2496]\tTrain_Loss 0.38935 (0.44891)\tTrain_Acc 0.84375 (0.83253)\tTrain_G-Mean 0.89638(0.88046)\tTrain_Kappa 0.77599(0.77073)\tTrain_MF1 0.75604(0.77732)\tTrain_Precision 0.73778(0.77219)\tTrain_Sensitivity 0.83096(0.80988)\tTrain_Specificity 0.96694(0.95844)\tTime 0.045s (0.050s)\tSpeed 1425.8 samples/s\tData 0.014s (0.014s)\t\n", "Epoch: [31/1000][2000/2496]\tTrain_Loss 0.40981 (0.44967)\tTrain_Acc 0.85938 (0.83241)\tTrain_G-Mean 0.89904(0.88074)\tTrain_Kappa 0.81574(0.77071)\tTrain_MF1 0.82815(0.77800)\tTrain_Precision 0.83333(0.77293)\tTrain_Sensitivity 0.83792(0.81044)\tTrain_Specificity 0.96462(0.95840)\tTime 0.070s (0.054s)\tSpeed 919.3 samples/s\tData 0.026s (0.017s)\t\n", "1145\n", "===========================================================Epoch : [31/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.449925650185786, Training Accuracy      : 0.8323899970722386, Training G-Mean      : 0.8807720626148423\n", "Training Kappa      : 0.7706614570493835,Training MF1     : 0.777924280420829, Training Precision      : 0.7729321954043549, Training Sensitivity      : 0.8105335630458561, Training Specificity      : 0.9583716528920013\n", "Validation Results : \n", "Validation Loss   : 0.5657098774536534, Validation Accuracy : 0.7978903603326147, Validation G-Mean      : 0.8406736233178432\n", "Validation Kappa     : 0.7199283423887057, Validation MF1      : 0.7203866273008716, Validation Precision      : 0.7368887927428688,  Validation Sensitivity      : 0.7484531035546024, Validation Specificity      : 0.9491967291524059\n", "Class wise sensitivity W: 0.8801802530702704, S1: 0.5766242318778554, S2: 0.7966807090123078, S3: 0.7408293873773819, R: 0.7479509364351968\n", "Class wise specificity W: 0.9770120096768384, S1: 0.8922377162682864, S2: 0.9228662229541292, S3: 0.976717796417223, R: 0.9771499004455553\n", "Class wise F1  W: 0.9107657176089744, S1: 0.4556297999636041, S2: 0.8147539613186585, S3: 0.6657525741253015, R: 0.7550310834878194\n", "===========================================================Training Epoch : [32/1000] ===========================================================================================================>\n", "Epoch: [32/1000][0/2496]\tTrain_Loss 0.46110 (0.46110)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.91115(0.91115)\tTrain_Kappa 0.79559(0.79559)\tTrain_MF1 0.81267(0.81267)\tTrain_Precision 0.79841(0.79841)\tTrain_Sensitivity 0.86199(0.86199)\tTrain_Specificity 0.96310(0.96310)\tTime 0.071s (0.071s)\tSpeed 897.6 samples/s\tData 0.032s (0.032s)\t\n", "Epoch: [32/1000][1000/2496]\tTrain_Loss 0.43154 (0.44273)\tTrain_Acc 0.82812 (0.83640)\tTrain_G-Mean 0.86595(0.88411)\tTrain_Kappa 0.77261(0.77596)\tTrain_MF1 0.78290(0.78208)\tTrain_Precision 0.79554(0.77750)\tTrain_Sensitivity 0.78455(0.81570)\tTrain_Specificity 0.95581(0.95943)\tTime 0.059s (0.055s)\tSpeed 1091.0 samples/s\tData 0.017s (0.016s)\t\n", "Epoch: [32/1000][2000/2496]\tTrain_Loss 0.43453 (0.44299)\tTrain_Acc 0.84375 (0.83549)\tTrain_G-Mean 0.84633(0.88394)\tTrain_Kappa 0.78567(0.77491)\tTrain_MF1 0.73610(0.78185)\tTrain_Precision 0.73286(0.77662)\tTrain_Sensitivity 0.74571(0.81558)\tTrain_Specificity 0.96053(0.95924)\tTime 0.068s (0.059s)\tSpeed 936.8 samples/s\tData 0.024s (0.019s)\t\n", "1145\n", "===========================================================Epoch : [32/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4454263653188275, Training Accuracy      : 0.8347370215421598, Training G-Mean      : 0.8832871307860268\n", "Training Kappa      : 0.7740269690020869,Training MF1     : 0.7815506436641159, Training Precision      : 0.7766914428116224, Training Sensitivity      : 0.8145646655944022, Training Specificity      : 0.9590391347280297\n", "Validation Results : \n", "Validation Loss   : 0.5419463869086735, Validation Accuracy : 0.7985512267734318, Validation G-Mean      : 0.8407228089350042\n", "Validation Kappa     : 0.7211321587430101, Validation MF1      : 0.7171379214754069, Validation Precision      : 0.7261968014960091,  Validation Sensitivity      : 0.7480010901485116, Validation Specificity      : 0.9496927512877897\n", "Class wise sensitivity W: 0.8728895751437592, S1: 0.5588410652809847, S2: 0.7919554779302803, S3: 0.7186341744962995, R: 0.7976851578912394\n", "Class wise specificity W: 0.9832680917639175, S1: 0.8985374174088916, S2: 0.9253155100199981, S3: 0.9771851831705782, R: 0.964157554075564\n", "Class wise F1  W: 0.9120730032159395, S1: 0.4547494231978428, S2: 0.8143044172625267, S3: 0.650180236259264, R: 0.7543825274414625\n", "===========================================================Training Epoch : [33/1000] ===========================================================================================================>\n", "Epoch: [33/1000][0/2496]\tTrain_Loss 0.31691 (0.31691)\tTrain_Acc 0.89062 (0.89062)\tTrain_G-Mean 0.90633(0.90633)\tTrain_Kappa 0.84275(0.84275)\tTrain_MF1 0.84307(0.84307)\tTrain_Precision 0.85000(0.85000)\tTrain_Sensitivity 0.84628(0.84628)\tTrain_Specificity 0.97063(0.97063)\tTime 0.072s (0.072s)\tSpeed 888.3 samples/s\tData 0.033s (0.033s)\t\n", "Epoch: [33/1000][1000/2496]\tTrain_Loss 0.50603 (0.43868)\tTrain_Acc 0.89062 (0.83707)\tTrain_G-Mean 0.81323(0.88379)\tTrain_Kappa 0.84158(0.77702)\tTrain_MF1 0.68979(0.78156)\tTrain_Precision 0.70598(0.77702)\tTrain_Sensitivity 0.68258(0.81514)\tTrain_Specificity 0.96890(0.95961)\tTime 0.062s (0.054s)\tSpeed 1027.0 samples/s\tData 0.024s (0.017s)\t\n", "Epoch: [33/1000][2000/2496]\tTrain_Loss 0.41343 (0.44040)\tTrain_Acc 0.85938 (0.83620)\tTrain_G-Mean 0.91319(0.88337)\tTrain_Kappa 0.80355(0.77592)\tTrain_MF1 0.83054(0.78085)\tTrain_Precision 0.80833(0.77633)\tTrain_Sensitivity 0.86486(0.81455)\tTrain_Specificity 0.96423(0.95940)\tTime 0.082s (0.059s)\tSpeed 777.5 samples/s\tData 0.034s (0.020s)\t\n", "1145\n", "===========================================================Epoch : [33/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.44103344965678376, Training Accuracy      : 0.8352445643799309, Training G-Mean      : 0.8832264392951321\n", "Training Kappa      : 0.7746667320117138,Training MF1     : 0.7804516572236584, Training Precision      : 0.7757135418345199, Training Sensitivity      : 0.8144911435456619, Training Specificity      : 0.9591796163612821\n", "Validation Results : \n", "Validation Loss   : 0.5771143750318474, Validation Accuracy : 0.7954666487013654, Validation G-Mean      : 0.8343544044624464\n", "Validation Kappa     : 0.7161438509346051, Validation MF1      : 0.7098422876463297, Validation Precision      : 0.7246662241925222,  Validation Sensitivity      : 0.7382875094186997, Validation Specificity      : 0.9483715827032334\n", "Class wise sensitivity W: 0.8711977009685876, S1: 0.5401898309796477, S2: 0.8097939074403536, S3: 0.7397434846945042, R: 0.7305126230104015\n", "Class wise specificity W: 0.982632746455141, S1: 0.9006671290747158, S2: 0.9120302829130782, S3: 0.9715703676924448, R: 0.9749573873807831\n", "Class wise F1  W: 0.911012662018781, S1: 0.44335642061724506, S2: 0.8150841319384167, S3: 0.6432124254591178, R: 0.7365457981980909\n", "===========================================================Training Epoch : [34/1000] ===========================================================================================================>\n", "Epoch: [34/1000][0/2496]\tTrain_Loss 0.33827 (0.33827)\tTrain_Acc 0.89062 (0.89062)\tTrain_G-Mean 0.93759(0.93759)\tTrain_Kappa 0.85287(0.85287)\tTrain_MF1 0.82420(0.82420)\tTrain_Precision 0.80753(0.80753)\tTrain_Sensitivity 0.90190(0.90190)\tTrain_Specificity 0.97469(0.97469)\tTime 0.071s (0.071s)\tSpeed 895.3 samples/s\tData 0.032s (0.032s)\t\n", "Epoch: [34/1000][1000/2496]\tTrain_Loss 0.37381 (0.43734)\tTrain_Acc 0.84375 (0.83723)\tTrain_G-Mean 0.90451(0.88421)\tTrain_Kappa 0.77908(0.77731)\tTrain_MF1 0.74691(0.78385)\tTrain_Precision 0.70976(0.77933)\tTrain_Sensitivity 0.84837(0.81596)\tTrain_Specificity 0.96436(0.95961)\tTime 0.070s (0.062s)\tSpeed 917.4 samples/s\tData 0.027s (0.020s)\t\n", "Epoch: [34/1000][2000/2496]\tTrain_Loss 0.38122 (0.43789)\tTrain_Acc 0.81250 (0.83703)\tTrain_G-Mean 0.88617(0.88413)\tTrain_Kappa 0.74001(0.77698)\tTrain_MF1 0.73933(0.78321)\tTrain_Precision 0.70428(0.77839)\tTrain_Sensitivity 0.82000(0.81573)\tTrain_Specificity 0.95769(0.95957)\tTime 0.082s (0.067s)\tSpeed 783.8 samples/s\tData 0.032s (0.025s)\t\n", "1145\n", "===========================================================Epoch : [34/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4385274690456497, Training Accuracy      : 0.8364397459011342, Training G-Mean      : 0.8842428213517612\n", "Training Kappa      : 0.7763112263255567,Training MF1     : 0.7829051304680233, Training Precision      : 0.7776798962257234, Training Sensitivity      : 0.8160123427255228, Training Specificity      : 0.9594502657413104\n", "Validation Results : \n", "Validation Loss   : 0.5698160286960281, Validation Accuracy : 0.8023383764500565, Validation G-Mean      : 0.8325906685019967\n", "Validation Kappa     : 0.7238333648017219, Validation MF1      : 0.710956683284221, Validation Precision      : 0.7251914299140317,  Validation Sensitivity      : 0.7343023843660198, Validation Specificity      : 0.9492114258790296\n", "Class wise sensitivity W: 0.8710002750418365, S1: 0.4859800692420147, S2: 0.834309128150474, S3: 0.682811852525042, R: 0.7974105968707311\n", "Class wise specificity W: 0.9843551538377532, S1: 0.9166737165542589, S2: 0.9001407175476014, S3: 0.9790728823988849, R: 0.9658146590566552\n", "Class wise F1  W: 0.9115313218214125, S1: 0.4325454610466541, S2: 0.8214355972684907, S3: 0.630061572238829, R: 0.7592094640457193\n", "===========================================================Training Epoch : [35/1000] ===========================================================================================================>\n", "Epoch: [35/1000][0/2496]\tTrain_Loss 0.31050 (0.31050)\tTrain_Acc 0.87500 (0.87500)\tTrain_G-Mean 0.92823(0.92823)\tTrain_Kappa 0.83813(0.83813)\tTrain_MF1 0.85685(0.85685)\tTrain_Precision 0.84806(0.84806)\tTrain_Sensitivity 0.88889(0.88889)\tTrain_Specificity 0.96932(0.96932)\tTime 0.091s (0.091s)\tSpeed 705.3 samples/s\tData 0.052s (0.052s)\t\n", "Epoch: [35/1000][1000/2496]\tTrain_Loss 0.28565 (0.43342)\tTrain_Acc 0.89062 (0.83852)\tTrain_G-Mean 0.91372(0.88732)\tTrain_Kappa 0.84741(0.77943)\tTrain_MF1 0.84343(0.78806)\tTrain_Precision 0.83323(0.78351)\tTrain_Sensitivity 0.85926(0.82112)\tTrain_Specificity 0.97164(0.96004)\tTime 0.050s (0.053s)\tSpeed 1280.9 samples/s\tData 0.017s (0.016s)\t\n", "Epoch: [35/1000][2000/2496]\tTrain_Loss 0.26277 (0.43366)\tTrain_Acc 0.90625 (0.83794)\tTrain_G-Mean 0.90212(0.88598)\tTrain_Kappa 0.87110(0.77825)\tTrain_MF1 0.80395(0.78525)\tTrain_Precision 0.79444(0.78050)\tTrain_Sensitivity 0.83183(0.81883)\tTrain_Specificity 0.97836(0.95984)\tTime 0.061s (0.056s)\tSpeed 1052.6 samples/s\tData 0.022s (0.019s)\t\n", "1145\n", "===========================================================Epoch : [35/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.43446609016078025, Training Accuracy      : 0.8374461638621795, Training G-Mean      : 0.885700967698565\n", "Training Kappa      : 0.7776159135193408,Training MF1     : 0.784909027976653, Training Precision      : 0.7801711466640997, Training Sensitivity      : 0.8184435118795742, Training Specificity      : 0.9596895505698047\n", "Validation Results : \n", "Validation Loss   : 0.564086764192706, Validation Accuracy : 0.7949453341546042, Validation G-Mean      : 0.840337466739119\n", "Validation Kappa     : 0.717241393365193, Validation MF1      : 0.7138286770300308, Validation Precision      : 0.7235159190810486,  Validation Sensitivity      : 0.7479073435328184, Validation Specificity      : 0.9491343829644273\n", "Class wise sensitivity W: 0.879883011520221, S1: 0.5736293431247924, S2: 0.7770987766523427, S3: 0.7279481442342878, R: 0.7809774421324489\n", "Class wise specificity W: 0.9803303024011548, S1: 0.8942657482873707, S2: 0.9295707757976459, S3: 0.9717855008276761, R: 0.9697195875082965\n", "Class wise F1  W: 0.913078522130873, S1: 0.4573700006849687, S2: 0.8082163779107272, S3: 0.635441325045797, R: 0.7550371593777838\n", "===========================================================Training Epoch : [36/1000] ===========================================================================================================>\n", "Epoch: [36/1000][0/2496]\tTrain_Loss 0.45009 (0.45009)\tTrain_Acc 0.81250 (0.81250)\tTrain_G-Mean 0.87082(0.87082)\tTrain_Kappa 0.74828(0.74828)\tTrain_MF1 0.76008(0.76008)\tTrain_Precision 0.74396(0.74396)\tTrain_Sensitivity 0.79511(0.79511)\tTrain_Specificity 0.95374(0.95374)\tTime 0.064s (0.064s)\tSpeed 1003.2 samples/s\tData 0.028s (0.028s)\t\n", "Epoch: [36/1000][1000/2496]\tTrain_Loss 0.48760 (0.43261)\tTrain_Acc 0.79688 (0.83893)\tTrain_G-Mean 0.85469(0.88685)\tTrain_Kappa 0.72925(0.78000)\tTrain_MF1 0.77839(0.78731)\tTrain_Precision 0.81837(0.78229)\tTrain_Sensitivity 0.77010(0.82016)\tTrain_Specificity 0.94858(0.96018)\tTime 0.051s (0.056s)\tSpeed 1244.7 samples/s\tData 0.019s (0.017s)\t\n", "Epoch: [36/1000][2000/2496]\tTrain_Loss 0.49271 (0.43243)\tTrain_Acc 0.81250 (0.83890)\tTrain_G-Mean 0.87089(0.88672)\tTrain_Kappa 0.75689(0.77974)\tTrain_MF1 0.78906(0.78676)\tTrain_Precision 0.78893(0.78190)\tTrain_Sensitivity 0.79518(0.81996)\tTrain_Specificity 0.95382(0.96013)\tTime 0.073s (0.060s)\tSpeed 878.5 samples/s\tData 0.029s (0.020s)\t\n", "1145\n", "===========================================================Epoch : [36/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4338326041873258, Training Accuracy      : 0.8383919078217457, Training G-Mean      : 0.8861177483243089\n", "Training Kappa      : 0.7790774244555673,Training MF1     : 0.7858122394832137, Training Precision      : 0.7809439270285523, Training Sensitivity      : 0.8189845467392247, Training Specificity      : 0.9599759382458456\n", "Validation Results : \n", "Validation Loss   : 0.589463360932693, Validation Accuracy : 0.7979448978544297, Validation G-Mean      : 0.8226832800774331\n", "Validation Kappa     : 0.7169703647642087, Validation MF1      : 0.6994596393606625, Validation Precision      : 0.7181503159161424,  Validation Sensitivity      : 0.7183543154641482, Validation Specificity      : 0.9476881876665889\n", "Class wise sensitivity W: 0.8741528003315651, S1: 0.45523456882613905, S2: 0.8423930290065719, S3: 0.6240484523367508, R: 0.7959427268197191\n", "Class wise specificity W: 0.981649923074932, S1: 0.9175274074597717, S2: 0.8945770571057085, S3: 0.9879097124564918, R: 0.9567768382360382\n", "Class wise F1  W: 0.9112434841783467, S1: 0.40655901626298563, S2: 0.8221862693211587, S3: 0.6208919898637718, R: 0.7364174371770539\n", "===========================================================Training Epoch : [37/1000] ===========================================================================================================>\n", "Epoch: [37/1000][0/2496]\tTrain_Loss 0.21454 (0.21454)\tTrain_Acc 0.92188 (0.92188)\tTrain_G-Mean 0.94373(0.94373)\tTrain_Kappa 0.88916(0.88916)\tTrain_MF1 0.86017(0.86017)\tTrain_Precision 0.85682(0.85682)\tTrain_Sensitivity 0.90756(0.90756)\tTrain_Specificity 0.98133(0.98133)\tTime 0.070s (0.070s)\tSpeed 918.5 samples/s\tData 0.033s (0.033s)\t\n", "Epoch: [37/1000][1000/2496]\tTrain_Loss 0.37788 (0.42591)\tTrain_Acc 0.89062 (0.83961)\tTrain_G-Mean 0.89913(0.88813)\tTrain_Kappa 0.83960(0.78100)\tTrain_MF1 0.76811(0.78953)\tTrain_Precision 0.73333(0.78412)\tTrain_Sensitivity 0.82785(0.82233)\tTrain_Specificity 0.97654(0.96033)\tTime 0.071s (0.062s)\tSpeed 906.3 samples/s\tData 0.026s (0.021s)\t\n", "Epoch: [37/1000][2000/2496]\tTrain_Loss 0.56869 (0.42947)\tTrain_Acc 0.76562 (0.83842)\tTrain_G-Mean 0.82331(0.88647)\tTrain_Kappa 0.67112(0.77917)\tTrain_MF1 0.70911(0.78675)\tTrain_Precision 0.70000(0.78110)\tTrain_Sensitivity 0.72273(0.81962)\tTrain_Specificity 0.93789(0.96000)\tTime 0.067s (0.065s)\tSpeed 957.3 samples/s\tData 0.024s (0.024s)\t\n", "1145\n", "===========================================================Epoch : [37/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4318765261903023, Training Accuracy      : 0.8377471261711046, Training G-Mean      : 0.8859220377362342\n", "Training Kappa      : 0.7782181037487101,Training MF1     : 0.7858928031819418, Training Precision      : 0.7806411212071396, Training Sensitivity      : 0.8187686281063807, Training Specificity      : 0.959818527603951\n", "Validation Results : \n", "Validation Loss   : 0.557739074889694, Validation Accuracy : 0.7894739374807515, Validation G-Mean      : 0.8425959488117067\n", "Validation Kappa     : 0.7111451880335647, Validation MF1      : 0.715789670256628, Validation Precision      : 0.7247711683612112,  Validation Sensitivity      : 0.7525024169116544, Validation Specificity      : 0.9482071619695391\n", "Class wise sensitivity W: 0.8659613448963948, S1: 0.5954304383921374, S2: 0.7556451630451917, S3: 0.7352696765103681, R: 0.8102054617141762\n", "Class wise specificity W: 0.984080635128221, S1: 0.8837711817306998, S2: 0.9347697125575514, S3: 0.9743264814217886, R: 0.9640877990094363\n", "Class wise F1  W: 0.9084631269098784, S1: 0.45633426589491477, S2: 0.797730233890848, S3: 0.6493466848062179, R: 0.7670740397812809\n", "===========================================================Training Epoch : [38/1000] ===========================================================================================================>\n", "Epoch: [38/1000][0/2496]\tTrain_Loss 0.40738 (0.40738)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.89620(0.89620)\tTrain_Kappa 0.78780(0.78780)\tTrain_MF1 0.80842(0.80842)\tTrain_Precision 0.79378(0.79378)\tTrain_Sensitivity 0.83607(0.83607)\tTrain_Specificity 0.96066(0.96066)\tTime 0.082s (0.082s)\tSpeed 783.2 samples/s\tData 0.039s (0.039s)\t\n", "Epoch: [38/1000][1000/2496]\tTrain_Loss 0.34103 (0.42126)\tTrain_Acc 0.87500 (0.84024)\tTrain_G-Mean 0.91232(0.88998)\tTrain_Kappa 0.82813(0.78194)\tTrain_MF1 0.83477(0.79106)\tTrain_Precision 0.81948(0.78571)\tTrain_Sensitivity 0.85848(0.82563)\tTrain_Specificity 0.96953(0.96050)\tTime 0.056s (0.057s)\tSpeed 1137.8 samples/s\tData 0.015s (0.018s)\t\n", "Epoch: [38/1000][2000/2496]\tTrain_Loss 0.47251 (0.43004)\tTrain_Acc 0.73438 (0.83829)\tTrain_G-Mean 0.87085(0.88798)\tTrain_Kappa 0.64293(0.77931)\tTrain_MF1 0.68332(0.78790)\tTrain_Precision 0.67895(0.78218)\tTrain_Sensitivity 0.80744(0.82238)\tTrain_Specificity 0.93923(0.96002)\tTime 0.064s (0.061s)\tSpeed 1001.3 samples/s\tData 0.026s (0.022s)\t\n", "1145\n", "===========================================================Epoch : [38/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4300890230478194, Training Accuracy      : 0.8383981678377713, Training G-Mean      : 0.8871298052742137\n", "Training Kappa      : 0.7792067570453272,Training MF1     : 0.7865582673404459, Training Precision      : 0.7810754157041606, Training Sensitivity      : 0.8208515752924794, Training Specificity      : 0.960015439122725\n", "Validation Results : \n", "Validation Loss   : 0.5788889087437543, Validation Accuracy : 0.8029110204291141, Validation G-Mean      : 0.8346304248943442\n", "Validation Kappa     : 0.7250693909645197, Validation MF1      : 0.7113875828904411, Validation Precision      : 0.7238557504655806,  Validation Sensitivity      : 0.737413719972599, Validation Specificity      : 0.9495709031664266\n", "Class wise sensitivity W: 0.892063172231794, S1: 0.4727660604276374, S2: 0.8216659380399328, S3: 0.7201695353000784, R: 0.7804038938635515\n", "Class wise specificity W: 0.9771774949515677, S1: 0.9184737305886667, S2: 0.9082076562532788, S3: 0.9758299864295473, R: 0.9681656476090715\n", "Class wise F1  W: 0.9175584371905469, S1: 0.42006874739811684, S2: 0.8191115928205936, S3: 0.64696905388778, R: 0.7532300831551744\n", "===========================================================Training Epoch : [39/1000] ===========================================================================================================>\n", "Epoch: [39/1000][0/2496]\tTrain_Loss 0.32584 (0.32584)\tTrain_Acc 0.89062 (0.89062)\tTrain_G-Mean 0.91570(0.91570)\tTrain_Kappa 0.84829(0.84829)\tTrain_MF1 0.83468(0.83468)\tTrain_Precision 0.84095(0.84095)\tTrain_Sensitivity 0.86113(0.86113)\tTrain_Specificity 0.97373(0.97373)\tTime 0.061s (0.061s)\tSpeed 1043.4 samples/s\tData 0.029s (0.029s)\t\n", "Epoch: [39/1000][1000/2496]\tTrain_Loss 0.34912 (0.42255)\tTrain_Acc 0.89062 (0.84267)\tTrain_G-Mean 0.91224(0.88873)\tTrain_Kappa 0.83456(0.78456)\tTrain_MF1 0.74582(0.79060)\tTrain_Precision 0.71667(0.78626)\tTrain_Sensitivity 0.85258(0.82294)\tTrain_Specificity 0.97608(0.96101)\tTime 0.053s (0.053s)\tSpeed 1197.6 samples/s\tData 0.018s (0.017s)\t\n", "Epoch: [39/1000][2000/2496]\tTrain_Loss 0.34700 (0.42504)\tTrain_Acc 0.82812 (0.84146)\tTrain_G-Mean 0.91329(0.88825)\tTrain_Kappa 0.77349(0.78307)\tTrain_MF1 0.83526(0.78961)\tTrain_Precision 0.82738(0.78481)\tTrain_Sensitivity 0.87115(0.82231)\tTrain_Specificity 0.95745(0.96069)\tTime 0.064s (0.058s)\tSpeed 1005.0 samples/s\tData 0.023s (0.021s)\t\n", "1145\n", "===========================================================Epoch : [39/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.42625570041127503, Training Accuracy      : 0.8411217563486193, Training G-Mean      : 0.8880310942596126\n", "Training Kappa      : 0.7827099383017406,Training MF1     : 0.7893104382014521, Training Precision      : 0.7844617765438608, Training Sensitivity      : 0.8219974887521511, Training Specificity      : 0.9606109513734037\n", "Validation Results : \n", "Validation Loss   : 0.5635855435596919, Validation Accuracy : 0.8035654706908942, Validation G-Mean      : 0.8387016898299579\n", "Validation Kappa     : 0.7266834925695258, Validation MF1      : 0.7160888738189062, Validation Precision      : 0.7258243286939919,  Validation Sensitivity      : 0.7446847635452972, Validation Specificity      : 0.950020095024642\n", "Class wise sensitivity W: 0.8976289275741078, S1: 0.5017621732702118, S2: 0.808966882019767, S3: 0.7448495819670575, R: 0.7702162528953419\n", "Class wise specificity W: 0.9755098788005936, S1: 0.9153806240042675, S2: 0.916275561688042, S3: 0.9740226060204065, R: 0.9689118046098979\n", "Class wise F1  W: 0.9195881418324682, S1: 0.4362504105709402, S2: 0.8177486754576782, S3: 0.6570257324510844, R: 0.7498314087823631\n", "===========================================================Training Epoch : [40/1000] ===========================================================================================================>\n", "Epoch: [40/1000][0/2496]\tTrain_Loss 0.68924 (0.68924)\tTrain_Acc 0.78125 (0.78125)\tTrain_G-Mean 0.86469(0.86469)\tTrain_Kappa 0.69252(0.69252)\tTrain_MF1 0.72260(0.72260)\tTrain_Precision 0.69442(0.69442)\tTrain_Sensitivity 0.79113(0.79113)\tTrain_Specificity 0.94509(0.94509)\tTime 0.074s (0.074s)\tSpeed 864.0 samples/s\tData 0.034s (0.034s)\t\n", "Epoch: [40/1000][1000/2496]\tTrain_Loss 0.43520 (0.42430)\tTrain_Acc 0.79688 (0.84320)\tTrain_G-Mean 0.84779(0.89047)\tTrain_Kappa 0.72641(0.78554)\tTrain_MF1 0.74747(0.79271)\tTrain_Precision 0.74354(0.78815)\tTrain_Sensitivity 0.75848(0.82604)\tTrain_Specificity 0.94762(0.96115)\tTime 0.062s (0.061s)\tSpeed 1035.4 samples/s\tData 0.021s (0.021s)\t\n", "Epoch: [40/1000][2000/2496]\tTrain_Loss 0.39596 (0.42634)\tTrain_Acc 0.81250 (0.84128)\tTrain_G-Mean 0.89430(0.88927)\tTrain_Kappa 0.75097(0.78290)\tTrain_MF1 0.80782(0.79064)\tTrain_Precision 0.83810(0.78597)\tTrain_Sensitivity 0.83721(0.82416)\tTrain_Specificity 0.95529(0.96072)\tTime 0.068s (0.066s)\tSpeed 944.9 samples/s\tData 0.030s (0.024s)\t\n", "1145\n", "===========================================================Epoch : [40/1000]  Evaluation ===========================================================================================================>\n", "Training Results : \n", "Training Loss     : 0.4273597816041169, Training Accuracy      : 0.8404764931582841, Training G-Mean      : 0.8890221323425173\n", "Training Kappa      : 0.7818799449919792,Training MF1     : 0.7902383160705748, Training Precision      : 0.7854602114572078, Training Sensitivity      : 0.8238681319068201, Training Specificity      : 0.9605083051352534\n", "Validation Results : \n", "Validation Loss   : 0.5627643177984689, Validation Accuracy : 0.8006477132737913, Validation G-Mean      : 0.8408207932007046\n", "Validation Kappa     : 0.7235624291734137, Validation MF1      : 0.7198401449966595, Validation Precision      : 0.7338991409040859,  Validation Sensitivity      : 0.748306190046964, Validation Specificity      : 0.9495998452887274\n", "Class wise sensitivity W: 0.8855963186547811, S1: 0.5591753728518727, S2: 0.7999755407295094, S3: 0.7388743472921079, R: 0.7579093707065516\n", "Class wise specificity W: 0.9768869347909358, S1: 0.9019332909875307, S2: 0.9194424787949101, S3: 0.9750112117273021, R: 0.9747253101429598\n", "Class wise F1  W: 0.9138514060416563, S1: 0.45732132957867927, S2: 0.8154411356486575, S3: 0.6587632503132962, R: 0.7538236034010093\n", "===========================================================Training Epoch : [41/1000] ===========================================================================================================>\n", "Epoch: [41/1000][0/2496]\tTrain_Loss 0.33996 (0.33996)\tTrain_Acc 0.84375 (0.84375)\tTrain_G-Mean 0.85753(0.85753)\tTrain_Kappa 0.78702(0.78702)\tTrain_MF1 0.75466(0.75466)\tTrain_Precision 0.74797(0.74797)\tTrain_Sensitivity 0.76554(0.76554)\tTrain_Specificity 0.96056(0.96056)\tTime 0.100s (0.100s)\tSpeed 641.4 samples/s\tData 0.055s (0.055s)\t\n", "Epoch: [41/1000][1000/2496]\tTrain_Loss 0.39025 (0.42053)\tTrain_Acc 0.79688 (0.84091)\tTrain_G-Mean 0.82470(0.88865)\tTrain_Kappa 0.72523(0.78276)\tTrain_MF1 0.69140(0.79028)\tTrain_Precision 0.68615(0.78462)\tTrain_Sensitivity 0.71377(0.82310)\tTrain_Specificity 0.95288(0.96060)\tTime 0.059s (0.059s)\tSpeed 1077.4 samples/s\tData 0.022s (0.019s)\t\n"]}], "source": ["# Training the model\n", "best_val_acc = 0\n", "best_val_kappa = 0\n", "for epoch_idx in range(n_epochs):  # loop over the dataset multiple times\n", "    if is_neptune:\n", "        run['train/epoch/learning_Rate'].log(optimizer.param_groups[0][\"lr\"]) \n", "    Net.train()\n", "    print(f'===========================================================Training Epoch : [{epoch_idx+1}/{n_epochs}] ===========================================================================================================>')\n", "    batch_time = AverageMeter()\n", "    data_time = AverageMeter()\n", "    \n", "    losses = AverageMeter()\n", "    val_losses = AverageMeter()\n", "    \n", "    train_accuracy = AverageMeter()\n", "    val_accuracy = AverageMeter()\n", "\n", "    train_sensitivity = AverageMeter()\n", "    val_sensitivity = AverageMeter()\n", "    \n", "    train_specificity = AverageMeter()\n", "    val_specificity = AverageMeter()\n", "\n", "    train_gmean = AverageMeter()\n", "    val_gmean = AverageMeter()\n", "\n", "    train_kappa = AverageMeter()\n", "    val_kappa = AverageMeter()\n", "\n", "    train_f1_score = AverageMeter()\n", "    val_f1_score = AverageMeter()\n", "\n", "    train_precision = AverageMeter()\n", "    val_precision = AverageMeter()\n", "\n", "    class1_sens = AverageMeter()\n", "    class2_sens = AverageMeter()\n", "    class3_sens = AverageMeter()\n", "    class4_sens = AverageMeter()\n", "    class5_sens = AverageMeter()\n", "\n", "    class1_spec = AverageMeter()\n", "    class2_spec = AverageMeter()\n", "    class3_spec = AverageMeter()\n", "    class4_spec = AverageMeter()\n", "    class5_spec = AverageMeter()\n", "\n", "    class1_f1 = AverageMeter()\n", "    class2_f1 = AverageMeter()\n", "    class3_f1 = AverageMeter()\n", "    class4_f1 = AverageMeter()\n", "    class5_f1 = AverageMeter()\n", "\n", "    end = time.time()\n", "\n", "    for batch_idx, data_input in enumerate(train_data_loader):\n", "        data_time.update(time.time() - end)\n", "        eeg,eog, labels = data_input\n", "        cur_batch_size = len(eeg)\n", "        \n", "        optimizer.zero_grad()\n", "\n", "        outputs,_,_ = Net(eeg.float().to(device), eog.float().to(device),finetune = True)\n", "\n", "        loss = criterion(outputs.cpu(), labels)\n", "\n", "\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        losses.update(loss.data.item())\n", "        train_accuracy.update(accuracy(outputs.cpu(), labels))\n", "\n", "        _,_,_,_,sens,spec,f1, prec = confusion_matrix(outputs.cpu(), labels, 5, cur_batch_size)\n", "        train_sensitivity.update(sens)\n", "        train_specificity.update(spec)\n", "        train_f1_score.update(f1)\n", "        train_precision.update(prec)\n", "        train_gmean.update(g_mean(sens, spec))\n", "        train_kappa.update(kappa(outputs.cpu(), labels))\n", "        \n", "        if is_neptune:\n", "            run['train/epoch/batch_loss'].log(losses.val)     #1\n", "            run['train/epoch/batch_accuracy'].log(train_accuracy.val)\n", "        \n", "        # measure elapsed time\n", "        batch_time.update(time.time() - end)\n", "        end = time.time()\n", "\n", "\n", "        if batch_idx % 1000 == 0:\n", "            \n", "            msg = 'Epoch: [{0}/{3}][{1}/{2}]\\t' \\\n", "                  'Train_Loss {loss.val:.5f} ({loss.avg:.5f})\\t'\\\n", "                  'Train_Acc {train_acc.val:.5f} ({train_acc.avg:.5f})\\t'\\\n", "                  'Train_G-Mean {train_gmean.val:.5f}({train_gmean.avg:.5f})\\t'\\\n", "                  'Train_Kappa {train_kap.val:.5f}({train_kap.avg:.5f})\\t'\\\n", "                  'Train_MF1 {train_mf1.val:.5f}({train_mf1.avg:.5f})\\t'\\\n", "                  'Train_Precision {train_prec.val:.5f}({train_prec.avg:.5f})\\t'\\\n", "                  'Train_Sensitivity {train_sens.val:.5f}({train_sens.avg:.5f})\\t'\\\n", "                  'Train_Specificity {train_spec.val:.5f}({train_spec.avg:.5f})\\t'\\\n", "                  'Time {batch_time.val:.3f}s ({batch_time.avg:.3f}s)\\t' \\\n", "                  'Speed {speed:.1f} samples/s\\t' \\\n", "                  'Data {data_time.val:.3f}s ({data_time.avg:.3f}s)\\t'.format(\n", "                      epoch_idx+1, batch_idx, len(train_data_loader),n_epochs, batch_time=batch_time,\n", "                      speed=data_input[0].size(0)/batch_time.val,\n", "                      data_time=data_time, loss=losses, train_acc = train_accuracy,\n", "                      train_sens =train_sensitivity, train_spec = train_specificity, train_gmean = train_gmean,\n", "                      train_kap = train_kappa, train_mf1 = train_f1_score, train_prec = train_precision)\n", "            print(msg)\n", "\n", "\n", "    #evaluation\n", "    with torch.no_grad():\n", "      Net.eval()\n", "      for batch_val_idx, data_val in enumerate(val_data_loader):\n", "        val_eeg,val_eog, val_labels = data_val\n", "        cur_val_batch_size = len(val_eeg)\n", "        pred,_,_ = Net(val_eeg.float().to(device), val_eog.float().to(device),finetune = True)\n", "\n", "        val_loss = criterion(pred.cpu(), val_labels)#\n", "        val_losses.update(val_loss.data.item())\n", "        val_accuracy.update(accuracy(pred.cpu(), val_labels))\n", "\n", "        sens_list,spec_list,f1_list,prec_list, sens,spec,f1,prec = confusion_matrix(pred.cpu(), val_labels,  5, cur_val_batch_size)\n", "        val_sensitivity.update(sens)\n", "        val_specificity.update(spec)\n", "        val_f1_score.update(f1)\n", "        val_precision.update(prec)\n", "        val_gmean.update(g_mean(sens, spec))\n", "        val_kappa.update(kappa(pred.cpu(), val_labels))\n", "\n", "        class1_sens.update(sens_list[0])\n", "        class2_sens.update(sens_list[1])\n", "        class3_sens.update(sens_list[2])\n", "        class4_sens.update(sens_list[3])\n", "        class5_sens.update(sens_list[4])\n", "\n", "        class1_spec.update(spec_list[0])\n", "        class2_spec.update(spec_list[1])\n", "        class3_spec.update(spec_list[2])\n", "        class4_spec.update(spec_list[3])\n", "        class5_spec.update(spec_list[4])\n", "\n", "        class1_f1.update(f1_list[0])\n", "        class2_f1.update(f1_list[1])\n", "        class3_f1.update(f1_list[2])\n", "        class4_f1.update(f1_list[3])\n", "        class5_f1.update(f1_list[4])\n", "\n", "      print(batch_val_idx)\n", "\n", "     \n", "\n", "      print(f'===========================================================Epoch : [{epoch_idx+1}/{n_epochs}]  Evaluation ===========================================================================================================>')\n", "      print(\"Training Results : \")\n", "      print(f\"Training Loss     : {losses.avg}, Training Accuracy      : {train_accuracy.avg}, Training G-Mean      : {train_gmean.avg}\") \n", "      print(f\"Training Kappa      : {train_kappa.avg},Training MF1     : {train_f1_score.avg}, Training Precision      : {train_precision.avg}, Training Sensitivity      : {train_sensitivity.avg}, Training Specificity      : {train_specificity.avg}\")\n", "      \n", "      print(\"Validation Results : \")\n", "      print(f\"Validation Loss   : {val_losses.avg}, Validation Accuracy : {val_accuracy.avg}, Validation G-Mean      : {val_gmean.avg}\") \n", "      print(f\"Validation Kappa     : {val_kappa.avg}, Validation MF1      : {val_f1_score.avg}, Validation Precision      : {val_precision.avg},  Validation Sensitivity      : {val_sensitivity.avg}, Validation Specificity      : {val_specificity.avg}\")\n", "    \n", "\n", "      print(f\"Class wise sensitivity W: {class1_sens.avg}, S1: {class2_sens.avg}, S2: {class3_sens.avg}, S3: {class4_sens.avg}, R: {class5_sens.avg}\")\n", "      print(f\"Class wise specificity W: {class1_spec.avg}, S1: {class2_spec.avg}, S2: {class3_spec.avg}, S3: {class4_spec.avg}, R: {class5_spec.avg}\")\n", "      print(f\"Class wise F1  W: {class1_f1.avg}, S1: {class2_f1.avg}, S2: {class3_f1.avg}, S3: {class4_f1.avg}, R: {class5_f1.avg}\")\n", "\n", "      if is_neptune:\n", "        run['train/epoch/epoch_train_loss'].log(losses.avg)\n", "        run['train/epoch/epoch_val_loss'].log(val_losses.avg)\n", "\n", "        run['train/epoch/epoch_train_accuracy'].log(train_accuracy.avg)\n", "        run['train/epoch/epoch_val_accuracy'].log(val_accuracy.avg)\n", "\n", "        run['train/epoch/epoch_train_sensitivity'].log(train_sensitivity.avg)\n", "        run['train/epoch/epoch_val_sensitivity'].log(val_sensitivity.avg)\n", "\n", "        run['train/epoch/epoch_train_specificity'].log(train_specificity.avg)\n", "        run['train/epoch/epoch_val_specificity'].log(val_specificity.avg)\n", "\n", "        run['train/epoch/epoch_train_G-Mean'].log(train_gmean.avg)\n", "        run['train/epoch/epoch_val_G-Mean'].log(val_gmean.avg)\n", "\n", "        run['train/epoch/epoch_train_Kappa'].log(train_kappa.avg)\n", "        run['train/epoch/epoch_val_Kappa'].log(val_kappa.avg)\n", "\n", "        run['train/epoch/epoch_train_MF1 Score'].log(train_f1_score.avg)\n", "        run['train/epoch/epoch_val_MF1 Score'].log(val_f1_score.avg)\n", "\n", "        run['train/epoch/epoch_train_Precision'].log(train_precision.avg)\n", "        run['train/epoch/epoch_val_Precision'].log(val_precision.avg)\n", "\n", "        #################################\n", "\n", "        run['train/epoch/epoch_val_Class wise sensitivity W'].log(class1_sens.avg)\n", "        run['train/epoch/epoch_val_Class wise sensitivity S1'].log(class2_sens.avg)\n", "        run['train/epoch/epoch_val_Class wise sensitivity S2'].log(class3_sens.avg)\n", "        run['train/epoch/epoch_val_Class wise sensitivity S3'].log(class4_sens.avg)\n", "        run['train/epoch/epoch_val_Class wise sensitivity R'].log(class5_sens.avg)\n", "\n", "        run['train/epoch/epoch_val_Class wise specificity W'].log(class1_spec.avg)\n", "        run['train/epoch/epoch_val_Class wise specificity S1'].log(class2_spec.avg)\n", "        run['train/epoch/epoch_val_Class wise specificity S2'].log(class3_spec.avg)\n", "        run['train/epoch/epoch_val_Class wise specificity S3'].log(class4_spec.avg)\n", "        run['train/epoch/epoch_val_Class wise specificity R'].log(class5_spec.avg)\n", "\n", "        run['train/epoch/epoch_val_Class wise F1 Score W'].log(class1_f1.avg)\n", "        run['train/epoch/epoch_val_Class wise F1 Score S1'].log(class2_f1.avg)\n", "        run['train/epoch/epoch_val_Class wise F1 Score S2'].log(class3_f1.avg)\n", "        run['train/epoch/epoch_val_Class wise F1 Score S3'].log(class4_f1.avg)\n", "        run['train/epoch/epoch_val_Class wise F1 Score R'].log(class5_f1.avg)\n", "\n", "      if val_accuracy.avg > best_val_acc or (epoch_idx+1)%100==0 or val_kappa.avg > best_val_kappa:\n", "          if val_accuracy.avg > best_val_acc:\n", "            best_val_acc = val_accuracy.avg\n", "            print(\"================================================================================================\")\n", "            print(\"                                          Saving Best Model (ACC)                                     \")\n", "            print(\"================================================================================================\")\n", "            torch.save(Net, f'{project_path}/checkpoint_model_best_acc.pth.tar')\n", "          if val_kappa.avg > best_val_kappa:\n", "            best_val_kappa = val_kappa.avg\n", "            print(\"================================================================================================\")\n", "            print(\"                                          Saving Best Model (Kappa)                                    \")\n", "            print(\"================================================================================================\")\n", "            torch.save(Net, f'{project_path}/checkpoint_model_best_kappa.pth.tar')\n", "          if (epoch_idx+1)%50==0:\n", "            torch.save(Net, f'{project_path}/checkpoint_model_epoch_{epoch_idx+1}.pth.tar')\n", "    lr_scheduler.step()\n", "         \n", "print('========================================Finished Training ===========================================')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"collapsed_sections": ["mp-EzMvdOlRO", "BGlbwPN1z9Co"], "name": " Jathu_Sleep_Stage_Classification_(Cross Transformer)_sleepEDF_(EEG,EEG2,EOG).ipynb", "provenance": []}, "interpreter": {"hash": "94c91063cbfafe7dd443522ce7f45eaee09f9c12e6866a8b2d3ce13a69535fc6"}, "kernelspec": {"display_name": "sleep_monitoring", "language": "python", "name": "sleep_monitoring"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.15"}}, "nbformat": 4, "nbformat_minor": 4}