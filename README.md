# Cross-Modal-Transformer Sleep Stage Classification

本项目包含两个独立的实现：
1. **CMT (Cross-Modal-Transformer)** - 原始实现
2. **MAMBAFORMER** - 新的改进实现，面向ICASSP 2026论文投稿

## 🎯 项目概述

本项目实现了一个简化但有效的MAMBAFORMER模型，用于自动睡眠阶段分类。采用严格的受试者级别K折交叉验证，确保无数据泄露，真实反映跨受试者泛化性能。

### 主要特点
- **平均准确率**: 81.75% ± 2.12%
- **平均F1分数**: 75.76% ± 2.86%
- **无数据泄露**: 通过严格审计验证
- **多任务学习**: 主任务（5类睡眠分期）+ 辅助任务（REM/SWS检测）

## 📁 项目结构

```
Cross-Modal-Transformer/
├── cmt_original/                 # 原始CMT实现
│   ├── models/                   # CMT模型定义
│   │   ├── sequence_cmt.py      # 序列级CMT
│   │   ├── epoch_cmt.py         # Epoch级CMT
│   │   └── model_blocks.py      # 模型组件
│   ├── preprocessing/            # CMT预处理
│   ├── utils/                    # CMT工具函数
│   └── data_preparations/        # 数据准备脚本
│
├── mambaformer/                  # MAMBAFORMER实现（⭐ 当前重点）
│   ├── models/                   # 模型定义
│   │   ├── simple_attn_mambaformer.py    # ⭐ 主模型
│   │   ├── mambaformer_model.py          # 完整版本
│   │   ├── crossmodal_attention.py       # 跨模态注意力
│   │   └── mamba_ssm.py                  # MAMBA模块
│   ├── training/                 # 训练脚本
│   │   ├── train_subject_aware_mambaformer.py  # ⭐ 主训练脚本
│   │   └── train_*.py                          # 其他版本
│   ├── evaluation/               # 评估工具
│   │   ├── subject_aware_kfold.py        # ⭐ K折分割
│   │   ├── data_leakage_audit.py         # ⭐ 数据审计
│   │   └── test_*.py                     # 测试脚本
│   ├── preprocessing/            # 预处理
│   ├── utils/                    # 工具函数
│   └── run_training.py           # ⭐ 快速运行脚本
│
├── configs/                      # 共享配置文件
│   ├── subject_aware_folds.json          # K折分割配置
│   └── subject_aware_mambaformer_results.json  # 训练结果
│
├── logs/                         # 训练日志
├── checkpoints/                  # 模型检查点
├── datasets/                     # 数据集目录
├── experiments/                  # 实验管理
├── docs/                         # 文档
└── requirements.txt              # 依赖包
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活conda环境
conda activate sl

# 进入项目目录
cd /media/main/ypf/eeg/Cross-Modal-Transformer
```

### 2. 运行MAMBAFORMER实验

#### 方法1: 使用快速运行脚本（推荐）
```bash
cd mambaformer
python run_training.py
```

#### 方法2: 分步运行
```bash
# 生成K折分割
python mambaformer/evaluation/subject_aware_kfold.py

# 验证数据分割（无泄露）
python mambaformer/evaluation/data_leakage_audit.py

# 开始训练
python mambaformer/training/train_subject_aware_mambaformer.py
```

### 5. 查看结果
```bash
# 查看训练日志
tail -f logs/subject_aware_mambaformer_*.log

# 查看最终结果
cat configs/subject_aware_mambaformer_results.json | jq '.summary_statistics'
```

## 📊 实验结果

### K折交叉验证结果
| Fold | 准确率 | F1分数 | 测试受试者 |
|------|--------|--------|------------|
| 1 | 82.16% | 75.20% | 00, 17, 15, 01 |
| 2 | 78.84% | 71.32% | 08, 05, 11, 03 |
| 3 | 80.52% | 76.22% | 18, 16, 13, 02 |
| 4 | 81.99% | 75.77% | 09, 19, 04, 12 |
| 5 | 85.27% | 80.29% | 07, 10, 14, 06 |
| **平均** | **81.75% ± 2.12%** | **75.76% ± 2.86%** | - |

### 各睡眠阶段性能
```
              precision    recall  f1-score
Wake            0.87      0.86      0.87
N1              0.42      0.41      0.41  # 最难分类
N2              0.86      0.84      0.85
N3              0.82      0.90      0.86
REM             0.78      0.79      0.78
```

## 🔧 配置说明

### 训练配置
编辑 `src/training/train_subject_aware_mambaformer.py` 中的配置：
```python
config = {
    'batch_size': 32,
    'learning_rate': 5e-5,
    'weight_decay': 1e-4,
    'num_epochs': 30,
    'patience': 8,
    'max_samples_per_file': 150,
    'use_channels': 3,
}
```

### 模型架构
- **输入**: 3通道EEG信号，30秒窗口（3000个采样点）
- **特征提取**: 3层CNN + BatchNorm + ReLU
- **编码器**: 4层Transformer编码器
- **输出**: 5类睡眠分期 + 2类辅助任务

## 📝 文件说明

### 核心文件
- `src/models/simple_attn_mambaformer.py`: 简化的MAMBAFORMER模型实现
- `src/training/train_subject_aware_mambaformer.py`: 无数据泄露的训练脚本
- `src/evaluation/subject_aware_kfold.py`: 生成受试者级别K折分割
- `src/evaluation/data_leakage_audit.py`: 严格的数据泄露审计工具

### 数据文件
- 原始数据: `/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/`
- 39个NPZ文件，20个受试者（19人×2晚 + 1人×1晚）

## 🎯 后续改进

1. **模型增强**
   - 逐步引入CrossModal注意力机制
   - 实现轻量级MAMBA组件
   - 探索自监督预训练

2. **数据处理**
   - 优化ICA预处理（当前版本过于激进）
   - 自适应伪迹去除
   - 多模态融合（EEG + EOG）

3. **训练策略**
   - 课程学习（从简单到困难）
   - 对比学习增强特征表示
   - 集成学习提升性能

## 📄 引用

如果使用本项目代码，请引用：
```
@inproceedings{yourname2026crossmodal,
  title={Cross-Modal Transformer for Automated Sleep Stage Classification},
  author={Your Name},
  booktitle={ICASSP 2026},
  year={2026}
}
```

## 📧 联系方式

如有问题，请联系：[<EMAIL>]