#!/bin/bash
# 完整Sleep-EDF数据集实验脚本

echo "🚀 开始完整Sleep-EDF数据集实验"
echo "=================================="

# 激活conda环境
eval "$(conda shell.bash hook)"
conda activate sl

# 1. 预处理数据（如果需要）
if [ ! -d "./processed_data" ] || [ -z "$(ls -A ./processed_data)" ]; then
    echo "📊 预处理数据集..."
    python preprocess_full_dataset.py \
        --data_dir /media/main/ypf/eeg/data-edf/sleep_edf_20 \
        --output_dir ./processed_data \
        --subjects "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20"
else
    echo "✓ 数据已预处理，跳过此步骤"
fi

# 2. 训练MAMBAFORMER模型
echo -e "\n🏗️ 训练MAMBAFORMER模型..."
python train_full_dataset.py \
    --model_name mambaformer \
    --data_path ./processed_data \
    --train_subjects "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16" \
    --val_subjects "17,18" \
    --test_subjects "19,20" \
    --batch_size 128 \
    --epochs 100 \
    --lr 1e-4 \
    --patience 15 \
    --experiment_name mambaformer_full_sleepEDF20 \
    --log_dir ./log \
    --save_dir ./checkpoints \
    --use_progressive \
    --device auto

# 3. 训练基准模型（可选）
echo -e "\n🏗️ 训练Cross-Modal Transformer基准模型..."
python train_full_dataset.py \
    --model_name baseline \
    --data_path ./processed_data \
    --train_subjects "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16" \
    --val_subjects "17,18" \
    --test_subjects "19,20" \
    --batch_size 128 \
    --epochs 100 \
    --lr 1e-4 \
    --patience 15 \
    --experiment_name baseline_full_sleepEDF20 \
    --log_dir ./log \
    --save_dir ./checkpoints \
    --device auto

echo -e "\n✅ 实验完成！"
echo "📊 结果保存在 ./log 目录"