{"cells": [{"cell_type": "markdown", "metadata": {"id": "Ij4Cglx7hnfe"}, "source": ["## Get Requirements"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gLKQsBn-hgDN", "outputId": "dbe85e77-445f-4c6d-8e06-95495a5b7263"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["1.10.0\n"]}], "source": ["import torch\n", "from torchvision import transforms, datasets\n", "import torch.nn as nn\n", "from torch import optim as optim\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "#import helpers\n", "import numpy as np\n", "from pathlib import Path\n", "import torch\n", "from torch.utils import data\n", "# import cv2\n", "import math\n", "from PIL import Image\n", "import random\n", "from torch.utils.data import Dataset, DataLoader\n", "import time\n", "import glob\n", "import scipy.signal\n", "from einops import rearrange, reduce, repeat\n", "from einops.layers.torch import Rearrange, Reduce\n", "print(torch.__version__)\n", "\n", "\n", "from datasets.sleep_edf import split_data, SleepEDF_MultiChan_Dataset, SleepEDF_Seq_MultiChan_Dataset\n", "from models.epoch_cmt import Epoch_Cross_Transformer_Network\n", "from models.sequence_cmt import Seq_Cross_Transformer_Network \n", "from utils.metrics import accuracy, kappa, g_mean, plot_confusion_matrix, confusion_matrix, AverageMeter "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"1\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6YSLmONqhyVM", "outputId": "eaa51650-2d1e-4039-a2fe-0e2eb0202362"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Project directory created at mmsm/Experiments/testing\n"]}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "device\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "device \n", "project_path = \"mmsm/Experiments/testing\"\n", "if not os.path.isdir(project_path):\n", "        os.makedirs(project_path)\n", "        print(f\"Project directory created at {project_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "mp-EzMvdOlRO"}, "source": ["### <PERSON> (Ignore this block)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["is_neptune = False\n", "if is_neptune:\n", "    import neptune.new as neptune\n", "\n", "    run = neptune.init(\n", "        project=\"jathurshan0330/V2-Cros\",\n", "        api_token=\"eyJhcGlfYWRkcmVzcyI6Imh0dHBzOi8vYXBwLm5lcHR1bmUuYWkiLCJhcGlfdXJsIjoiaHR0cHM6Ly9hcHAubmVwdHVuZS5haSIsImFwaV9rZXkiOiJmYmRmNjE0Zi0xMDRkLTRlNzUtYmIxNi03NzM2ODBlZDc5NTMifQ==\",\n", "    )  # your credentials"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "pV_9lNe_x0Hd"}, "outputs": [], "source": [" if is_neptune:\n", "    experiment = \"V2-Cros-76\"   #Change This\n", "    !mkdir \"/home/<USER>/fyp_g15_sleep_monitoring/Experiments/Sleep_edfx/V2-Cros-76\"  # Change This"]}, {"cell_type": "markdown", "metadata": {"id": "ARyAHPPkl0JS"}, "source": ["## Data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['/home/<USER>/Sleep_EDF_Dataset/x1.h5' '/home/<USER>/Sleep_EDF_Dataset/x2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/x3.h5' '/home/<USER>/Sleep_EDF_Dataset/x4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/x5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/mean1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/mean2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/mean3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/mean4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/mean5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/std1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/std2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/std3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/std4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/std5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_m1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_m2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_m3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_m4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_m5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_s1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_s2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_s3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eog_s4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eog_s5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eeg_m1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_m2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_m3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_m4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eeg_m5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eeg_m1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_m2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_m3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_m4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eeg_m5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eeg_s1.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_s2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_s3.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/eeg_s4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/eeg_s5.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/y1.h5' '/home/<USER>/Sleep_EDF_Dataset/y2.h5'\n", " '/home/<USER>/Sleep_EDF_Dataset/y3.h5' '/home/<USER>/Sleep_EDF_Dataset/y4.h5']\n", "['/home/<USER>/Sleep_EDF_Dataset/y5.h5']\n", "[4]\n"]}], "source": ["train_data_list = [0,1,2,3]  \n", "val_data_list = [4]  \n", "path = \"/home/<USER>/Sleep_EDF_Dataset\"\n", "\n", "eeg_list = glob.glob(f'{path}/x*.h5')\n", "eeg_list.sort()\n", "[train_eeg_list, val_eeg_list] = split_data(eeg_list,train_data_list,val_data_list)\n", "print(train_eeg_list)\n", "print(val_eeg_list)\n", "\n", "mean_eeg_list = glob.glob(f'{path}/mean*.h5')\n", "mean_eeg_list.sort()\n", "[train_mean_eeg_list, val_mean_eeg_list] = split_data(mean_eeg_list,train_data_list,val_data_list)\n", "print(train_mean_eeg_list)\n", "print(val_mean_eeg_list)\n", "\n", "sd_eeg_list = glob.glob(f'{path}/std*.h5')\n", "sd_eeg_list.sort()\n", "[train_sd_eeg_list, val_sd_eeg_list] = split_data(sd_eeg_list,train_data_list,val_data_list)\n", "print(train_sd_eeg_list)\n", "print(val_sd_eeg_list)\n", "\n", "#########################################################################################################################\n", "\n", "eog_list = glob.glob(f'{path}/eog*.h5')\n", "eog_list.sort()\n", "[train_eog_list, val_eog_list] = split_data(eog_list,train_data_list,val_data_list)\n", "print(train_eog_list)\n", "print(val_eog_list)\n", "\n", "mean_eog_list = glob.glob(f'{path}/eog_m*.h5')\n", "mean_eog_list.sort()\n", "[train_mean_eog_list, val_mean_eog_list] = split_data(mean_eog_list,train_data_list,val_data_list)\n", "print(train_mean_eog_list)\n", "print(val_mean_eog_list)\n", "\n", "sd_eog_list = glob.glob(f'{path}/eog_s*.h5')\n", "sd_eog_list.sort()\n", "[train_sd_eog_list, val_sd_eog_list] = split_data(sd_eog_list,train_data_list,val_data_list)\n", "print(train_sd_eog_list)\n", "print(val_sd_eog_list)\n", "\n", "\n", "\n", "eeg2_list = glob.glob(f'{path}/eeg*.h5')\n", "eeg2_list.sort()\n", "[train_eeg2_list, val_eeg2_list] = split_data(eeg2_list,train_data_list,val_data_list)\n", "print(train_eeg2_list)\n", "print(val_eeg2_list)\n", "\n", "mean_eeg2_list = glob.glob(f'{path}/eeg_m*.h5')\n", "mean_eeg2_list.sort()\n", "[train_mean_eeg2_list, val_mean_eeg2_list] = split_data(mean_eeg2_list,train_data_list,val_data_list)\n", "print(train_mean_eeg2_list)\n", "print(val_mean_eeg2_list)\n", "\n", "sd_eeg2_list = glob.glob(f'{path}/eeg_s*.h5')\n", "sd_eeg2_list.sort()\n", "[train_sd_eeg2_list, val_sd_eeg2_list] = split_data(sd_eeg2_list,train_data_list,val_data_list)\n", "print(train_sd_eeg2_list)\n", "print(val_sd_eeg2_list)\n", "\n", "\n", "\n", "label_list = glob.glob(f'{path}/y*.h5')\n", "label_list.sort()\n", "[train_label_list, val_label_list] = split_data(label_list,train_data_list,val_data_list)\n", "print(train_label_list)\n", "print(val_label_list)\n", "\n", "\n", "print(val_data_list)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ELCDezM1Qkjs", "outputId": "3b314d02-e1cb-4164-8457-93800f41952f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading from /home/<USER>/Sleep_EDF_Dataset/x1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 43754\n", "Shape of each data : (43754, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 43754\n", "Shape of each data : (43754, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 40155\n", "Shape of each data : (40155, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 40155\n", "Shape of each data : (40155, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 35373\n", "Shape of each data : (35373, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 35373\n", "Shape of each data : (35373, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 40411\n", "Shape of each data : (40411, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 40411\n", "Shape of each data : (40411, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Labels count: [54292 17253 56137 10814 21197]\n", "Shape of EEG : (159693, 1, 3000) , EOG : (159693, 1, 3000)\n", "Shape of Labels : <PERSON>.<PERSON>ze([159693])\n", "Reading Subject wise mean and sd\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s1.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 43754\n", "Shape of each data : (43754,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s2.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 40155\n", "Shape of each data : (40155,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s3.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 35373\n", "Shape of each data : (35373,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s4.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 40411\n", "Shape of each data : (40411,)\n", "Shapes of Mean  : EEG: (159693,), EOG : (159693,)\n", "Shapes of Sd  : EEG: (159693,), EOG : (159693,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/x5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['x']>\n", "Number of samples : 36657\n", "Shape of each data : (36657, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog']>\n", "Number of samples : 36657\n", "Shape of each data : (36657, 1, 3000)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/y5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['y']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Labels count: [12530  4269 12995  2225  4638]\n", "Shape of EEG : (36657, 1, 3000) , EOG : (36657, 1, 3000)\n", "Shape of Labels : torch.Size([36657])\n", "Reading Subject wise mean and sd\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/mean5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['mean']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/std5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['std']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_m5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog mean']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Reading from /home/<USER>/Sleep_EDF_Dataset/eog_s5.h5 ====================================================\n", "Keys in the h5py file : <KeysViewHDF5 ['eog std']>\n", "Number of samples : 36657\n", "Shape of each data : (36657,)\n", "Shapes of Mean  : EEG: (36657,), EOG : (36657,)\n", "Shapes of Sd  : EEG: (36657,), EOG : (36657,)\n"]}], "source": ["num_seq = 5\n", "train_dataset = SleepEDF_Seq_MultiChan_Dataset(eeg_file = train_eeg_list , \n", "                                           eog_file = train_eog_list, \n", "                                           label_file = train_label_list, \n", "                                           device = device, mean_eeg_l = train_mean_eeg_list, sd_eeg_l = train_sd_eeg_list, \n", "                                           mean_eog_l = train_mean_eog_list, sd_eog_l = train_sd_eog_list, \n", "                                           sub_wise_norm = True,\n", "                                           num_seq = num_seq,\n", "                                           transform=transforms.Compose([\n", "                                               transforms.To<PERSON><PERSON><PERSON>(),\n", "                                                ]) )\n", "\n", "val_dataset = SleepEDF_Seq_MultiChan_Dataset(eeg_file = val_eeg_list ,\n", "                                         eog_file = val_eog_list, \n", "                                         label_file = val_label_list, \n", "                                         device = device, mean_eeg_l = val_mean_eeg_list, sd_eeg_l = val_sd_eeg_list,\n", "                                         mean_eog_l = val_mean_eog_list, sd_eog_l = val_sd_eog_list,\n", "                                         sub_wise_norm = True, num_seq = num_seq,\n", "                                         transform=transforms.Compose([\n", "                                               transforms.To<PERSON><PERSON><PERSON>(),\n", "                                                ]) )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "IpXQ66ipml-q"}, "outputs": [], "source": ["batch_size = 8\n", "train_data_loader = data.DataLoader(train_dataset, batch_size = batch_size, shuffle = True)\n", "val_data_loader = data.DataLoader(val_dataset, batch_size = batch_size, shuffle = True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "O3c5Neaqmu6-", "outputId": "2dd0adc7-2e82-48aa-81b8-ac34d19b7f74"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EEG batch shape: torch.Size([8, 1, 5, 3000])\n", "EOG batch shape: torch.Size([8, 1, 5, 3000])\n", "Labels batch shape: torch.Size([8, 5])\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["EEG batch shape: torch.Size([8, 1, 5, 3000])\n", "EOG batch shape: torch.Size([8, 1, 5, 3000])\n", "Labels batch shape: torch.Size([8, 5])\n"]}, {"data": {"image/png": "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*****************************************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\n", "text/plain": ["<Figure size 720x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["EEG Minimum :-7.16865294787065\n", "EEG Maximum :8.752567039153805\n", "EOG Minimum :-8.662216323035913\n", "EOG Maximum :8.69009413953209\n", "EEG Mean :-0.001596284507227166\n", "EEG Standard Deviation :0.8237608708685435\n", "EOG Mean :-0.0006302369468444693\n", "EOG Standard Deviation :0.6388035075029015\n"]}], "source": ["eeg_data, eog_data, label = next(iter(train_data_loader))\n", "print(f\"EEG batch shape: {eeg_data.size()}\")\n", "print(f\"EOG batch shape: {eog_data.size()}\")\n", "# print(f\"EMG batch shape: {eeg2_data.size()}\")\n", "print(f\"Labels batch shape: {label.size()}\")\n", "\n", "eeg_data_temp = torch.reshape(eeg_data[0],(1,eeg_data[0].shape[1]*eeg_data[0].shape[2]))\n", "eog_data_temp = torch.reshape(eog_data[0],(1,eog_data[0].shape[1]*eog_data[0].shape[2]))\n", "\n", "t = np.arange(0,30,1/100)\n", "plt.figure(figsize = (10,10))\n", "plt.plot(eeg_data_temp[0].squeeze())\n", "plt.plot(eog_data_temp[0].squeeze()+5)\n", "# plt.plot(t,eeg2_data[0].squeeze()+10)\n", "plt.title(f\"Label {label[0].squeeze()}\")\n", "plt.show()\n", "\n", "eeg_data, eog_data, label = next(iter(val_data_loader))\n", "print(f\"EEG batch shape: {eeg_data.size()}\")\n", "print(f\"EOG batch shape: {eog_data.size()}\")\n", "# print(f\"EMG batch shape: {eeg2_data.size()}\")\n", "print(f\"Labels batch shape: {label.size()}\")\n", "\n", "eeg_data_temp = torch.reshape(eeg_data[0],(1,eeg_data[0].shape[1]*eeg_data[0].shape[2]))\n", "eog_data_temp = torch.reshape(eog_data[0],(1,eog_data[0].shape[1]*eog_data[0].shape[2]))\n", "\n", "# t = np.arange(0,30,1/100)\n", "plt.figure(figsize = (10,10))\n", "plt.plot(eeg_data_temp[0].squeeze())\n", "plt.plot(eog_data_temp[0].squeeze()+5)\n", "# plt.plot(t,eeg2_data[0].squeeze()+10)\n", "plt.title(f\"Label {label[0].squeeze()}\")\n", "plt.show()\n", "\n", "\n", "print(f\"EEG Minimum :{eeg_data.min()}\")\n", "print(f\"EEG Maximum :{eeg_data.max()}\")\n", "print(f\"EOG Minimum :{eog_data.min()}\")\n", "print(f\"EOG Maximum :{eog_data.max()}\")\n", "# print(f\"EMG Minimum :{eeg2_data.min()}\")\n", "# print(f\"EMG Maximum :{eeg2_data.max()}\")\n", "\n", "\n", "print(f\"EEG Mean :{torch.mean(eeg_data)}\")\n", "print(f\"EEG Standard Deviation :{torch.std(eeg_data)}\")\n", "print(f\"EOG Mean :{torch.mean(eog_data)}\")\n", "print(f\"EOG Standard Deviation :{torch.std(eog_data)}\")\n", "# print(f\"EMG Mean :{torch.mean(eeg2_data)}\")\n", "# print(f\"EMG Standard Deviation :{torch.std(eeg2_data)}\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "MAv7VEk-z9Ch"}, "source": ["### Classification Model Sequence Cross-Modal Transformer"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BBI0JVRcz9Ck", "outputId": "5a21dd30-94a8-4779-b291-d4cb1a97eae2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["weights: tensor([1., 2., 1., 2., 2.])\n", "5 torch.<PERSON><PERSON>([8, 5])\n"]}], "source": ["import torch.optim as optim\n", "\n", "d_model = 256 \n", "dim_feedforward=1024  #\n", "window_size = 50#25 50\n", "Net = Seq_Cross_Transformer_Network(d_model = d_model, dim_feedforward=dim_feedforward,\n", "                                window_size = window_size ).to(device)\n", "# Net = torch.load('/home/<USER>/fyp_g15_sleep_monitoring/Experiments/Sleep_edfx/V2_Seq_CMT/V2SEQ-32/checkpoint_model_best_kappa.pth.tar').to(device)\n", "\n", "lr = 0.001#0.001\n", "beta_1 =  0.9    \n", "beta_2 =  0.999    \n", "eps = 1e-9\n", "n_epochs = 1000\n", "weights = torch.tensor([1., 2., 1., 2., 2.]) \n", "print(f\"weights: {weights}\")\n", "criterion = nn.CrossEntropyLoss(weight=weights)\n", "optimizer = torch.optim.Adam(Net.parameters(), lr=lr, betas=(beta_1, beta_2),eps = eps, weight_decay = 0.0001)\n", "lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.5) \n", "\n", "pred= Net(eeg_data.float().to(device), eog_data.float().to(device))\n", "print(len(pred),pred[0].shape)#,cls_outs.shape,len(feat_list))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "wCowMlFVz9Cl"}, "outputs": [], "source": ["if is_neptune:\n", "    parameters = {\n", "        \"Experiment\" : \"Training test\",\n", "        'Model Type' : \"Epoch Cross-Modal Transformer\",\n", "        'd_model' : d_model,\n", "        'dim_feedforward' : dim_feedforward,\n", "        'window_size ':window_size ,\n", "        'Batch Size': batch_size,\n", "        'Loss': f\"Weighted Categorical Loss,{weights}\", \n", "        'Optimizer' : \"<PERSON>\",        # Check this every time   \n", "        'Learning Rate': lr,\n", "        'eps' : eps,\n", "        \"LR Schduler\": \"StepLR\",\n", "        'Beta 1': beta_1,\n", "        'Beta 2': beta_2,\n", "        'n_epochs': n_epochs,\n", "        'val_set' : val_data_list[0]+1,\n", "        'num seq': num_seq\n", "    }\n", "    \n", "    run['model/parameters'] = parameters\n", "    run['model/model_architecture'] = Net"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Training"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_4Pl9HUrz9Cn", "outputId": "378a408d-cc32-4248-a2b6-a036a00d34e2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===========================================================Training Epoch : [1/1000] ===========================================================================================================>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/work/Cross-Modal-Transformer/utils/metrics.py:136: UserWarning: indexing with dtype torch.uint8 is now deprecated, please use a dtype torch.bool instead. (Triggered internally at  /opt/conda/conda-bld/pytorch_1634272155627/work/aten/src/ATen/native/IndexingUtils.h:30.)\n", "  FP = conf_matrix[c, idx].sum()\n", "/home/<USER>/work/Cross-Modal-Transformer/utils/metrics.py:137: UserWarning: indexing with dtype torch.uint8 is now deprecated, please use a dtype torch.bool instead. (Triggered internally at  /opt/conda/conda-bld/pytorch_1634272155627/work/aten/src/ATen/native/IndexingUtils.h:30.)\n", "  FN = conf_matrix[idx, c].sum()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch: [1/1000][0/19961]\tTrain_Loss 8.05284 (8.05284)\tTrain_Acc 0.12500 (0.32500)\tTrain_Kappa -0.07692(0.04744)\tTrain_MF1 0.05714(0.12106)\tTrain_G-Mean 0.22804(0.38173)\tTrain_Precision 0.05000(0.09733)\tTrain_Sensitivity 0.06667(0.18667)\tTrain_Specificity 0.78000(0.81033)\tTime 0.342s (0.342s)\tSpeed 23.4 samples/s\tData 0.049s (0.049s)\t\n", "Epoch: [1/1000][1000/19961]\tTrain_Loss 7.21841 (6.65465)\tTrain_Acc 0.37500 (0.51633)\tTrain_Kappa 0.20000(0.32354)\tTrain_MF1 0.23333(0.29159)\tTrain_G-Mean 0.57900(0.52855)\tTrain_Precision 0.16667(0.30360)\tTrain_Sensitivity 0.40000(0.33395)\tTrain_Specificity 0.83810(0.87263)\tTime 0.164s (0.169s)\tSpeed 48.7 samples/s\tData 0.011s (0.012s)\t\n", "Epoch: [1/1000][2000/19961]\tTrain_Loss 5.26556 (6.53659)\tTrain_Acc 0.62500 (0.52867)\tTrain_Kappa 0.33333(0.33593)\tTrain_MF1 0.24000(0.29989)\tTrain_G-Mean 0.46771(0.53484)\tTrain_Precision 0.33333(0.31222)\tTrain_Sensitivity 0.25000(0.33993)\tTrain_Specificity 0.87500(0.87502)\tTime 0.167s (0.170s)\tSpeed 47.8 samples/s\tData 0.020s (0.012s)\t\n", "Epoch: [1/1000][3000/19961]\tTrain_Loss 6.08611 (6.45015)\tTrain_Acc 0.50000 (0.53224)\tTrain_Kappa 0.17949(0.33988)\tTrain_MF1 0.23333(0.30490)\tTrain_G-Mean 0.44140(0.53796)\tTrain_Precision 0.23333(0.31807)\tTrain_Sensitivity 0.23333(0.34350)\tTrain_Specificity 0.83500(0.87572)\tTime 0.165s (0.170s)\tSpeed 48.4 samples/s\tData 0.011s (0.012s)\t\n", "Epoch: [1/1000][4000/19961]\tTrain_Loss 5.78962 (6.46873)\tTrain_Acc 0.75000 (0.52986)\tTrain_Kappa 0.61905(0.33636)\tTrain_MF1 0.43810(0.30433)\tTrain_G-Mean 0.68243(0.53699)\tTrain_Precision 0.45000(0.31806)\tTrain_Sensitivity 0.50000(0.34273)\tTrain_Specificity 0.93143(0.87498)\tTime 0.203s (0.170s)\tSpeed 39.4 samples/s\tData 0.015s (0.012s)\t\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-12-2d6a357a1ca6>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     61\u001b[0m         \u001b[0moptimizer\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mzero_grad\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     62\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 63\u001b[0;31m         \u001b[0moutputs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mNet\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0meeg\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfloat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mto\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdevice\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0meog\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfloat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mto\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdevice\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     64\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     65\u001b[0m         \u001b[0mloss\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m_call_impl\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m   1100\u001b[0m         if not (self._backward_hooks or self._forward_hooks or self._forward_pre_hooks or _global_backward_hooks\n\u001b[1;32m   1101\u001b[0m                 or _global_forward_hooks or _global_forward_pre_hooks):\n\u001b[0;32m-> 1102\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mforward_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1103\u001b[0m         \u001b[0;31m# Do not call functions when jit is used\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1104\u001b[0m         \u001b[0mfull_backward_hooks\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnon_full_backward_hooks\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/work/Cross-Modal-Transformer/models/sequence_cmt.py\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, eeg, eog, num_seg)\u001b[0m\n\u001b[1;32m     77\u001b[0m         \u001b[0mepoch_3\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mepoch_3\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0meeg\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0meog\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     78\u001b[0m         \u001b[0mepoch_4\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mepoch_4\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0meeg\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m3\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0meog\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m3\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 79\u001b[0;31m         \u001b[0mepoch_5\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mepoch_5\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0meeg\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m4\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0meog\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m4\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     80\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     81\u001b[0m         \u001b[0mseq\u001b[0m \u001b[0;34m=\u001b[0m  \u001b[0mtorch\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mepoch_1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mepoch_2\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mepoch_3\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mepoch_4\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mepoch_5\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdim\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m_call_impl\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m   1100\u001b[0m         if not (self._backward_hooks or self._forward_hooks or self._forward_pre_hooks or _global_backward_hooks\n\u001b[1;32m   1101\u001b[0m                 or _global_forward_hooks or _global_forward_pre_hooks):\n\u001b[0;32m-> 1102\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mforward_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1103\u001b[0m         \u001b[0;31m# Do not call functions when jit is used\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1104\u001b[0m         \u001b[0mfull_backward_hooks\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnon_full_backward_hooks\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/work/Cross-Modal-Transformer/models/sequence_cmt.py\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, eeg, eog)\u001b[0m\n\u001b[1;32m     30\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     31\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0meeg\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mTensor\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0meog\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mTensor\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;31m#,finetune = False):\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 32\u001b[0;31m         \u001b[0mself_eeg\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0meeg_atten\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0meeg\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     33\u001b[0m         \u001b[0mself_eog\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0meog_atten\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0meog\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     34\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m_call_impl\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m   1100\u001b[0m         if not (self._backward_hooks or self._forward_hooks or self._forward_pre_hooks or _global_backward_hooks\n\u001b[1;32m   1101\u001b[0m                 or _global_forward_hooks or _global_forward_pre_hooks):\n\u001b[0;32m-> 1102\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mforward_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1103\u001b[0m         \u001b[0;31m# Do not call functions when jit is used\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1104\u001b[0m         \u001b[0mfull_backward_hooks\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnon_full_backward_hooks\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/work/Cross-Modal-Transformer/models/model_blocks.py\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m    141\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    142\u001b[0m         \u001b[0msrc2\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mself_attn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msrc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msrc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msrc\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 143\u001b[0;31m         \u001b[0mout\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msrc\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropout\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msrc2\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    144\u001b[0m         \u001b[0mout\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnorm\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mout\u001b[0m\u001b[0;34m)\u001b[0m   \u001b[0;31m########\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    145\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mout\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m_call_impl\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m   1100\u001b[0m         if not (self._backward_hooks or self._forward_hooks or self._forward_pre_hooks or _global_backward_hooks\n\u001b[1;32m   1101\u001b[0m                 or _global_forward_hooks or _global_forward_pre_hooks):\n\u001b[0;32m-> 1102\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mforward_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1103\u001b[0m         \u001b[0;31m# Do not call functions when jit is used\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1104\u001b[0m         \u001b[0mfull_backward_hooks\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnon_full_backward_hooks\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/torch/nn/modules/dropout.py\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, input)\u001b[0m\n\u001b[1;32m     56\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     57\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mTensor\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mTensor\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 58\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mF\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropout\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minplace\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     59\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     60\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/conda-envs/sleep_monitoring/lib/python3.6/site-packages/torch/nn/functional.py\u001b[0m in \u001b[0;36mdropout\u001b[0;34m(input, p, training, inplace)\u001b[0m\n\u001b[1;32m   1167\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mp\u001b[0m \u001b[0;34m<\u001b[0m \u001b[0;36m0.0\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0mp\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1.0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1168\u001b[0m         \u001b[0;32mraise\u001b[0m \u001b[0mValueError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"dropout probability has to be between 0 and 1, \"\u001b[0m \u001b[0;34m\"but got {}\"\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mformat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1169\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0m_VF\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropout_\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mp\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtraining\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0minplace\u001b[0m \u001b[0;32melse\u001b[0m \u001b[0m_VF\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropout\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mp\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtraining\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1170\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1171\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# Training the model\n", "best_val_acc = 0\n", "best_val_kappa = 0\n", "for epoch_idx in range(n_epochs):  # loop over the dataset multiple times\n", "    if is_neptune:\n", "        run['train/epoch/learning_Rate'].log(optimizer.param_groups[0][\"lr\"]) \n", "    Net.train()\n", "    print(f'===========================================================Training Epoch : [{epoch_idx+1}/{n_epochs}] ===========================================================================================================>')\n", "    batch_time = AverageMeter()\n", "    data_time = AverageMeter()\n", "    \n", "    losses = AverageMeter()\n", "    val_losses = AverageMeter()\n", "    \n", "    train_accuracy = AverageMeter()\n", "    val_accuracy = AverageMeter()\n", "\n", "    train_sensitivity = AverageMeter()\n", "    val_sensitivity = AverageMeter()\n", "    \n", "    train_specificity = AverageMeter()\n", "    val_specificity = AverageMeter()\n", "\n", "    train_gmean = AverageMeter()\n", "    val_gmean = AverageMeter()\n", "\n", "    train_kappa = AverageMeter()\n", "    val_kappa = AverageMeter()\n", "\n", "    train_f1_score = AverageMeter()\n", "    val_f1_score = AverageMeter()\n", "\n", "    train_precision = AverageMeter()\n", "    val_precision = AverageMeter()\n", "\n", "    class1_sens = AverageMeter()\n", "    class2_sens = AverageMeter()\n", "    class3_sens = AverageMeter()\n", "    class4_sens = AverageMeter()\n", "    class5_sens = AverageMeter()\n", "\n", "    class1_spec = AverageMeter()\n", "    class2_spec = AverageMeter()\n", "    class3_spec = AverageMeter()\n", "    class4_spec = AverageMeter()\n", "    class5_spec = AverageMeter()\n", "\n", "    class1_f1 = AverageMeter()\n", "    class2_f1 = AverageMeter()\n", "    class3_f1 = AverageMeter()\n", "    class4_f1 = AverageMeter()\n", "    class5_f1 = AverageMeter()\n", "\n", "    end = time.time()\n", "\n", "    for batch_idx, data_input in enumerate(train_data_loader):\n", "        # get the inputs; data is a list of [inputs, labels]\n", "        data_time.update(time.time() - end)\n", "        eeg,eog, labels = data_input\n", "        cur_batch_size = len(eeg)\n", "        optimizer.zero_grad()\n", "\n", "        outputs = Net(eeg.float().to(device), eog.float().to(device))\n", "\n", "        loss = 0\n", "        for ep in range(num_seq):\n", "          loss += criterion(outputs[ep].cpu(), labels[:,ep])\n", "\n", "\n", "          train_accuracy.update(accuracy(outputs[ep].cpu(), labels[:,ep]))\n", "          _,_,_,_,sens,spec,f1, prec = confusion_matrix(outputs[ep].cpu(), labels[:,ep], 5, cur_batch_size)\n", "          train_sensitivity.update(sens)\n", "          train_specificity.update(spec)\n", "          train_f1_score.update(f1)\n", "          train_precision.update(prec)\n", "          train_gmean.update(g_mean(sens, spec))\n", "          train_kappa.update(kappa(outputs[ep].cpu(), labels[:,ep]))\n", "        \n", "        loss.backward()\n", "        optimizer.step()\n", "        # scheduler.step()\n", "        losses.update(loss)\n", "        \n", "        if is_neptune:\n", "            run['train/epoch/batch_loss'].log(losses.val)     \n", "            run['train/epoch/batch_accuracy'].log(train_accuracy.val)\n", "        \n", "        # measure elapsed time\n", "        batch_time.update(time.time() - end)\n", "        end = time.time()\n", "\n", "\n", "        if batch_idx % 1000 == 0:\n", "            msg = 'Epoch: [{0}/{3}][{1}/{2}]\\t' \\\n", "                  'Train_Loss {loss.val:.5f} ({loss.avg:.5f})\\t'\\\n", "                  'Train_Acc {train_acc.val:.5f} ({train_acc.avg:.5f})\\t'\\\n", "                  'Train_Kappa {train_kap.val:.5f}({train_kap.avg:.5f})\\t'\\\n", "                  'Train_MF1 {train_mf1.val:.5f}({train_mf1.avg:.5f})\\t'\\\n", "                  'Train_G-Mean {train_gmean.val:.5f}({train_gmean.avg:.5f})\\t'\\\n", "                  'Train_Precision {train_prec.val:.5f}({train_prec.avg:.5f})\\t'\\\n", "                  'Train_Sensitivity {train_sens.val:.5f}({train_sens.avg:.5f})\\t'\\\n", "                  'Train_Specificity {train_spec.val:.5f}({train_spec.avg:.5f})\\t'\\\n", "                  'Time {batch_time.val:.3f}s ({batch_time.avg:.3f}s)\\t' \\\n", "                  'Speed {speed:.1f} samples/s\\t' \\\n", "                  'Data {data_time.val:.3f}s ({data_time.avg:.3f}s)\\t'.format(\n", "                      epoch_idx+1, batch_idx, len(train_data_loader),n_epochs, batch_time=batch_time,\n", "                      speed=data_input[0].size(0)/batch_time.val,\n", "                      data_time=data_time, loss=losses, train_acc = train_accuracy,\n", "                      train_sens =train_sensitivity, train_spec = train_specificity, train_gmean = train_gmean,\n", "                      train_kap = train_kappa, train_mf1 = train_f1_score, train_prec = train_precision)\n", "            \n", "            print(msg)\n", "\n", "\n", "    #evaluation\n", "    with torch.no_grad():\n", "      Net.eval()\n", "      for batch_val_idx, data_val in enumerate(val_data_loader):\n", "        val_eeg,val_eog, val_labels = data_val\n", "        cur_val_batch_size = len(val_eeg)\n", "        pred = Net(val_eeg.float().to(device), val_eog.float().to(device))\n", "        \n", "        val_loss = 0\n", "        for ep in range(num_seq):\n", "          val_loss += criterion(pred[ep].cpu(), val_labels[:,ep])\n", "\n", "          val_accuracy.update(accuracy(pred[ep].cpu(), val_labels[:,ep]))\n", "          sens_list,spec_list,f1_list,prec_list, sens,spec,f1,prec = confusion_matrix(pred[ep].cpu(), val_labels[:,ep],  5, cur_val_batch_size)\n", "          val_sensitivity.update(sens)\n", "          val_specificity.update(spec)\n", "          val_f1_score.update(f1)\n", "          val_precision.update(prec)\n", "          val_gmean.update(g_mean(sens, spec))\n", "          val_kappa.update(kappa(pred[ep].cpu(), val_labels[:,ep]))\n", "\n", "          class1_sens.update(sens_list[0])\n", "          class2_sens.update(sens_list[1])\n", "          class3_sens.update(sens_list[2])\n", "          class4_sens.update(sens_list[3])\n", "          class5_sens.update(sens_list[4])\n", "\n", "          class1_spec.update(spec_list[0])\n", "          class2_spec.update(spec_list[1])\n", "          class3_spec.update(spec_list[2])\n", "          class4_spec.update(spec_list[3])\n", "          class5_spec.update(spec_list[4])\n", "\n", "          class1_f1.update(f1_list[0])\n", "          class2_f1.update(f1_list[1])\n", "          class3_f1.update(f1_list[2])\n", "          class4_f1.update(f1_list[3])\n", "          class5_f1.update(f1_list[4])\n", "\n", "        \n", "        val_losses.update(val_loss)#.data.item())\n", "        \n", "\n", "      print(batch_val_idx)\n", "\n", "     \n", "\n", "      print(f'===========================================================Epoch : [{epoch_idx+1}/{n_epochs}]  Evaluation ===========================================================================================================>')\n", "      print(\"Training Results : \")\n", "      print(f\"Training Loss     : {losses.avg}, Training Accuracy      : {train_accuracy.avg}, Training G-Mean      : {train_gmean.avg}\") \n", "      print(f\"Training Kappa      : {train_kappa.avg},Training MF1     : {train_f1_score.avg}, Training Precision      : {train_precision.avg}, Training Sensitivity      : {train_sensitivity.avg}, Training Specificity      : {train_specificity.avg}\")\n", "      \n", "      print(\"Validation Results : \")\n", "      print(f\"Validation Loss   : {val_losses.avg}, Validation Accuracy : {val_accuracy.avg}, Validation G-Mean      : {val_gmean.avg}\") \n", "      print(f\"Validation Kappa     : {val_kappa.avg}, Validation MF1      : {val_f1_score.avg}, Validation Precision      : {val_precision.avg},  Validation Sensitivity      : {val_sensitivity.avg}, Validation Specificity      : {val_specificity.avg}\")\n", "    \n", "\n", "      print(f\"Class wise sensitivity W: {class1_sens.avg}, S1: {class2_sens.avg}, S2: {class3_sens.avg}, S3: {class4_sens.avg}, R: {class5_sens.avg}\")\n", "      print(f\"Class wise specificity W: {class1_spec.avg}, S1: {class2_spec.avg}, S2: {class3_spec.avg}, S3: {class4_spec.avg}, R: {class5_spec.avg}\")\n", "      print(f\"Class wise F1  W: {class1_f1.avg}, S1: {class2_f1.avg}, S2: {class3_f1.avg}, S3: {class4_f1.avg}, R: {class5_f1.avg}\")\n", "\n", "      if is_neptune:\n", "          run['train/epoch/epoch_train_loss'].log(losses.avg)\n", "          run['train/epoch/epoch_val_loss'].log(val_losses.avg)\n", "\n", "          run['train/epoch/epoch_train_accuracy'].log(train_accuracy.avg)\n", "          run['train/epoch/epoch_val_accuracy'].log(val_accuracy.avg)\n", "\n", "          run['train/epoch/epoch_train_sensitivity'].log(train_sensitivity.avg)\n", "          run['train/epoch/epoch_val_sensitivity'].log(val_sensitivity.avg)\n", "\n", "          run['train/epoch/epoch_train_specificity'].log(train_specificity.avg)\n", "          run['train/epoch/epoch_val_specificity'].log(val_specificity.avg)\n", "\n", "          run['train/epoch/epoch_train_G-Mean'].log(train_gmean.avg)\n", "          run['train/epoch/epoch_val_G-Mean'].log(val_gmean.avg)\n", "\n", "          run['train/epoch/epoch_train_Kappa'].log(train_kappa.avg)\n", "          run['train/epoch/epoch_val_Kappa'].log(val_kappa.avg)\n", "\n", "          run['train/epoch/epoch_train_MF1 Score'].log(train_f1_score.avg)\n", "          run['train/epoch/epoch_val_MF1 Score'].log(val_f1_score.avg)\n", "\n", "          run['train/epoch/epoch_train_Precision'].log(train_precision.avg)\n", "          run['train/epoch/epoch_val_Precision'].log(val_precision.avg)\n", "\n", "          #################################\n", "\n", "          run['train/epoch/epoch_val_Class wise sensitivity W'].log(class1_sens.avg)\n", "          run['train/epoch/epoch_val_Class wise sensitivity S1'].log(class2_sens.avg)\n", "          run['train/epoch/epoch_val_Class wise sensitivity S2'].log(class3_sens.avg)\n", "          run['train/epoch/epoch_val_Class wise sensitivity S3'].log(class4_sens.avg)\n", "          run['train/epoch/epoch_val_Class wise sensitivity R'].log(class5_sens.avg)\n", "\n", "          run['train/epoch/epoch_val_Class wise specificity W'].log(class1_spec.avg)\n", "          run['train/epoch/epoch_val_Class wise specificity S1'].log(class2_spec.avg)\n", "          run['train/epoch/epoch_val_Class wise specificity S2'].log(class3_spec.avg)\n", "          run['train/epoch/epoch_val_Class wise specificity S3'].log(class4_spec.avg)\n", "          run['train/epoch/epoch_val_Class wise specificity R'].log(class5_spec.avg)\n", "\n", "          run['train/epoch/epoch_val_Class wise F1 Score W'].log(class1_f1.avg)\n", "          run['train/epoch/epoch_val_Class wise F1 Score S1'].log(class2_f1.avg)\n", "          run['train/epoch/epoch_val_Class wise F1 Score S2'].log(class3_f1.avg)\n", "          run['train/epoch/epoch_val_Class wise F1 Score S3'].log(class4_f1.avg)\n", "          run['train/epoch/epoch_val_Class wise F1 Score R'].log(class5_f1.avg)\n", "\n", "      if val_accuracy.avg > best_val_acc or (epoch_idx+1)%100==0 or val_kappa.avg > best_val_kappa:\n", "          if val_accuracy.avg > best_val_acc:\n", "            run['model/bestmodel_acc'].log(epoch_idx+1)\n", "            best_val_acc = val_accuracy.avg\n", "            print(\"================================================================================================\")\n", "            print(\"                                          Saving Best Model (ACC)                                     \")\n", "            print(\"================================================================================================\")\n", "            torch.save(Net, f'{project_path}/checkpoint_model_best_acc.pth.tar')\n", "          if val_kappa.avg > best_val_kappa:\n", "            run['model/bestmodel_kappa'].log(epoch_idx+1)\n", "            best_val_kappa = val_kappa.avg\n", "            print(\"================================================================================================\")\n", "            print(\"                                          Saving Best Model (Kappa)                                    \")\n", "            print(\"================================================================================================\")\n", "            torch.save(Net, f'{project_path}/checkpoint_model_best_kappa.pth.tar')\n", "          if (epoch_idx+1)%50==0:\n", "            torch.save(Net, f'{project_path}/checkpoint_model_epoch_{epoch_idx+1}.pth.tar')\n", "    lr_scheduler.step()\n", "print('========================================Finished Training ===========================================')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"collapsed_sections": ["mp-EzMvdOlRO", "BGlbwPN1z9Co"], "name": " Jathu_Sleep_Stage_Classification_(Cross Transformer)_sleepEDF_(EEG,EEG2,EOG).ipynb", "provenance": []}, "interpreter": {"hash": "94c91063cbfafe7dd443522ce7f45eaee09f9c12e6866a8b2d3ce13a69535fc6"}, "kernelspec": {"display_name": "sleep_monitoring", "language": "python", "name": "sleep_monitoring"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.15"}}, "nbformat": 4, "nbformat_minor": 4}