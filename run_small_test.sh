#!/bin/bash
# 小规模测试脚本 - 只使用5个subjects进行快速验证

echo "🧪 小规模测试 - 5个subjects"
echo "=================================="

# 激活conda环境
eval "$(conda shell.bash hook)"
conda activate sl

# 1. 预处理小批量数据
echo "📊 预处理数据集(5个subjects)..."
python preprocess_full_dataset.py \
    --data_dir /media/main/ypf/eeg/data-edf/sleep_edf_20 \
    --output_dir ./processed_data_small \
    --subjects "1,2,3,4,5"

# 2. 训练MAMBAFORMER模型（少量epochs）
echo -e "\n🏗️ 训练MAMBAFORMER模型(10 epochs)..."
python train_full_dataset.py \
    --model_name mambaformer \
    --data_path ./processed_data_small \
    --train_subjects "1,2,3" \
    --val_subjects "4" \
    --test_subjects "5" \
    --batch_size 64 \
    --epochs 10 \
    --lr 1e-4 \
    --patience 5 \
    --experiment_name mambaformer_small_test \
    --log_dir ./log \
    --save_dir ./checkpoints \
    --use_progressive \
    --device auto

echo -e "\n✅ 测试完成！"
echo "📊 结果保存在 ./log 目录"